import Layout from "components/shared/layout";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  height: calc(100vh - 324px);
`;
export default function Custom500() {
  const { t } = useTranslation();
  return (
    <Layout>
      <Div className="d-flex justify-content-center align-items-center">
        <h1 className="text-capitalize">{t("page not found")}</h1>
      </Div>
    </Layout>
  );
}
