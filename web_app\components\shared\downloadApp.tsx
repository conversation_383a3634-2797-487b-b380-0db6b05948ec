/* eslint-disable @next/next/no-img-element */
import { Grid } from "@material-ui/core";
import { useRouter } from "next/dist/client/router";
import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const GridElement = styled(Grid)`
  padding: 2rem 0;
  p {
    font-size: 1.2rem;
  }
  @media (max-width: 900px) {
    .section-fancy-title {
      margin-bottom: 1rem;
    }
    #Android,
    #ios {
      text-align: center;
      margin-bottom: 20px;
    }
  }
  #buttons-wrap {
    margin-top: 35px;
    #Android,
    #ios {
      cursor: pointer;
    }
    img {
      -webkit-box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
      -moz-box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
      box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
      border-radius: var(--radius-2);
      width: 75%;
    }
  }
`;

export default function DownloadApp() {
  const { t } = useTranslation();
  const { pathname, asPath } = useRouter();

  useEffect(() => {
    if (typeof window != "undefined" && asPath?.includes("#download-app")) {
      document
        .querySelector("#download-app")
        .scrollIntoView({ behavior: "smooth" }, true);
    }
  }, [asPath]);

  return (
    <GridElement
      container
      justifyContent="space-between"
      alignItems="flex-start"
    >
      <Grid item xs={12} md={2}>
        <div className="section-fancy-title">
          <h2>{`${t("Download")} ${t("The App Now")}`}</h2>
        </div>
      </Grid>
      <Grid item xs={12} md={7}>
        <p>
          {
            t(
              "Renting a car has never been easier with Carwah app. Get connected to get exclusive offers, personalized experience and other features with our app on Android or iOS platforms."
            ) as string
          }
        </p>
        <Grid container id="buttons-wrap">
          <Grid id="Android" item xs={12} md={6}>
            <a
              href="https://play.google.com/store/apps/details?id=com.carwah.customer&hl=en_US&gl=US"
              target="_blank"
              rel="noreferrer"
            >
              <img src={`/assets/images/android.svg`} alt="android" />
            </a>
          </Grid>
          <Grid id="ios" item xs={12} md={6}>
            <a
              href="https://apps.apple.com/sa/app/carwah-%D9%83%D8%B1%D9%88%D8%A9/id1387161215"
              target="_blank"
              rel="noreferrer"
            >
              <img src={`/assets/images/ios.svg`} alt="ios" />
            </a>
          </Grid>
        </Grid>
      </Grid>
      {pathname === "/" ? (
        <Grid item xs={12} md={2} className="text-center">
          <img src={`/assets/images/qr.gif`} alt="qr" />
        </Grid>
      ) : null}
    </GridElement>
  );
}
