/* eslint-disable @next/next/no-img-element */
import React, { useEffect } from "react";
import { ClickAwayListener, LinearProgress } from "@material-ui/core";
import Tooltip from "components/shared/tooltip";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import useGeoLocation from "hooks/useGeoLocation";
import { useTranslation } from "react-i18next";
import { Areas_Query } from "gql/queries/areas";
import { useQuery } from "@apollo/client";
import { setAreasList } from "store/search/action";

const AreasTooltip = ({
  action,
  isOpen,
  setIsOpen,
  filteredAreas,
  areaChangeHandler,
}) => {
  // Hooks
  const { navigatorGeoCode } = useGeoLocation();
  const { i18n, t } = useTranslation();

  // Store
  const dispatch = useDispatch();
  const { language, search_data } =
    useSelector((state: RootStateOrAny) => state) || {};
  const { areas } = search_data || {};

  // Queries
  const { data: areasListRes } = useQuery(Areas_Query, {
    fetchPolicy: "cache-first",
  });

  useEffect(() => {
    if (areasListRes?.areas && !areas?.list?.length) {
      dispatch(setAreasList({ list: areasListRes?.areas, language }));
    }
  }, [areasListRes, areas, dispatch, language]);

  const clickHandler = (args) => {
    areaChangeHandler && areaChangeHandler();
    dispatch(action({ ...args }));
    setTimeout(() => {
      setIsOpen(false);
    });
  };

  function geoLocationCallback(cb) {
    const filteredArea = areasListRes?.areas?.find((i) => i.name == cb.name);
    clickHandler(filteredArea);
  }
  return (
    <ClickAwayListener
      mouseEvent="onMouseDown"
      touchEvent="onTouchStart"
      onClickAway={() => {
        setIsOpen(false);
      }}
    >
      <div className="tooltip-wrap">
        <Tooltip
          isOpen={isOpen}
          // position={i18n.language === "en" ? "left" : "right"}
        >
          <div
            className="d-flex flex-column"
            style={{ width: "max-content", maxWidth: "86vw" }}
          >
            <div
              className="head d-flex justify-content-start mb-2"
              onClick={() =>
                navigatorGeoCode(action, setIsOpen, geoLocationCallback)
              }
              style={{ borderBottom: "solid 1px var(--color-4)" }}
            >
              <img src={`/assets/images/direction.svg`} alt="direction" />
              <span className="color-4">{t("Current location") as string}</span>
            </div>
            {(filteredAreas || areasListRes?.areas?.length
              ? filteredAreas || areasListRes?.areas
              : []
            ).map((i) => (
              <div key={i.id}>
                <div
                  className={`cursor-pointer ${
                    !i.airports ? "mb-2" : ""
                  } d-flex justify-content-start`}
                  onClick={(e) => {
                    clickHandler({
                      ...i,
                      airport: null,
                      address: i.name,
                      lat: i.centerLat,
                      lng: i.centerLng,
                    });
                  }}
                >
                  <img src={`/assets/images/cityIcon.svg`} alt="direction" />
                  <span
                    className="color-4 d-inline-block"
                    style={{ transform: "translateY(2px)" }}
                  >
                    {i?.[`${i18n.language}Name`]}
                  </span>
                </div>
                {i?.airports?.map((airport) => (
                  <div
                    key={airport.id}
                    className={`cursor-pointer mb-2 d-flex justify-content-start ${
                      i18n.language === "en" ? "ml-4" : "mr-4"
                    }`}
                    onClick={() => {
                      clickHandler({
                        ...i,
                        airport,
                      });
                    }}
                  >
                    <img src={`/assets/icons/airport.svg`} alt="direction" />
                    <span className="color-4 d-inline-block mx-2">
                      {airport?.[`${i18n.language}Name`]}
                    </span>
                  </div>
                ))}
              </div>
            ))}
            {filteredAreas && filteredAreas.length === 0 && (
              <div className="text-center py-2">
                <span className="color-4">
                  {t("No matching results!") as string}
                </span>
              </div>
            )}
            {!filteredAreas && !areasListRes?.areas?.length && (
              <LinearProgress />
            )}
          </div>
        </Tooltip>
      </div>
    </ClickAwayListener>
  );
};

export default AreasTooltip;
