/* eslint-disable @next/next/no-img-element */
import { Button } from "@material-ui/core";
import Popup from "components/shared/popup";
import { memo } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { toggleFursanModal } from "store/loyality/action";
import styled from "styled-components";

const Div = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  h4 {
    margin-bottom: 15px !important;
  }
  button {
    padding: 10px 50px;
    border: var(--radius-5);
    font-weight: bold;
    background-color: var(--color-3);
    color: white;
    &:hover {
      background-color: var(--color-3);
    }
    margin-top: 20px;
  }
`;

function Verified() {
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const { is_fursan_membershipId_verified, is_fursan_modal_open } =
    useSelector((state: RootStateOrAny) => state.loyality) || {};

  return (
    <Popup
      {...{
        isOpen: is_fursan_membershipId_verified && is_fursan_modal_open,
        setIsOpen: () => dispatch(toggleFursanModal(false)),
      }}
    >
      <Div>
        <img src="/assets/icons/verified.svg" alt="img" />
        <h4 className="bold text-center color-3">
          {t("Verified successfully") as string}
        </h4>
        <div className="note-loyality">
          <h6 className="bold">{t("Note") as string}*</h6>
          <p>
            {
              t(
                "Miles will be successfully earned once the car is returned and the booking is invoiced"
              ) as string
            }
          </p>
        </div>
        <Button
          onClick={() => {
            dispatch(toggleFursanModal(false));
          }}
        >
          {t("Okay") as string}
        </Button>
      </Div>
    </Popup>
  );
}

export default memo(Verified);
