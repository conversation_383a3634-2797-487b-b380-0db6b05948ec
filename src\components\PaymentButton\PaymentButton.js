import React, { useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Button } from "reactstrap";
import { Payment } from "@material-ui/icons";
import { NotificationManager } from "react-notifications";
import PaymentFlow from "../PaymentFlow/PaymentFlow";
import useAgencyStatus from "../../hooks/useAgencyStatus";
import "./PaymentButton.scss";

const PaymentButton = ({
  rentalId,
  extensionId = null,
  installmentId = null,
  totalAmount,
  walletBalance = 0,
  onPaymentSuccess,
  onPaymentError,
  disabled = false,
  size = "sm",
  color = "primary",
  className = "",
  children,
}) => {
  const { formatMessage } = useIntl();
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const { isAgencyDeactivated } = useAgencyStatus();

  const handlePaymentClick = () => {
    if (isAgencyDeactivated) {
      NotificationManager.error(
        formatMessage({ id: "Agency is deactivated. Payment is not allowed." })
      );
      return;
    }
    setPaymentModalOpen(true);
  };

  const handlePaymentSuccess = (paymentMethod) => {
    setPaymentModalOpen(false);
    NotificationManager.success(
      formatMessage({ id: "Payment completed successfully" })
    );
    if (onPaymentSuccess) {
      onPaymentSuccess(paymentMethod);
    }
  };

  const handlePaymentError = (error) => {
    setPaymentModalOpen(false);
    NotificationManager.error(error);
    if (onPaymentError) {
      onPaymentError(error);
    }
  };

  // Determine if this is an installment payment
  const isInstallment = !!installmentId;

  return (
    <>
      <Button
        color={color}
        size={size}
        onClick={handlePaymentClick}
        disabled={disabled || isAgencyDeactivated}
        className={`payment-button ${className}`}
        title={
          isAgencyDeactivated
            ? formatMessage({ id: "Agency is deactivated. Payment is not allowed." })
            : ""
        }
      >
        <Payment className="me-1" />
        {children || <FormattedMessage id="Pay Now" />}
      </Button>

      <PaymentFlow
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        rentalId={rentalId}
        extensionId={extensionId}
        isInstallment={isInstallment}
        totalAmount={totalAmount}
        walletBalance={walletBalance}
        isAgencyDeactivated={isAgencyDeactivated}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
      />
    </>
  );
};

export default PaymentButton;
