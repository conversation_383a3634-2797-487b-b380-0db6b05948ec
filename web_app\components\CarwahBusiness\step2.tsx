/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import Select, { createFilter } from "react-select";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Add from "@material-ui/icons/Add";
import Remove from "@material-ui/icons/Remove";
import { useEffect, useState } from "react";
import { DateTimePicker, MuiPickersUtilsProvider } from "@material-ui/pickers";
import MomentUtils from "@date-io/moment";
import moment from "moment";
import { useMutation, useQuery, useLazyQuery } from "@apollo/client";
import "moment/locale/ar";
import "moment/locale/en-au";
import {
  Insurances_Query,
  Makes_Query,
  ModelsOfMake_Query,
  Version_Query,
} from "gql/queries/car";
import { gql_limits } from "utilities/enums";
import { Areas_Query } from "gql/queries/areas";
import { CreateBusinessRental_Mutation } from "gql/mutations/businessRental";
import { set, useForm } from "react-hook-form";
import {
  Button,
  CircularProgress,
  IconButton,
  Snackbar,
} from "@material-ui/core";
import CloseIcon from "@material-ui/icons/Close";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import {
  SaveBookingData,
  EditBookingData,
  RemoveEditStatus,
} from "store/boooking/action";
import { useRouter } from "next/router";

const Form = styled.form`
  > div {
    margin-top: 28px !important;
    text-align: ${(props) => (props.language === "ar" ? "right" : "left")};
    & > div {
      padding: 7px 15px;
      width: 100%;
      &,
      &:hover {
        border: solid 1px var(--color-7);
      }
    }
  }
  h4 {
    margin-top: 30px !important;
  }
  > input {
    margin-bottom: 28px !important;
    padding: 13px 25px;
    width: 100%;
    border-radius: var(--radius-1);
    border: solid 1px var(--color-7);
  }
  .required {
    label {
      position: relative;
      &:after {
        content: "*";
        color: var(--color-9);
        position: absolute;
        top: 0;
        right: ${(props) => (props.language === "en" ? "-6px" : null)};
        left: ${(props) => (props.language === "ar" ? "-6px" : null)};
      }
    }
    &.select {
      > div {
        padding-right: 0 !important;
        padding-left: 0 !important;
      }
      div:first-child {
        width: fit-content;
        position: relative;
        &:before {
          content: "*";
          color: var(--color-9);
          position: absolute;
          top: 0;
          right: ${(props) => (props.language === "en" ? "-10px" : null)};
          left: ${(props) => (props.language === "ar" ? "-10px" : null)};
        }
      }
      div:only-child {
        width: auto !important;
        &:before {
          display: none;
        }
      }
    }
    [aria-hidden="true"],
    [aria-disabled="false"] {
      width: auto !important;
      &:after,
      &:before {
        display: none;
      }
    }
  }
`;
const PeriodWrapper = styled.div`
  svg {
    cursor: pointer;
  }
  > div {
    /* padding: 7px 20px !important; */
    border-radius: var(--radius-1);
    color: var(--color-8);
  }
  input {
    background: none;
    border: none;
    text-align: center !important;
    font-size: 24px;
    font-weight: bold;
    color: var(--color-1);
  }
`;

const CalendarWrapper = styled.div`
  > div {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  div {
    border-radius: var(--radius-1);
    &:after,
    &:before {
      border: none;
    }
    input {
      color: var(--color-8);
      padding: 10px 10px !important;
    }
    .MuiInput-input {
      padding-right: 5px;
      padding-left: 5px;
    }
  }
  .MuiInput-underline {
    &:before {
      border: none !important;
    }
  }
  .MuiTextField-root {
    position: relative;
    &:after {
      content: "*";
      color: var(--color-9);
      position: absolute;
      top: 6px;
      right: ${(props) => (props.language === "ar" ? "152px" : null)};
      left: ${(props) => (props.language === "en" ? "164px" : null)};
    }
  }
  &.hasValue {
    .MuiTextField-root {
      &:after {
        display: none;
      }
    }
  }
  img {
    transform: ${(props) =>
      props.language === "ar" ? "translateX(10px)" : "translateX(-10px)"};
  }
`;
const CarNumbersWrapper = styled.div`
  position: relative;
  span {
    position: absolute;
    top: 12px;
    right: ${(props) => (props.language === "ar" ? "15px" : null)};
    left: ${(props) => (props.language === "en" ? "15px" : null)};
  }
  input {
    padding: 13px 10px;
    width: 100%;
    border-radius: var(--radius-1);
    border: solid 1px var(--color-7);
  }
  &.number-cars {
    position: relative;
    &:after {
      content: "*";
      color: var(--color-9);
      position: absolute;
      top: 10px;
      right: ${(props) => (props.language === "ar" ? "45px" : null)};
      left: ${(props) => (props.language === "en" ? "52px" : null)};
    }
    &.hasValue {
      &:after {
        display: none;
      }
    }
  }
`;
const InsuranceTypeWrapper = styled.div`
  > div {
    border: none !important;
    text-align: center;
    margin-top: 0 !important;
    background-color: var(--color-6);
    color: var(--color-8);
    border-radius: var(--radius-3);
    padding: 18px 0 !important;
    cursor: pointer;
    &.active {
      color: var(--color-3);
      background: #f2f8fa;
      position: relative;
      &:after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: calc(50% - 15px);
        width: 30px;
        height: 5px;
        background: var(--color-3);
        border-radius: var(--radius-1) var(--radius-1) 0 0;
      }
    }
  }
`;
const AdditionalNotesWrapper = styled.div`
  margin-top: 25px !important;
  textarea {
    width: 100%;
    height: 150px;
    resize: none;
    border-radius: var(--radius-3);
    border-color: var(--color-7);
    padding: 15px;
  }
`;
const ButtonsWrapper = styled.div`
  button {
    width: 100%;
    border-radius: var(--radius-3);
    padding: 18px 0;
    font-weight: bold;
    border: none;
    background: none;
    &:first-child {
      color: var(--color-4);
      background: var(--color-1);
    }
  }
`;

const OtherMakeWrapper = styled.div`
  input {
    padding: 13px 10px;
    width: 100%;
    border-radius: var(--radius-1);
    border: solid 1px var(--color-7);
  }
  &.required {
    position: relative;
    &:after {
      content: "*";
      color: var(--color-9);
      position: absolute;
      top: 10px;
      right: ${(props) => (props.language === "ar" ? "42px" : null)};
      left: ${(props) => (props.language === "en" ? "50px" : null)};
    }
    &.hasValue {
      &:after {
        display: none;
      }
    }
  }
`;
const OtherCityWrapper = styled.div`
  input {
    padding: 13px 10px;
    width: 100%;
    border-radius: var(--radius-1);
    border: solid 1px var(--color-7);
  }
  &.required {
    position: relative;
    &:after {
      content: "*";
      color: var(--color-9);
      position: absolute;
      top: 10px;
      right: ${(props) => (props.language === "ar" ? "88px" : null)};
      left: ${(props) => (props.language === "en" ? "80px" : null)};
    }
    &.hasValue {
      &:after {
        display: none;
      }
    }
  }
`;

const Validation = styled.span`
  display: inline-block;
  color: var(--color-9);
  padding: 0 10px;
  transform: translateY(10px);
  width: 100%;
`;
const Steppeing = styled.div`
  min-width: 300px;
  .container {
    width: 100%;
    margin-top: 10px;
  }

  .progressbar {
    counter-reset: step;
  }
  .progressbar li {
    list-style: none;
    display: inline-block;
    width: 30.33%;
    position: relative;
    cursor: pointer;
    color: #aeaeae;
  }
  .progressbar li:before {
    content: "";
    counter-increment: step;
    width: 8px;
    height: 8px;
    border: 1px solid #ddd;
    border-radius: 100%;
    display: block;
    right: 0;
    background-color: #fff;
    position: relative;
    z-index: 9;
  }
  .progressbar li:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #eff1f5;
    top: 2px;
    left: ${(props) => (props.language == "en" ? "-100%" : "100%")};
  }
  .progressbar li:first-child:after {
    content: none;
  }
  .progressbar li.active {
    color: var(--color-2);
  }
  .progressbar li.active:before {
    border-color: var(--color-2);
  }

  .progressbar li.active:before {
    background-color: var(--color-2) !important;
  }
  .progressbar li span {
    transform: ${(props) =>
      props.language == "en" ? "translateX(-30px)" : "translateX(30px)"};
    display: inline-block;
    width: 100%;
    text-align: center;
  }
  .progressbar li:first-child:after {
    content: none;
  }
  .progressbar li.active {
    color: var(--color-2);
  }
  .progressbar li.end {
    color: #7ab3c5;
  }
  .progressbar li.active:before {
    border-color: var(--color-2);
  }
  .progressbar li.end:before {
    background-color: #7ab3c5 !important;
  }
  .progressbar li.active:before {
    background-color: var(--color-2) !important;
  }
`;

const Head = styled.div`
  @media (max-width: 768px) {
    flex-direction: column;
    ul,
    .container {
      padding: 0;
      margin: 0;
    }
  }
`;

export default function Step2() {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const BookingState = useSelector((state: RootStateOrAny) => state.booking);
  const state = useSelector((state: RootStateOrAny) => state);
  const [listover, setListOver] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    clearErrors,
    setValue,
    getValues,
  } = useForm({ mode: "onChange" });
  const [periodMonths, setPeriodMonths] = useState(1);
  const [selectedDateTime, setSelectedDateTime] = useState(null);
  const [insuranceType, setInsuranceType] = useState(1);
  const [snackBarClosed, setSnackBarClosed] = useState(true);
  const [snackBarMessage, setSnackBarMessage] = useState("");
  const [BookingData, setBookingData] = useState({});
  const [showOtherMake, setShowOtherMake] = useState(false);
  const [showOtherCityName, setShowOtherCityName] = useState(false);
  const [showPeriodMonthsErr, setShowPeriodMonthsErr] = useState(false);
  const router = useRouter();

  const [
    createBusinessRental,
    { data: businessRentalRes, loading: businessRentalResLoading },
  ] = useMutation(CreateBusinessRental_Mutation);

  const { data: makes_res, loading: loadingMakes } = useQuery(Makes_Query, {
    variables: { limit: 500, status: true },
  });
  const { data: areas_res, loading: loadingAreas } = useQuery(Areas_Query, {
    variables: { limit: 500 },
    fetchPolicy: "cache-first",
  });
  const { data: Insurances_res, loading: loadingInsurances } =
    useQuery(Insurances_Query);

  const [fetchModelsofMake, { data: modelsRes, loading: loadingModels }] =
    useLazyQuery(ModelsOfMake_Query, {
      variables: { limit: 500 },
    });
  const [fetchVersions, { data: versionsRes, loading: loadingVersions }] =
    useLazyQuery(Version_Query, {
      variables: { limit: 500 },
    });

  const handleDateTimeChange = (e) => {
    if (BookingState.EditStatus) {
      BookingState.BookingListToEdit.pickUpDatetime = e
        .locale("en")
        .format("DD/MM/YYYY hh:mm A")
        .toString();
    }
    setSelectedDateTime(e);
    setValue("pickUpDatetime", e);
    clearErrors("pickUpDatetime");
  };

  moment.locale(i18n.language);

  useEffect(() => {
    let clean = false;
    if (!clean) {
      setValue("numberOfMonths", periodMonths);
    }
    return (clean = true);
  }, [periodMonths]);

  useEffect(() => {
    let clean = false;
    if (!clean) {
      moment.locale(i18n.language === "ar" ? "ar" : "en-au");
    }
    return (clean = true);
  }, [i18n.language]);

  const onSubmit = (data) => {
    if (BookingState.list?.length == 10 && !BookingState.EditStatus) {
      setListOver(true);
      router.push("carwah-business#step3");
      return;
    }
    data.numberOfMonths = periodMonths;
    data.numberOfCars = Number(data.numberOfCars);
    const oldDateTime = data.pickUpDatetime;
    data.pickUpDatetime = `${moment(oldDateTime)
      .locale("en")
      .format("DD/MM/YYYY")} ${moment(oldDateTime)
      .locale("en")
      .format("hh:mm A")}`;
    data.userId = state.authentication.login_data.user.id;
    data.insuranceId = insuranceType;
    const bookingdate = {
      ...BookingData,
      pickUpDatetime: data.pickUpDatetime,
      numberOfMonths: data.numberOfMonths,
      additionalNotes: data.additionalNotes,
    };
    if (BookingState.EditStatus) {
      BookingState.BookingListToEdit.numberOfMonths = periodMonths;
      BookingState.BookingListToEdit.additionalNotes = data.additionalNotes;
      dispatch(
        EditBookingData([
          data,
          Object.keys(bookingdate).length ==
          Object.keys(BookingState.BookingListToEdit).length
            ? bookingdate
            : BookingState.BookingListToEdit,
        ])
      );
      router.push("carwah-business#step3");
      return;
    }
    dispatch(SaveBookingData([data, bookingdate]));
    router.push("carwah-business#step3");
  };

  function handleCloseSnackBar() {
    setSnackBarClosed(true);
  }
  const action = (
    <>
      <IconButton
        size="small"
        aria-label="close"
        color="inherit"
        onClick={handleCloseSnackBar}
      >
        <CloseIcon fontSize="small" />
      </IconButton>
    </>
  );
  useEffect(() => {
    let clean = false;
    const data = BookingState?.EditStatus
      ? BookingState?.EditBooking
      : BookingState?.list?.[0];

    if (!clean && data) {
      if (data?.makeId) {
        if (data?.makeId == 999) {
          setShowOtherMake(true);
        }
        setValue("makeId", data.makeId);
        fetchModelsofMake({
          variables: {
            id: data.makeId,
          },
        });
      }
      if (data?.carModelId) {
        setValue("carModelId", data.carModelId);
        fetchVersions({
          variables: {
            carModelId: +data.carModelId,
            limit: 500,
            isActive: true,
          },
        });
      }
      if (data?.pickUpCityId) {
        setValue("pickUpCityId", data.pickUpCityId);
      }
      if (data?.numberOfMonths) {
        setPeriodMonths(data.numberOfMonths);
      }
      if (data?.insuranceId) {
        setInsuranceType(+data.insuranceId);
      }
      if (data?.pickUpDatetime) {
        setSelectedDateTime(moment(data.pickUpDatetime, "DD/MM/YYYY hh:mm A"));
        setValue(
          "pickUpDatetime",
          moment(data.pickUpDatetime, "DD/MM/YYYY hh:mm A")
        );
      }
      if (data?.carVersionId) {
        setValue("carVersionId", data.carVersionId);
      }
      if (data?.numberOfCars) {
        setValue("numberOfCars", data.numberOfCars);
      }
      if (data?.additionalNotes) {
        setValue("additionalNotes", data?.additionalNotes);
      }
      if (data?.car_name) {
        setValue("car_name", data?.car_name);
      }
      if (BookingState?.EditBooking?.city_name) {
        setValue("city_name", BookingState?.EditBooking?.city_name);
      }
    }
    return (clean = true);
  }, [BookingState]);

  useEffect(() => {
    let clean = false;
    if (
      !clean &&
      Insurances_res?.insurances?.length &&
      !BookingState.EditStatus
    ) {
      const item = Insurances_res.insurances.find((ins) => +ins.id == 1);
      setBookingData({
        ...BookingData,
        insuranceType: {
          enName: item.enName,
          arName: item.arName,
        },
      });
    }
    return (clean = true);
  }, [Insurances_res?.insurances]);

  return (
    <>
      <div>
        <Head className="d-flex justify-content-between">
          <div>
            <h2 className="font-27px bold mb-3">{t("Add Request")}</h2>
          </div>
          <Steppeing language={i18n.language}>
            <div className="container">
              <ul className="progressbar text-align-localized">
                <li
                  className="end"
                  onClick={() => router.push("carwah-business#step1")}
                >
                  <span>{t("step1") as string}</span>
                </li>
                <li
                  className="active"
                  onClick={() => {
                    if (
                      BookingState?.list?.length &&
                      BookingState?.BookingList?.length
                    ) {
                      router.push("carwah-business#step2");
                    }
                  }}
                >
                  <span>{t("step2") as string}</span>
                </li>
                <li onClick={() => router.push("carwah-business#step3")}>
                  <span>{t("step3") as string}</span>
                </li>
              </ul>
            </div>
          </Steppeing>
        </Head>
        {BookingState?.BookingPositionToEdit ? (
          <h3 className="bold mb-5 font-20px">
            {t("Car No.")}{" "}
            <span>{BookingState?.BookingPositionToEdit + 1}</span>
          </h3>
        ) : null}
        <Form
          id="step2"
          language={i18n.language}
          onSubmit={handleSubmit(onSubmit)}
        >
          {makes_res?.makes && (
            <Select
              id="makeId"
              {...register("makeId", { required: true })}
              placeholder={t("Make")}
              isSearchable
              isClearable={() => alert("et")}
              className="select required"
              filterOption={createFilter({
                matchFrom: "any",
                stringify: (option) => `${option.label}`,
              })}
              defaultValue={
                makes_res?.makes &&
                BookingState.EditStatus &&
                [
                  ...makes_res?.makes?.collection,
                  {
                    id: 999,
                    name: t("Other"),
                    enName: "other",
                    arName: "أخري",
                  },
                ]
                  .filter(
                    (item) => +item.id == +BookingState.EditBooking.makeId
                  )
                  .map((item) => {
                    return {
                      label: item.name,
                      value: item.id,
                      enName: item.enName,
                      arName: item.arName,
                    };
                  })
              }
              options={
                !loadingMakes
                  ? [
                      ...makes_res?.makes?.collection,
                      {
                        id: 999,
                        name: t("Other"),
                        enName: "other",
                        arName: "أخري",
                      },
                    ].map((item) => {
                      return {
                        label: item.name,
                        value: item.id,
                        enName: item.enName,
                        arName: item.arName,
                      };
                    })
                  : []
              }
              isLoading={loadingMakes}
              onFocus={() => {
                document.querySelector("#makeId").scrollIntoView();
                window.scrollBy(0, -200);
              }}
              onChange={(e) => {
                setValue("carModelId", null);
                if (e?.enName === "other") {
                  setShowOtherMake(true);
                  BookingState.BookingListToEdit.carModelId = null;
                  BookingState.BookingListToEdit.carVersionId = null;
                } else {
                  setShowOtherMake(false);
                }
                if (e?.value) {
                  if (BookingState.EditStatus) {
                    BookingState.BookingListToEdit.makeId = {
                      enName: e.enName,
                      arName: e.arName,
                    };
                  }
                  setBookingData({
                    ...BookingData,
                    makeId: { enName: e.enName, arName: e.arName },
                  });
                  fetchModelsofMake({ variables: { id: e.value } });
                  setValue("makeId", e.value);
                  clearErrors("makeId");
                } else {
                  setValue("makeId", null);
                }
              }}
            />
          )}
          {!showOtherMake ? (
            <>
              {errors.makeId && (
                <Validation className="text-align-localized">
                  {t("This field is required")}
                </Validation>
              )}
              {modelsRes ? (
                <Select
                  id="carModelId"
                  {...register("carModelId", { required: true })}
                  placeholder={t("Model")}
                  isSearchable
                  isClearable
                  className="select required"
                  isLoading={loadingModels}
                  filterOption={createFilter({
                    matchFrom: "any",
                    stringify: (option) => `${option.label}`,
                  })}
                  options={
                    !loadingModels
                      ? modelsRes.make.carModels.map((item) => {
                          return {
                            label: item.name,
                            value: item.id,
                            enName: item.enName,
                            arName: item.arName,
                          };
                        })
                      : []
                  }
                  defaultValue={modelsRes.make.carModels
                    .filter(
                      (item) =>
                        +item.id == +BookingState.EditBooking?.carModelId
                    )
                    .map((item) => {
                      return {
                        label: item.name,
                        value: item.id,
                        enName: item.enName,
                        arName: item.arName,
                      };
                    })}
                  onFocus={() => {
                    document.querySelector("#carModelId").scrollIntoView();
                    window.scrollBy(0, -200);
                  }}
                  onChange={(e) => {
                    if (e?.value) {
                      if (BookingState.EditStatus) {
                        BookingState.BookingListToEdit.carModelId = {
                          enName: e.enName,
                          arName: e.arName,
                        };
                      }
                      setBookingData({
                        ...BookingData,
                        carModelId: { enName: e.enName, arName: e.arName },
                      });
                      setValue("carVersionId", null);
                      fetchVersions({
                        variables: {
                          carModelId: Number(e.value),
                          isActive: true,
                          limit: 500,
                        },
                      });
                      setValue("carModelId", e.value);
                      clearErrors("carModelId");
                    } else {
                      setValue("carModelId", null);
                    }
                  }}
                />
              ) : null}
              {errors.carModelId && (
                <Validation className="text-align-localized">
                  {t("This field is required")}
                </Validation>
              )}
              {versionsRes ? (
                <Select
                  id="carVersionId"
                  onFocus={() => {
                    document.querySelector("#carVersionId").scrollIntoView();
                    window.scrollBy(0, -200);
                  }}
                  {...register("carVersionId", { required: true })}
                  placeholder={t("Make Year")}
                  isSearchable
                  isClearable
                  className="select required"
                  isLoading={loadingVersions}
                  filterOption={createFilter({
                    matchFrom: "any",
                    stringify: (option) => `${option.label}`,
                  })}
                  // defaultValue={BookingState.EditBooking.carVersionId}
                  defaultValue={versionsRes.carVersions.collection
                    .filter(
                      (item) =>
                        +item.id == +BookingState.EditBooking?.carVersionId
                    )
                    .map((item) => {
                      return {
                        label: item.year,
                        value: item.id,
                      };
                    })}
                  options={
                    !loadingVersions
                      ? versionsRes.carVersions.collection.map((item) => {
                          return {
                            label: item.year,
                            value: item.id,
                          };
                        })
                      : []
                  }
                  onChange={(e) => {
                    if (e?.value) {
                      if (BookingState.EditStatus) {
                        BookingState.BookingListToEdit.carVersionId = e.label;
                      }
                      setBookingData({ ...BookingData, carVersionId: e.label });

                      setValue("carVersionId", e.value);
                      clearErrors("carVersionId");
                    } else {
                      setValue("carVersionId", null);
                    }
                  }}
                />
              ) : null}
              {errors.carVersionId && (
                <Validation className="text-align-localized">
                  {t("This field is required")}
                </Validation>
              )}
            </>
          ) : (
            <>
              <OtherMakeWrapper
                className={`required ${
                  (BookingData?.car_name ||
                    BookingState?.EditBooking?.car_name) &&
                  "hasValue"
                }`}
                language={i18n.language}
              >
                <input
                  type="text"
                  placeholder={t("Other")}
                  {...register("car_name", { required: true })}
                  maxLength={150}
                  defaultValue={
                    BookingState.EditStatus
                      ? BookingState?.EditBooking?.car_name
                      : BookingData?.car_name
                  }
                  onChange={(e) => {
                    if (e.target.value) {
                      if (BookingState.EditStatus) {
                        BookingState.BookingListToEdit.car_name =
                          e.target.value;
                      }
                      setBookingData({
                        ...BookingData,
                        car_name: e.target.value,
                      });

                      setValue("car_name", e.target.value);
                      clearErrors("car_name");
                    } else {
                      setValue("car_name", null);
                      setBookingData({
                        ...BookingData,
                        car_name: null,
                      });
                    }
                  }}
                />
              </OtherMakeWrapper>
              {errors.car_name && (
                <Validation className="text-align-localized">
                  {t("This field is required")}
                </Validation>
              )}
            </>
          )}

          <CarNumbersWrapper
            className={`number-cars ${
              (BookingData?.numberOfCars ||
                (BookingState?.EditBooking?.numberOfCars &&
                  BookingState.EditStatus)) &&
              "hasValue"
            }`}
            language={i18n.language}
          >
            <input
              {...register("numberOfCars", { required: true })}
              type="text"
              inputMode="numeric"
              pattern="\d*"
              placeholder={t("Count")}
              defaultValue={
                BookingState.EditStatus
                  ? BookingState?.EditBooking?.numberOfCars
                  : null
              }
              onInput={(e) => {
                e.target.value = e.target.value.replace(
                  /^(?!([1-9]))[0-9]+$/g,
                  ""
                );
                if (BookingState.EditStatus) {
                  BookingState.BookingListToEdit.numberOfCars = e.target.value;
                }
                setBookingData({
                  ...BookingData,
                  numberOfCars: e.target.value,
                });

                clearErrors("numberOfCars");
              }}
              maxLength={5}
            />
          </CarNumbersWrapper>
          {errors.numberOfCars && (
            <Validation className="text-align-localized">
              {t("This field is required")}
            </Validation>
          )}
          {!loadingAreas && (
            <Select
              {...register("pickUpCityId", { required: true })}
              placeholder={t("City")}
              isSearchable
              isClearable
              className="select required"
              isLoading={loadingAreas}
              filterOption={createFilter({
                matchFrom: "any",
                stringify: (option) => `${option.label}`,
              })}
              defaultValue={
                BookingState.EditStatus &&
                !loadingAreas &&
                [
                  ...areas_res?.areas,
                  {
                    id: 999,
                    name: t("Other"),
                    enName: "other",
                    arName: "أخري",
                  },
                ]
                  ?.filter(
                    (item) =>
                      +item.id == +BookingState.EditBooking?.pickUpCityId
                  )
                  .map((item) => {
                    return {
                      label: item.name,
                      value: item.id,
                      enName: item.enName,
                      arName: item.arName,
                    };
                  })
              }
              options={
                !loadingAreas
                  ? [
                      ...areas_res?.areas,
                      {
                        id: 999,
                        name: t("Other"),
                        enName: "other",
                        arName: "أخري",
                      },
                    ]?.map((item) => {
                      return {
                        label: item.name,
                        value: item.id,
                        enName: item.enName,
                        arName: item.arName,
                      };
                    })
                  : []
              }
              onChange={(e) => {
                if (e?.enName === "other") {
                  setShowOtherCityName(true);
                  BookingState.BookingListToEdit.pickUpCityId = null;
                } else {
                  setShowOtherCityName(false);
                }
                if (e?.value) {
                  if (BookingState.EditStatus) {
                    BookingState.BookingListToEdit.pickUpCityId = {
                      enName: e.enName,
                      arName: e.arName,
                    };
                  }
                  setBookingData({
                    ...BookingData,
                    pickUpCityId: { enName: e.enName, arName: e.arName },
                  });

                  setValue("pickUpCityId", e.value);
                  clearErrors("pickUpCityId");
                } else {
                  setValue("pickUpCityId", null);
                }
              }}
            />
          )}

          {!showOtherCityName ? (
            errors.pickUpCityId && (
              <Validation className="text-align-localized">
                {t("This field is required")}
              </Validation>
            )
          ) : (
            <>
              <OtherCityWrapper
                className={`required ${
                  (BookingData?.city_name ||
                    BookingState?.EditBooking?.city_name) &&
                  "hasValue"
                }`}
                language={i18n.language}
              >
                <input
                  type="text"
                  placeholder={t("City Name")}
                  {...register("city_name", { required: true })}
                  maxLength={150}
                  defaultValue={
                    BookingState.EditStatus
                      ? BookingState?.EditBooking?.city_name
                      : BookingData?.city_name
                  }
                  onChange={(e) => {
                    if (e.target.value) {
                      if (BookingState.EditStatus) {
                        BookingState.BookingListToEdit.city_name =
                          e.target.value;
                      }
                      setBookingData({
                        ...BookingData,
                        city_name: e.target.value,
                      });

                      setValue("city_name", e.target.value);
                      clearErrors("city_name");
                    } else {
                      setValue("city_name", null);
                      setBookingData({
                        ...BookingData,
                        city_name: null,
                      });
                    }
                  }}
                />
              </OtherCityWrapper>
              {errors.city_name && (
                <Validation className="text-align-localized">
                  {t("This field is required")}
                </Validation>
              )}
            </>
          )}
          <PeriodWrapper>
            <div className="d-flex justify-content-between align-items-center px-2 required position-relative">
              <label className="px-1">{t("Duration in months")}</label>
              <div className="d-flex flex-row-reverse align-items-center justify-content-between">
                <span
                  onClick={() =>
                    Number(periodMonths) < 60
                      ? (setPeriodMonths(Number(periodMonths) + 1),
                        setShowPeriodMonthsErr(false))
                      : setShowPeriodMonthsErr(true)
                  }
                >
                  <Add />
                </span>
                <input
                  {...register("numberOfMonths", { required: true })}
                  type="number"
                  min={0}
                  max={60}
                  value={periodMonths}
                  disabled
                />
                <span
                  onClick={() =>
                    Number(periodMonths) > 1
                      ? (setPeriodMonths(Number(periodMonths) - 1),
                        setShowPeriodMonthsErr(false))
                      : setShowPeriodMonthsErr(true)
                  }
                >
                  <Remove />
                </span>
              </div>
            </div>
            {showPeriodMonthsErr && (
              <Validation className="text-align-localized">
                {t("Min. 1& Max. 60")}
              </Validation>
            )}
          </PeriodWrapper>
          <CalendarWrapper
            language={i18n.language}
            className={`${selectedDateTime && "hasValue"}`}
          >
            <div className="d-flex justify-content-between align-items-center required position-relative">
              <div style={{ flexBasis: "100%" }}>
                <MuiPickersUtilsProvider
                  libInstance={moment}
                  utils={MomentUtils}
                  locale={i18n.language}
                >
                  <DateTimePicker
                    {...register("pickUpDatetime", { required: true })}
                    value={selectedDateTime}
                    onChange={handleDateTimeChange}
                    okLabel={t("Ok")}
                    cancelLabel={t("Cancel")}
                    ampm={true}
                    emptyLabel={t("Expected pick up date")}
                    disablePast
                    className="w-100"
                  />
                </MuiPickersUtilsProvider>
              </div>
              <img
                src="/assets/images/business/calendar.svg"
                alt="calendar-icon"
              />
            </div>
          </CalendarWrapper>
          {errors.pickUpDatetime && (
            <Validation className="text-align-localized">
              {t("This field is required")}
            </Validation>
          )}
          <h4 className="font-20px bold color-1">{t("Insurance type")}</h4>
          <InsuranceTypeWrapper className="d-flex justify-content-between gap-30px">
            {!loadingInsurances ? (
              Insurances_res.insurances.map((item) => {
                return (
                  <div
                    key={item.id}
                    className={`${insuranceType == item.id && "active"} bold`}
                    onClick={() => {
                      if (BookingState.EditStatus) {
                        BookingState.BookingListToEdit.insuranceType = {
                          enName: item.enName,
                          arName: item.arName,
                          id: item.id,
                        };
                      }
                      setBookingData({
                        ...BookingData,
                        insuranceType: {
                          enName: item.enName,
                          arName: item.arName,
                        },
                      });
                      setInsuranceType(item.id);
                    }}
                  >
                    {item.name}
                  </div>
                );
              })
            ) : (
              <div style={{ background: "none" }}>
                <CircularProgress />
              </div>
            )}
            <input
              {...register("insuranceId", { required: true })}
              hidden
              value={insuranceType}
            />
          </InsuranceTypeWrapper>
          <h4 className="font-20px bold color-1">{t("Additional Notes")}</h4>
          <AdditionalNotesWrapper>
            <textarea
              {...register("additionalNotes")}
              maxLength={1000}
              placeholder={t("Type here")}
              defaultValue={
                BookingState.EditStatus
                  ? BookingState.EditBooking?.additionalNotes
                  : ""
              }
            />
          </AdditionalNotesWrapper>
          <ButtonsWrapper className="d-flex justify-content-between gap-30px">
            <button type="sumbit">{t("Save")}</button>
            <button
              onClick={(e) => {
                e.preventDefault();
                if (BookingState.EditStatus) {
                  dispatch(RemoveEditStatus());
                  setTimeout(() => {
                    router.push("carwah-business#step3");
                  }, 1000);
                  return;
                }
                router.push("carwah-business#step1");
              }}
            >
              {BookingState.EditStatus ? t("Cancel") : t("Back")}
            </button>
          </ButtonsWrapper>
        </Form>
      </div>
      <Snackbar
        open={!snackBarClosed}
        autoHideDuration={6000}
        onClose={handleCloseSnackBar}
        message={snackBarMessage}
        action={action}
      />
      <Snackbar
        open={listover}
        autoHideDuration={6000}
        onClose={() => setListOver(false)}
        message={t("Max. no of requests is 10 per time")}
        action={action}
      />
    </>
  );
}
