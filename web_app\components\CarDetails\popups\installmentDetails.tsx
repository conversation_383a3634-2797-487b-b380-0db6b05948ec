/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @next/next/no-img-element */
/* eslint-disable react-hooks/exhaustive-deps */
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import styled from "styled-components";
import { installmentStatuses } from "utilities/enums";
import moment from "moment";
import { convertToEnglishNumbers } from "utilities/helpers";
import useLogic from "../Invoice/useLogic";
import useRentalType from "../heplers/rentalType";
import { InstallmetsPaymentsPrices } from "../InstallmetsPayments";
import ToggleTabs from "components/shared/ToggleTabs";
import { useState } from "react";
import Invoice from "../Invoice";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Wrapper = styled.div`
  /* height: 500px; */
  overflow-y: auto;
  .shadow {
    /* -webkit-box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
    -moz-box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
    box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%); */
    border-radius: var(--radius-3);
    overflow: hidden;
    background-color: #fff;
    box-shadow: none !important;
  }
  .card-grey {
    background-color: #f8f8f8;
    border-radius: var(--radius-3);
  }
`;

export default function InstallmentDetails({
  setExtensionId,
  extensionId,
  refetchRentalDetails,
  setCalendarTooltipOpen,
  isThereInstallmentToPay,
  isInstallmentDetailsModalOpen,
}) {
  //Hooks
  const { t, i18n } = useTranslation();
  const { toalDueAmoutOutside, getInstallmentsInvoiceData } = useLogic();
  const { isRentToOwn } = useRentalType();

  //Store
  const { rentalDetails } =
    useSelector((state: RootStateOrAny) => state.booking) || {};
  const { bookingNo } = rentalDetails || {};

  const { rental_about_price } = useSelector(
    (state: RootStateOrAny) => state.booking
  );

  const { installments, totalInstallmentsAmount, remainingInstallmentsAmount } =
    rental_about_price || {};

  //State
  const [installmentTypes, setInstallmentTypes] = useState<"paid" | "unpaid">(
    "unpaid"
  );

  const isPaid = (status: any) => {
    if (status === "paid" || status === "partially_refunded") return true;
    if (status != "paid" || status === "fully_refunded") return false;
  };

  return (
    <>
      <Wrapper>
        <div className="d-flex justify-content-between">
          <h4 className="bold">{t("Installments details") as string}</h4>
          <div
            style={{
              background: "#E6E6E6",
              borderRadius: "40px",
              padding: "3px 20px 5px 20px",
            }}
          >
            <p className="text-center bold">{bookingNo}</p>
          </div>
        </div>

        {isRentToOwn ? (
          <InstallmetsPaymentsPrices
            {...{
              t,
              title: t("Due Amount Currently"),
              price: toalDueAmoutOutside,
            }}
            style={{
              background: "black",
              color: "white",
              padding: "20px",
              borderRadius: "var(--radius-2)",
              marginTop: "20px",
            }}
          />
        ) : (
          <div className="shadow d-flex justify-content-between gap-10px p-3 my-3">
            <div className="card-grey p-3 text-center">
              <h6>{t("Total Amount Due") as string}</h6>
              <div className="bold d-flex gap-5px">
                <RiyalSymbol
                  style={{ order: i18n.language === "ar" ? 1 : 0 }}
                />
                <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                  {`${toalDueAmoutOutside || 0} `}
                </span>
              </div>
            </div>
            <div className="text-center p-3" style={{ color: "#6FCFA1" }}>
              <h6>{t("Total Paid") as string}</h6>
              <div className="bold d-flex gap-5px">
                <RiyalSymbol
                  style={{ order: i18n.language === "ar" ? 1 : 0 }}
                />
                <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                  {`${
                    getInstallmentsInvoiceData(installments)?.totalPaidAmount
                  } `}
                </span>
              </div>
            </div>
          </div>
        )}
        <div className="separator mt-3 mb-2" />
        <ToggleTabs
          tabs={[
            {
              title: t("Unpaid") as string,
              clickAction: () => {
                setInstallmentTypes("unpaid");
              },
              routerQuery: "unpaid",
            },
            {
              title: t("Paid") as string,
              clickAction: () => {
                setInstallmentTypes("paid");
              },
              routerQuery: "paid",
            },
          ]}
          activeTab={installmentTypes}
          activeTabColor="var(--color-2)"
        />
        {installments?.length
          ? installments?.map(({ amount, dueDate, id, status }) => {
              if (
                (isPaid(status) && installmentTypes === "paid") ||
                (!isPaid(status) && installmentTypes === "unpaid")
              ) {
                return (
                  <div key={id} className="shadow p-3 my-4">
                    <div className="d-flex gap-5px mb-2">
                      <span className="color-12" style={{ fontSize: " 14px" }}>
                        {convertToEnglishNumbers(
                          moment(dueDate).locale(i18n.language).format("LL")
                        )}
                      </span>
                    </div>
                    <div className="d-flex justify-content-between">
                      <span className="bold">
                        <span
                          style={{ fontSize: "24px", fontWeight: "bold" }}
                          className="d-flex gap-5px"
                        >
                          <RiyalSymbol
                            style={{ order: i18n.language === "ar" ? 1 : 0 }}
                          />
                          <span
                            style={{ order: i18n.language === "ar" ? 0 : 1 }}
                          >
                            {amount}
                          </span>
                        </span>
                      </span>
                      <span
                        className="bold"
                        style={{
                          background: installmentStatuses[status].color,
                          color: "white",
                          border: `solid 1px ${installmentStatuses[status].color}`,
                          padding:
                            i18n.language === "en"
                              ? "4px 35px 12px 25px"
                              : "4px 25px 12px 35px",
                          // borderRadius: "50px",
                          borderRadius:
                            i18n.language === "en" ? "15px 0" : "0 15px",
                          transform:
                            i18n.language === "en"
                              ? "translate(25px, 20px)"
                              : "translate(-25px, 20px)",
                        }}
                      >
                        {installmentStatuses[status].name[i18n.language]}
                      </span>
                    </div>
                  </div>
                );
              }
            })
          : null}
      </Wrapper>
      {Boolean(installmentTypes === "unpaid") && (
        <div
          style={{
            position: !isThereInstallmentToPay ? "absolute" : "initial",
            top: !isThereInstallmentToPay ? "-9999px" : "",
            left: !isThereInstallmentToPay ? "-9999px" : "",
          }}
        >
          <Invoice
            setCalendarTooltipOpen={setCalendarTooltipOpen}
            refetchRentalDetails={refetchRentalDetails}
            extensionId={extensionId}
            setExtensionId={setExtensionId}
            isIntallmentDetailsModal
            isInInstallmentBreakDown={undefined}
            hideAboutPrice={true}
          />
        </div>
      )}
    </>
  );
}
