import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import En from "./languages/en.json";
import Ar from "./languages/ar.json";

const resources = {
  ar: {
    translation: Ar,
  },
  en: {
    translation: En,
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng:
    typeof window != "undefined"
      ? window.location.pathname.includes("en")
        ? "en"
        : "ar"
      : undefined,
  initImmediate: false,
});

export default i18n;
