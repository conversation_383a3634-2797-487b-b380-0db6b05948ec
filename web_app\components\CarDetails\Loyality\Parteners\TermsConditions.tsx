/* eslint-disable @next/next/no-img-element */
import Popup from "components/shared/popup";
import { memo } from "react";
import { useTranslation, i18n } from "react-i18next";
import styled from "styled-components";
import React from "react";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    margin: 10px 0;
  }
`;
const termsConditions = (getSettingValue) => [
  {
    title: {
      en: "Payment and Settlement",
      ar: "الدفع و السداد",
    },
    body: {
      en: (
        <p>
          The payment method must be{" "}
          <span
            style={{
              textDecoration: "underline",
              margin: "0 1px",
              display: "inline-block",
            }}
          >
            through the app only
          </span>{" "}
          to earn rewards with one of our partners. Rewards will not be counted
          if cash payment is chosen.
        </p>
      ),
      ar: (
        <p>
          حتى تتمكن من اكتساب المكافأة لدى أحد شركائنا، يجب أن تكون طريقة الدفع
          <span
            style={{
              textDecoration: "underline",
              margin: "0 1px",
              display: "inline-block",
            }}
          >
            عبر التطبيق فقط
          </span>{" "}
          ، لن يتم احتساب المكافأة عند اختيار الدفع النقدي
        </p>
      ),
    },
  },
  {
    title: {
      en: "VAT",
      ar: "ضريبة القيمة المضافة",
    },
    body: {
      en: <p>The reward value is calculated before adding the VAT amount.</p>,
      ar: <p>يتم احتساب قيمة المكافأة قبل احتساب قيمة ضريبة القيمة المضافة</p>,
    },
  },
  {
    title: {
      en: "How are rewards calculated?",
      ar: "كيف يتم احتساب المكافآة؟",
    },
    body: {
      en: "For every {amount} spent in Carwah, you earn {mile} in AlFursan.",
      ar: "لكل {amount} يتم صرفها في كروة يكون مقابلها {mile} في الفرسان",
    },
  },
  {
    title: {
      en: "When are the rewards transferred?",
      ar: "متى يتم تحويل المكافأة؟",
    },
    body: {
      en: (
        <ul>
          <li>
            For daily, weekly, and monthly bookings, rewards are transferred 24
            hours after the booking is successfully invoiced and the car is
            returned.
          </li>
          <li>
            For installment bookings, rewards are transferred 60 days after the
            payment is completed.
          </li>
        </ul>
      ),
      ar: (
        <ul>
          <li className="text-align-localized">
            للحجوزات اليومية والأسبوعية والشهرية يتم تحويل المكافأة بعد 24 ساعة
            من فوترة الحجز بنجاح وإرجاع السيارة.
          </li>
          <li className="text-align-localized">
            لحجوزات الدفعات يتم تحويل المكافأة بعد 60 يوم من سداد قيمة الأشهر
            بنجاح.
          </li>
        </ul>
      ),
    },
  },
  {
    title: {
      en: "Booking Cancellation",
      ar: "إلغاء الحجز",
    },
    body: {
      en: (
        <p>
          The reward value will not be counted if the booking is canceled,
          whether the car has been received or not.
        </p>
      ),
      ar: (
        <p>
          لن يتم احتساب قيمة المكافأة في حال تم إلغاء الحجز سواء تم استلام
          السيارة أو قبل استلامها.
        </p>
      ),
    },
  },
  {
    title: {
      en: "For more terms and conditions related to Alfursan, please check:",
      ar: "للاطاع على المزيد من الشروط والأحكام  الخاصة بالفرسان:",
    },
    body: {
      en: (
        <a
          style={{
            textDecoration: "underline",
            color: "var(--color-3)",
            textAlign: "center",
            width: "100%",
            display: "inline-block",
          }}
          target="_blank"
          href="https://www.saudia.com/pages/help/useful-links/legal-and-terms-and-conditions"
          rel="noreferrer"
        >
          AlFursan Terms & Conditions
        </a>
      ),
      ar: (
        <a
          style={{
            textDecoration: "underline",
            color: "var(--color-3)",
            textAlign: "center",
            width: "100%",
            display: "inline-block",
          }}
          target="_blank"
          href="https://www.saudia.com/pages/help/useful-links/legal-and-terms-and-conditions"
          rel="noreferrer"
        >
          شروط وأحكام الفرسان
        </a>
      ),
    },
  },
];

function Item({ title, body, showHr, i18n, getSettingValue }) {
  const formattedBody =
    typeof body === "string"
      ? body
          .replace(
            "{amount}",
            `<div class="d-flex gap-5px">
      <span style="order: ${i18n.language === "ar" ? 1 : 0}">
        <RiyalSymbol />
      </span>
      <span style="order: ${i18n.language === "ar" ? 0 : 1}">
        ${getSettingValue("fursan_mile_rate") || 1}
      </span>
    </div>`
          )
          .replace(
            "{mile}",
            `<span style="text-decoration: underline; margin: 0 1px; display: inline-block;">
      ${i18n.language === "ar" ? "1 ميل" : "1 mile"}
    </span>`
          )
      : body;

  return (
    <div style={{ width: "100%", margin: "10px 0" }}>
      <div className="bold text-align-localized" style={{ fontSize: "17px" }}>
        {title}
      </div>
      <div dangerouslySetInnerHTML={{ __html: formattedBody }} />
      {showHr && <hr style={{ marginBottom: "10px" }} />}
    </div>
  );
}

function TermsConditions({ logo, isOpen, setIsOpen, getSettingValue }) {
  const { t, i18n } = useTranslation();
  return (
    <Popup {...{ isOpen, setIsOpen }}>
      <Div>
        <h5>{t("Terms & Conditions") as string}</h5>
        <img src={logo} alt="logo" />
        {termsConditions(getSettingValue).map(({ title, body }: any, index) => {
          return (
            <Item
              key={index}
              {...{
                title: title[i18n.language],
                body: body[i18n.language],
                showHr: index < termsConditions(getSettingValue).length - 1,
                i18n,
                getSettingValue,
              }}
            />
          );
        })}
      </Div>
    </Popup>
  );
}

export default memo(TermsConditions);
