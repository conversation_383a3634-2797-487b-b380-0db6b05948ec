/* eslint-disable react-hooks/exhaustive-deps */
import { Container, Grid, CircularProgress } from "@material-ui/core";
import Layout from "components/shared/layout";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Step1 from "./step1";
import Step2 from "./step2";
import Step3 from "./step3";
import Success from "./success";
import Info from "./info";
import { useEffect } from "react";
import { RootStateOrAny, useSelector } from "react-redux";

const Div = styled.div`
  background: url(/assets/images/business/bg.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  background-attachment: fixed;

  @media (max-width: 961px) {
    padding: 50px 0 50px 0;
  }
  &,
  > div,
  > div > div {
    min-height: 100vh;
  }
`;

const Card = styled.div`
  background: #fff;
  z-index: 9;
  border-radius: var(--radius-2);
`;

const Hashtag = styled.div`
  color: var(--color-4);
  @media (min-width: 961px) {
    position: absolute;
    bottom: 0;
    bottom: -25vh;
  }
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  @media (max-width: 960px) {
    margin-top: 35px;
  }
`;

const Loader = styled.div`
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
`;

export default function CarwahBusiness({ token }) {
  const { t } = useTranslation();
  const router = useRouter();
  const { token: _token } =
    useSelector((state: RootStateOrAny) => state.authentication.login_data) ||
    {};
  const componentname = router.asPath.replace(`${router.pathname}`, "");

  useEffect(() => {
    //Handle token if send from mobile apps in url as url search params
    if (token) {
      router.replace("./carwah-business#step1");
    } else if (!token && !_token) {
      router.replace("./carwah-business");
    }
  }, [token]);

  return (
    <Layout>
      <Div>
        <Container>
          <Grid
            container
            direction="row-reverse"
            justifyContent="space-between"
            alignItems="center"
          >
            {componentname.includes("#step1") ? (
              <Grid item xs={12} md={5}>
                <Card className="p-4">
                  <Step1 />
                </Card>
              </Grid>
            ) : componentname.includes("#step2") ? (
              <Grid item xs={12} md={5}>
                <Card className="p-4">
                  <Step2 />
                </Card>
              </Grid>
            ) : componentname.includes("#step3") ? (
              <Grid item xs={12} md={5}>
                <Card className="p-4">
                  <Step3 />
                </Card>
              </Grid>
            ) : componentname.includes("#success") ? (
              <Grid item xs={12} md={5}>
                <Card className="p-4">
                  <Success />
                </Card>
              </Grid>
            ) : (
              <Grid item xs={12} md={5}>
                <Card className="p-4">
                  <Info />
                </Card>
              </Grid>
            )}
            <Grid item xs={12} md={3} className="position-relative">
              <Hashtag>
                <h1 className="bold">{t("Carwah Business") as string}</h1>
                <h2 className="mt-3">{t("#Rental_Future") as string}</h2>
              </Hashtag>
            </Grid>
          </Grid>
          {token ? (
            <Loader className="d-flex justify-content-center align-items-center position-fixed">
              <CircularProgress />
            </Loader>
          ) : null}
        </Container>
      </Div>
    </Layout>
  );
}
