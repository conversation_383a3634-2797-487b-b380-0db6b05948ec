/* eslint-disable @next/next/no-img-element */
import {
  <PERSON><PERSON>,
  ButtonsWrapper,
  Card,
  FullCalendar,
  ImageCard,
  InputRow,
  PhoneWrapper,
} from "./styled";
import { CircularProgress, Grid } from "@material-ui/core";
import { DatePicker, MuiPickersUtilsProvider } from "@material-ui/pickers";
import SelectComponent from "components/shared/select";
import Select from "react-select";
import DateFnsUtils from "@date-io/date-fns";
import arLocale from "date-fns/locale/ar";
import enLocale from "date-fns/locale/en-GB";
import moment from "moment";
import HijriUtilsExtended from "components/shared/hijriUtilsExtended";

import useLogic from "./useLogic";
import { memo } from "react";
import PhoneInput from "react-phone-input-2";
import ar from "react-phone-input-2/lang/ar.json";

function Profile() {
  const {
    t,
    i18n,
    register,
    handleSubmit,
    errors,
    showInputError,
    setShowInputError,
    selectProfilePicture,
    selectInternationalDriverLicense,
    selectDriverLicsenseWithSelfie,
    selectPassportFrontImage,
    selectIdImage,
    idImage,
    selectVisaImage,
    visaImage,
    userProfileLoading,
    customerStatusesRes,
    nationalitiesRes,
    imageUploadLoading,
    profilePicture,
    userProfileRes,
    setValue,
    clearErrors,
    getIdentity,
    statusType,
    onSubmit,
    getExpiryTitle,
    getValues,
    internationalDriverLicense,
    passportFrontImage,
    driverLicsenseWithSelfie,
    getDriverLicText,
    getDriverLicWithSelfieText,
  } = useLogic();
  return (
    <>
      {(userProfileLoading || imageUploadLoading) && (
        <div
          className="position-fixed d-flex align-items-center justify-content-center"
          style={{
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            zIndex: 999,
          }}
        >
          <CircularProgress />
        </div>
      )}
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Card className="white-background d-flex align-items-center">
              <div className="d-flex gap-20px align-items-center">
                <Avatar>
                  {userProfileRes?.profile?.profileImage || profilePicture ? (
                    <img
                      className="picture"
                      src={
                        (!profilePicture &&
                          userProfileRes?.profile?.profileImage) ||
                        (profilePicture?.file?.type === "image/jpg" ||
                        profilePicture?.file?.type === "image/jpeg" ||
                        profilePicture?.file?.type === "image/png"
                          ? profilePicture?.source
                          : "/assets/images/account/avatar2.svg")
                      }
                      alt="avatar"
                    />
                  ) : (
                    <img
                      src="/assets/images/account/avatar2.svg"
                      alt="avatar"
                    />
                  )}
                  <img
                    className="cursor-pointer position-absolute"
                    src="/assets/images/account/plus.svg"
                    alt="avatar"
                    style={{ left: "0" }}
                    onClick={() =>
                      selectProfilePicture(
                        {
                          accept: "image/jpg, image/jpeg, image/png",
                        },
                        ({ source, name, size, file }) => {
                          console.log({ source, name, size, file });
                        }
                      )
                    }
                  />
                </Avatar>
                <div className="font-27px text-capitalize">
                  {!userProfileLoading &&
                  userProfileRes?.profile?.firstName &&
                  userProfileRes?.profile?.lastName ? (
                    <span>{`${userProfileRes?.profile?.firstName} ${userProfileRes?.profile?.lastName}`}</span>
                  ) : null}
                </div>
              </div>
            </Card>
            {profilePicture?.size > 20000000 && (
              <p className="color-9 font-14px d-inline-block">
                {t("The max. allowed size is 20 Mb.") as string}
              </p>
            )}
            {profilePicture?.file?.type === "image/jpeg" ||
            profilePicture?.file?.type === "image/jpg" ||
            profilePicture?.file?.type === "image/png"
              ? null
              : profilePicture && (
                  <p className="color-9 font-14px d-inline-block">
                    {
                      t(
                        "File's extensions are JPG / JPEG / PNG only allowed"
                      ) as string
                    }
                  </p>
                )}
          </Grid>

          <Grid item xs={12}>
            <Card className="white-background">
              <Grid container spacing={2}>
                <Grid item md={6} xs={12}>
                  <h2 className="font-20px medium mb-4">
                    {t("Basic Data") as string}
                  </h2>
                  <Grid container justifyContent="space-between">
                    <Grid item xs={12}>
                      <InputRow container spacing={2}>
                        <Grid item xs={3} md={2}>
                          <label className="color-19 text-start w-100">
                            {t("Name") as string}
                            <span className="color-9">*</span>
                          </label>
                        </Grid>
                        <Grid
                          item
                          xs={9}
                          md={10}
                          container
                          justifyContent="space-between"
                        >
                          <Grid item xs={5}>
                            <input
                              className="text-capitalize"
                              {...register("firstName", {
                                required: true,
                              })}
                              type="text"
                              maxLength={20}
                              placeholder={t("First Name")}
                            />
                            {errors?.firstName && (
                              <p className="color-9">
                                {t("This field is required") as string}
                              </p>
                            )}
                          </Grid>
                          <Grid item xs={6}>
                            <input
                              className="text-capitalize"
                              {...register("lastName", {
                                required: true,
                              })}
                              type="text"
                              maxLength={20}
                              placeholder={t("Last Name")}
                            />
                            {errors?.lastName && (
                              <p className="color-9">
                                {t("This field is required") as string}
                              </p>
                            )}
                          </Grid>
                        </Grid>
                      </InputRow>
                      <InputRow container spacing={2}>
                        <Grid item xs={3} md={2}>
                          <label className="color-19 text-start w-100">
                            {t("Email") as string}
                            <span className="color-9">*</span>
                          </label>
                        </Grid>
                        <Grid item xs={9} md={10}>
                          <input
                            {...register("email", {
                              required: true,
                              pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                            })}
                            type="email"
                            placeholder="<EMAIL>"
                          />
                          {errors?.email?.type === "required" && (
                            <p className="color-9">
                              {t("This field is required") as string}
                            </p>
                          )}
                          {errors?.email?.type === "pattern" && (
                            <p className="color-9">
                              {t("Please enter valid value") as string}
                            </p>
                          )}
                        </Grid>
                      </InputRow>
                      <InputRow container spacing={2}>
                        <Grid item xs={3} md={2}>
                          <label className="color-19 text-start w-100">
                            {t("Personal Phone Number") as string}
                            <span className="color-9">*</span>
                          </label>
                        </Grid>
                        <Grid
                          item
                          xs={9}
                          md={10}
                          style={{ pointerEvents: "none" }}
                        >
                          <PhoneInput
                            localization={
                              i18n.language === "ar" ? ar : undefined
                            }
                            searchPlaceholder={t("Search")}
                            enableSearch={true}
                            value={userProfileRes?.profile?.mobile}
                            masks={{ sa: "... ... ..." }}
                            onChange={(e) => {
                              if (e.startsWith("966")) {
                                if (
                                  new RegExp(
                                    /^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/
                                  ).test(e) &&
                                  e.length > 6
                                ) {
                                  setShowInputError(false);
                                } else {
                                  setShowInputError(true);
                                }
                              } else if (e.length > 6) {
                                setShowInputError(false);
                              }
                            }}
                            countryCodeEditable={false}
                            inputProps={{
                              name: "mobile",
                              required: true,
                              autoFocus: true,
                            }}
                          />
                        </Grid>
                      </InputRow>
                      <InputRow container spacing={2}>
                        <Grid item xs={3} md={2}>
                          <label className="color-19 text-start w-100">
                            {t("Gender") as string}
                            <span className="color-9">*</span>
                          </label>
                        </Grid>
                        <Grid item xs={9} md={10} className="text-capitalize">
                          <Select
                            {...register("gender", { required: true })}
                            key={userProfileRes?.profile?.gender}
                            defaultValue={{
                              label: t(userProfileRes?.profile?.gender),
                              value: userProfileRes?.profile?.gender,
                            }}
                            className="required text-align-localized select-styled"
                            options={[
                              { label: t("Male"), value: "male" },
                              { label: t("Female"), value: "female" },
                            ]}
                            onChange={(e) => {
                              if (e?.value) {
                                setValue("gender", e);
                                clearErrors("gender");
                              }
                            }}
                          />
                          {errors?.gender?.type === "required" && (
                            <p className="color-9">
                              {t("This field is required") as string}
                            </p>
                          )}
                        </Grid>
                      </InputRow>
                      <InputRow container spacing={2}>
                        <Grid item xs={3} md={2}>
                          <label className="color-19 text-start w-100">
                            {t("Status") as string}
                            <span className="color-9">*</span>
                          </label>
                        </Grid>
                        <Grid item xs={9} md={10} className="text-capitalize">
                          <Select
                            {...register("status", { required: true })}
                            key={userProfileRes?.profile?.status}
                            className="required select-styled text-align-localized"
                            defaultValue={{
                              label:
                                userProfileRes?.profile?.customerProfile
                                  ?.statusLocalized,
                              value: userProfileRes?.profile?.status,
                            }}
                            options={customerStatusesRes?.customerStatuses.map(
                              (item) => {
                                return {
                                  label: item.value,
                                  value: item.key,
                                };
                              }
                            )}
                            onChange={(e) => {
                              if (e) {
                                setValue("status", e);
                                clearErrors("status");
                                if (e.value === "gulf_citizen") {
                                  setValue("nationalityId", null);
                                }
                              }
                            }}
                          />
                          {errors?.status?.type === "required" && (
                            <p className="color-9">
                              {t("This field is required") as string}
                            </p>
                          )}
                        </Grid>
                      </InputRow>
                      <InputRow container spacing={2} alignItems="flex-end">
                        <Grid item xs={5} md={2}>
                          <label className="color-19 text-start w-100">
                            {t("Company Name") as string}
                          </label>
                        </Grid>
                        <Grid item xs={7} md={10}>
                          <input {...register("companyName")} />
                        </Grid>
                      </InputRow>
                    </Grid>
                    <Grid item md={5} xs={12}></Grid>
                  </Grid>
                  <h2 className="font-20px medium my-4">
                    {t("Identity") as string}
                  </h2>
                  <Grid container justifyContent="space-between">
                    <Grid item xs={12}>
                      <InputRow container spacing={2}>
                        <Grid item xs={3} md={2}>
                          <label className="color-19 text-start w-100">
                            {getIdentity()}

                            <span className="color-9">
                              {!getValues("borderNumber") ? "*" : false}
                            </span>
                          </label>
                        </Grid>
                        <Grid item xs={9} md={10}>
                          <input
                            key={statusType()}
                            className="hide-arrows"
                            {...register(
                              statusType() !== "visitor"
                                ? "nid"
                                : "passportNumber",
                              {
                                required: !getValues("borderNumber")
                                  ? true
                                  : false,
                                pattern:
                                  statusType() === "resident"
                                    ? /^2\d{9}$/
                                    : statusType() === "citizen"
                                    ? /^1\d{9}$/
                                    : statusType() === "gulf_citizen"
                                    ? /^\d{1,20}$/
                                    : null,
                              }
                            )}
                            maxLength={50}
                            placeholder="xxxxxxxxxxxx"
                          />
                          {errors?.nid?.type === "required" &&
                            !getValues("borderNumber") && (
                              <p className="color-9">
                                {t("This field is required") as string}
                              </p>
                            )}
                          {errors?.nid?.type === "pattern" && (
                            <p className="color-9">
                              {statusType() === "citizen"
                                ? (t(
                                    "ID must start with 1, and consist of 10 digits"
                                  ) as string)
                                : statusType() === "resident"
                                ? (t(
                                    "ID must start with 2, and consist of 10 digits"
                                  ) as string)
                                : (t("This field is required") as string)}
                            </p>
                          )}
                        </Grid>
                      </InputRow>
                      {statusType() === "visitor" ? (
                        <InputRow container spacing={2}>
                          <Grid item xs={3} md={2}>
                            <label className="color-19 text-start w-100">
                              {t("Border Number") as string}
                              <span className="color-9">
                                {(statusType() !== "visitor" &&
                                  !getValues("nid")) ||
                                (statusType() === "visitor" &&
                                  !getValues("passportNumber"))
                                  ? "*"
                                  : false}
                              </span>
                            </label>
                          </Grid>
                          <Grid item xs={9} md={10}>
                            <input
                              {...register("borderNumber", {
                                required:
                                  (statusType() !== "visitor" &&
                                    !getValues("nid")) ||
                                  (statusType() === "visitor" &&
                                    !getValues("passportNumber"))
                                    ? true
                                    : false,
                              })}
                              type="number"
                            />
                            {errors?.borderNumber?.type === "required" &&
                              !getValues("nid") && (
                                <p className="color-9">
                                  {t("This field is required") as string}
                                </p>
                              )}
                          </Grid>
                        </InputRow>
                      ) : null}

                      {statusType() === "resident" ||
                      statusType() === "citizen" ? (
                        <InputRow container spacing={2}>
                          <Grid item xs={3} md={2}>
                            <label className="color-19 text-start w-100">
                              {t("Version") as string}
                              <span className="color-9">*</span>
                            </label>
                          </Grid>
                          <Grid item xs={9} md={10}>
                            <input
                              {...register("nationalIdVersion", {
                                required: true,
                              })}
                              type="number"
                            />
                            {errors?.nationalIdVersion?.type === "required" && (
                              <p className="color-9">
                                {t("This field is required") as string}
                              </p>
                            )}
                          </Grid>
                        </InputRow>
                      ) : null}
                      <InputRow container spacing={2}>
                        <Grid item md={2} xs={3}>
                          <label className="color-19 text-start w-100">
                            {getExpiryTitle()}
                            <span className="color-9">*</span>
                          </label>
                        </Grid>
                        <Grid item md={10} xs={9}>
                          <div className="required position-relative">
                            <FullCalendar
                              className="d-flex gap-20px"
                              language={i18n.language}
                            >
                              <MuiPickersUtilsProvider
                                utils={DateFnsUtils}
                                locale={
                                  i18n.language === "en" ? enLocale : arLocale
                                }
                              >
                                <DatePicker
                                  {...register(
                                    statusType() !== "visitor"
                                      ? "nationalIdExpireAt"
                                      : "passportExpireAt",
                                    {
                                      required:
                                        Boolean(
                                          statusType() &&
                                            !getValues("nationalIdExpireAt") &&
                                            statusType() !== "visitor"
                                        ) ||
                                        Boolean(
                                          statusType() &&
                                            !getValues("passportExpireAt") &&
                                            statusType() === "visitor"
                                        ),
                                    }
                                  )}
                                  value={
                                    getValues(
                                      statusType() !== "visitor"
                                        ? "nationalIdExpireAt"
                                        : "passportExpireAt"
                                    ) ||
                                    userProfileRes?.profile?.customerProfile?.[
                                      statusType() !== "visitor"
                                        ? "nationalIdExpireAt"
                                        : "passportExpireAt"
                                    ]
                                  }
                                  onChange={(e) => {
                                    if (statusType() !== "visitor") {
                                      setValue("nationalIdExpireAt", e);
                                      clearErrors("passportExpireAt");
                                    } else {
                                      setValue("passportExpireAt", e);
                                      clearErrors("nationalIdExpireAt");
                                    }
                                  }}
                                  okLabel={t("Ok") as string}
                                  cancelLabel={t("Cancel") as string}
                                  disablePast
                                  minDateMessage={t("minDateMessage") as string}
                                  openTo="year"
                                  format="dd-MM-yyyy"
                                />
                              </MuiPickersUtilsProvider>
                              <MuiPickersUtilsProvider
                                libInstance={moment}
                                utils={HijriUtilsExtended}
                                locale={
                                  i18n.language === "en" ? enLocale : arLocale
                                }
                              >
                                <DatePicker
                                  minDate="1937-03-14"
                                  maxDate="2076-11-26"
                                  value={
                                    getValues(
                                      statusType() !== "visitor"
                                        ? "nationalIdExpireAt"
                                        : "passportExpireAt"
                                    ) ||
                                    userProfileRes?.profile?.customerProfile?.[
                                      statusType() !== "visitor"
                                        ? "nationalIdExpireAt"
                                        : "passportExpireAt"
                                    ]
                                  }
                                  onChange={(e) => {
                                    if (statusType() !== "visitor") {
                                      setValue("nationalIdExpireAt", e);
                                      clearErrors("nationalIdExpireAt");
                                    } else {
                                      setValue("passportExpireAt", e);
                                      clearErrors("passportExpireAt");
                                    }
                                  }}
                                  okLabel={t("Ok") as string}
                                  cancelLabel={t("Cancel") as string}
                                  disablePast
                                  views={["year", "month", "date"]}
                                  openTo="year"
                                  format="dd-MM-yyyy"
                                  labelFunc={(date) =>
                                    date ? date.format("iDD-iMM-iYYYY") : ""
                                  }
                                />
                              </MuiPickersUtilsProvider>
                              <img
                                src="/assets/images/business/calendar.svg"
                                alt="calendar-icon"
                              />
                            </FullCalendar>
                            {errors?.[
                              statusType() !== "visitor"
                                ? "nationalIdExpireAt"
                                : "passportExpireAt"
                            ]?.type === "required" && (
                              <p className="color-9">
                                {t("This field is required") as string}
                              </p>
                            )}
                          </div>
                        </Grid>
                      </InputRow>
                      <InputRow container spacing={2}>
                        <Grid item md={2} xs={3}>
                          <label className="color-19 text-start w-100">
                            {t("Date of Birth") as string}
                            <span className="color-9">*</span>
                          </label>
                        </Grid>
                        <Grid item md={10} xs={9}>
                          <div className="required position-relative">
                            <FullCalendar
                              className="d-flex gap-20px"
                              language={i18n.language}
                            >
                              <MuiPickersUtilsProvider
                                utils={DateFnsUtils}
                                locale={
                                  i18n.language === "en" ? enLocale : arLocale
                                }
                              >
                                <DatePicker
                                  key={userProfileRes?.profile?.dob}
                                  {...register("dob", {
                                    required: true,
                                  })}
                                  value={
                                    getValues("dob") ||
                                    userProfileRes?.profile?.dob ||
                                    null
                                  }
                                  onChange={(e) => {
                                    setValue("dob", e);
                                    clearErrors("dob");
                                  }}
                                  okLabel={t("Ok") as string}
                                  cancelLabel={t("Cancel") as string}
                                  disableFuture
                                  minDate="1937-03-14"
                                  maxDate={moment().subtract(18, "years")}
                                  openTo="year"
                                  views={["year", "month", "date"]}
                                  format="dd-MM-yyyy"
                                />
                              </MuiPickersUtilsProvider>
                              <MuiPickersUtilsProvider
                                libInstance={moment}
                                utils={HijriUtilsExtended}
                                locale={
                                  i18n.language === "en" ? enLocale : arLocale
                                }
                              >
                                <DatePicker
                                  minDate="1937-03-14"
                                  maxDate="2076-11-26"
                                  {...register("dobHijri", {
                                    required: false,
                                  })}
                                  key={userProfileRes?.profile?.dob}
                                  {...register("dob", {
                                    required: true,
                                  })}
                                  value={getValues("dob") || null}
                                  onChange={(e) => {
                                    setValue("dob", e);
                                    clearErrors("dob");
                                  }}
                                  okLabel={t("Ok") as string}
                                  cancelLabel={t("Cancel") as string}
                                  disableFuture
                                  openTo="year"
                                  views={["year", "month", "date"]}
                                  format="dd-MM-yyyy"
                                  labelFunc={(date) =>
                                    date ? date.format("iDD-iMM-iYYYY") : ""
                                  }
                                />
                              </MuiPickersUtilsProvider>
                              <img
                                src="/assets/images/business/calendar.svg"
                                alt="calendar-icon"
                              />
                            </FullCalendar>
                            {errors?.dob?.type === "required" && (
                              <p className="color-9">
                                {t("This field is required") as string}
                              </p>
                            )}
                          </div>
                        </Grid>
                      </InputRow>
                      <InputRow container spacing={2}>
                        <Grid item md={2} xs={3}>
                          <label className="color-19 text-start w-100">
                            {t("Driver license number") as string}
                            <span className="color-9">*</span>
                          </label>
                        </Grid>
                        <Grid item md={10} xs={9}>
                          <input
                            className="hide-arrows"
                            {...register("driverLicense", {
                              required: true,
                              pattern: /^\d{1,20}$/,
                            })}
                            type="number"
                            placeholder="xxxxxxxxxxxx"
                          />
                          {errors?.driverLicense?.type === "required" && (
                            <p className="color-9">
                              {t("This field is required") as string}
                            </p>
                          )}
                          {errors?.driverLicense?.type === "pattern" && (
                            <p className="color-9">
                              {t("Max. 20 numbers") as string}
                            </p>
                          )}
                        </Grid>
                      </InputRow>
                      {statusType() !== "citizen" ? (
                        <InputRow container spacing={2}>
                          <Grid item md={2} xs={3}>
                            <label className="color-19 text-start w-100">
                              {t("Nationality") as string}
                              <span className="color-9">*</span>
                            </label>
                          </Grid>
                          <Grid item md={10} xs={9}>
                            <Select
                              {...register("nationalityId", {
                                required: true,
                              })}
                              key={
                                userProfileRes?.profile?.customerProfile
                                  ?.nationality
                              }
                              placeholder={t("Nationality")}
                              className="required text-align-localized select-styled"
                              defaultValue={{
                                label:
                                  userProfileRes?.profile?.customerProfile
                                    ?.nationality?.name,
                                value:
                                  userProfileRes?.profile?.customerProfile
                                    ?.nationality?.id,
                              }}
                              options={nationalitiesRes?.nationalities.map(
                                (item) => {
                                  return {
                                    label: item.name,
                                    value: item.id,
                                  };
                                }
                              )}
                              onChange={(e) => {
                                if (e) {
                                  setValue("nationalityId", e);
                                  clearErrors("nationalityId");
                                }
                              }}
                            />

                            {errors?.nationalityId?.type === "required" && (
                              <p className="color-9">
                                {t("This field is required") as string}
                              </p>
                            )}
                          </Grid>
                        </InputRow>
                      ) : null}
                      <InputRow container spacing={2}>
                        <Grid item md={2} xs={3}>
                          <label className="color-19 text-start w-100">
                            {t("Driver Liscence Expiry Date") as string}
                            <span className="color-9">*</span>
                          </label>
                        </Grid>
                        <Grid item md={10} xs={9}>
                          <div className="required position-relative">
                            <FullCalendar
                              className="d-flex gap-20px"
                              language={i18n.language}
                            >
                              <MuiPickersUtilsProvider
                                utils={DateFnsUtils}
                                locale={
                                  i18n.language === "en" ? enLocale : arLocale
                                }
                              >
                                <DatePicker
                                  key={
                                    userProfileRes?.profile
                                      ?.driverLicenseExpireAt
                                  }
                                  {...register("driverLicenseExpireAt", {
                                    required: true,
                                  })}
                                  value={
                                    getValues("driverLicenseExpireAt") ||
                                    userProfileRes?.profile
                                      ?.driverLicenseExpireAt ||
                                    null
                                  }
                                  onChange={(e) => {
                                    // setSelectedDateTime(e);
                                    setValue("driverLicenseExpireAt", e);
                                    clearErrors("driverLicenseExpireAt");
                                  }}
                                  okLabel={t("Ok") as string}
                                  cancelLabel={t("Cancel") as string}
                                  disablePast
                                  views={["year", "month", "date"]}
                                  openTo="year"
                                  format="dd-MM-yyyy"
                                />
                              </MuiPickersUtilsProvider>
                              <img
                                src="/assets/images/business/calendar.svg"
                                alt="calendar-icon"
                              />
                              <MuiPickersUtilsProvider
                                libInstance={moment}
                                utils={HijriUtilsExtended}
                                locale={
                                  i18n.language === "en" ? enLocale : arLocale
                                }
                              >
                                <DatePicker
                                  minDate="1937-03-14"
                                  maxDate="2076-11-26"
                                  key={
                                    userProfileRes?.profile?.customerProfile
                                      ?.driverLicenseExpireAt
                                  }
                                  value={
                                    getValues("driverLicenseExpireAt") ||
                                    userProfileRes?.profile?.customerProfile
                                      ?.nationalIdExpireAt ||
                                    null
                                  }
                                  onChange={(e) => {
                                    setValue("driverLicenseExpireAt", e);
                                    clearErrors("driverLicenseExpireAt");
                                  }}
                                  okLabel={t("Ok") as string}
                                  cancelLabel={t("Cancel") as string}
                                  disablePast
                                  views={["year", "month", "date"]}
                                  openTo="year"
                                  format="dd-MM-yyyy"
                                  labelFunc={(date) =>
                                    date ? date.format("iDD-iMM-iYYYY") : ""
                                  }
                                />
                              </MuiPickersUtilsProvider>
                            </FullCalendar>
                            {errors?.driverLicenseExpireAt?.type ===
                              "required" && (
                              <p className="color-9">
                                {t("This field is required") as string}
                              </p>
                            )}
                          </div>
                        </Grid>
                      </InputRow>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item md={6} xs={12}>
                  <h2 className="font-20px medium my-4">
                    {t("Images") as string}
                  </h2>
                  <div>
                    <label className="color-19 text-align-localized w-100">
                      {getDriverLicText()}
                      <span className="color-9">*</span>
                    </label>
                    {errors.licenseFrontImage && (
                      <p className="color-9 font-14px">
                        {t("This field is required") as string}
                      </p>
                    )}
                    <div>
                      <ImageCard>
                        <img
                          className="add cursor-pointer"
                          src="/assets/images/profile/add.svg"
                          alt="add"
                          onClick={(e) => {
                            selectInternationalDriverLicense(
                              {
                                accept: "image/jpg, image/jpeg, image/png",
                              },
                              ({ source, name, size, file }) => {
                                console.log({
                                  source,
                                  name,
                                  size,
                                  file,
                                });
                              }
                            );
                          }}
                        />
                        <input
                          hidden
                          {...register("licenseFrontImage", {
                            required: true,
                          })}
                        />
                        {internationalDriverLicense ||
                        userProfileRes?.profile?.licenseFrontImage ? (
                          <img
                            src={
                              (!internationalDriverLicense &&
                                userProfileRes?.profile?.licenseFrontImage) ||
                              (internationalDriverLicense?.file?.type ===
                                "image/jpg" ||
                              internationalDriverLicense?.file?.type ===
                                "image/jpeg" ||
                              internationalDriverLicense?.file?.type ===
                                "image/png"
                                ? internationalDriverLicense?.source
                                : "/assets/images/profile/driver.svg")
                            }
                            alt="internationalDriverLicense"
                          />
                        ) : (
                          <img
                            src="/assets/images/profile/driver.svg"
                            alt="add"
                          />
                        )}
                      </ImageCard>
                      {internationalDriverLicense?.size > 20000000 && (
                        <p
                          className="color-9 font-14px"
                          style={{ transform: "translateY(-25px)" }}
                        >
                          {t("The max. allowed size is 20 Mb.") as string}
                        </p>
                      )}
                      {internationalDriverLicense?.file?.type ===
                        "image/jpeg" ||
                      internationalDriverLicense?.file?.type === "image/jpg" ||
                      internationalDriverLicense?.file?.type === "image/png"
                        ? null
                        : internationalDriverLicense && (
                            <p
                              className="color-9 font-14px"
                              style={{ transform: "translateY(-25px)" }}
                            >
                              {
                                t(
                                  "File's extensions are JPG / JPEG / PNG only allowed"
                                ) as string
                              }
                            </p>
                          )}
                    </div>
                  </div>
                  {statusType() === "visitor" && (
                    <div>
                      <label className="color-19 text-align-localized w-100">
                        {t("Passport Front Image") as string}
                        <span className="color-9">*</span>
                      </label>
                      {errors.passportFrontImage && (
                        <p className="color-9 font-14px">
                          {t("This field is required") as string}
                        </p>
                      )}
                      <div>
                        <ImageCard>
                          <img
                            className="add cursor-pointer"
                            src="/assets/images/profile/add.svg"
                            alt="add"
                            onClick={() =>
                              selectPassportFrontImage(
                                {
                                  accept: "image/jpg, image/jpeg, image/png",
                                },
                                ({ source, name, size, file }) => {
                                  console.log({
                                    source,
                                    name,
                                    size,
                                    file,
                                  });
                                }
                              )
                            }
                          />
                          <input
                            hidden
                            {...register("passportFrontImage", {
                              required: true,
                            })}
                          />
                          {passportFrontImage ||
                          userProfileRes?.profile?.customerProfile
                            ?.passportFrontImage ? (
                            <img
                              src={
                                (!passportFrontImage &&
                                  userProfileRes?.profile?.customerProfile
                                    ?.passportFrontImage) ||
                                (passportFrontImage?.file?.type ===
                                  "image/jpg" ||
                                passportFrontImage?.file?.type ===
                                  "image/jpeg" ||
                                passportFrontImage?.file?.type === "image/png"
                                  ? passportFrontImage?.source
                                  : "/assets/images/profile/passport.svg")
                              }
                              alt="passportFrontImage"
                            />
                          ) : (
                            <img
                              src="/assets/images/profile/passport.svg"
                              alt="add"
                            />
                          )}
                        </ImageCard>
                        {passportFrontImage?.size > 20000000 && (
                          <p
                            className="color-9 font-14px"
                            style={{ transform: "translateY(-25px)" }}
                          >
                            {t("The max. allowed size is 20 Mb.") as string}
                          </p>
                        )}
                        {passportFrontImage?.file?.type === "image/jpeg" ||
                        passportFrontImage?.file?.type === "image/jpg" ||
                        passportFrontImage?.file?.type === "image/png"
                          ? null
                          : passportFrontImage && (
                              <p
                                className="color-9 font-14px"
                                style={{
                                  transform: "translateY(-25px)",
                                }}
                              >
                                {
                                  t(
                                    "File's extensions are JPG / JPEG / PNG only allowed"
                                  ) as string
                                }
                              </p>
                            )}
                      </div>
                    </div>
                  )}
                  {statusType() === "gulf_citizen" && (
                    <div>
                      <label className="color-19 text-align-localized w-100">
                        {t("ID") as string}
                      </label>

                      <div>
                        <ImageCard>
                          <img
                            className="add cursor-pointer"
                            src="/assets/images/profile/add.svg"
                            alt="add"
                            onClick={() =>
                              selectIdImage(
                                {
                                  accept: "image/jpg, image/jpeg, image/png",
                                },
                                ({ source, name, size, file }) => {
                                  console.log({
                                    source,
                                    name,
                                    size,
                                    file,
                                  });
                                }
                              )
                            }
                          />
                          <input hidden {...register("nidImage")} />
                          {idImage || userProfileRes?.profile?.nidImage ? (
                            <img
                              src={
                                (!idImage &&
                                  userProfileRes?.profile?.nidImage) ||
                                (idImage?.file?.type === "image/jpg" ||
                                idImage?.file?.type === "image/jpeg" ||
                                idImage?.file?.type === "image/png"
                                  ? idImage?.source
                                  : "/assets/images/profile/passport.svg")
                              }
                              alt="nidImage"
                            />
                          ) : (
                            <img
                              src="/assets/images/profile/passport.svg"
                              alt="add"
                            />
                          )}
                        </ImageCard>
                        {idImage?.size > 20000000 && (
                          <p
                            className="color-9 font-14px"
                            style={{ transform: "translateY(-25px)" }}
                          >
                            {t("The max. allowed size is 20 Mb.") as string}
                          </p>
                        )}
                        {idImage?.file?.type === "image/jpeg" ||
                        idImage?.file?.type === "image/jpg" ||
                        idImage?.file?.type === "image/png"
                          ? null
                          : idImage && (
                              <p
                                className="color-9 font-14px"
                                style={{
                                  transform: "translateY(-25px)",
                                }}
                              >
                                {
                                  t(
                                    "File's extensions are JPG / JPEG / PNG only allowed"
                                  ) as string
                                }
                              </p>
                            )}
                      </div>
                    </div>
                  )}
                  {statusType() === "visitor" && (
                    <div>
                      <label className="color-19 text-align-localized w-100">
                        {t("VISA") as string}
                      </label>

                      <div>
                        <ImageCard>
                          <img
                            className="add cursor-pointer"
                            src="/assets/images/profile/add.svg"
                            alt="add"
                            onClick={() =>
                              selectVisaImage(
                                {
                                  accept: "image/jpg, image/jpeg, image/png",
                                },
                                ({ source, name, size, file }) => {
                                  console.log({
                                    source,
                                    name,
                                    size,
                                    file,
                                  });
                                }
                              )
                            }
                          />
                          <input hidden {...register("visaImage")} />
                          {visaImage || userProfileRes?.profile?.visaImage ? (
                            <img
                              src={
                                (!visaImage &&
                                  userProfileRes?.profile?.visaImage) ||
                                (visaImage?.file?.type === "image/jpg" ||
                                visaImage?.file?.type === "image/jpeg" ||
                                visaImage?.file?.type === "image/png"
                                  ? visaImage?.source
                                  : "/assets/images/profile/passport.svg")
                              }
                              alt="visaImage"
                            />
                          ) : (
                            <img
                              src="/assets/images/profile/passport.svg"
                              alt="add"
                            />
                          )}
                        </ImageCard>
                        {visaImage?.size > 20000000 && (
                          <p
                            className="color-9 font-14px"
                            style={{ transform: "translateY(-25px)" }}
                          >
                            {t("The max. allowed size is 20 Mb.") as string}
                          </p>
                        )}
                        {visaImage?.file?.type === "image/jpeg" ||
                        visaImage?.file?.type === "image/jpg" ||
                        visaImage?.file?.type === "image/png"
                          ? null
                          : visaImage && (
                              <p
                                className="color-9 font-14px"
                                style={{
                                  transform: "translateY(-25px)",
                                }}
                              >
                                {
                                  t(
                                    "File's extensions are JPG / JPEG / PNG only allowed"
                                  ) as string
                                }
                              </p>
                            )}
                      </div>
                    </div>
                  )}
                  <div>
                    <label className="color-19 text-align-localized w-100">
                      {getDriverLicWithSelfieText()}
                      <span className="color-9"></span>
                    </label>

                    <div>
                      <ImageCard>
                        <img
                          className="add cursor-pointer"
                          src="/assets/images/profile/add.svg"
                          alt="add"
                          onClick={() =>
                            selectDriverLicsenseWithSelfie(
                              {
                                accept: "image/jpg, image/jpeg, image/png",
                              },
                              ({ source, name, size, file }) => {
                                console.log({
                                  source,
                                  name,
                                  size,
                                  file,
                                });
                              }
                            )
                          }
                        />
                        <input
                          hidden
                          {...register("licenseSelfieImage", {
                            required: false,
                          })}
                        />
                        {driverLicsenseWithSelfie ||
                        userProfileRes?.profile?.licenseSelfieImage ? (
                          <img
                            src={
                              (!driverLicsenseWithSelfie &&
                                userProfileRes?.profile?.licenseSelfieImage) ||
                              (driverLicsenseWithSelfie?.file?.type ===
                                "image/jpg" ||
                              driverLicsenseWithSelfie?.file?.type ===
                                "image/jpeg" ||
                              driverLicsenseWithSelfie?.file?.type ===
                                "image/png"
                                ? driverLicsenseWithSelfie?.source
                                : "/assets/images/profile/driver.svg")
                            }
                            alt="driverLicsenseWithSelfie"
                          />
                        ) : (
                          <img
                            src="/assets/images/profile/driver.svg"
                            alt="add"
                          />
                        )}
                      </ImageCard>
                      {driverLicsenseWithSelfie?.size > 20000000 && (
                        <p
                          className="color-9 font-14px"
                          style={{ transform: "translateY(-25px)" }}
                        >
                          {t("The max. allowed size is 20 Mb.") as string}
                        </p>
                      )}
                      {driverLicsenseWithSelfie?.file?.type === "image/jpeg" ||
                      driverLicsenseWithSelfie?.file?.type === "image/jpg" ||
                      driverLicsenseWithSelfie?.file?.type === "image/png"
                        ? null
                        : driverLicsenseWithSelfie && (
                            <p
                              className="color-9 font-14px"
                              style={{ transform: "translateY(-25px)" }}
                            >
                              {
                                t(
                                  "File's extensions are JPG / JPEG / PNG only allowed"
                                ) as string
                              }
                            </p>
                          )}
                    </div>
                  </div>
                </Grid>
              </Grid>
            </Card>
          </Grid>
          <ButtonsWrapper className="d-flex gap-20px justify-content-end p-3 w-100">
            <button
              onClick={(e) => {
                e.preventDefault();
              }}
            >
              {t("Cancel") as string}
            </button>
            <button type="submit">{t("Save") as string}</button>
          </ButtonsWrapper>
        </Grid>
      </form>
    </>
  );
}

export default memo(Profile);
