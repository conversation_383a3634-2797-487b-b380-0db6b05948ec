@font-face {
    font-family: 'Helvetica';
    src: url('./fonts/HelveticaNeueLTArabic-Roman.eot');
    src: url('./fonts/HelveticaNeueLTArabic-Roman.eot?#iefix') format('embedded-opentype'),
        url('./fonts/HelveticaNeueLTArabic-Roman.woff2') format('woff2'),
        url('./fonts/HelveticaNeueLTArabic-Roman.woff') format('woff'),
        url('./fonts/HelveticaNeueLTArabic-Roman.ttf') format('truetype'),
        url('./fonts/HelveticaNeueLTArabic-Roman.svg#HelveticaNeueLTArabic-Roman') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
*{
    font-family: 'Helvetica';
}
html, body{
    height: 100%;
}
body{
    margin: 0;
    box-sizing: border-box;
}
.bold{
    font-weight: bold;
}
#main{
    position: relative;
    min-height: 100%;
    background-image: url(./images/bg.png);
    background-repeat: repeat;
}
img#top-pattern {
    position: absolute;
    top: 30px;
    width: 100%;
    height: 33px;
    object-fit: cover;
}
.top-pattern-wrapper{
    position: relative;
}
.top-pattern-wrapper .text-wrapper{
    position: absolute;
    top: 30px;
    right: 30px;
    text-align: right;
    background-color: #fff;
    padding: 0px 18px;
    -moz-transform: skew(-30deg, 0deg);
    -webkit-transform: skew(-30deg, 0deg);
    -o-transform: skew(-30deg, 0deg);
    -ms-transform: skew(-30deg, 0deg);
    transform: skew(-30deg, 0deg);
}
.top-pattern-wrapper .text-wrapper h2{
    color: #80B1C1;
    margin: 0 0 5px 0;
    font-size: 20px;
    line-height: 16px;
    -moz-transform: skew(30deg, 0deg);
    -webkit-transform: skew(30deg, 0deg);
    -o-transform: skew(30deg, 0deg);
    -ms-transform: skew(30deg, 0deg);
    transform: skew(30deg, 0deg);
}
.top-pattern-wrapper .text-wrapper h4{
    color: #B2B2B2;
    margin: 0;
    font-size: 16px;
    line-height: 14px;
    -moz-transform: skew(30deg, 0deg);
    -webkit-transform: skew(30deg, 0deg);
    -o-transform: skew(30deg, 0deg);
    -ms-transform: skew(30deg, 0deg);
    transform: skew(30deg, 0deg);
}
img#bottom-pattern {
    position: absolute;
    bottom: 30px;
    width: 100%;
    height: 33px;
    object-fit: cover;
}
#text-terms{
    text-align: right;
    direction: rtl;
    padding: 70px 25px 10px 25px;
}
#text-terms h5{
    font-size: 18px;
    line-height: 24px;
    color: #80B1C1;
    margin: 20px 0 0 0;
    padding: 5px 0;
    width: fit-content;
    font-weight: bold;
    position: relative;
}
#text-terms h5:after {
    content: '';
    width: 100%;
    border-bottom: solid 2px #80B1C1;
    display: block;
    position: absolute;
    bottom: 0;
}
#text-terms p{
    font-size: 18px;
    line-height: 24px;
    margin: 15px 0 0 0;
    color: #77767B;
}
.note{
    color: #80B1C1;
    text-decoration: underline;
}
#carwah-copywrites{
    padding: 10px 25px 120px 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 14px;
}

