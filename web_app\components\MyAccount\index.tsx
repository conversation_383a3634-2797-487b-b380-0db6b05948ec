/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { Container, Grid } from "@material-ui/core";
import Layout from "components/shared/layout";
import { Hr } from "./styled";
import useLogic from "./useLogic";
import WalletTransactions from "./WalletTransactions";
import Tabs from "./tabs";
import Profile from "./Profile";
import { memo } from "react";

function MyAccountView() {
  const { t, balance, tab } = useLogic();

  return (
    <Layout>
      <div className="py-5 grey-background">
        <Container>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <h1 className="font-27px bold pb-3">
                {t("My Account") as string}
              </h1>
            </Grid>
          </Grid>
          <Tabs />
        </Container>
        <Hr />
        <Container className="mt-5 position-relative">
          {tab === "wallet" ? (
            <WalletTransactions balance={balance} />
          ) : (
            <Profile />
          )}
        </Container>
      </div>
    </Layout>
  );
}

export default memo(MyAccountView);
