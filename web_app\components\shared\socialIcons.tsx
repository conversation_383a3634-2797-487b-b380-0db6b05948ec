import { Facebook, Instagram, Twitter } from "@material-ui/icons";
import styled from "styled-components";

/* eslint-disable @next/next/no-img-element */
const Div = styled.div`
  display: grid;
  grid-template-columns: 25px 25px 25px;
  grid-column-gap: 20px;
`;

export default function SocialIcons() {
  return (
    <Div className="social-icons px-3 justify-content-center">
      <a
        href="https://twitter.com/carwah_sa?s=21"
        target="_blank"
        rel="noreferrer"
      >
        <Twitter style={{ fontSize: 30, color: "white" }} />
      </a>
      <a
        href="https://www.instagram.com/carwah_sa/?utm_medium=copy_link"
        target="_blank"
        rel="noreferrer"
      >
        <Instagram style={{ fontSize: 30, color: "white" }} />
      </a>
      <a
        href="https://www.facebook.com/officialcarwah/"
        target="_blank"
        rel="noreferrer"
      >
        <Facebook style={{ fontSize: 30, color: "white" }} />
      </a>
    </Div>
  );
}
