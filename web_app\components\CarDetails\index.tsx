/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { useState, memo, useEffect } from "react";
import { useDispatch, useSelector, RootStateOrAny } from "react-redux";
import styled from "styled-components";
import Grid from "@material-ui/core/Grid";
import Layout from "components/shared/layout";
import { useLazyQuery, useQuery } from "@apollo/client";
import { useRouter } from "next/router";
import Script from "next/script";
import { useTranslation } from "react-i18next";
import { Branch_Data } from "gql/queries/car";
import {
  RentalDetails,
  Rental_About_Price,
} from "gql/queries/rentalDetailsQuery";
import {
  SelectPaymentMethod,
  SetAvialablePaymentMethods,
  setCarData,
} from "store/cars/action";
import { GetRentalDetails, setRentalAboutPrice } from "store/boooking/action";
import { clearExtensionsData } from "store/extensions/action";
import { payWithWalletAction } from "store/wallet/action";
import { setPayWithInstallments } from "store/installments/action";
import useWallet from "hooks/useWallet";
import { useAboutRentPrice } from "hooks/useAboutRentPrice";
import { TpaymentMethods, installmentStatuses } from "utilities/enums";
import RequestLoader from "components/shared/requestLoader";
import CarSection from "./CarSection";
import BookingTime from "./bookingTime";
import ExtraServices from "./extraServices";
import HandoverInAnotherBranch from "./handoverInAnotherBranch";
import CarDelivery from "./carDelivery";
import TamaraPromotional from "./tamara";
import ExtensionRequest from "./ExtensionRequest";
import RentalPackages from "./rentalPackages/index";
import ViewAllExtensionsModal from "./popups/viewAllExtensions";
import DetailsCard from "./detailsCard";
import Insurance from "./Insurance";
import Invoice from "./Invoice/index";
import AllyData from "./allyData";
import WorkingHours from "../shared/WorkingHours";
import { useCarProfile } from "./useCarProfile";
import useCustomEffect from "./useEffects";
import CarMedia from "./RentToOwnCarDetails/CarMedia";
import CarFeatures from "./RentToOwnCarDetails/CarFeatures";
import { advantages } from "./RentToOwnCarDetails/staticData";
import RentToOwnPlans from "./RentToOwnCarDetails/Plans";
import RentToOwnAdvantages from "./RentToOwnCarDetails/Advantages";
import Dates from "./RentToOwnCarDetails/Dates";
import InstallmentsPayments from "./InstallmetsPayments";
import useRentalType from "./heplers/rentalType";
import PolicyCancellation from "components/shared/policyCancellation";
import TermsConditions from "./popups/termsConditions";
import useUserProfile from "hooks/useUserProfile";
import Loyality from "./Loyality";
import PaymentOrder from "./paymentOrder";

const Div = styled.div`
  padding: 30px 0;
  @media screen and (max-width: 768px) {
    padding: 0 !important;
    .mobile-flex-col-reveresd {
      display: flex;
      > div {
        width: 100%;
      }
    }
    font-size: 1rem !important;
  }
  #extension-requests {
    > div > div {
      margin-top: 0 !important;
    }
  }
`;

function CarDetails() {
  const { i18n, t } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();
  const { bookingId, checkout_id, id: hyperPayPaymentId } = router?.query || {};
  const [calendarTooltipOpen, setCalendarTooltipOpen] = useState(false);
  const [isExtensionModalOpen, setIsExtensionModalOpen] = useState(false);
  const [termsConditionModalOpen, setTermsConditionModalOpen] = useState(false);
  const [isViewAllExtensionModalOpen, setIsViewAllExtensionModalOpen] =
    useState(false);
  const [extensionId, setExtensionId] = useState();
  const { pay_with_wallet } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};
  const rentalData = useSelector((state: RootStateOrAny) => state.booking);
  const { deliverType, status: rentalStatus } = rentalData?.rentalDetails || {};
  const { isRentToOwn } = useRentalType();
  const { paymentMethod: _paymentMethod } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const paymentMethod = _paymentMethod as TpaymentMethods;
  const { search_data, cars } =
    useSelector((state: RootStateOrAny) => state) || {};
  const { car_data: carData, about_rent_price } = cars || {};
  const { pay_with_installments } =
    useSelector((state: RootStateOrAny) => state.installments) || {};
  const { availablePaymentMethods } = about_rent_price || {};
  const { paymentMethod: branchSupportedPayment, deliveryPaymentMethod } =
    carData?.branch || {};

  const isThereNotCollected =
    rentalData?.rental_about_price?.installments?.find(
      (i: { status: string }) =>
        i.status === installmentStatuses.not_collected.value
    );
  const isThereInstallmentToPay =
    // !isRentToOwn &&
    rentalData?.rental_about_price?.installments &&
    rentalData?.rental_about_price?.installments?.filter(
      (i: { status: string }) =>
        i.status === installmentStatuses.due.value ||
        i.status === installmentStatuses.overdue.value ||
        (i.status === installmentStatuses.upcoming.value &&
          !isThereNotCollected)
    )?.length
      ? true
      : false;

  const showInstallmentDetails =
    ((rentalStatus === "car_received" && isRentToOwn) || !isRentToOwn) &&
    rentalData?.rental_about_price?.installments?.filter(
      (i) =>
        i.status === installmentStatuses.paid.value ||
        i.status === installmentStatuses.partially_refunded.value ||
        i.status === installmentStatuses.not_collected.value
    )?.length;

  const [
    getRentalDetails,
    {
      data: rentalDetails,
      refetch: refetchRentalDetails,
      loading: rentalDetailsLoading,
    },
  ] = useLazyQuery(RentalDetails, {
    variables: {
      id: bookingId,
    },
  });
  const {
    branchId,
    dropOffBranchId,
    rentalDateExtensionRequests,
    lastRentalDateExtensionRequest,
    paymentMethod: rentalPaymentMethod,
    installments,
    payable,
    status,
    pendingPaymentOrder,
    totalWalletPaidAmount,
    deliveryPrice,
    handoverPrice,
  } = rentalDetails?.rentalDetails || {};

  useEffect(() => {
    dispatch(payWithWalletAction(false));
  }, []);

  const { data: RentalAboutPrice, refetch: refetchRentalAboutPrice } = useQuery(
    Rental_About_Price,
    {
      variables: {
        id: bookingId,
        withInstallment: pay_with_installments,
        withWallet: pay_with_wallet,
        paymentBrand:
          paymentMethod?.toUpperCase() !== "CASH" &&
          (payable || installments?.find((i: any) => i.payable))
            ? paymentMethod
            : undefined,
      },
      skip: !bookingId && !rentalDetails ? true : false,
      fetchPolicy: "no-cache",
    }
  );

  const { carProfileLoader, carDataRes } = useCarProfile(
    router.query.car,
    carData
  );

  const canDelivery = Boolean(
    !bookingId ? carData?.branch?.canDelivery : deliveryPrice
  );
  const canHandoverInAntherCity = Boolean(
    !bookingId
      ? carData?.branch?.allyCompany?.canHandoverInAntherCity
      : handoverPrice
  );

  useWallet();
  const { getRentPrice } = useAboutRentPrice();
  const [getBranchData] = useLazyQuery(Branch_Data);

  useCustomEffect({
    bookingId,
    carDataRes,
    rentalDetails,
    getRentalDetails,
    dispatch,
    setCarData,
    GetRentalDetails,
    payWithWalletAction,
    setPayWithInstallments,
    getRentPrice,
    search_data,
    cars,
    pay_with_installments,
    pay_with_wallet,
    RentalAboutPrice,
    setRentalAboutPrice,
    availablePaymentMethods,
    deliverType,
    deliveryPaymentMethod,
    branchSupportedPayment,
    SetAvialablePaymentMethods,
    paymentMethod,
    SelectPaymentMethod,
    rentalData,
    isExtensionModalOpen,
    clearExtensionsData,
    rentalPaymentMethod,
    dropOffBranchId,
    branchId,
    getBranchData,
    refetchRentalAboutPrice,
  });

  useUserProfile();

  return (
    <Layout>
      <RequestLoader loading={carProfileLoader || rentalDetailsLoading} />

      {Boolean(i18n.language) && (
        <Script id="payment">{`var wpwlOptions = {style:"card", locale: "${i18n.language}"}`}</Script>
      )}
      {Boolean(checkout_id) && (
        <Script
          src={`${process.env.NEXT_PUBLIC_HYPERPAY_URL}?checkoutId=${checkout_id}`}
          async
          id={`${checkout_id}`}
        />
      )}

      <div className="grey-background">
        <Div className="container">
          {Boolean(carData) && (
            <>
              {!isRentToOwn && <CarSection />}
              <Grid
                container
                spacing={3}
                className="mt-3 w-100 mobile-0-margin mobile-flex-col-reveresd"
              >
                <Grid item md={4} sm={12} className="w-100">
                  {isRentToOwn ? (
                    <>
                      <CarMedia />
                      {!bookingId && (
                        <>
                          <RentToOwnPlans />
                          <RentToOwnAdvantages
                            {...{ advantages: advantages(t) }}
                          />
                        </>
                      )}
                    </>
                  ) : (
                    <BookingTime
                      {...{
                        calendarTooltipOpen,
                        setCalendarTooltipOpen,
                        isExtensionModalOpen,
                        setIsExtensionModalOpen,
                        setExtensionId,
                        refetchRentalDetails,
                      }}
                    />
                  )}
                  {Boolean(bookingId) && (
                    <>
                      {Boolean(showInstallmentDetails) && (
                        <InstallmentsPayments
                          {...{
                            setExtensionId,
                            extensionId,
                            refetchRentalDetails,
                            setCalendarTooltipOpen,
                            isThereInstallmentToPay,
                          }}
                        />
                      )}
                      <DetailsCard />
                    </>
                  )}
                  {!bookingId && !isRentToOwn && <RentalPackages />}
                  {!bookingId && (
                    <div>
                      <ExtraServices />
                    </div>
                  )}
                  {Boolean(canDelivery) && (
                    <div
                      style={{ pointerEvents: bookingId ? "none" : undefined }}
                    >
                      <CarDelivery {...{ isRentToOwn }} />
                    </div>
                  )}

                  {Boolean(canHandoverInAntherCity && !isRentToOwn) && (
                    <div
                      style={{ pointerEvents: bookingId ? "none" : undefined }}
                    >
                      <HandoverInAnotherBranch />
                    </div>
                  )}
                </Grid>
                <Grid item md={8} sm={12}>
                  {isRentToOwn && (
                    <>
                      <CarFeatures />
                      <Dates
                        {...{
                          isRentToOwn,
                          calendarTooltipOpen,
                          setCalendarTooltipOpen,
                        }}
                      />
                    </>
                  )}
                  {Boolean(pendingPaymentOrder) && (
                    <PaymentOrder
                      {...{
                        paymentOrder: pendingPaymentOrder,
                      }}
                    />
                  )}
                  {!isRentToOwn && (
                    <div id="extension-requests">
                      {Boolean(rentalDateExtensionRequests?.length) && (
                        <div>
                          <ExtensionRequest
                            extensionDetails={rentalDateExtensionRequests[0]}
                            showMore={rentalDateExtensionRequests?.length > 1}
                            setIsViewAllExtensionModalOpen={
                              setIsViewAllExtensionModalOpen
                            }
                            setExtensionId={setExtensionId}
                          />
                        </div>
                      )}
                      {Boolean(rentalDateExtensionRequests?.length > 1) && (
                        <ViewAllExtensionsModal
                          {...{
                            title: t("Extension Request Logs"),
                            isOpen: isViewAllExtensionModalOpen,
                            setIsOpen: setIsViewAllExtensionModalOpen,
                            rentalDateExtensionRequests,
                            lastRentalDateExtensionRequest,
                          }}
                        />
                      )}
                    </div>
                  )}
                  <AllyData />
                  {!isRentToOwn && (
                    <div
                      id="working-hours"
                      style={{
                        backgroundColor: "#fff",
                        borderRadius: "var(--radius-2)",
                      }}
                    >
                      <WorkingHours />
                    </div>
                  )}
                  {!bookingId && (
                    <div
                      style={{ pointerEvents: bookingId ? "none" : undefined }}
                    >
                      <Insurance {...{ isRentToOwn }} />
                    </div>
                  )}
                  {!hyperPayPaymentId ? <TamaraPromotional /> : undefined}
                  {!bookingId ? <Loyality /> : null}
                  {Boolean(paymentMethod !== "CASH") && !isRentToOwn && (
                    <PolicyCancellation />
                  )}
                  {isRentToOwn && !bookingId ? (
                    <>
                      <div
                        className="d-flex gap-5px mt-4 justify-content-center align-items-center text-align-localized"
                        style={{ flexWrap: "wrap" }}
                      >
                        <span>
                          {
                            t(
                              "By continuing you indicate your acceptance of the"
                            ) as string
                          }
                        </span>
                        <span
                          className="color-3 d-inline-block text-decoration-underline cursor-pointer "
                          onClick={() => setTermsConditionModalOpen(true)}
                        >
                          {t("Terms_and_Conditions") as string}
                        </span>
                      </div>
                      <TermsConditions
                        isOpen={termsConditionModalOpen}
                        setIsOpen={setTermsConditionModalOpen}
                      />
                    </>
                  ) : null}
                  {carData ? (
                    <Invoice
                      {...{
                        setCalendarTooltipOpen,
                        refetchRentalDetails,
                        extensionId,
                        setExtensionId,
                        isIntallmentDetailsModal: undefined,
                        isInInstallmentBreakDown: undefined,
                        hideAboutPrice: undefined,
                        refetchRentalAboutPrice,
                      }}
                    />
                  ) : null}
                </Grid>
              </Grid>
            </>
          )}
        </Div>
      </div>
    </Layout>
  );
}

export default memo(CarDetails);
