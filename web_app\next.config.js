// next.config.js
const withTM = require("next-transpile-modules")([
  "react-date-object/locales/arabic_ar",
]); // pass the modules you would like to see transpiled

const isProd = process.env.NEXT_PUBLIC_ENV !== "development";

module.exports = withTM({
  i18n: {
    locales: ["default", "en", "ar"],
    defaultLocale: "default",
    localeDetection: true,
  },
  output: "standalone",
  experimental: {
    outputStandalone: true,
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  compress: isProd,
});
