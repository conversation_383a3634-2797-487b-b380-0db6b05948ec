import { Grid } from "@material-ui/core";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import ArrowForwardIcon from "@material-ui/icons/ArrowForward";
import ArrowBackIcon from "@material-ui/icons/ArrowBack";
import PopupForm from "components/Home/popupForm";
import ToAppPopup from "components/shared/toAppPopup";

const Div = styled.div`
  border-radius: 15px;
  margin-top: 20px;
  margin-bottom: 20px;
  > div {
    width: 100%;
    height: 100%;
    border-radius: inherit;
    @media (min-width: 960px) {
      padding: 50px;
    }
    @media (max-width: 959px) {
      padding: 45px 30px;
      background-position: right;
    }
    background: url(/assets/images/contactus-bg.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    h3 {
      text-transform: capitalize;
      color: var(--color-4);
      font-weight: 400;
      letter-spacing: 2px;
      margin-bottom: 10px;
    }
  }
  .btn {
    width: 90%;
    max-width: 240px;
    font-size: 17px;
    font-weight: 500;
    padding: 18px 20px;
    border: none;
    color: var(--color-4);
    background-color: var(--color-1);
    border-radius: var(--radius-1);
    display: grid;
    grid-template-columns: 1fr 20px;
    @media (min-width: 961px) {
      margin: auto;
    }
    @media (max-width: 960px) {
      margin-top: 30px;
    }
    > span {
      text-align: start;
    }
    &:focus {
      outline: none;
    }
  }
`;
export default function ContactUs() {
  const { t, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState<boolean>();
  const [isOpenToAppPopup, setIsOpenToAppPopup] = useState<boolean>();

  return (
    <>
      <Div>
        <div
          onClick={() => {
            if (!isOpenToAppPopup) {
              setIsOpenToAppPopup(true);
            }
          }}
        >
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item xs={12} md={3}>
              <h3>{t("Contact Us") as string}</h3>
              <h3 style={{ fontWeight: "bolder" }}>
                {t("For Any Questions") as string}
              </h3>
            </Grid>
            <Grid item xs={12} md={3}>
              <button
                className="btn cursor-pointer"
                type="submit"
                onClick={() => {
                  // setIsOpen(true);
                }}
              >
                <span>{t("Contact Us") as string}</span>
                {i18n.language === "ar" ? (
                  <ArrowBackIcon />
                ) : (
                  <ArrowForwardIcon />
                )}
              </button>
            </Grid>
          </Grid>
        </div>
      </Div>
      <PopupForm isOpen={isOpen} setIsOpen={setIsOpen} />
      <ToAppPopup
        isOpenToAppPopup={isOpenToAppPopup}
        setIsOpenToAppPopup={setIsOpenToAppPopup}
      />
    </>
  );
}
