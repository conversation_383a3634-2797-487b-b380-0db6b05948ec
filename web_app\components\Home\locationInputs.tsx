/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import React, { CSSProperties, useState } from "react";
import ClickAwayListener from "@material-ui/core/ClickAwayListener";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import {
  setUserPickupAddressAction,
  setUserReturnAddressAction,
} from "store/search/action";
import AreasTooltip from "components/shared/areasToolTip";
import styled from "styled-components";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Grid } from "@material-ui/core";

const InputWrap = styled.div`
  position: relative;
  display: flex;
  justify-content: flex-start;
  input:focus ~ .floating-label,
  input:not(:focus):valid ~ .floating-label {
    top: -15px;
  }
  input {
    border-radius: var(--radius-3);
    padding: 20px !important;
  }
  .hasValue {
    .floating-label {
      top: -15px !important;
    }
  }
  .floating-label {
    position: absolute;
    pointer-events: none;
    top: ${(props) => (props.hasValue ? "-15px" : "25px")};
    background-color: white;
    transition: 0.2s ease all;
    font-size: 14px;
    left: ${(props) => (props.lang === "en" ? "20px" : null)};
    right: ${(props) => (props.lang === "ar" ? "20px" : null)};
    color: #858585;
    @media (max-width: 768px) {
      top: ${(props) => (props.hasValue ? "-17px" : "14px")} !important;
      left: ${(props) => (props.lang === "en" ? "5px" : null)};
      right: ${(props) => (props.lang === "ar" ? "5px" : null)};
      z-index: 9;
    }
  }
`;

export default function LocationInputs({
  hideInputLabel,
  inputStyles,
  inputChangeHandler,
}: {
  hideInputLabel?: boolean;
  inputStyles?: CSSProperties;
  inputChangeHandler?: () => void;
}) {
  //State
  const [pickupToolTipOpen, setPickupToolTipOpen] = useState(false);
  const [filteredAreas, setFilteredAreas] = useState();

  //Store
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const userAddress = useSelector(
    (state: RootStateOrAny) => state.search_data.user_address
  );
  const { search_data } = useSelector((state: RootStateOrAny) => state) || {};
  const { areas } = search_data || {};
  const areaList = areas.list || [];

  //Functions
  function findAreaHandler(e, state, setState, dispatchAction) {
    dispatch(dispatchAction());
    if (!state) {
      setState(true);
    }
    const filteredList = areaList?.filter((item) =>
      item?.[`${i18n.language}Name`]
        .toLowerCase()
        .includes(e.target.value.toLowerCase())
    );

    setFilteredAreas(filteredList || []);
  }

  useEffect(() => {
    dispatch(setUserReturnAddressAction(userAddress?.pick_up)); //restores return location on switch closed
  }, []);

  return (
    <Grid item container md={5} spacing={2}>
      <Grid item xs={12}>
        <InputWrap
          className="input"
          lang={i18n.language}
          hasValue={
            userAddress?.pick_up?.airport?.name || userAddress?.pick_up?.name
          }
        >
          {/* {userAddress?.pick_up?.name} */}
          <input
            key={`${userAddress?.pick_up?.airport?.name} - ${userAddress?.pick_up?.name} - ${i18n.language}`}
            type="text"
            defaultValue={
              userAddress?.pick_up?.airport?.[
                i18n.language === "en" ? "enName" : "arName"
              ] ||
              userAddress?.pick_up?.[
                i18n.language === "en" ? "enName" : "arName"
              ] ||
              userAddress?.pick_up?.name
            }
            placeholder={t("pickup location") as string}
            style={inputStyles}
            onClick={() => {
              setPickupToolTipOpen(true);
            }}
            onChange={(e) => {
              findAreaHandler(
                e,
                pickupToolTipOpen,
                setPickupToolTipOpen,
                setUserPickupAddressAction
              );
            }}
          />

          <span
            className="floating-label"
            style={{
              opacity:
                userAddress?.pick_up?.airport?.[
                  i18n.language === "en" ? "enName" : "arName"
                ] ||
                (userAddress?.pick_up?.[
                  i18n.language === "en" ? "enName" : "arName"
                ] &&
                  !hideInputLabel)
                  ? "1"
                  : "0",
            }}
          >
            {t("pickup location") as string}
          </span>
        </InputWrap>

        <div>
          <AreasTooltip
            action={setUserPickupAddressAction}
            isOpen={pickupToolTipOpen}
            setIsOpen={setPickupToolTipOpen}
            filteredAreas={filteredAreas}
            areaChangeHandler={() => {
              if (inputChangeHandler) {
                inputChangeHandler();
              }
            }}
          />
        </div>
      </Grid>
    </Grid>
  );
}
