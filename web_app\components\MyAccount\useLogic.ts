/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { useEffect } from "react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { useFileUpload } from "use-file-upload";
import { getBase64 } from "utilities/helpers";
import { useSnackbar } from "notistack";
import {
  BasicProfileMutation,
  LicenseProfileMuation,
  ProfileImageMuation,
} from "gql/mutations/profile";
import { useMutation, useQuery } from "@apollo/client";
import { UploadImage } from "gql/mutations/imageUpload";
import moment from "moment";
import {
  User_Profile_Query,
  Customer_Statuses_Query,
} from "gql/queries/profile";
import { Nationalities_Query } from "gql/queries/lookups";
import { useRouter } from "next/router";
import { RootStateOrAny, useSelector } from "react-redux";
import useWallet from "hooks/useWallet";

function useLogic() {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const { tab, route } = router.query || {};
  const { enqueueSnackbar } = useSnackbar();
  const {
    register,
    handleSubmit,
    clearErrors,
    setValue,
    formState: { errors },
    getValues,
    watch,
  } = useForm({ mode: "onBlur" });

  useWallet();

  const [showInputError, setShowInputError] = useState(false);

  const statusType = () => {
    return getValues("status")?.value?.toLowerCase();
  };
  const [profilePicture, selectProfilePicture] = useFileUpload() as any;
  const [internationalDriverLicense, selectInternationalDriverLicense] =
    useFileUpload() as any;
  const [driverLicsenseWithSelfie, selectDriverLicsenseWithSelfie] =
    useFileUpload() as any;
  const [idImage, selectIdImage] = useFileUpload() as any;
  const [visaImage, selectVisaImage] = useFileUpload() as any;
  const [passportFrontImage, selectPassportFrontImage] = useFileUpload() as any;
  const [licenseFrontImageUrl, setLicenseFrontImageUrl] = useState();
  const [passportFrontImageUrl, setPassportFrontImageUrl] = useState();
  const [driverLicsenseWithSelfieUrl, setDriverLicsenseWithSelfieUrl] =
    useState();
  const [idImageUrl, setIdImageUrl] = useState();
  const [visaImageUrl, setVisaImageUrl] = useState();
  const [profileImageUrl, setProfileImageUrl] = useState();
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);

  useEffect(() => {
    if (
      internationalDriverLicense &&
      internationalDriverLicense?.size < 20000000 &&
      (internationalDriverLicense?.file?.type === "image/jpeg" ||
        internationalDriverLicense?.file?.type === "image/jpg" ||
        internationalDriverLicense?.file?.type === "image/png")
    ) {
      getBase64(internationalDriverLicense.file).then((data) =>
        imageUpload({
          variables: {
            image: data,
            topic: internationalDriverLicense.name,
          },
        }).then((res) => {
          setLicenseFrontImageUrl(res.data.imageUpload.imageUpload.imageUrl);
        })
      );
    }
  }, [internationalDriverLicense]);

  useEffect(() => {
    if (
      passportFrontImage &&
      passportFrontImage?.size < 20000000 &&
      (passportFrontImage?.file?.type === "image/jpeg" ||
        passportFrontImage?.file?.type === "image/jpg" ||
        passportFrontImage?.file?.type === "image/png")
    ) {
      getBase64(passportFrontImage.file).then((data) =>
        imageUpload({
          variables: {
            image: data,
            topic: passportFrontImage.name,
          },
        }).then((res) => {
          setPassportFrontImageUrl(res.data.imageUpload.imageUpload.imageUrl);
        })
      );
    }
  }, [passportFrontImage]);

  useEffect(() => {
    if (
      driverLicsenseWithSelfie &&
      driverLicsenseWithSelfie?.size < 20000000 &&
      (driverLicsenseWithSelfie?.file?.type === "image/jpeg" ||
        driverLicsenseWithSelfie?.file?.type === "image/jpg" ||
        driverLicsenseWithSelfie?.file?.type === "image/png")
    ) {
      getBase64(driverLicsenseWithSelfie.file).then((data) =>
        imageUpload({
          variables: {
            image: data,
            topic: driverLicsenseWithSelfie.name,
          },
        }).then((res) => {
          setDriverLicsenseWithSelfieUrl(
            res.data.imageUpload.imageUpload.imageUrl
          );
        })
      );
    }
  }, [driverLicsenseWithSelfie]);

  useEffect(() => {
    if (
      idImage &&
      idImage?.size < 20000000 &&
      (idImage?.file?.type === "image/jpeg" ||
        idImage?.file?.type === "image/jpg" ||
        idImage?.file?.type === "image/png")
    ) {
      getBase64(idImage.file).then((data) =>
        imageUpload({
          variables: {
            image: data,
            topic: idImage.name,
          },
        }).then((res) => {
          setIdImageUrl(res.data.imageUpload.imageUpload.imageUrl);
        })
      );
    }
  }, [idImage]);

  useEffect(() => {
    if (
      visaImage &&
      visaImage?.size < 20000000 &&
      (visaImage?.file?.type === "image/jpeg" ||
        visaImage?.file?.type === "image/jpg" ||
        visaImage?.file?.type === "image/png")
    ) {
      getBase64(visaImage.file).then((data) =>
        imageUpload({
          variables: {
            image: data,
            topic: visaImage.name,
          },
        }).then((res) => {
          setVisaImageUrl(res.data.imageUpload.imageUpload.imageUrl);
        })
      );
    }
  }, [visaImage]);

  useEffect(() => {
    if (
      profilePicture &&
      profilePicture?.size < 20000000 &&
      (profilePicture?.file?.type === "image/jpeg" ||
        profilePicture?.file?.type === "image/jpg" ||
        profilePicture?.file?.type === "image/png")
    ) {
      getBase64(profilePicture.file).then((data) =>
        imageUpload({
          variables: {
            image: data,
            topic: profilePicture.name,
          },
        }).then((res) => {
          setProfileImageUrl(res.data.imageUpload.imageUpload.imageUrl);
        })
      );
    }
  }, [profilePicture]);

  const {
    data: userProfileRes,
    loading: userProfileLoading,
    refetch: refetchUserProfile,
  } = useQuery(User_Profile_Query, {
    errorPolicy: "all",
    fetchPolicy: "no-cache",
    nextFetchPolicy: "no-cache",
  });

  const { data: customerStatusesRes } = useQuery(Customer_Statuses_Query, {
    errorPolicy: "all",
    fetchPolicy: "cache-first",
    nextFetchPolicy: "cache-first",
  });
  const { balance } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};

  const { data: nationalitiesRes } = useQuery(Nationalities_Query, {
    variables: { isGulf: statusType() === "gulf_citizen" ? true : false },
    fetchPolicy: "cache-first",
    nextFetchPolicy: "cache-first",
  });

  const [basicProfile] = useMutation(BasicProfileMutation, {
    errorPolicy: "all",
  });

  const [imageUpload, { loading: imageUploadLoading }] = useMutation(
    UploadImage,
    { errorPolicy: "all" }
  );

  const [profileImage] = useMutation(ProfileImageMuation);

  const [licenseProfile] = useMutation(LicenseProfileMuation, {
    errorPolicy: "all",
  });

  useEffect(() => {
    if (internationalDriverLicense) {
      setValue("licenseFrontImage", internationalDriverLicense.source);
      clearErrors("licenseFrontImage");
    }
  }, [internationalDriverLicense]);

  useEffect(() => {
    if (passportFrontImage) {
      setValue("passportFrontImage", passportFrontImage.source);
      clearErrors("passportFrontImage");
    }
  }, [passportFrontImage]);
  useEffect(() => {
    if (driverLicsenseWithSelfie) {
      setValue("licenseSelfieImage", driverLicsenseWithSelfie.source);
      clearErrors("licenseSelfieImage");
    }
  }, [driverLicsenseWithSelfie]);
  useEffect(() => {
    if (idImage) {
      setValue("nidImage", idImage.source);
      clearErrors("nidImage");
    }
  }, [idImage]);
  useEffect(() => {
    if (visaImage) {
      setValue("visaImage", visaImage.source);
      clearErrors("visaImage");
    }
  }, [visaImage]);

  useEffect(() => {
    // sync react hook form values with fields values from query
    if (userProfileRes?.profile?.firstName) {
      setValue("firstName", userProfileRes.profile.firstName);
    }
    if (userProfileRes?.profile?.lastName) {
      setValue("lastName", userProfileRes.profile.lastName);
    }
    if (userProfileRes?.profile?.email) {
      setValue("email", userProfileRes.profile.email);
    }
    if (userProfileRes?.profile?.gender) {
      setValue("gender", {
        label: userProfileRes.profile.gender,
        value: userProfileRes.profile.gender,
      });
    }
    if (userProfileRes?.profile?.status) {
      setValue("status", {
        label: userProfileRes.profile.customerProfile?.statusLocalized,
        value: userProfileRes.profile.status,
      });
    }
    if (userProfileRes?.profile?.companyName) {
      setValue("companyName", userProfileRes.profile.companyName);
    }
    if (userProfileRes?.profile?.nid) {
      setValue("nid", userProfileRes.profile.nid);
    }
    if (userProfileRes?.profile?.passportNumber) {
      setValue("passportNumber", userProfileRes.profile.passportNumber);
    }
    if (userProfileRes?.profile?.customerProfile?.nationalIdExpireAt) {
      setValue(
        "nationalIdExpireAt",
        userProfileRes.profile.customerProfile.nationalIdExpireAt
      );
      clearErrors("nationalIdExpireAt");
      clearErrors("passportExpireAt");
    }
    if (userProfileRes?.profile?.customerProfile?.passportExpireAt) {
      setValue(
        "passportExpireAt",
        userProfileRes.profile.customerProfile.passportExpireAt
      );
      clearErrors("nationalIdExpireAt");
      clearErrors("passportExpireAt");
    }
    if (userProfileRes?.profile?.customerProfile?.nationalIdVersion) {
      setValue(
        "nationalIdVersion",
        userProfileRes.profile.customerProfile.nationalIdVersion
      );
    }
    if (userProfileRes?.profile?.driverLicenseExpireAt) {
      setValue(
        "driverLicenseExpireAt",
        userProfileRes.profile.driverLicenseExpireAt
      );
    }
    if (userProfileRes?.profile?.borderNumber) {
      setValue("borderNumber", userProfileRes.profile.borderNumber);
    }
    if (userProfileRes?.profile?.nidImage) {
      setValue("nidImage", userProfileRes.profile.nidImage);
    }
    if (userProfileRes?.profile?.driverLicense) {
      setValue("driverLicense", userProfileRes.profile.driverLicense);
    }
    if (userProfileRes?.profile?.dob) {
      setValue("dob", userProfileRes.profile.dob);
    }
    if (userProfileRes?.profile?.licenseFrontImage) {
      setValue("licenseFrontImage", userProfileRes.profile.licenseFrontImage);
    }
    if (userProfileRes?.profile?.customerProfile?.passportFrontImage) {
      setValue(
        "passportFrontImage",
        userProfileRes?.profile?.customerProfile?.passportFrontImage
      );
    }
    if (userProfileRes?.profile?.licenseSelfieImage) {
      setValue("licenseSelfieImage", userProfileRes.profile.licenseSelfieImage);
    }
    if (userProfileRes?.profile?.customerProfile?.nationality) {
      setValue("nationalityId", {
        label: userProfileRes.profile.customerProfile.nationality.name,
        value: userProfileRes.profile.customerProfile.nationality.id,
      });
      clearErrors("nationalityId");
    }
  }, [userProfileRes?.profile]);

  async function profileImageMutaion() {
    const res = await profileImage({
      variables: {
        input: {
          profileImage: profileImageUrl,
        },
      },
    });
    if (res.data.profileImage.status) {
      // enqueueSnackbar(t("profile image changed") as string, {
      //   variant: "success",
      // });
    }
  }
  async function mutateProfileImage() {
    if (profileImageUrl) {
      try {
        profileImageMutaion();
      } catch (err) {
        console.log(err);
      }
    }
  }
  async function muatateBasicProfile(data) {
    const res = await basicProfile({
      variables: {
        input: {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          gender: data.gender.value,
          companyName: data.companyName,
        },
      },
    });
    if (res?.data?.basicProfile?.status === "success") {
      refetchUserProfile();
      // enqueueSnackbar(t("Basic profile updated") as string, {
      //   variant: "success",
      // });
    }
    if (res?.errors) {
      res?.errors?.map((err) =>
        enqueueSnackbar(err.message, { variant: "error" })
      );
    }
  }
  async function mutateLicenseProfile(data) {
    const res = await licenseProfile({
      variables: {
        input: {
          status: data.status.value,
          nid: statusType() !== "visitor" ? data.nid : undefined,
          nationalIdExpireAt:
            statusType() !== "visitor"
              ? moment(data.nationalIdExpireAt)
                  .locale("en")
                  .format("DD/MM/YYYY")
              : undefined,
          passportNumber:
            statusType() === "visitor" ? data.passportNumber : undefined,
          borderNumber:
            statusType() === "visitor" ? data.borderNumber : undefined,
          passportExpireAt:
            statusType() === "visitor"
              ? moment(data.passportExpireAt).locale("en").format("DD/MM/YYYY")
              : undefined,
          nationalIdVersion: +data?.nationalIdVersion,
          nationalityId: data?.nationalityId?.value,
          driverLicense: data?.driverLicense,
          driverLicenseExpireAt: moment(data.driverLicenseExpireAt)
            .locale("en")
            .format("DD/MM/YYYY"),
          dob: moment(data.dob).locale("en").format("DD/MM/YYYY"),
          licenseFrontImage:
            licenseFrontImageUrl || userProfileRes?.profile?.licenseFrontImage,
          passportFrontImage:
            passportFrontImageUrl ||
            userProfileRes?.profile?.customerProfile?.passportFrontImage,
          licenseSelfieImage:
            driverLicsenseWithSelfieUrl ||
            userProfileRes?.profile?.licenseSelfieImage,
          nidImage:
            statusType() === "gulf_citizen"
              ? idImageUrl || userProfileRes?.profile?.nidImage
              : undefined,
          visaImage:
            statusType() === "visitor"
              ? visaImageUrl || userProfileRes?.profile?.visaImage
              : undefined,
        },
      },
    });
    if (res?.data?.licenseProfile?.status === "success") {
      refetchUserProfile();
      enqueueSnackbar(t("Profile updated") as string, {
        variant: "success",
      });
      if (route) {
        router.push(route as string);
      }
    }
    if (res?.errors) {
      res?.errors?.map((err) =>
        enqueueSnackbar(err.message, { variant: "error" })
      );
    }
  }
  async function onSubmit(data) {
    // Handle profile image update if available
    if (profileImageUrl) {
      try {
        mutateProfileImage();
      } catch (err) {
        console.log(err);
      }
    }

    try {
      // First call basic profile mutation
      await muatateBasicProfile(data);

      // Then call license profile mutation directly
      try {
        await mutateLicenseProfile(data);
      } catch (err) {
        console.log(err);
      }
    } catch (err) {
      console.log(err);
    }
  }
  function getIdentity() {
    return statusType() === "resident"
      ? t("Iqama ID")
      : statusType() === "citizen"
      ? t("National ID")
      : statusType() === "visitor"
      ? t("Passport No.")
      : t("Gulf National ID");
  }
  function getExpiryTitle() {
    return statusType() === "resident"
      ? t("Iqama expiry date")
      : statusType() === "citizen"
      ? t("National ID Expiray Date")
      : statusType() === "gulf_citizen"
      ? t("Gulf ID Expiray Date")
      : t("Passport Expiray Date");
  }
  function getDriverLicText() {
    return t("Driver Licence");
  }
  function getDriverLicWithSelfieText() {
    return t("Driver Licence with Selfie");
  }
  return {
    t,
    i18n,
    register,
    handleSubmit,
    errors,
    showInputError,
    setShowInputError,
    selectProfilePicture,
    selectInternationalDriverLicense,
    selectDriverLicsenseWithSelfie,
    selectPassportFrontImage,
    selectIdImage,
    idImage,
    selectVisaImage,
    visaImage,
    userProfileLoading,
    customerStatusesRes,
    nationalitiesRes,
    imageUploadLoading,
    profilePicture,
    userProfileRes,
    setValue,
    clearErrors,
    getIdentity,
    statusType,
    onSubmit,
    getExpiryTitle,
    getValues,
    internationalDriverLicense,
    passportFrontImage,
    driverLicsenseWithSelfie,
    getDriverLicText,
    getDriverLicWithSelfieText,
    balance,
    isWalletModalOpen,
    setIsWalletModalOpen,
    tab,
    watch,
  };
}
export default useLogic;
