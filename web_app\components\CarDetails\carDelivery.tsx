/* eslint-disable react-hooks/exhaustive-deps */
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import {
  Checkbox,
  FormControlLabel,
  FormGroup,
  Switch,
} from "@material-ui/core";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import DeliveryInputs from "components/Home/deliveryInputs";
import {
  setDelivery,
  setDeliveryReturn,
  setDeliveryType,
  setHandoverInAnotherBranch,
} from "store/cars/action";
import { memo, useEffect } from "react";
import { useRouter } from "next/router";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  margin: 24px 0;
  position: relative;
  .delivery-input {
    margin-top: -10px;
    /* justify-content: space-between !important; */
    padding: 0 !important;
    width: 100%;
    border: none !important;
    img {
      display: none;
    }
    input {
      padding: 0 !important;
      width: 100%;
    }
  }
`;

function CarDelivery({ isRentToOwn }: { isRentToOwn?: boolean }) {
  //Hooks
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();
  const { bookingId } = router?.query || {};

  //Store
  const { selection_index, confirmed_delivery_location } =
    useSelector((state: RootStateOrAny) => state.search_data) || {};
  const { car_data } = useSelector((state: RootStateOrAny) => state.cars) || {};

  const {
    about_rent_price,
    delivery,
    deliveryType,
    handover_in_another_branch,
    delivery_return,
  } = useSelector((state: RootStateOrAny) => state.cars) || {};
  const { rentalDetails, rental_about_price } =
    useSelector((state: RootStateOrAny) => state.booking) || {};

  const {
    deliverType: deliverType_details,
    handoverAddress,
    handoverAntherCity,
  } = rentalDetails || {};

  //Varaibles
  const { canHandover } = car_data?.branch || {};

  //Functions
  function switchHandler(e) {
    const checked = e?.target?.checked;
    dispatch(setDelivery({ ...delivery, checked }));
    if (!checked) {
      dispatch(setDeliveryType("no_delivery"));
    } else {
      if (delivery_return == 0) {
        dispatch(setDeliveryType("two_ways"));
      } else {
        dispatch(setDeliveryType("one_way"));
      }
    }
  }

  useEffect(() => {
    if (
      (!bookingId &&
        confirmed_delivery_location &&
        selection_index == 2 &&
        !delivery) ||
      (!bookingId && delivery && delivery?.checked) ||
      (bookingId && deliverType_details?.includes("way"))
    ) {
      dispatch(setDelivery({ ...delivery, checked: true }));
      if (delivery_return == 0) {
        dispatch(setDeliveryType("two_ways"));
      } else {
        dispatch(setDeliveryType("one_way"));
      }
    } else {
      dispatch(setDelivery({ ...delivery, checked: false }));
      dispatch(setDeliveryType("no_delivery"));
    }
  }, []);

  return (
    <Div className="p-4" style={{ display: "block" }}>
      <div className="d-flex gap-10px align-items-center">
        <div className="d-flex justify-content-between w-100 align-items-center">
          <div>
            <h6 className="bold">{t("Car Delivery") as string}</h6>
            {(!bookingId && delivery?.checked && confirmed_delivery_location) ||
            (bookingId && deliverType_details?.includes("way")) ? (
              <div className="d-flex align-items-center">
                <span className="color-12 font-13px d-flex gap-2px">
                  <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                    <RiyalSymbol />
                  </span>
                  <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                    {`${
                      about_rent_price?.deliveryPrice ||
                      rental_about_price?.deliveryPrice
                    } `}
                  </span>
                </span>
                <span className="color-12 font-13px">
                  {t("/ Per Destination") as string}
                </span>
              </div>
            ) : null}
          </div>
          <Switch
            key={`${bookingId} ${delivery?.checked} ${deliverType_details}`}
            checked={
              !bookingId
                ? delivery?.checked
                : deliverType_details?.includes("way")
            }
            onChange={switchHandler}
            inputProps={{ "aria-label": "controlled" }}
          />
        </div>
      </div>
      {(!bookingId && delivery?.checked) ||
      (bookingId && deliverType_details?.includes("way")) ? (
        <>
          <label className="mt-4 mb-1 color-12 w-100">
            {t("Pick-up location") as string}
          </label>
          <DeliveryInputs rentalDetailsLocation={handoverAddress} />
          {!isRentToOwn &&
          !handover_in_another_branch?.checked &&
          (!bookingId || (bookingId && !handoverAntherCity)) ? (
            <>
              <div className="separator my-3" />
              <div className="font-13px">
                <FormGroup>
                  {canHandover ? (
                    <div className="d-flex justify-content-between gap-10px">
                      <FormControlLabel
                        className="mb-2"
                        control={
                          <Checkbox
                            className="px-2 py-0"
                            checked={
                              bookingId
                                ? deliverType_details === "two_ways"
                                : deliveryType === "two_ways"
                            }
                            onChange={() => {
                              dispatch(setDeliveryReturn(0));
                              dispatch(setDeliveryType("two_ways"));
                            }}
                            color="primary"
                          />
                        }
                        label={t("Return at same pick-up location") as string}
                      />
                      <div className="color-3 d-flex gap-5px">
                        <div style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                          <RiyalSymbol />
                        </div>
                        <div style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                          {about_rent_price?.handoverInTheSameCityPrice}
                        </div>
                      </div>
                    </div>
                  ) : null}
                  <FormControlLabel
                    control={
                      <Checkbox
                        className="px-2 py-0"
                        checked={
                          bookingId
                            ? deliverType_details === "one_way"
                            : deliveryType === "one_way"
                        }
                        onChange={() => {
                          dispatch(setDeliveryReturn("1"));
                          dispatch(setDeliveryType("one_way"));
                        }}
                        color="primary"
                      />
                    }
                    label={t("Return to the branch") as string}
                  />
                </FormGroup>
              </div>
            </>
          ) : null}
        </>
      ) : null}
    </Div>
  );
}

export default memo(CarDelivery);
