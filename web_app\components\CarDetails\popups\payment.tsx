/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @next/next/no-img-element */
/* eslint-disable react-hooks/exhaustive-deps */
import { CreditCard } from "@material-ui/icons";
import Popup from "components/shared/popup";
import { useRouter } from "next/router";
import { useSnackbar } from "notistack";
import { Dispatch, SetStateAction, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector, useDispatch } from "react-redux";
import styled from "styled-components";
import { TavialablePaymentMethods, TpaymentMethods } from "utilities/enums";
import { Button, Switch } from "@material-ui/core";
import { payWithWalletAction } from "store/wallet/action";
import { SelectPaymentMethod } from "store/cars/action";
import RiyalSymbol from "components/shared/RiyalSymbol";

declare global {
  interface Window {
    TamaraInstallmentPlan: any;
  }
}
const PaymentWrapper = styled.div`
  padding: 0 30px;
  button {
    background-color: var(--color-2);
    color: var(--color-4);
    width: 100%;
    font-weight: bold;
    margin-top: 30px;
    &:hover {
      background-color: var(--color-2);
    }
  }
  .wrap {
    display: grid;
    margin-top: 15px !important;
    grid-gap: 20px;

    > div {
      justify-content: space-between !important;
    }
    @media (max-width: 768px) {
      display: flex !important ;
      gap: 20px;
      flex-direction: column;
    }
  }
  @media (max-width: 768px) {
    padding: 0 !important;
    h4 {
      font-size: 18px;
    }
  }
`;

export default function PaymentPopup({
  isOpen,
  setIsOpen,
  onClose,
  hideCash,
}: {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  onClose?: () => void;
  hideCash?: boolean;
}) {
  //Hooks
  const { t, i18n } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();
  const router = useRouter();
  const dispatch = useDispatch();

  const { bookingId: booking_id } = router.query || {};

  //Store
  const {
    tamara_used,
    about_rent_price,
    tamara_availability,
    paymentMethod: _paymentMethod,
    availablePaymentMethods: _availablePaymentMethods,
  } = useSelector((state: RootStateOrAny) => state.cars) || {};
  const availablePaymentMethods =
    _availablePaymentMethods as TavialablePaymentMethods;
  const paymentMethod = _paymentMethod as TpaymentMethods;
  const { totalPrice, paymentMethod: paymentMethodFromDetails } =
    about_rent_price || {};
  const { rentalDetails, cash_option } =
    useSelector((state: RootStateOrAny) => state.booking) || {};
  const {
    totalBookingPrice: totalPriceFromDetails,
    totalAmountDue: totalAmountDueFromDetails,
  } = rentalDetails || {};
  const { pay_with_wallet } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};
  const { balance } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};

  const showCash = useMemo(() => {
    return Boolean(
      // !(booking_id && rentalDetails?.pendingPaymentOrder) &&
      !hideCash &&
        (availablePaymentMethods?.toUpperCase().includes("CASH") ||
          availablePaymentMethods?.toUpperCase().includes("all"))
    );
  }, [
    hideCash,
    booking_id,
    paymentMethodFromDetails,
    cash_option,
    availablePaymentMethods,
    rentalDetails,
  ]);

  //GQL
  function switchHandler(e: React.ChangeEvent<HTMLInputElement>) {
    const checked = e?.target?.checked;

    if (balance >= totalAmountDueFromDetails && booking_id) {
      dispatch(payWithWalletAction(false));
    }
    if (balance) {
      dispatch(payWithWalletAction(checked));
    } else {
      enqueueSnackbar(t(`Insufficient Balance`) as string, { variant: "info" });
      dispatch(payWithWalletAction(false));
    }
  }

  useEffect(() => {
    if (
      tamara_used &&
      typeof window != "undefined" &&
      window.TamaraInstallmentPlan
    ) {
      setTimeout(() => {
        window.TamaraInstallmentPlan.init({
          lang: i18n.language,
        });
        window.TamaraInstallmentPlan.render();
      });
    }
  }, [tamara_used, isOpen]);

  useEffect(() => {
    if (!paymentMethod) {
      dispatch(SelectPaymentMethod("MADA"));
    }
    // force mada when tamara is selected but not eligible for payment due to price
    if (paymentMethod === "TAMARA" && !tamara_availability) {
      dispatch(SelectPaymentMethod("MADA"));
    }
  }, [paymentMethod, tamara_availability]);

  if (isOpen) {
    return (
      <>
        <Popup
          maxWidth="sm"
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          title=""
          onClose={() => {
            onClose && onClose();
          }}
        >
          <PaymentWrapper>
            <div>
              <div className="d-flex gap-20px align-items-center">
                <CreditCard />
                <h4>{t("Choose a payment method") as string}</h4>
              </div>

              <div className="wrap">
                {availablePaymentMethods !== "cash" ? (
                  <>
                    <div
                      className={`d-flex gap-5px cursor-pointer align-items-center`}
                      onClick={() => {
                        dispatch(SelectPaymentMethod("MADA"));
                      }}
                    >
                      <div className="d-flex align-items-center gap-5px">
                        <img
                          src={
                            paymentMethod === "MADA"
                              ? "/assets/icons/filledCircle.svg"
                              : "/assets/icons/emptyCircle.svg"
                          }
                          alt="icon"
                          width={30}
                          height={30}
                        />
                        <h6>{t("Mada") as string}</h6>
                      </div>
                      <div>
                        <img
                          src="/assets/icons/mada.svg"
                          alt="mada"
                          width={65}
                        />
                      </div>
                    </div>
                    <div
                      className={`d-flex gap-5px cursor-pointer align-items-center `}
                      onClick={() => {
                        dispatch(SelectPaymentMethod("CREDIT_CARD"));
                      }}
                    >
                      <div className="d-flex align-items-center gap-5px">
                        <img
                          src={
                            paymentMethod === "CREDIT_CARD"
                              ? "/assets/icons/filledCircle.svg"
                              : "/assets/icons/emptyCircle.svg"
                          }
                          alt="icon"
                          width={30}
                          height={30}
                        />
                        <h6>{t("Credit Card") as string}</h6>
                      </div>
                      <div>
                        <img
                          src="/assets/icons/visa.svg"
                          alt="visa"
                          width={40}
                        />
                        <img
                          src="/assets/icons/master.svg"
                          alt="master"
                          width={40}
                        />
                      </div>
                    </div>
                  </>
                ) : null}
                {tamara_availability &&
                availablePaymentMethods?.toLowerCase() !== "cash" ? (
                  <div
                    className={`d-flex gap-30px cursor-pointer`}
                    onClick={() => {
                      dispatch(SelectPaymentMethod("TAMARA"));
                    }}
                  >
                    <div className="d-flex align-items-center gap-5px">
                      <img
                        src={
                          paymentMethod === "TAMARA"
                            ? "/assets/icons/filledCircle.svg"
                            : "/assets/icons/emptyCircle.svg"
                        }
                        alt="icon"
                        width={30}
                        height={30}
                      />
                      <div>
                        <h6>{t("tamara-payment-label") as string}</h6>
                      </div>
                    </div>
                    <div>
                      <img
                        src="/assets/icons/tamara.svg"
                        alt="tamara"
                        width={70}
                      />
                    </div>
                  </div>
                ) : null}
                {showCash ? (
                  <div
                    className="d-flex align-items-center gap-5px cursor-pointer"
                    onClick={() => {
                      dispatch(SelectPaymentMethod("CASH"));
                    }}
                  >
                    <div className="d-flex align-items-center gap-5px">
                      <img
                        src={
                          paymentMethod === "CASH"
                            ? "/assets/icons/filledCircle.svg"
                            : "/assets/icons/emptyCircle.svg"
                        }
                        alt="icon"
                        width={30}
                        height={30}
                      />
                      <h6>{t("Pay upon receipt") as string}</h6>
                    </div>
                    <div>
                      <img src="/assets/icons/cash.svg" alt="visa" width={40} />
                    </div>
                  </div>
                ) : null}
              </div>
              {balance && paymentMethod !== "CASH" ? (
                <div className="d-flex justify-content-between align-content-center">
                  <div
                    className="d-flex gap-5px align-items-center mt-3"
                    style={{ fontWeight: 500, padding: "0 5px" }}
                  >
                    <span>{t("Wallet.payment") as string}</span>
                    <div className="d-flex gap-5px font-14px">
                      <RiyalSymbol
                        style={{ order: i18n.language === "ar" ? 1 : 0 }}
                      />
                      <p style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                        {balance}{" "}
                      </p>
                    </div>
                    <Switch
                      checked={pay_with_wallet}
                      onChange={switchHandler}
                      inputProps={{ "aria-label": "controlled" }}
                    />
                  </div>
                  <img src="/assets/icons/wallet.svg" alt="wallet" />
                </div>
              ) : null}
            </div>
            <Button
              className="radius-3"
              style={{
                background: "var(--color-2)",
                color: "var(--color-4)",
                fontWeight: "bold",
                padding: "5px 25px 10px 25px",
                borderRadius: "var(--radius-3)",
              }}
              disabled={!paymentMethod}
              onClick={() => {
                setIsOpen(false);
              }}
            >
              {t("close") as string}
            </Button>
          </PaymentWrapper>
          {paymentMethod === "TAMARA" ? (
            <div
              className="tamara-installment-plan-widget"
              data-lang={i18n.language}
              data-price={!booking_id ? totalPrice : totalPriceFromDetails}
              data-currency="SAR"
              data-color-type="default"
              data-country-code="SA"
              data-number-of-installments="3"
            />
          ) : (
            ""
          )}
        </Popup>
      </>
    );
  }
  return null;
}
