import { gql } from "@apollo/client";

export const ListRentToOwnCars_Query = gql`
  query listRentToOwnCars(
    $filter: String
    $isNewCar: Boolean
    $limit: Int
    $page: Int
    $pickUpLocationId: ID
  ) {
    listRentToOwnCars(
      filter: $filter
      isNewCar: $isNewCar
      limit: $limit
      page: $page
      pickUpLocationId: $pickUpLocationId
    ) {
      collection {
        id
        make {
          name
        }
        carModel {
          name
        }
        carVersion {
          name
        }
        year
        availabilityStatus
        availableCarsCount
        minimumMonthlyInstallment
        carMakeName
        carImages
        carThumb
        # vehicleTypeName
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

export const CarGrouping_Query = gql`
  query listRentToOwnCarOptions(
    $carId: ID!
    $limit: Int
    $page: Int
    $pickUpLocationId: ID
  ) {
    listRentToOwnCarOptions(
      carId: $carId
      limit: $limit
      page: $page
      pickUpLocationId: $pickUpLocationId
    ) {
      collection {
        isUnlimited
        isUnlimitedFree
        id
        minimumMonthlyInstallment
        availabilityStatus
        carMakeName
        carModelName
        carImages
        carThumb
        year
        transmissionName
        distanceByMonth
        additionalDistanceCost
        branch {
          districtName
          area {
            name
          }
        }
        ownCarDetail {
          availableAfterDays
          km
          isRented
          color {
            id
            name
          }
          ownCarPlans {
            id
            noOfMonths
            monthlyInstallment
            firstInstallment
          }
        }
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

export const CarProfile_Query = gql`
  query carProfile($id: ID!) {
    carProfile(id: $id) {
      carImages
      carMakeName
      carModelName
      year
      distanceByMonth
      distanceBetweenCarUser
      additionalDistanceCost
      ownCarDetail {
        ownCarMedia {
          id
          displayOrder
          mediaType
          mediaUrl
        }
        ownCarPlans {
          id
          isActive
          noOfMonths
          finalInstallment
        }
        ownCarFeature {
          features {
            id
            icon
            name
            isActive
          }
          colorName
          km
          transmissionName
        }
      }
    }
  }
`;
