import React from "react";
import { useTranslation } from "react-i18next";
import PartenersCard from "../Card";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { selectFursanAction, toggleFursanModal } from "store/loyality/action";
import { GoToLogin } from "store/authentication/action";
import VerifyMembershipModal from "../VerifyMembershipModal";
import { memo } from "react";
import RiyalSymbol from "components/shared/RiyalSymbol";

function Fursan({ getSettingValue }) {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();

  const { is_fursan_selected } =
    useSelector((state: RootStateOrAny) => state.loyality) || {};
  const authentication = useSelector(
    (state: RootStateOrAny) => state.authentication
  );
  const { token } = authentication?.login_data || {};
  const { is_fursan_membershipId_verified, is_fursan_modal_open } =
    useSelector((state: RootStateOrAny) => state.loyality) || {};

  return (
    <>
      <PartenersCard
        {...{
          logo: "/assets/icons/fursan.svg",
          setting: (
            <div className="d-flex gap-5px align-items-baseline">
              <div className="d-flex align-items-baseline gap-5px">
                <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                  <RiyalSymbol />
                </span>
                <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                  {getSettingValue("fursan_mile_rate")}
                </span>
              </div>
              <span style={{ transform: "translateY(-3px)" }}>=</span>
              <span
                style={{
                  order: i18n.language === "ar" ? 0 : 1,
                }}
              >
                {`1 ${t("Mile")}`}
              </span>
            </div>
          ),
          checked: is_fursan_selected,
          setChecked: (isChecked: boolean) => {
            if (!token && isChecked) return dispatch(GoToLogin(true));
            if (!is_fursan_membershipId_verified && isChecked) {
              dispatch(toggleFursanModal(true));
            }
            dispatch(selectFursanAction(isChecked));
          },
        }}
      />

      <VerifyMembershipModal
        {...{
          logo: "/assets/icons/fursan.svg",
          isOpen: is_fursan_modal_open,
          setIsOpen: (e: any) => {
            dispatch(toggleFursanModal(e));
            if (!is_fursan_membershipId_verified) {
              dispatch(selectFursanAction(false));
              return;
            }
          },
          title: t("Membership ID"),
          subTitle: t("Enter AlFursan membership ID below"),
          inputLabel: t("Membership ID.input"),
          getSettingValue,
        }}
      />
    </>
  );
}

export default memo(Fursan);
