import Popup from "./popup";
import { SyntheticEvent, memo, useState } from "react";
import { useQuery, useMutation } from "@apollo/client";

import styled from "styled-components";
import { CancelRental_Mutation } from "gql/mutations/CancelRental";
import { CustomerCancelReasons } from "gql/queries/CustomerCancelReasons";
import TextareaAutosize from "@material-ui/core/TextareaAutosize";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "notistack";

import {
  FormControlLabel,
  FormGroup,
  List,
  RadioGroup,
  ListItem,
  Radio,
  FormControl,
} from "@material-ui/core";
import { useRouter } from "next/router";
import useRentalType from "components/CarDetails/heplers/rentalType";

const DIV = styled.div`
  text-align: center;
  .cancelReasons {
    text-align: center;
    background-color: var(--color-3);
    border-radius: var(--radius-2);
    color: var(--color-4);
    padding: 15px 20px 15px 20px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 20px;
  }
`;
const CancelReasons = ({
  isOpen,
  title,
  setIsOpen,
  rentalId = undefined,
  status,
  isRentToOwn,
}) => {
  const { t, i18n } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();
  const router = useRouter();
  const [reasonsList, setReasonsList] = useState<any>([]);
  const [message, setMessage] = useState("");
  const [messagePopup, setMessagePopup] = useState(false);
  const { isRentToOwn: _isRentToOwn } = useRentalType();

  const { data: reasons } = useQuery(CustomerCancelReasons, {
    skip: !isOpen,
    variables: {
      rentalType: isRentToOwn || _isRentToOwn ? "rent_to_own" : "rental",
      status: status === "car_received" ? "car received" : status,
      userType: "customer",
    },
  });

  const [cancelRental] = useMutation(CancelRental_Mutation, {
    errorPolicy: "all",
  });

  const handleChange = (event: SyntheticEvent, oneReason) => {
    event.stopPropagation();
    // const selectedId = oneReason.id; // Assuming `oneReason` is passed to `handleChange` with the checkbox's onChange event
    const isChecked = (event.target as any).checked;
    setReasonsList((prevReasons: any) => [oneReason]);
  };

  const CancelRental = () => {
    cancelRental({
      variables: {
        cancelledReason: message,
        cancelledReasons: reasonsList.map((reasonsList: any) => {
          return +reasonsList.id;
        }),
        rentalId: rentalId,
      },
    }).then((res) => {
      if (res?.errors?.find((err) => err.message.includes("cancel"))) {
        return setMessagePopup(true);
      }
      if (res?.data?.cancelRental?.errors?.length) {
        const { errors }: { errors: any } = res.data.cancelRental;
        enqueueSnackbar(errors.message, { variant: "error" });
      } else {
        enqueueSnackbar(t("rent cancelled successfully") as string, {
          variant: "success",
        });
        setIsOpen(false);
        router.push("/my-rentals?tab=current");
      }
    });
  };

  function validateReason(reasonText: string) {
    return Boolean(
      reasonsList?.find((reasonItem: any) =>
        reasonItem.enBody.toLowerCase().includes(reasonText.toLowerCase())
      )
    );
  }

  return (
    <>
      <Popup
        maxWidth="md"
        fullWidth
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        title={title}
      >
        <DIV>
          <List style={{ width: "100%" }}>
            <FormControl aria-label="reasons" style={{ width: "100%" }}>
              <RadioGroup name="cancel-reason">
                {reasons?.cancelledReasons &&
                  reasons?.cancelledReasons.map(
                    (oneReason: any, index: number) => (
                      <ListItem key={index} role={undefined} dense>
                        <FormControlLabel
                          control={
                            <Radio
                              id={`radio-${oneReason.id}`} // Ensure IDs are unique
                              onChange={(event) =>
                                handleChange(event, oneReason)
                              }
                              value={oneReason.id}
                            />
                          }
                          label={oneReason.body}
                          style={{
                            marginLeft: 0,
                            marginRight: 0,
                            width: "100%",
                          }}
                        />
                      </ListItem>
                    )
                  )}
              </RadioGroup>
            </FormControl>
          </List>
          {validateReason("other") || validateReason("early") ? (
            <TextareaAutosize
              className="w-100"
              onChange={(e) => setMessage(e.target.value)}
              aria-label="minimum height"
              minRows={3}
              placeholder={t("Reason")}
            />
          ) : null}
          <div>
            <button
              className="cancelReasons border-0"
              onClick={() => {
                CancelRental();
              }}
              disabled={
                (validateReason("early") && !message?.length) ||
                !reasonsList?.length
              }
            >
              {t("Cancel Reservation") as string}
            </button>
          </div>
        </DIV>
      </Popup>
      <Popup
        maxWidth="md"
        fullWidth
        isOpen={messagePopup}
        setIsOpen={setMessagePopup}
        title={""}
      >
        {t("car rental refuse") as string}
      </Popup>
    </>
  );
};
export default memo(CancelReasons);
