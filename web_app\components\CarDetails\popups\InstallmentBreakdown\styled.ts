import styled from "styled-components";

export const Div = styled.div`
  padding: 0 20px;

  #installment-breakdown {
    > img {
      width: 180px;
      margin: 0 auto;
    }
    .seperate-line {
      width: 100%;
      background-color: #bebebe;
      height: 1px;
    }
  }
  .card-installement {
    -webkit-box-shadow: 15px 15px 50px 10px rgb(0 0 0 / 10%);
    -moz-box-shadow: 15px 15px 50px 10px rgb(0 0 0 / 10%);
    box-shadow: 15px 15px 50px 10px rgb(0 0 0 / 10%);
    padding: 15px;
    border-radius: var(--radius-3);
    margin-top: 15px;
  }
  .installment-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
    .header {
      background: #fbfbfb;
    }
    span {
      border-bottom: 1px solid var(--color-13);
    }
  }
  .scrollable {
    max-height: 225px;
    overflow-y: auto;
  }
`;
