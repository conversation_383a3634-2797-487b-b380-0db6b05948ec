/* eslint-disable @next/next/no-img-element */
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import MessagePopup from "./messagePopup";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import styled from "styled-components";
import { setCouponInvalid } from "store/coupons/action";
export const DIV = styled.div`
  gap: 7px;
  margin-top: 20px;
  > div {
    width: 50%;
    margin-top: 20px;
    &:last-child {
      color: var(--color-3);
      border: solid 2px var(--color-3);
      border-radius: var(--radius-2);
      padding: 15px 20px 15px 20px !important;
    }
  }
  .button {
    background: var(--color-3);
    padding: 15px 20px 15px 20px;
    cursor: pointer;
    color: #fff;
  }
  .btn-back {
    color: #000;
    padding: 10px 20px 15px 20px;
    cursor: pointer;
  }
`;

export default function CouponPopUp({ isOpen, setIsOpen, couponErrorMessage }) {
  const router = useRouter();
  const bookingId = router?.query.bookingId;
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const { coupon_data } =
    useSelector((state: RootStateOrAny) => state.coupons) || {};

  const { minRentDays } = coupon_data || {};

  const showMinimumRentAlert =
    couponErrorMessage &&
    ((couponErrorMessage.includes("exceed") &&
      couponErrorMessage.includes("day")) ||
      couponErrorMessage.includes("يوم") ||
      couponErrorMessage.includes("يتجاوز"));
  const handelClick = () => {
    document.getElementById("rent-action")?.click();
  };

  function closeHandler() {
    dispatch(setCouponInvalid(false));
  }

  if (isOpen && couponErrorMessage) {
    return (
      <MessagePopup
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        onClose={closeHandler}
        body={
          <div className="text-center">
            <img src="/assets/icons/coupon.svg" alt="icon" width={100} />
            <h4 className="bold text-center mb-2">
              {!bookingId
                ? (t("Coupon not applied") as string)
                : (t("Coupon applied") as string)}
            </h4>
            {showMinimumRentAlert ? (
              <p className="mt-3 text-center">
                {i18n.language === "en"
                  ? `Dear customer, the number of rental days must at least ${minRentDays} days or more to be able to use the discount coupon.`
                  : `عزيزنا العميل.. لتتمكن من الاستفادة من كوبون الخصم يجب ان يكون عدد ايام التاجير ${minRentDays} يوم واكثر`}
              </p>
            ) : (
              <p className="mt-3 text-center">{couponErrorMessage}</p>
            )}
            <DIV
              className={`d-flex ${bookingId ? "justify-content-center" : "justify-content-between"
                } mt-3`}
            >
              {!bookingId ? (
                <div
                  className="button radius-3"
                  onClick={() => {
                    closeHandler();
                    handelClick();
                  }}
                >
                  {t("Proceed") as string}
                </div>
              ) : null}
              <div
                className="btn-back"
                onClick={() => {
                  closeHandler();
                  setIsOpen(false);
                }}
              >
                {!bookingId ? (t("Back") as string) : (t("Done") as string)}
              </div>
            </DIV>
          </div>
        }
      // title={t("Coupon not applied")}
      />
    );
  }
  return null;
}
