export const CREATING_EXTENSION = "PAY_EXTENSION";
export const PAY_EXTENSION = "PAY_EXTENSION";
export const SUCCESSFUL_EXTENSION_CREATED = "SUCCESSFUL_EXTENSION_CREATED";
export const CLEAR_EXTENSION_DATA = "C<PERSON>AR_EXTENSION_DATA";
export const EXTENSION_PRICE = "EXTENSION_PRICE";
export const EXTENSION_WITH_WALLET_ACTION = "EXTENSION_WITH_WALLET_ACTION";

export function setCreatingOnlineExtensionRequset(payload) {
  return {
    type: CREATING_EXTENSION,
    payload,
  };
}
export function setIsExtensionRequestPaid(payload) {
  return {
    type: PAY_EXTENSION,
    payload,
  };
}
export function setIsExtensionRequestCreatedSuccessfully(payload) {
  return {
    type: SUCCESSFUL_EXTENSION_CREATED,
    payload,
  };
}
export function setExtensionTotalRemainingPrice(payload) {
  return {
    type: EXTENSION_PRICE,
    payload,
  };
}
export function setExtensionWithWalletAction(payload) {
  return {
    type: EXTENSION_WITH_WALLET_ACTION,
    payload,
  };
}
export function clearExtensionsData() {
  return {
    type: CLEAR_EXTENSION_DATA,
  };
}
