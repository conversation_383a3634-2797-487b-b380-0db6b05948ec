/* eslint-disable react-hooks/exhaustive-deps */
import Popup from "components/shared/popup";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { setShowCahOption } from "store/boooking/action";
import InstallmentDetails from "../popups/installmentDetails";
import { useRouter } from "next/router";

function InstallmentDetailsModal({
  setExtensionId,
  extensionId,
  refetchRentalDetails,
  setCalendarTooltipOpen,
  isThereInstallmentToPay,
  isInstallmentDetailsModalOpen,
  setIsInstallmentDetailsModalOpen,
}) {
  const dispatch = useDispatch();
  const router = useRouter();
  const { PayNow } = router.query || {};

  useEffect(() => {
    if (isInstallmentDetailsModalOpen) {
      dispatch(setShowCahOption(true));
    }
  }, [isInstallmentDetailsModalOpen]);

  useEffect(() => {
    if (PayNow) {
      setIsInstallmentDetailsModalOpen(true);
    }
  }, [PayNow]);
  return isInstallmentDetailsModalOpen ? (
    <Popup
      maxWidth="xs"
      fullWidth
      isOpen={isInstallmentDetailsModalOpen}
      setIsOpen={setIsInstallmentDetailsModalOpen}
      onClose={() => {
        setIsInstallmentDetailsModalOpen(false);
        dispatch(setShowCahOption(false));
      }}
      title=""
      backgroundColor="#F5F5F5"
    >
      <InstallmentDetails
        {...{
          setExtensionId,
          extensionId,
          refetchRentalDetails,
          setCalendarTooltipOpen,
          isThereInstallmentToPay,
          isInstallmentDetailsModalOpen,
        }}
      />
    </Popup>
  ) : null;
}

export default InstallmentDetailsModal;
