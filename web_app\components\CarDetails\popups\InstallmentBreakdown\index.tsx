/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { Switch } from "@material-ui/core";
import Popup from "components/shared/popup";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import {
  setPayWithInstallments,
  setShowInstallmmentBreakdownModal,
} from "store/installments/action";
import { installmentStatuses } from "utilities/enums";
import { convertToEnglishNumbers } from "utilities/helpers";
import Invoice from "../../Invoice";
import { Div } from "./styled";
import { useEffect } from "react";
import { setShowCahOption } from "store/boooking/action";
import { useRouter } from "next/router";

export default function InstallmentsBreakDown({
  title,
  setTermsConditionModalOpen,
  setExtensionId,
  extensionId,
  refetchRentalDetails,
  setCalendarTooltipOpen,
}) {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();
  const { pay_with_installments, show_installment_breakdown_modal } =
    useSelector((state: RootStateOrAny) => state.installments);
  const { about_rent_price } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const { rentalDetails } =
    useSelector((state: RootStateOrAny) => state.booking) || {};
  const installemtsBreakdown =
    about_rent_price?.installmentsBreakdown ||
    rentalDetails?.installments ||
    [];

  useEffect(() => {
    if (show_installment_breakdown_modal) {
      dispatch(setShowCahOption(true));
    }
  }, [show_installment_breakdown_modal]);

  return (
    <Popup
      maxWidth="sm"
      fullWidth
      isOpen={show_installment_breakdown_modal}
      setIsOpen={() =>
        dispatch(
          setShowInstallmmentBreakdownModal(!show_installment_breakdown_modal)
        )
      }
      onClose={() => {
        dispatch(setShowInstallmmentBreakdownModal(false));
        dispatch(setShowCahOption(false));
      }}
      title={title}
    >
      <Div>
        <div className="d-flex gap-10px flex-column" id="installment-breakdown">
          <img
            src={`/assets/images/${
              i18n.language == "en" ? "packagesEn" : "packages"
            }.svg`}
            alt="rental packages"
          />
          <div>
            <p className="font-18px text-center">
              {
                t(
                  "One of Carwah's products that aims at customizing packages that meets our customers' needs"
                ) as string
              }
            </p>
          </div>
          <div className="seperate-line" />
          {!Boolean(router.query?.isRentToOwn) && (
            <div className="d-flex justify-content-between align-items-center card-installement">
              <p>{t("Split full amount on monthly installments") as string}</p>
              <Switch
                checked={pay_with_installments}
                onChange={() =>
                  dispatch(setPayWithInstallments(!pay_with_installments))
                }
                inputProps={{ "aria-label": "controlled" }}
                disabled={Boolean(router.query?.bookingId)}
              />
            </div>
          )}
          {pay_with_installments && installemtsBreakdown?.length ? (
            <div className="card-installement scrollable">
              <h6 className="color-12 mb-3">
                {t("The monthly installments are as follows") as string}
              </h6>

              <div className=" installment-grid text-center">
                <span className="color-14 bold header">
                  {t("Date") as string}
                </span>
                <span className="color-14 bold header">
                  {t("Amount") as string}
                </span>
                <span className="color-14 bold header">
                  {t("Status") as string}
                </span>
                {installemtsBreakdown?.map(
                  (
                    { amount, amountDue, dueDate, installmentNumber, status },
                    index
                  ) => {
                    return (
                      <>
                        <span
                          className="p-1"
                          style={{ color: installmentStatuses[status].color }}
                        >
                          {convertToEnglishNumbers(
                            moment(dueDate).locale(i18n.language).format("ll")
                          )}
                        </span>
                        <span
                          className="bold"
                          style={{ color: installmentStatuses[status].color }}
                        >
                          {amount}
                        </span>
                        <span
                          className=""
                          style={{ color: installmentStatuses[status].color }}
                        >
                          {installmentStatuses[status].name[i18n.language]}
                        </span>
                      </>
                    );
                  }
                )}
                <></>
              </div>
            </div>
          ) : null}
        </div>
        {pay_with_installments ? (
          <div
            className="d-flex gap-5px mt-4 justify-content-center align-items-center text-align-localized"
            style={{ flexWrap: "wrap" }}
          >
            <span>
              {t("By continuing you indicate your acceptance of the") as string}
            </span>
            <span
              className="color-3 d-inline-block text-decoration-underline cursor-pointer "
              onClick={() => setTermsConditionModalOpen(true)}
            >
              {t("Terms_and_Conditions") as string}
            </span>
          </div>
        ) : null}
      </Div>
      <Invoice
        setCalendarTooltipOpen={setCalendarTooltipOpen}
        refetchRentalDetails={refetchRentalDetails}
        extensionId={extensionId}
        setExtensionId={setExtensionId}
        isInInstallmentBreakDown
        isIntallmentDetailsModal={undefined}
      />
    </Popup>
  );
}
