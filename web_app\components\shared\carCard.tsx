/* eslint-disable @next/next/no-img-element */
import { Grid } from "@material-ui/core";
import WhiteCard from "components/shared/whiteCard";
import React from "react";
import { useTranslation } from "react-i18next";
import { ReactSVG } from "react-svg";
import styled from "styled-components";

const Div = styled.div`
  position: relative;
  -webkit-box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
  -moz-box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
  box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
  border-radius: 15px;
  > div {
    padding: 20px;
    border: solid 2px #f8f8f8;
    height: 100%;
  }
  .deal {
    margin: 0 5px;
  }
  .head {
    > div {
      width: fit-content;
    }
    .favorite-wrap {
      cursor: pointer;
    }
    .fill {
      * {
        transition: all 0.5s ease;
        fill: var(--color-9);
      }
    }
    img {
      width: 20px;
      margin-top: -2px;
    }
  }
  .body {
    h2 {
      line-height: 20px;
      margin: 15px 0 10px 0;
      span:nth-child(1) {
        font-size: 18px;
        line-height: 28px;
        font-weight: 900;
      }
      span:nth-child(2) {
        font-size: 15px;
        line-height: 24px;
        font-weight: 400;
        color: var(--color-1);
        opacity: 0.5;
      }
    }
    .car-img {
      margin-top: 30px;
      img {
        width: 100%;
        /* height: 140px; */
      }
    }
    .location-car {
      display: flex;
      h6 {
        margin: 0 5px;
        font-size: 15px;
        color: var(--color-1);
        opacity: 0.5;
        flex-direction: ${(props) =>
          props.language === "ar" ? "row-reverse" : null};
      }
    }
    .deal-description {
      color: var(--color-9);
      font-size: 15px;
      font-weight: 400;
      margin-top: 22px;
    }
  }
  .foot {
    border-top: solid 1px var(--color-10);
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 15px 0px 0 0px;
    span {
      margin: 0 10px;
      padding: 8px 10px 8px 10px;
      border-radius: var(--radius-1);
      font-size: 14px;
      &:first-child {
        color: #c4c4c4;
        strong {
          text-decoration: line-through;
          text-decoration-color: var(--color-9);
        }
      }
      &:last-child {
        background-color: var(--color-2);
        color: var(--color-4);
      }
    }
  }
`;

export default function CarCard({ deal, car }) {
  const { t, i18n } = useTranslation();
  return (
    <Div language={i18n.language}>
      <WhiteCard>
        <div>
          <Grid container justifyContent="space-between" className="head">
            {deal ? (
              <Grid item xs="auto" container alignItems="center">
                <Grid item>
                  <img src={deal.icon} alt="icon" />
                </Grid>
                <Grid item>
                  <span className="deal">{deal.text}</span>
                </Grid>
              </Grid>
            ) : null}
            <div
              className="favorite-wrap"
              onClick={(e) =>
                e.target.closest(".favorite-wrap").classList.toggle("fill")
              }
            >
              <ReactSVG src={`/assets/images/favorite.svg`} />
            </div>
          </Grid>
          <div className="body">
            <div className="text-center car-img">
              <img src={car.img} alt="car" />
            </div>
            <h2>
              <span>{`${car.model} ${car.year} `}</span>
            </h2>
            <div className="location-car">
              <img src={`/assets/images/location.svg`} alt="loaction" />
              <h6>{`${car.location.city}, ${t("Dist.")} ${
                car.location.district
              }`}</h6>
            </div>
            {deal ? (
              <div className="deal-description text-align-localized">
                {deal.description}
              </div>
            ) : null}
          </div>
        </div>

        <div className="foot">
          <span>
            <strong>{car.price.before}</strong>
            {` ${t("SR/day")} `}
          </span>
          <span>
            <strong>{car.price.after}</strong>
            {` ${t("SR/day")} `}
          </span>
        </div>
      </WhiteCard>
    </Div>
  );
}
