export const SAVE = "SAVE";
export const DELETE = "DELETE";
export const EDIT = "EDIT";
export const SAVEEDIT = "SAVEEDIT";
export const REMOVEEDITSTATUS = "REMOVEEDITSTATUS";
export const REMOVELIST = "REMOVELIST";
export const MY_RENTALS_CURREMT_PAGE = "MY_RENTALS_CURREMT_PAGE";
export const RENTAL_DETAILS = "RENTAL_DETAILS";
export const RENTAL_BRANCH = "RENTAL_BRANCH";
export const SET_RENTAL_ABOUT_PRICE = "SET_RENTAL_ABOUT_PRICE";
export const SET_RENTAL_DETAILS = "SET_RENTAL_DETAILS";
export const PRICE_TEMP = "PRICE_TEMP";
export const CASH_OPTION = "CASH_OPTION";
export const CLEAR_DATA = "CLEAR_DATA";

export function SaveBookingData(payload) {
  return {
    type: SAVE,
    payload,
  };
}

export function EditBookingData(payload) {
  return {
    type: SAVEEDIT,
    payload,
  };
}
export function DeleteBooking(payload) {
  return {
    type: DELETE,
    payload,
  };
}
export function EditBooking(payload) {
  return {
    type: EDIT,
    payload,
  };
}
export function RemoveEditStatus() {
  return {
    type: REMOVEEDITSTATUS,
  };
}
export function RemoveList() {
  return {
    type: REMOVELIST,
  };
}
export function MyRentalsCurrentPageAction(payload) {
  return {
    type: MY_RENTALS_CURREMT_PAGE,
    payload,
  };
}
export function GetRentalDetails(payload) {
  return {
    type: RENTAL_DETAILS,
    payload,
  };
}
export function setRentalBranchData(payload) {
  return {
    type: RENTAL_BRANCH,
    payload,
  };
}

export function setRentalAboutPrice(payload) {
  return {
    type: SET_RENTAL_ABOUT_PRICE,
    payload,
  };
}
export function setRentalDetails(payload) {
  return {
    type: SET_RENTAL_DETAILS,
    payload,
  };
}

export function setPriceTemp(payload) {
  return {
    type: PRICE_TEMP,
    payload,
  };
}
export function setShowCahOption(payload) {
  return {
    type: CASH_OPTION,
    payload,
  };
}
export function clearBookingData() {
  return {
    type: CASH_OPTION,
  };
}
