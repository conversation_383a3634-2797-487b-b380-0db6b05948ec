import { gql } from "@apollo/client";

export const ProfileImageMuation = gql`
  mutation ProfileImage($input: ProfileImageInput!) {
    profileImage(input: $input) {
      status
      errors
      user {
        profileImage
      }
    }
  }
`;

export const BasicProfileMutation = gql`
  mutation BasicProfile($input: BasicProfileInput!) {
    basicProfile(input: $input) {
      clientMutationId
      errors
      status
      user {
        companyName
        createdAt
        dob
        driverLicense
        driverLicenseExpireAt
        driverLicenseStatus
        email
        emailConfirmed
        firstName
        gender
        id
        isActive
        isCustomerProfileCompleted
        lastName
        lat
        licenseFrontImage
        licenseSelfieImage
        lng
        middleName
        mobile
        name
        nid
        passportNumber
        profileImage
        status
        title
      }
    }
  }
`;

export const LicenseProfileMuation = gql`
  mutation LicenseProfile($input: LicenseProfileInput!) {
    licenseProfile(input: $input) {
      errors
      status
      user {
        companyName
        createdAt
        dob
        driverLicense
        driverLicenseExpireAt
        driverLicenseStatus
        email
        emailConfirmed
        firstName
        gender
        id
        isActive
        isCustomerProfileCompleted
        lastName
        lat
        licenseFrontImage
        licenseSelfieImage
        lng
        middleName
        mobile
        name
        nid
        passportNumber
        profileImage
        status
        title
      }
    }
  }
`;

export const YakeenVerifyProfileMutation = gql`
  mutation YakeenVerifyProfile($input: YakeenVerifyProfileInput!) {
    yakeenVerifyProfile(input: $input) {
      errors
      success
    }
  }
`;
export const UpdateProfileImagesMutation = gql`
  mutation UpdateProfileImages($input: UpdateProfileImagesInput!) {
    updateProfileImages(input: $input) {
      errors
      status
    }
  }
`;
