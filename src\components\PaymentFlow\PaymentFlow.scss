.payment-form-container {
  .payment-method-info {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;

    h6 {
      margin: 0 0 8px 0;
      font-weight: 600;
      color: #333;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .hyperpay-form-container {
    min-height: 300px;
    padding: 20px 0;

    .paymentWidgets {
      max-width: 100%;
    }

    // Override Hyperpay default styles
    :global {
      .wpwl-form {
        max-width: 100% !important;
        margin: 0 auto;
        
        .wpwl-group {
          margin-bottom: 20px;
          
          .wpwl-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            display: block;
          }
          
          .wpwl-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            
            &:focus {
              border-color: #007bff;
              outline: none;
              box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
            }
          }
        }
        
        .wpwl-button {
          background-color: #007bff !important;
          border-color: #007bff !important;
          color: white !important;
          padding: 12px 24px !important;
          border-radius: 4px !important;
          font-size: 16px !important;
          font-weight: 500 !important;
          width: 100% !important;
          margin-top: 20px !important;
          cursor: pointer !important;
          transition: all 0.3s ease !important;
          
          &:hover {
            background-color: #0056b3 !important;
            border-color: #0056b3 !important;
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
          
          &:disabled {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            cursor: not-allowed !important;
            transform: none !important;
          }
        }
        
        .wpwl-hint {
          font-size: 12px;
          color: #666;
          margin-top: 4px;
        }
        
        .wpwl-error {
          color: #dc3545;
          font-size: 12px;
          margin-top: 4px;
        }
      }
      
      // Card brand icons
      .wpwl-brand {
        .wpwl-brand-card {
          width: 40px;
          height: 25px;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
        }
      }
      
      // Loading state
      .wpwl-form-loading {
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
        }
      }
    }
  }
}

// Loading state
.payment-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  
  .loading-spinner {
    margin-bottom: 16px;
  }
  
  .loading-text {
    color: #666;
    font-size: 14px;
    text-align: center;
  }
}

// Error state
.payment-error {
  padding: 20px;
  text-align: center;
  
  .error-icon {
    font-size: 48px;
    color: #dc3545;
    margin-bottom: 16px;
  }
  
  .error-message {
    color: #dc3545;
    margin-bottom: 20px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .payment-form-container {
    .payment-method-info {
      padding: 12px;
      
      h6 {
        font-size: 14px;
      }
    }
    
    .hyperpay-form-container {
      padding: 16px 0;
      
      :global {
        .wpwl-form {
          .wpwl-group {
            margin-bottom: 16px;
            
            .wpwl-control {
              padding: 10px;
              font-size: 16px; // Prevent zoom on iOS
            }
          }
          
          .wpwl-button {
            padding: 14px 20px !important;
            font-size: 16px !important;
          }
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .payment-form-container {
    .payment-method-info {
      border-left: none;
      border-right: 4px solid #007bff;
    }
  }
  
  :global {
    .wpwl-form {
      direction: rtl;
      
      .wpwl-group {
        .wpwl-label {
          text-align: right;
        }
      }
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .payment-form-container {
    .payment-method-info {
      background: #2d3748;
      color: #e2e8f0;
      
      h6 {
        color: #e2e8f0;
      }
    }
    
    :global {
      .wpwl-form {
        .wpwl-group {
          .wpwl-label {
            color: #e2e8f0;
          }
          
          .wpwl-control {
            background: #4a5568;
            border-color: #718096;
            color: #e2e8f0;
            
            &:focus {
              border-color: #63b3ed;
            }
          }
        }
        
        .wpwl-hint {
          color: #a0aec0;
        }
      }
    }
  }
}
