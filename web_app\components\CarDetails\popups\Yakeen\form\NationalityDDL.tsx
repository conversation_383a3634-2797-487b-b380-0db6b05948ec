import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import Select from "react-select";
import { useQuery } from "@apollo/client";
import { Nationalities_Query } from "gql/queries/lookups";
import { TProfileStatus } from "types/profile";
import styled from "styled-components";

const Styled = styled(Select)`
  > div {
    padding: 8px 0;
    border-radius: 12px !important;
  }
`;

function NationalityDDL({ watch, control, errors }) {
  const { t } = useTranslation();
  const { data: nationalitiesRes } = useQuery(Nationalities_Query, {
    variables: {
      isGulf:
        (watch("status") as TProfileStatus) === "gulf_citizen" ? true : false,
    },
  });

  return (
    <div className="mt-4">
      <label className="color-19 text-start w-100 mb-2">
        {t("Nationality") as string}

        <span className="color-9">*</span>
      </label>
      <Controller
        name={"nationalityId"}
        control={control}
        rules={{
          required: true,
        }}
        render={({ field, fieldState }) => {
          return (
            <Styled
              placeholder={""}
              className="required text-align-localized select-styled"
              // defaultValue={{
              //   label:
              //     userProfileRes?.profile?.customerProfile?.nationality?.name,
              //   value:
              //     userProfileRes?.profile?.customerProfile?.nationality?.id,
              // }}
              options={nationalitiesRes?.nationalities.map((item) => {
                return {
                  label: item.name,
                  value: item.id,
                };
              })}
              onChange={(e) => {
                if (e) {
                  field.onChange(e);
                }
              }}
            />
          );
        }}
      />
      {errors?.nationalityId?.type === "required" && (
        <p className="color-9">{t("This field is required") as string}</p>
      )}
    </div>
  );
}

export default NationalityDDL;
