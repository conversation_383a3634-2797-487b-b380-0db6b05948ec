/* eslint-disable @next/next/no-img-element */
/* eslint-disable react-hooks/exhaustive-deps */
import {
  Container,
  Grid,
  Button,
  ClickAwayListener,
  Box,
  useMediaQuery,
  Theme,
} from "@material-ui/core";
import Menu from "./menu";
import MenuIcon from "@material-ui/icons/Menu";
import Link from "next/link";
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { memo, useEffect, useState } from "react";
import KeyboardArrowDownIcon from "@material-ui/icons/KeyboardArrowDown";
import { GoToLogin, LogOutAction } from "store/authentication/action";
import { setCouponData } from "store/coupons/action";
import { claerUserDataAction } from "store/user/action";
import LoggedInTooltip from "./loggedInTooltip";
import Tooltip from "./tooltip";

const HeaderTag = styled.header`
  @media (min-width: 768px) {
    height: 72px;
  }
  position: fixed;
  width: 100%;
  z-index: 999;
  background-color: ${(props) =>
    props.pathname === "/" ? "var(--color-4)" : "var(--color-4)"};
  border-bottom: solid 1px #e1e3e7;
  > div,
  > div > div {
    height: 100%;
  }
  .burger-menu {
    @media (min-width: 961px) {
      display: none;
    }
    svg {
      cursor: pointer;
    }
  }
  .menu {
    nav {
      padding: 0 3rem;
      svg {
        display: none;
      }
    }
    &.toggle {
      @media (max-width: 960px) {
        transform: ${(props) =>
          props.language === "en"
            ? "translateX(-100vw)"
            : "translateX(+100vw)"};
        transition: all ease 0.3s;
      }
    }
    @media (max-width: 960px) {
      height: 100vh;
      z-index: 2;
      width: 50vw !important;
      position: fixed;
      left: ${(props) => (props.language === "en" ? 0 : "")};
      right: ${(props) => (props.language === "ar" ? 0 : "")};
      top: 0;
      background-color: var(--color-4);
      padding: 30px 0;
      box-shadow: 0px 0 5px 0px;
      nav {
        padding: 0 30px;
        height: 100%;
        display: block !important;
        a {
          margin: 20px 0;
        }
      }
      svg {
        display: block;
        position: fixed;
        right: 10px;
        top: 10px;
        cursor: pointer;
      }
    }
    @media (min-width: 961px) {
      svg {
        display: none !important;
      }
    }
  }
  > div {
    > div {
      padding: 25px 0 9px 0;
      @media (max-width: 960px) {
        padding: 10px 0;
        justify-content: space-between;
      }
      > div:last-child {
        display: flex;
        /* justify-content: flex-end; */
        align-items: center;
        > span {
          color: var(--color-11);
          display: inline-block;
        }
        button {
          background-color: var(--color-2);
          color: var(--color-4);
          padding: 12px 25px 18px 25px;
          font-size: 15px;
          line-height: 22px;
          @media (max-width: 570px) {
            font-size: 11px;
            padding: 3px 7px 7px 7px !important;
            font-weight: 700;
          }
          &:focus {
            outline: none;
          }
        }
        @media (max-width: 900px) {
          /* width: 75%; */
        }
      }
    }
  }
  .language-login-btns {
    position: relative;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    align-items: center;
    white-space: nowrap;

    @media (max-width: 960px) {
      gap: 8px;
    }
    @media (max-width: 768px) {
      gap: 5px;
    }
    @media (max-width: 576px) {
      flex-direction: row !important;
      flex-wrap: nowrap !important;
    }

    .header-button {
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50px;
      padding: 0 15px;
      font-weight: bold;
      font-size: 14px;
      line-height: 1;

      @media (max-width: 768px) {
        height: 32px;
        padding: 0 10px;
        font-size: 12px;
      }

      @media (max-width: 576px) {
        height: 28px;
        padding: 0 7px;
        font-size: 11px;
        min-width: unset;
      }
    }

    .lang-selector {
      background: #f0f0f0;
      border: solid 1px rgba(156, 156, 156, 0.5);
    }

    .login-button {
      background: #f0f0f0;
      color: #4e4e4e;
      border: solid 1px rgba(156, 156, 156, 0.5);
      box-shadow: unset !important;
      text-transform: capitalize;
      min-width: unset;
    }

    .try-button {
      background: var(--color-2);
      color: white;
      box-shadow: unset !important;
      text-transform: capitalize;
    }

    a {
      @media (max-width: 960px) {
        font-size: 13px;
      }
    }
    > span {
      color: var(--color-2) !important;
      font-size: 14px;
    }
  }
  #select-language {
    border: none;
  }
  @media (min-width: 768px) {
    min-height: 70px;
  }
  @media (max-width: 767px) {
    .logo {
      max-width: 100px;
      margin: 0 10px;
    }
  }
  #download-app {
    position: relative;
    > div {
      position: absolute;
      top: 36px;
      left: ${(props) => (props.language === "ar" ? 0 : "auto")};
      right: ${(props) => (props.language === "en" ? 0 : "auto")};
      background: #f8f8f8;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.25);
      padding: 5px 15px 20px 15px;
      display: flex;
      flex-direction: column;
      /* gap: 10px; */
      border-radius: 0 0px 7px 7px;
      transition: all ease 0.5s;
      width: min-content;
      p {
        text-align: center;
        margin-top: 7px;
        white-space: normal !important;
      }

      img {
        width: 150px;
        border-radius: 7px;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
        margin-top: 5px;
      }
      @media (max-width: 576px) {
        /* right: 0; */
        img {
          width: 120px;
        }
      }
    }
  }
  .lang-selector-text {
    transition: all ease 0.3s;
    &:hover {
      color: var(--color-2);
    }
  }
`;

function Header({ showHeader }) {
  const router = useRouter();
  const [settingsMenuOpen, setSettingsMenuOpen] = useState(false);
  const [isDownloadOpen, setIsDownloadOpen] = useState(false);
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const authentication = useSelector(
    (state: RootStateOrAny) => state.authentication
  );
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const isTablet = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("md")
  );

  const [dimensions, setDimensions] = useState({
    width: process.browser ? window.innerWidth : null,
    height: process.browser ? window.innerHeight : null,
  });

  const handleResize = () => {
    setDimensions({
      width: window.innerWidth,
      height: window.innerHeight,
    });
  };

  function localeChangeHandler(e) {
    const locale = e?.target?.value || e;
    i18n.changeLanguage(locale);
    sessionStorage.setItem("locale", locale);
    router
      .push(
        {
          query: router.query,
        },
        router.asPath,
        { locale }
      )
      .then(() => {
        window.location.reload(); // Force swiper carousel to reload after language change
      });
  }

  async function selectChangeHandler(e) {
    const locale = e?.target?.value || e;
    localeChangeHandler(locale);
    setTimeout(() => {
      window.scrollTo(0, 0);
      // window.location.reload();
    }, 500);
  }

  function forceDefaultLocaleHandler() {
    //force set Locale to ar in case of now locale in url
    if (!router.locale || !window.location.pathname?.includes(router.locale)) {
      router.push(
        {
          query: router.query,
        },
        router.asPath,
        { locale: "ar" }
      );
      i18n.changeLanguage("ar");
    }
  }
  useEffect(() => {
    forceDefaultLocaleHandler();
    window.addEventListener("resize", handleResize, false);
    return () => {
      window.removeEventListener("resize", handleResize, false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function downloadOffHandler() {
    setIsDownloadOpen(false);
  }

  function downloadOnHandler() {
    setIsDownloadOpen(true);
  }

  function styleHandler() {
    if (!isDownloadOpen) {
      downloadOnHandler();
    } else {
      downloadOffHandler();
    }
  }

  useEffect(() => {
    document
      .querySelector(".toggle-download")
      ?.addEventListener("click", styleHandler);
    document
      .querySelector(".toggle-download span")
      ?.addEventListener("mouseenter", (e) => {
        e.stopPropagation();

        downloadOnHandler();
      });

    document.querySelector("body").addEventListener("click", (e) => {
      e.stopPropagation();
      const target = e.target as HTMLElement;
      if (target.tagName !== "SPAN" && target.tagName !== "IMG") {
        downloadOffHandler();
      }
    });
  }, []);

  if (showHeader) {
    return (
      <>
        <HeaderTag language={i18n.language} pathname={router.pathname}>
          <Container>
            <Grid
              container
              justifyContent="center"
              alignItems="center"
              style={{ flexWrap: "nowrap" }}
              spacing={isMobile ? 1 : 2}
            >
              <Grid
                item
                md={1}
                xs={1}
                className="burger-menu"
                onClick={() => {
                  setTimeout(() => {
                    document.querySelector(".menu").classList.remove("toggle");
                  });
                }}
              >
                <MenuIcon />
              </Grid>
              <Grid item xs="auto">
                <Link href="/" locale={i18n.language} prefetch={false}>
                  <a>
                    <img
                      className="logo"
                      src="/assets/images/logo.svg"
                      alt="logo"
                    />
                  </a>
                </Link>
              </Grid>
              <ClickAwayListener
                mouseEvent="onMouseDown"
                touchEvent="onTouchStart"
                onClickAway={() => {
                  if (process.browser && dimensions.width < 960) {
                    document.querySelector(".menu").classList.add("toggle");
                  }
                }}
              >
                <Grid
                  item
                  xs="auto"
                  style={{ width: "100%" }}
                  className={`menu ${
                    process.browser
                      ? dimensions.width < 960
                        ? "toggle"
                        : null
                      : "toggle"
                  }`}
                >
                  <Menu header dimensions={dimensions} />
                </Grid>
              </ClickAwayListener>
              <Grid item xs={"auto"} className="language-login-btns">
                {!authentication?.login_data?.token ? (
                  <>
                    <div className="header-button lang-selector">
                      <div
                        className="position-relative"
                        style={{ cursor: "pointer" }}
                      >
                        <div
                          className="lang-selector-text"
                          onClick={() =>
                            selectChangeHandler(
                              i18n.language === "ar" ? "en" : "ar"
                            )
                          }
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "3px",
                            transform:
                              i18n.language === "en"
                                ? "translateY(-3px)"
                                : "translateY(0px)",
                          }}
                        >
                          {i18n.language === "ar" ? "EN" : "عربي"}
                        </div>
                      </div>
                    </div>

                    <Button
                      variant="contained"
                      onClick={() => dispatch(GoToLogin(true))}
                      className="header-button login-button"
                    >
                      {t("Login") as string}
                    </Button>
                  </>
                ) : (
                  <LoggedInTooltip loginData={authentication?.login_data} />
                )}
                {!authentication?.login_data?.token ? (
                  <Button
                    variant="contained"
                    className="header-button try-button toggle-download"
                  >
                    {t("Try it now") as string}
                  </Button>
                ) : null}

                <div
                  id="download-app"
                  style={{
                    visibility: isDownloadOpen ? "visible" : "hidden",
                    opacity: isDownloadOpen ? 1 : 0,
                  }}
                >
                  <div>
                    <div className="text-center">
                      <img src={`/assets/images/qr.gif`} alt="qr" />
                      <p>{t("Download or scan the QR Code") as string}</p>
                    </div>
                    <div>
                      <a
                        href="https://play.google.com/store/apps/details?id=com.carwah.customer&hl=en_US&gl=US"
                        target="_blank"
                        rel="noreferrer"
                      >
                        <img src={`/assets/images/android.svg`} alt="android" />
                      </a>
                    </div>
                    <div>
                      <a
                        href="https://apps.apple.com/sa/app/carwah-%D9%83%D8%B1%D9%88%D8%A9/id1387161215"
                        target="_blank"
                        rel="noreferrer"
                      >
                        <img src={`/assets/images/ios.svg`} alt="ios" />
                      </a>
                    </div>
                  </div>
                </div>
              </Grid>
            </Grid>
          </Container>
        </HeaderTag>
      </>
    );
  }
  return null;
}

export default memo(Header);
