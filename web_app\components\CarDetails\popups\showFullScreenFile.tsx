/* eslint-disable @next/next/no-img-element */
import Popup from "components/shared/popup";

export default function FullScreenModal({ isOpen, setIsOpen, file }) {
  return (
    <Popup maxWidth="sm" fullWidth isOpen={isOpen} setIsOpen={setIsOpen}>
      {file?.mediaType === "image" ? (
        <img src={file?.mediaUrl} alt="img" width={"100%"} height={"100%"} />
      ) : (
        <video controls width="100%" height="100%">
          <source
            src={file?.mediaUrl}
            type={`video/${file?.mediaUrl?.split(".")?.[1]}`}
          />
        </video>
      )}
    </Popup>
  );
}
