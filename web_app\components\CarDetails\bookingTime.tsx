import { KeyboardArrowLeft, KeyboardArrowRight } from "@material-ui/icons";
import { memo, useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import styled from "styled-components";
import moment from "moment-hijri";

import "moment/locale/ar";
import { DateTimeCombined } from "utilities/helpers";
import CalendarTooltip from "components/shared/calendarTooltip";
import { useRouter } from "next/router";
import ExtensionRequestModal from "./popups/ExtensionRequestModal";
import SuccessfulExtensionPopup from "./popups/successfulExtension";

/* eslint-disable @next/next/no-img-element */

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  position: relative;
  #extension-btn {
    background: var(--color-3);
    color: var(--color-4);
    border: none;
    border-radius: var(--radius-3);
    padding: 10px 0;
    width: 100%;
    margin-top: 30px;
  }
`;

function BookingTime({
  calendarTooltipOpen,
  setCalendarTooltipOpen,
  isExtensionModalOpen,
  setIsExtensionModalOpen,
  setExtensionId,
  refetchRentalDetails,
}) {
  const { t, i18n } = useTranslation();
  const [changed, setChanged] = useState(false);
  const router = useRouter();

  const bookingId = router?.query.bookingId;
  const { pickUpDate, dropOffDate, pickup_time, return_time } = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  ) || {
    selected_days_count: 0,
    reservation_days: [],
    pickUpDate: "",
    dropOffDate: "",
    pickup_time: "",
    return_time: "",
  };
  const { rental_about_price } = useSelector(
    (state: RootStateOrAny) => state.booking
  );

  const { about_rent_price } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const { rentalDetails } =
    useSelector((state: RootStateOrAny) => state.booking) || {};
  const { installments: installmetsDetails } = rental_about_price || {};

  const installments = !bookingId ? [] : installmetsDetails;
  const hasInstallment = installments?.length;
  return (
    <Div>
      <CalendarTooltip
        isOpen={calendarTooltipOpen}
        setIsOpen={setCalendarTooltipOpen}
        setChanged={setChanged}
      />
      <div className="d-flex align-items-center justify-content-between px-4 py-3 position-relative">
        <div className="d-flex gap-10px align-items-center">
          <h6 className="bold">{t("Booking Time") as string}</h6>
        </div>
        {(bookingId &&
          rentalDetails?.status.toLowerCase() == "pending" &&
          rentalDetails?.paymentMethod?.toLowerCase() === "cash") ||
        !bookingId ? (
          <div
            onClick={(e) => {
              e.stopPropagation();
              setCalendarTooltipOpen(true);
            }}
            className="cursor-pointer"
          >
            <span className="color-3">{t("edit") as string}</span>
            {i18n.language === "en" ? (
              <KeyboardArrowRight className="color-3" />
            ) : (
              <KeyboardArrowLeft className="color-3" />
            )}
          </div>
        ) : null}
      </div>
      <div className="separator" />
      <div className="p-4">
        <div className="d-flex flex-column text-align-localized">
          <span className="color-5 bold">
            {t("Booking Duration") as string}
          </span>
          <span>
            {bookingId && !changed
              ? `${rentalDetails?.numberOfDays} ${t("days")} `
              : bookingId && changed
              ? `${moment(dropOffDate, "DD/MM/YYYY").diff(
                  moment(pickUpDate, "DD/MM/YYYY"),
                  "days"
                )} ${t("days")}`
              : `${about_rent_price?.numberOfDays} ${t("days")}`}
          </span>
        </div>
        <div className="d-flex mt-3 flex-column text-align-localized">
          <div className="color-5 bold">{t("Pickup Day & Time") as string}</div>
          <div>
            {DateTimeCombined(
              bookingId && !changed
                ? moment(rentalDetails?.pickUpDate, "YYYY-MM-DD").format(
                    "DD/MM/YYYY"
                  )
                : pickUpDate,
              bookingId && !changed ? rentalDetails?.pickUpTime : pickup_time,
              i18n.language
            )}
          </div>
        </div>
        <div className="d-flex flex-column mt-3 text-align-localized">
          <div className="color-5 bold">{t("Return Day & Time") as string}</div>
          <div>
            {DateTimeCombined(
              bookingId && !changed
                ? moment(rentalDetails?.dropOffDate, "YYYY-MM-DD").format(
                    "DD/MM/YYYY"
                  )
                : dropOffDate,
              bookingId && !changed ? rentalDetails?.dropOffTime : return_time,
              i18n.language
            )}
          </div>
        </div>
        {bookingId &&
        rentalDetails?.isExtendable &&
        !rentalDetails?.hasPendingExtensionRequests ? (
          <button
            id="extension-btn"
            onClick={() => {
              router.push(
                router.asPath.replace("#extension", "") + "#extension"
              );
              setIsExtensionModalOpen(true);
            }}
          >
            {t("Extension Request") as string}
          </button>
        ) : null}
      </div>
      {isExtensionModalOpen ? (
        <ExtensionRequestModal
          {...{
            isOpen: isExtensionModalOpen,
            setIsOpen: setIsExtensionModalOpen,
            title: t("Extension Request"),
            setExtensionId,
            refetchRentalDetails,
            withInstallment: hasInstallment,
            payNow: undefined,
            extensionDetails: undefined,
          }}
        />
      ) : null}
      <SuccessfulExtensionPopup {...{ setIsExtensionModalOpen }} />
    </Div>
  );
}

export default memo(BookingTime);
