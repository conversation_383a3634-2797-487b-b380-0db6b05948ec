/* eslint-disable @next/next/no-img-element */
import { Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Card from "./howCard";

const Div = styled.div`
  position: relative;
  padding-bottom: 50px;
  .section-fancy-title {
    position: absolute;
    top: 0;
  }
  .logo-wrapper {
    @media (max-width: 768px) {
      display: flex;
      justify-content: center;
    }
    .logo {
      padding: 20px;
      border-radius: var(--radius-2);
      background-color: var(--color-4);
    }
  }
  .cards-wrapper {
    display: grid;
    grid-row-gap: 20px;
  }
  @media (max-width: 960px) {
    .section-fancy-title {
      position: relative !important;
    }
    .logo {
      margin-bottom: 15px;
      margin-top: 5px;
    }
  }
`;

export default function HowItWorks() {
  const { t } = useTranslation();

  const cardsData = [
    {
      title: t("Contact Us"),
      description: t(
        "Fill in the registration form with the required information and then we will get back to you as soon as possible."
      ),
    },
    {
      title: t("Training"),
      description: t(
        "We provide professional training to our allies through a trained dedicated team on Carwah platform."
      ),
    },
    {
      title: t("start boosting your profit"),
      description: t(
        "Start receiving more bookings from our customers, increasing your sales, and comprehensive reporting systems with our platform."
      ),
    },
  ];

  return (
    <Div>
      <Grid container justifyContent="space-between" alignItems="center">
        <Grid item xs={12} md={2}>
          <div className="section-fancy-title">
            <h2>
              <span>{`${t("How")}`}</span> {t("It Works") as string}
            </h2>
          </div>
          <div className="logo-wrapper">
            <img
              className="logo"
              src="/assets/images/ally/logo.svg"
              alt="logo"
            />
          </div>
        </Grid>
        <Grid item xs={12} md={9} className="cards-wrapper">
          {cardsData.map((card, index) => {
            return <Card key={index} data={{ ...card, count: index + 1 }} />;
          })}
        </Grid>
      </Grid>
    </Div>
  );
}
