/* eslint-disable react/jsx-key */
import { Container } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled(Container)`
  margin-top: 70px;
  padding-bottom: 65px;
`;

export default function RentCarInCity({ cityData }) {
  const { t, i18n } = useTranslation();

  return (
    <Div>
      <h2 className="mb-3">{`${t("Rent a car in")} ${
        i18n.language === "en" ? cityData?.nameEn : cityData?.nameAr
      }`}</h2>
      <p className="font-18px mt-2">
        {i18n.language === "en" ? cityData?.textEn : cityData?.textAr}
      </p>
      <ul className="mt-2">
        {cityData[`bullets${i18n.language === "en" ? "En" : "Ar"}`]?.map(
          (item) => {
            return <li className="text-align-localized">{item}</li>;
          }
        )}
      </ul>
      <p className="font-18px">
        {`${t(
          "We advise you to rent a car from the Carwah app, which will make it easy for you to move around easily and easily and visit all the tourist places in the city"
        )} ${i18n.language === "en" ? cityData?.nameEn : cityData?.nameAr}.`}
      </p>
    </Div>
  );
}
