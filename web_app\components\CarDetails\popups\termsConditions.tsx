import Popup from "components/shared/popup";
import { useQuery } from "@apollo/client";
import { GetTermsAndConditions_Query } from "gql/queries/rentalPackages";
import RequestLoader from "components/shared/requestLoader";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

export default function TermsConditions({ isOpen, setIsOpen }) {
  const { data, loading } = useQuery(GetTermsAndConditions_Query, {
    variables: { section: "installments_terms" },
  });
  const { localizedContent } = data
    ? data.getTermsAndConditions
    : { localizedContent: undefined };
  const { title, content } = localizedContent || {};

  const { t } = useTranslation();
  const Button = styled.button`
    background: var(--color-3);
    color: var(--color-4);
    width: fit-content;
    padding: 15px 25px 20px 25px;
    border-radius: var(--radius-3);
    margin: 0 auto;
    margin-top: 20px;
  `;

  return (
    <>
      <Popup
        maxWidth="sm"
        fullWidth
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        title={title}
      >
        {content?.length ? (
          <>
            <ul>
              {content.map((item: any, index: any) => {
                return (
                  <li key={index} className="text-align-localized riyal-symbol">
                    {item?.replace("ريال", "﷼")?.replace("SAR", "﷼")}
                  </li>
                );
              })}
            </ul>
            <Button
              className="button border-0"
              onClick={() => {
                setIsOpen(false);
              }}
            >
              {t("Done.well")}
            </Button>
          </>
        ) : null}
      </Popup>
      <RequestLoader loading={loading} />
    </>
  );
}
