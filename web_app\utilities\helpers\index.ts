import moment from "moment-hijri";
import "moment/locale/ar";
import "moment/locale/en-in";
import { DateObject } from "react-multi-date-picker";

import _crypto from "crypto";
import { ENCRYPTION_SECRET_KEY } from "utilities/enums";

export const convertToArabicNumbers = (num) => {
  const arabicNumbers =
    "\u0660\u0661\u0662\u0663\u0664\u0665\u0666\u0667\u0668\u0669";
  // eslint-disable-next-line no-new-wrappers
  return new String(num).replace(/[0123456789]/g, (d) => {
    return arabicNumbers[d];
  });
};

export function convertToEnglishNumbers(string) {
  if (string) {
    return string.replace(/[\u0660-\u0669]/g, function (c) {
      return c.charCodeAt(0) - 0x0660;
    });
  }
}

export function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export function fnBrowserDetect() {
  let userAgent = navigator.userAgent;
  let browserName;

  if (userAgent.match(/chrome|chromium|crios/i)) {
    browserName = "chrome";
  } else if (userAgent.match(/firefox|fxios/i)) {
    browserName = "firefox";
  } else if (userAgent.match(/safari/i)) {
    browserName = "safari";
  } else if (userAgent.match(/opr\//i)) {
    browserName = "opera";
  } else if (userAgent.match(/edg/i)) {
    browserName = "edge";
  } else {
    browserName = "No browser detection";
  }
  return browserName;
}

export function getMobileOperatingSystem() {
  var userAgent = navigator.userAgent || (window as any).opera;

  // Windows Phone must come first because its UA also contains "Android"
  if (/windows phone/i.test(userAgent)) {
    return "Windows Phone";
  }

  if (/android/i.test(userAgent)) {
    return "Android";
  }

  if (/iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream) {
    return "iOS";
  }

  return "unknown";
}

export function calendarDaysConvert(date, isHijri?: boolean) {
  const dateObj = new DateObject(date);
  if (isHijri) {
    return moment(
      `${dateObj?.day < 10 ? "0" : ""}${dateObj?.day}/${
        dateObj?.month?.number < 10 ? "0" : ""
      }${dateObj?.month?.number}/${dateObj?.year}`,
      "iD/iM/iYYYY"
    )
      .locale("en")
      .format("D/M/YYYY");
  } else {
    return moment(
      `${dateObj?.day < 10 ? "0" : ""}${dateObj?.day}/${
        dateObj?.month?.number < 10 ? "0" : ""
      }${dateObj?.month?.number}/${dateObj?.year}`,
      "DD/MM/YYYY"
    )
      .locale("en")
      .format("DD/MM/YYYY");
  }
}

export const DateTimeCombined = (date, time, locale) => {
  if (date && time && locale) {
    return convertToEnglishNumbers(
      `${moment(`${date}`, "DD/MM/YYYY").locale(locale).format("ll")} ${moment(
        time,
        "HH:mm"
      )
        .locale(locale)
        .format("hh:mm A")}`
    );
  }
  return null;
};

export const isEligibleForInstallments = (days) => {
  return +days >= 60 && +days % 30 === 0;
};

export const signatureGenerator = ({
  queryName,
  variables,
  nonce,
  timestamp,
}: {
  queryName: string;
  variables: Record<any, any>;
  nonce: string;
  timestamp: string;
}) => {
  const queryParams = JSON.stringify(variables);

  const message = `${queryName}${queryParams}timestamp=${timestamp}nonce=${nonce}`;

  const signature = _crypto
    .createHmac("sha256", ENCRYPTION_SECRET_KEY)
    .update(message)
    .digest("hex");
  return signature;
};

export const modValidator = (input: string) => {
  const trimemeValue = +input.substring(0, input.length - 1);
  const mod = trimemeValue % 7;
  if (mod === +input[input.length - 1]) return true;
  return false;
};
