/* eslint-disable @next/next/no-img-element */
import { Box, Grid, Typography } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import { AboutContainer, Content, StyledGrid } from "./styles";

function Values() {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.language === "ar";

  return (
    <Box style={{ backgroundColor: "#F8F8F8" }}>
      <AboutContainer background="#F8F8F8">
        <StyledGrid
          container
          spacing={2}
          justifyContent="space-between"
          className="our-story py-4"
          lang={i18n.language}
        >
          <Grid item xs={12} md="auto">
            <Box className="text-align-localized mt-1">
              <img
                src="/assets/images/about/icons/values.svg"
                alt="Story Icon"
                loading="lazy"
              />
              <Typography
                variant="h3"
                className="bold"
                style={{ fontSize: "1.7rem", fontWeight: 700 }}
                dangerouslySetInnerHTML={{
                  __html: t("aboutus.values.title") as string,
                }}
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={10} className="pt-3">
            <Grid
              container
              direction="row"
              justifyContent="space-between"
              spacing={4}
            >
              <Grid
                item
                xs={12}
                md={5}
                className="my-2 text-align-localized text-numbered"
              >
                <span>1</span>
                <Typography
                  variant="body1"
                  dangerouslySetInnerHTML={{
                    __html: t("aboutus.values.content1") as string,
                  }}
                />
              </Grid>

              <Grid
                item
                xs={12}
                md={5}
                className="my-2 text-align-localized text-numbered"
              >
                <span>2</span>
                <Typography
                  variant="body1"
                  dangerouslySetInnerHTML={{
                    __html: t("aboutus.values.content2") as string,
                  }}
                />
              </Grid>
              <Grid
                item
                xs={12}
                md={5}
                className="my-2 text-align-localized text-numbered"
              >
                <span>3</span>
                <Typography
                  variant="body1"
                  dangerouslySetInnerHTML={{
                    __html: t("aboutus.values.content3") as string,
                  }}
                />
              </Grid>
              <Grid
                item
                xs={12}
                md={5}
                className="my-2 text-align-localized text-numbered"
              >
                <span>4</span>
                <Typography
                  variant="body1"
                  dangerouslySetInnerHTML={{
                    __html: t("aboutus.values.content4") as string,
                  }}
                />
              </Grid>
              <Grid
                item
                xs={12}
                md={5}
                className="my-2 text-align-localized text-numbered"
              >
                <span>5</span>
                <Typography
                  variant="body1"
                  dangerouslySetInnerHTML={{
                    __html: t("aboutus.values.content5") as string,
                  }}
                />
              </Grid>
            </Grid>
          </Grid>
        </StyledGrid>
      </AboutContainer>
    </Box>
  );
}

export default Values;
