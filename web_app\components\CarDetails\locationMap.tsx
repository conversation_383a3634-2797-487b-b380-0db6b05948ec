/* eslint-disable @next/next/no-img-element */
import React from "react";
import GoogleMapReact from "google-map-react";
import styled from "styled-components";

const Div = styled.div`
  position: relative;
  > div {
    display: none;
    background-color: white;
    width: fit-content;
    padding: 5px 10px;
    position: absolute;
    top: -25px;
    left: 0;
  }

  img {
    cursor: pointer;
  }
`;

const BranchLocationComponent = ({ showRentalData }) => {
  if (showRentalData) return <></>;
  return (
    <Div className="location-wrap">
      <img src={`/assets/images/ellipse.svg`} alt="circle" />
    </Div>
  );
};

function LocationMap({ lat, lng, showRentalData }) {
  const defaultProps = {
    center: {
      lat: lat || 21.403357,
      lng: lng || 39.8170105,
    },
    zoom: 11,
  };

  return (
    <>
      <div
        key={`${lat},${lng}`}
        style={{
          height: "300px",
          width: "100%",
          minHeight: "300px",
          maxHeight: "700px",
        }}
      >
        <GoogleMapReact
          bootstrapURLKeys={{
            key: process.env.NEXT_PUBLIC_MAP_API,
          }}
          defaultCenter={defaultProps.center}
          defaultZoom={defaultProps.zoom}
          options={{
            gestureHandling: "greedy",
          }}
          onGoogleApiLoaded={({ map, maps }) => {
            if (showRentalData) {
              new maps.Marker({
                position: { lat, lng },
                map: map,
                draggable: false,
              });
            }
          }}
        >
          <BranchLocationComponent {...{ showRentalData }} />
        </GoogleMapReact>
      </div>
    </>
  );
}

export default LocationMap;
