/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import styled from "styled-components";

import { CircularProgress } from "@material-ui/core";
import { memo, useEffect, useState } from "react";

import Popup from "components/shared/popup";
import { useTranslation } from "react-i18next";
import { carInsurance, carInsuranceType } from "store/cars/action";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { isEligibleForInstallments } from "utilities/helpers";
import { setFullInsuranceExtraService } from "store/search/action";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  padding: 20px;
  margin: 24px 0;
  > div {
  }
`;
const InsuranceTypeWrapper = styled.div`
  > div {
    border: none !important;
    text-align: center;
    margin-top: 0 !important;
    background-color: var(--color-6);
    color: ${(props) => (props.isRentToOwn ? "#000000" : "var(--color-8)")};
    width: 50%;
    border-radius: var(--radius-3);
    padding: 14px 0 !important;
    cursor: pointer;
    &.active {
      color: #fff;
      background: ${(props) =>
        props.isRentToOwn ? "var(--color-2)" : "var(--color-2)"};
      position: relative;
      /* &:after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: calc(50% - 15px);
        width: 30px;
        height: 5px;
        background: #fff;
        border-radius: var(--radius-1) var(--radius-1) 0 0;
      } */
    }
  }
`;

const Insurance = ({ isRentToOwn }: { isRentToOwn?: boolean }) => {
  //State
  const [isOpen, setIsOpen] = useState(false);

  //Hooks
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const router = useRouter();

  //Store
  const { insuranceId, car_data } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const bookingId = router?.query.bookingId;
  const rentalData = useSelector((state: RootStateOrAny) => state.booking);
  const { selected_days_count } = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );
  const { fullInsurance } =
    useSelector((state: RootStateOrAny) => state.search_data) || {};

  /**
   * @param {Number} selectionIndex zero-based index 0 is daily, 1 is monthly, 2 is delivery
   */
  const selectionIndex = useSelector(
    (state: RootStateOrAny) => state.search_data.selection_index
  );

  //Helper
  function getInsuranceValue({ value, monthlyValue }) {
    if (
      isRentToOwn ||
      isEligibleForInstallments(selected_days_count) ||
      selectionIndex == 1
    )
      return monthlyValue || value;
    return value;
  }

  //LifeCycle
  useEffect(() => {
    if (car_data?.carInsurances?.length) {
      const basic = car_data?.carInsurances?.find((i) => i.insuranceId == 1);
      const full = car_data?.carInsurances?.find((i) => i.insuranceId == 2);
      dispatch(carInsurance(fullInsurance ? full?.id : basic?.id || full?.id));
      if (basic?.id && !fullInsurance) {
        dispatch(carInsuranceType(t("Basic")));
      } else {
        dispatch(carInsuranceType(t("Full")));
      }
    }
  }, [car_data.carInsurances, fullInsurance]);

  return (
    <Div>
      <div className="d-flex gap-5px mb-4">
        <span className="bold">{t("Select Your Insurance") as string}</span>
        <img
          src="/assets/images/info.svg"
          alt="icon"
          className="cursor-pointer"
          onClick={() => setIsOpen(true)}
        />
        <Popup
          maxWidth="md"
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          title={t("Insurance")}
        >
          <p>{t("Our allies have two types of insurance") as string}</p>
          <p>
            {
              t(
                "Full is to pay an additional fee to cover the costs of the accident even if you are the main cause"
              ) as string
            }
          </p>
          <p>
            {
              t(
                "Standard in the event of personal responsibility, you pay the percentage of bearing the costs of the accident"
              ) as string
            }
          </p>
        </Popup>
      </div>
      <InsuranceTypeWrapper
        isRentToOwn={isRentToOwn}
        className={`d-flex ${
          !car_data?.carInsurances?.length ||
          car_data?.carInsurances?.length == 1
            ? "justify-content-center"
            : "justify-content-between"
        }  gap-30px`}
      >
        {car_data?.carInsurances?.length ? (
          car_data.carInsurances.map((item: any, index: any) => {
            return (
              <div
                key={item.id}
                className={`${
                  bookingId && rentalData?.rentalDetails
                    ? rentalData.rentalDetails.insuranceId == item.id
                      ? "active"
                      : " "
                    : item.id == insuranceId && "active"
                } bold`}
                onClick={() => {
                  dispatch(carInsurance(item.id));
                  if (item.insuranceId == 1) {
                    dispatch(carInsuranceType(t("Basic")));
                    dispatch(setFullInsuranceExtraService(false));
                  } else {
                    dispatch(carInsuranceType(t("Full")));
                  }
                }}
              >
                {item.insuranceName} <br />
                <div className="d-flex gap-5px justify-content-center align-items-baseline">
                  <RiyalSymbol
                    style={{ order: i18n.language === "ar" ? 1 : 0 }}
                  />
                  <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                    {getInsuranceValue(item)}
                  </span>
                </div>
                <span>{item.insuranceDetails}</span>
              </div>
            );
          })
        ) : (
          <div className={`active `}>
            <p className="bold active m-0 text-center">{t("Full") as string}</p>{" "}
            {t("Insurance is included in the monthly installments") as string}
          </div>
        )}
      </InsuranceTypeWrapper>
    </Div>
  );
};
export default memo(Insurance);
