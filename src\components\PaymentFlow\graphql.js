import { gql } from "@apollo/client";

// Query to get checkout ID for Hyperpay
export const CHECKOUT_ID_QUERY = gql`
  query GetCheckoutId(
    $paymentBrand: RentalPaymentBrand
    $rentalId: ID!
    $withWallet: Boolean
  ) {
    getCheckoutId(
      paymentBrand: $paymentBrand
      rentalId: $rentalId
      withWallet: $withWallet
    ) {
      checkoutId
      integrity
    }
  }
`;

// Query to check payment status
export const PAYMENT_STATUS_QUERY = gql`
  query GetPaymentStatus($checkoutId: String!, $rentalId: ID!) {
    getPaymentStatus(checkoutId: $checkoutId, rentalId: $rentalId) {
      status
      rental {
        isIntegratedRental
        rentalIntegrationStatus
      }
      errors
    }
  }
`;

// Mutation for Tamara checkout
export const TAMARA_CREATE_CHECKOUT_MUTATION = gql`
  mutation tamaraCreateCheckoutSession(
    $locale: String
    $rentalId: ID!
    $requestPlatform: String!
    $withWallet: Boolean
  ) {
    tamaraCreateCheckoutSession(
      locale: $locale
      rentalId: $rentalId
      requestPlatform: $requestPlatform
      withWallet: $withWallet
    ) {
      checkoutUrl
      errors
    }
  }
`;

// Mutation for wallet payment
export const PAY_BY_WALLET_MUTATION = gql`
  mutation PayByWallet($rentalId: ID!) {
    payByWallet(rentalId: $rentalId) {
      rental {
        id
      }
    }
  }
`;

// Installment-specific queries and mutations
export const INSTALLMENT_CHECKOUT_ID_QUERY = gql`
  query GetIntallmentCheckoutId(
    $paymentBrand: RentalPaymentBrand
    $rentalId: ID!
    $withWallet: Boolean
  ) {
    installmentGetCheckoutId(
      paymentBrand: $paymentBrand
      rentalId: $rentalId
      withWallet: $withWallet
    ) {
      checkoutId
      integrity
    }
  }
`;

export const INSTALLMENT_PAYMENT_STATUS_QUERY = gql`
  query GetInstallmentPaymentStatus($checkoutId: String!, $rentalId: ID!) {
    installmentGetPaymentStatus(checkoutId: $checkoutId, rentalId: $rentalId) {
      status
      errors
    }
  }
`;

export const TAMARA_CREATE_INSTALLMENT_CHECKOUT_MUTATION = gql`
  mutation tamaraCreateInstallmentCheckoutSession(
    $locale: String
    $rentalId: ID!
    $requestPlatform: String!
    $withWallet: Boolean
  ) {
    tamaraCreateInstallmentCheckoutSession(
      locale: $locale
      rentalId: $rentalId
      requestPlatform: $requestPlatform
      withWallet: $withWallet
    ) {
      checkoutUrl
      errors
    }
  }
`;

// Extension-specific queries and mutations
export const EXTENSION_CHECKOUT_ID_QUERY = gql`
  query ExtensionGetCheckoutId(
    $paymentBrand: RentalPaymentBrand
    $rentalExtensionId: ID!
    $withWallet: Boolean
  ) {
    extensionGetCheckoutId(
      paymentBrand: $paymentBrand
      rentalExtensionId: $rentalExtensionId
      withWallet: $withWallet
    ) {
      checkoutId
      integrity
    }
  }
`;

export const EXTENSION_PAYMENT_STATUS_QUERY = gql`
  query ExtensionGetPaymentStatus(
    $checkoutId: String!
    $rentalExtensionId: ID!
  ) {
    extensionGetPaymentStatus(
      checkoutId: $checkoutId
      rentalExtensionId: $rentalExtensionId
    ) {
      status
      rentalDateExtensionRequest {
        rental {
          isIntegratedRental
          rentalIntegrationStatus
        }
      }
      errors
    }
  }
`;

export const TAMARA_CREATE_EXTENSION_CHECKOUT_MUTATION = gql`
  mutation tamaraCreateExtensionCheckoutSession(
    $locale: String
    $rentalExtensionId: ID!
    $requestPlatform: String!
    $withWallet: Boolean
  ) {
    tamaraCreateExtensionCheckoutSession(
      locale: $locale
      rentalExtensionId: $rentalExtensionId
      requestPlatform: $requestPlatform
      withWallet: $withWallet
    ) {
      checkoutUrl
      errors
    }
  }
`;

export const EXTENSION_PAY_BY_WALLET_MUTATION = gql`
  mutation ExtensionPayByWallet($extensionRequestId: ID!) {
    extensionPayByWallet(extensionRequestId: $extensionRequestId) {
      rentalDateExtensionRequest {
        additionalUnlimitedKilometer
        id
        refundedAt
        refundedBy
        status
        withWallet
      }
    }
  }
`;

// Agency-specific queries (if needed)
export const AGENCY_STATUS_QUERY = gql`
  query GetAgencyStatus($agencyId: ID!) {
    agency(id: $agencyId) {
      id
      isActive
      status
    }
  }
`;

// Wallet balance query
export const WALLET_BALANCE_QUERY = gql`
  query GetWalletBalance {
    me {
      walletBalance
    }
  }
`;
