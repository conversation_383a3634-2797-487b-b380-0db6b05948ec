import React from "react";
import { Grid } from "@material-ui/core";

export default function Toggle({ variations }) {
  return (
    <Grid container justifyContent="space-between" id="toggle">
      {variations.map((item, index) => {
        return (
          <Grid
            key={index}
            item
            xs={12 / variations.length}
            container
            alignItems="center"
          >
            <Grid item xs="auto">
              {item.svgIcon}
            </Grid>
            <Grid item xs="auto" style={{ margin: "0 10px" }}>
              <span>{item.text}</span>
            </Grid>
          </Grid>
        );
      })}
    </Grid>
  );
}
