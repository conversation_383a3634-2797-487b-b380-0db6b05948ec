/* eslint-disable @next/next/no-img-element */
import Popup from "components/shared/popup";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  font-size: 22px;
  line-height: 30px;
  max-width: 80vw;
  margin: 0 auto;
  gap: 30px;
  h3 {
    @media (max-width: 900px) {
      font-size: 18px;
      margin-top: 15px !important;
    }
  }
  p {
    text-align: center !important;
    color: #808080;
    @media (max-width: 900px) {
      font-size: 16px;
    }
  }
  img {
    width: 70%;
  }
  .app {
    gap: 20px;
  }
  @media (min-width: 901px) {
    width: 65%;
  }
`;
export default function ToAppPopup({ isOpenToAppPopup, setIsOpenToAppPopup }) {
  const { t } = useTranslation();

  return (
    <Popup isOpen={isOpenToAppPopup} setIsOpen={setIsOpenToAppPopup}>
      <Div className="d-flex justify-content-center align-items-center flex-column">
        <h3 className="bold text-center">
          {t("We've got something special for you in our apps!")}
        </h3>
        <img
          src="/assets/images/capture.png"
          alt="Carwah Car Rental - كروة لأيجار السيارات"
        />
        <p className="bold">
          {t(
            "Our website will be backed up with more excited and stunning features for your best car renting experience, till that time you can now download our Apps and enjoy!"
          )}
        </p>
        <div className="d-flex flex-column app justify-content-center">
          <a
            href="https://apps.apple.com/sa/app/carwah-%D9%83%D8%B1%D9%88%D8%A9/id1387161215"
            target="_blank"
            rel="noreferrer"
            className="text-center"
          >
            <img
              src="/assets/images/ios-black.png"
              alt="Carwah Car Rental - كروة لأيجار السيارات"
            />
          </a>
          <a
            href="https://play.google.com/store/apps/details?id=com.carwah.customer&hl=en_US&gl=US"
            target="_blank"
            rel="noreferrer"
            className="text-center"
          >
            <img
              src="/assets/images/android-black.png"
              alt="Carwah Car Rental - كروة لأيجار السيارات"
            />
          </a>
        </div>
      </Div>
    </Popup>
  );
}
