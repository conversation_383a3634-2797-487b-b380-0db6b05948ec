/* eslint-disable @next/next/no-img-element */
import { ButtonsWrapper } from "components/MyAccount/styled";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  setYakeenVerificationSuccess,
  toggleYakeenCompleteMissingModal,
  toggleYakeenModal,
} from "store/user/action";
import { TUserReducer } from "store/user/reducer";
import styled from "styled-components";
import useFirebase from "useFirebase";

const Div = styled.div``;

function VerificationStatus({
  is_yakeen_verification_success,
}: {
  is_yakeen_verification_success: "success" | "error";
}) {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch();
  const { profile } =
    useSelector((state: { user: TUserReducer }) => state.user) || {};

  const { remoteConfigValues } = useFirebase();
  const { is_yakeen_feature_enabled } = remoteConfigValues || {
    is_yakeen_feature_enabled: { _value: "false" },
  };

  return (
    <Div>
      <div className="text-center">
        {profile?.customerProfile?.isYakeenVerified ? (
          <img src="/assets/icons/verified.svg" alt="verified" />
        ) : (
          <img src="/assets/icons/notVerified.svg" alt="notVerified" />
        )}
        <h4
          className="bold text-center"
          style={{
            color: !profile?.customerProfile?.isYakeenVerified
              ? "var(--color-9)"
              : "var(--color-3)",
          }}
        >
          {profile?.customerProfile?.isYakeenVerified
            ? (t("Verified") as string)
            : ""}
        </h4>
        <h5
          className="text-center mt-2"
          style={{
            color: profile?.customerProfile?.isYakeenVerified
              ? "var(--color-9)"
              : "var(--color-3)",
          }}
        >
          {profile?.customerProfile?.isYakeenVerified
            ? (t("Your Account") as string)
            : (t("not verified") as string)}
        </h5>
        {!profile?.customerProfile?.isYakeenVerified && (
          <p className="text-center mt-3 px-5">
            {
              t(
                "The data might not have been accurately recorded, or it's possible that your data hasn't been registered with Elm"
              ) as string
            }
          </p>
        )}
        {profile?.customerProfile?.yakeenTriesLeft == 1 &&
          !profile?.customerProfile?.isYakeenVerified && (
            <div
              className="mt-5 mb-1"
              style={{
                fontSize: "14px",
                backgroundColor: "rgba(248, 248, 248, 1)",
                padding: "5px 20px 7px 20px",
                color: "var(--color-9)",
                borderRadius: "20px",
                fontWeight: "bold",
                width: "fit-content",
                margin: "0 auto",
              }}
            >
              {t("You have one remaining attempt") as string}
            </div>
          )}
        <ButtonsWrapper className="d-flex gap-20px justify-content-center p-3 w-100 mt-4">
          {profile?.customerProfile?.isYakeenVerified && (
            <button
              type="submit"
              style={{ width: "50%" }}
              onClick={() => {
                if (profile?.isCustomerProfileCompleted) {
                  document.getElementById("rent-action").click();
                } else {
                  if (is_yakeen_feature_enabled?._value == "true") {
                    dispatch(toggleYakeenCompleteMissingModal(true));
                  }
                }
                dispatch(toggleYakeenModal(false));
              }}
            >
              {profile?.isCustomerProfileCompleted
                ? (t("Proceed") as string)
                : (t("Proceed to Final Step") as string)}
            </button>
          )}
          {!profile?.customerProfile?.isYakeenVerified &&
            profile?.customerProfile?.yakeenTriesLeft == 0 &&
            profile?.isCustomerProfileCompleted && (
              <button
                type="submit"
                style={{ width: "50%" }}
                onClick={() => {
                  document.getElementById("rent-action").click();
                  dispatch(toggleYakeenCompleteMissingModal(false));
                  dispatch(toggleYakeenModal(false));
                }}
              >
                {t("Proceed") as string}
              </button>
            )}
          {!profile?.customerProfile?.isYakeenVerified &&
            profile?.customerProfile?.yakeenTriesLeft == 0 &&
            !profile?.isCustomerProfileCompleted && (
              <button
                onClick={(e) => {
                  e.preventDefault();
                  router.push("my-account");
                }}
                type="submit"
                style={{ width: "50%" }}
              >
                {t("Manual data entry") as string}
              </button>
            )}
          {!profile?.customerProfile?.isYakeenVerified &&
            profile?.customerProfile?.yakeenTriesLeft != 0 && (
              <button
                type="submit"
                style={{ width: "50%" }}
                onClick={() => {
                  dispatch(setYakeenVerificationSuccess(null));
                }}
              >
                {t("Re-enter data") as string}
              </button>
            )}
        </ButtonsWrapper>
      </div>
    </Div>
  );
}

export default VerificationStatus;
