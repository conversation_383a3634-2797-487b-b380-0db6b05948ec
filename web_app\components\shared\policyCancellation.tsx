import { KeyboardArrowDown, KeyboardArrowUp } from "@material-ui/icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export default function PolicyCancellation() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { t } = useTranslation();

  return (
    <div>
      <hr />
      <p className="mb-2 bold">
        {t("Payment & Cancellation Policy") as string}:
      </p>
      {!isCollapsed && (
        <div>
          <p>
            -{" "}
            {
              t(
                "In the event that you cancel the reservation while it is"
              ) as string
            }
            <span className="color-3 mx-1 bold">{t("PENDING") as string}</span>
            {
              t(
                "the full amount will be refunded to your card automatically."
              ) as string
            }
          </p>
          <p>
            -
            {
              t(
                "In the event that you cancel the reservation while it is"
              ) as string
            }
            <span className="color-3 mx-1 bold">
              {t("CONFIRMED") as string}
            </span>
            {
              t(
                "24 hours before the time of receiving the car, the full amount will be refunded to your card. After filling out the Refund Request form."
              ) as string
            }
          </p>
          <p>
            -
            {
              t(
                "In the event that you cancel the reservation, and it is"
              ) as string
            }
            <span className="color-3 mx-1 bold">
              {t("CONFIRMED") as string}
            </span>
            {
              t(
                "within 24 hours from the time of receiving the car, the value of one rental day, inclusive of tax will be deducted ad the remaining amount will be refunded to your card. After filling out the Refund Request form."
              ) as string
            }
          </p>
        </div>
      )}
      {
        <div className="text-center ">
          {isCollapsed ? (
            <KeyboardArrowDown
              style={{ cursor: "pointer" }}
              onClick={() => setIsCollapsed(false)}
            />
          ) : (
            <KeyboardArrowUp
              style={{ cursor: "pointer" }}
              onClick={() => setIsCollapsed(true)}
            />
          )}
        </div>
      }
    </div>
  );
}
