import { gql } from "@apollo/client";
export const CreatBooking = gql`
  mutation CreateRental(
    $allyExtraServices: [ID!]
    $branchExtraServices: [ID!]
    $carId: ID!
    $couponId: ID
    $customerBookingLat: Float
    $customerBookingLng: Float
    $deliverAddress: String
    $deliverLat: Float
    $deliverLng: Float
    $deliverType: String
    $deliveryPrice: Float
    $dropOffBranchId: ID
    $dropOffCityId: ID
    $dropOffDate: String
    $dropOffTime: String
    $handoverAddress: String
    $handoverLat: Float
    $handoverLng: Float
    $handoverPrice: Float
    $insuranceId: ID
    $isUnlimited: Boolean
    $note: String
    $paymentMethod: PaymentMethod!
    $pickUpCityId: ID!
    $pickUpDate: String!
    $pickUpTime: String!
    $suggestedPrice: Float
    $userId: ID!
    $withInstallment: Boolean
    $withWallet: Boolean
    $ownCarPlanId: ID
    $loyaltyType: LoyaltyType
  ) {
    createRental(
      allyExtraServices: $allyExtraServices
      branchExtraServices: $branchExtraServices
      carId: $carId
      couponId: $couponId
      customerBookingLat: $customerBookingLat
      customerBookingLng: $customerBookingLng
      deliverAddress: $deliverAddress
      deliverLat: $deliverLat
      deliverLng: $deliverLng
      deliverType: $deliverType
      deliveryPrice: $deliveryPrice
      dropOffBranchId: $dropOffBranchId
      dropOffCityId: $dropOffCityId
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      handoverAddress: $handoverAddress
      handoverLat: $handoverLat
      handoverLng: $handoverLng
      handoverPrice: $handoverPrice
      insuranceId: $insuranceId
      isUnlimited: $isUnlimited
      note: $note
      paymentMethod: $paymentMethod
      pickUpCityId: $pickUpCityId
      pickUpDate: $pickUpDate
      pickUpTime: $pickUpTime
      suggestedPrice: $suggestedPrice
      userId: $userId
      withInstallment: $withInstallment
      withWallet: $withWallet
      ownCarPlanId: $ownCarPlanId
      loyaltyType: $loyaltyType
    ) {
      errors
      rental {
        carId
        id
      }
      pendingRentalId
      status
    }
  }
`;
