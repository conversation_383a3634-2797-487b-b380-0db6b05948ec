import { gql } from "@apollo/client";
export const Requests = gql`
  query CustomerBusinessRentals(
    $limit: Int
    $orderBy: String
    $page: Int
    $status: String
  ) {
    customerBusinessRentals(
      limit: $limit
      page: $page
      orderBy: $orderBy
      status: $status
    ) {
      collection {
        additionalNotes
        allyCompanyId
        arBusinessActivity
        arMakeName
        arModelName
        arPickUpCityName
        bookingNo
        businessActivityId
        businessActivityName
        businessRentalOffers {
          businessRental {
            arMakeName
            enMakeName
            id
          }
        }
        carImage
        carModelId
        carVersionId
        commercialRegistrationCertificate
        commercialRegistrationNo
        companyEmail
        companyName
        companyPhone
        createdAt
        customerName
        enBusinessActivity
        enMakeName
        enModelName
        enPickUpCityName
        id
        insuranceId
        insuranceName
        makeId
        makeName
        modelName
        numberOfCars
        numberOfMonths
        otherCarName
        phone
        pickUpCityId
        pickUpCityName
        pickUpDatetime
        status
        statusLocalized
        userId
        year
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;
