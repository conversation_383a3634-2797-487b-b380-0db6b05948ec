import { Grid, MenuItem, Select } from "@material-ui/core";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { ReactSVG } from "react-svg";
import styled from "styled-components";
import CitySelect from "./citySelect";
import ToggleCitiesAirports from "./toggleCitiesAirports";
import Carousel from "./dealsCarousel";
import ToAppPopup from "components/shared/toAppPopup";

const Div = styled.div`
  margin: 80px 0;
  #toggle {
    margin-top: 40px;
    svg {
      cursor: not-allowed;
    }
  }
  #city-select {
    margin: 25px 0;
  }
  .MuiInputBase-root {
    width: 100%;
    .MuiSelect-icon {
      right: 15px;
    }
    &.select {
      font-size: 14px;
      line-height: 25px;
      font-weight: 900;
      text-transform: uppercase;
      color: var(--color-3);
      border: solid 2px var(--color-7);
      border-radius: var(--radius-3);
      padding: 10px 25px;
    }
  }
  #deals-select {
    label {
      color: var(--color-1);
      opacity: 0.5;
      margin-bottom: 5px;
      width: 100%;
    }
  }
`;

export default function Deals() {
  const { t } = useTranslation();
  const [isOpenToAppPopup, setIsOpenToAppPopup] = useState();

  return (
    <>
      <ToAppPopup
        isOpenToAppPopup={isOpenToAppPopup}
        setIsOpenToAppPopup={setIsOpenToAppPopup}
      />
      <Div
        onClick={() => {
          setIsOpenToAppPopup(true);
        }}
      >
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item xs={12} md={2}>
            <div className="section-fancy-title">
              <h2>{t("Check Our")}</h2>
              <h2>{t("Great Deals")}</h2>
            </div>
            <ToggleCitiesAirports
              variations={[
                {
                  svgIcon: <ReactSVG src={`/assets/images/city.svg`} />,
                  text: t("City"),
                },
                {
                  svgIcon: <ReactSVG src={`/assets/images/airport.svg`} />,
                  text: t("Airport"),
                },
              ]}
            />
            <CitySelect t={t} />
            <div id="deals-select">
              <label>{t("Sort By")}</label>
              <Select
                className="select"
                defaultValue="All Deals"
                disableUnderline
              >
                <MenuItem value="All Deals">{t("All Deals")}</MenuItem>
              </Select>
            </div>
          </Grid>
          <Grid item xs={12} md={9}>
            <Carousel />
          </Grid>
        </Grid>
      </Div>
    </>
  );
}
