/* eslint-disable @next/next/no-img-element */
import { useMutation } from "@apollo/client";
import { CircularProgress } from "@material-ui/core";
import {
  Accept_Business_Offers,
  Reject_Business_Offers,
} from "gql/mutations/offers";
import i18n from "localization";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Swal from "sweetalert2";
import Link from "next/link";

const Div = styled.div`
  padding-top: 65px !important;
  position: relative;
  .border-localized {
    border-right: ${(props) =>
      props.language === "en" ? "solid 1px var(--color-11)" : null};
    border-left: ${(props) =>
      props.language === "ar" ? "solid 1px var(--color-15)" : null};
  }
  @media (max-width: 786px) {
    display: flex;
    flex-direction: column;
    gap: 20px;
    > div:not(:first-child),
    > div:not(:first-child) > div,
    > div:not(:first-child) > div > div {
      max-width: 100% !important;
      padding: 0 !important;
      border: none !important;
      margin: 0 !important;
    }
  }
`;

const Head = styled.div`
  position: absolute;
  top: 0;
  right: ${(props) => (props.language === "ar" ? 0 : null)};
  left: ${(props) => (props.language === "en" ? 0 : null)};
  width: 224px;
  display: grid;
  grid-template-columns: 60px 1fr;
  align-items: center;
  border-bottom: solid 1px #e1e3e7;
  img {
    width: 20px;
  }
  .class {
    color: var(--color-3);
    padding: 5px 10px;
    border-radius: var(--radius-1);
    background: var(--color-13);
  }
  .count {
    border-radius: ${(props) =>
      props.language === "ar" ? "0 15px 0 15px" : "15px 0 15px 0"};
    display: block;
    background: var(--color-2);
    color: var(--color-4);
    padding: 15px 20px;
  }
`;
export default function Card(props) {
  const { t } = useTranslation();
  const [acceptBusinessRentalOffer, { loading: loadingAccept }] = useMutation(
    Accept_Business_Offers
  );
  const [rejectBusinessRentalOffer, { loading: loadingReject }] = useMutation(
    Reject_Business_Offers
  );
  return (
    <Div
      className={`white-background m-0 p-0 row radius-2 p-4 align-items-center ${
        props?.businessRental?.status !== "pending"
          ? "justify-content-between"
          : null
      }`}
      language={i18n.language}
    >
      <Head language={i18n.language}>
        <div className="bold count d-flex">
          <span>#</span>
          <span>1</span>
        </div>
        <div className="bold d-flex gap-15px align-items-center">
          <div className="d-flex gap-10px border-localized px-3 align-items-center">
            <span className="class">{props?.acceptedOffer?.allyClass}</span>
            <span className="rate">{props?.acceptedOffer?.allyRate}</span>
          </div>
          <div className="d-flex gap-10px">
            <span>{props?.acceptedOffer?.rate}/5</span>
            <img src="/assets/images/star.svg" alt="star" />
          </div>
        </div>
      </Head>
      <div className="col-4 row border-localized align-items-center">
        <div className="col-5 font-18px text-align-localized car-details">
          {props?.makeId ? (
            <>
              <span className="bold">{`${props?.[`${i18n.language}MakeName`]} ${
                props?.[`${i18n.language}ModelName`]
              }`}</span>
              <br />
              <span>{props.year}</span>
            </>
          ) : (
            <span>{props?.otherCarName}</span>
          )}
        </div>
        <div
          className="col-7 d-flex flex-column align-items-center color-3 py-4 radius-3"
          style={{ background: "var(--color-18)" }}
        >
          <div className="bold font-27px" style={{ marginTop: "-10px" }}>
            {props?.acceptedOffer?.offerPrice}
          </div>
          <div className="font-14px">{t("SR/month")}</div>
          <div className="font-14px color-11">{t("Includes vat")}</div>
        </div>
      </div>
      <div className="col-3 border-localized">
        <div className="px-3">
          <div>
            <h6 className="color-11 mb-1">{t("kilometer/month")}:</h6>
            <p className="bold">{props?.acceptedOffer?.kilometerPerMonth}</p>
          </div>
          <div className="mt-4">
            <h6 className="color-11 mb-1">{t("Insurance type")}:</h6>
            <div className="d-flex gap-10px">
              {props?.acceptedOffer?.carInsuranceStandard ? (
                <p className="bold font-14px text-center">{`${t("Standard")} (${
                  props?.acceptedOffer?.carInsuranceStandard
                } ${t("﷼ Monthly")})`}</p>
              ) : null}
              {props?.carInsuranceStandard && props?.carInsuranceFull
                ? "/"
                : null}
              {props?.acceptedOffer?.carInsuranceFull ? (
                <p className="bold font-14px text-center">{`${t("Full")} (${
                  props?.acceptedOffer?.carInsuranceFull
                } ${t("﷼ Monthly")})`}</p>
              ) : null}
            </div>
          </div>
        </div>
      </div>
      <div className="col-4">
        <div>
          <div>
            <h6 className="color-11 mb-1">{t("AllyPolicies")}:</h6>
            <ul className="p-0 mt-3" style={{ textAlign: "justify" }}>
              <li className="bold m-0 d-inline-block">
                <span> - </span>
                {props?.acceptedOffer?.policyAndConditions[0]}.
              </li>
              <li className="bold m-0 d-inline-block">
                <span> - </span>
                {props?.acceptedOffer?.policyAndConditions[1]}.
              </li>
            </ul>
          </div>
          {/* <div style={{textAlign:"initial"}}>
          <Link href="/">
                <a style={{textDecoration:"underline",color:"#7ab3c5"}}>{t("see_more")}</a>
                </Link>
          </div> */}
        </div>
      </div>
      {<div className="col-1"></div>}
    </Div>
  );
}
