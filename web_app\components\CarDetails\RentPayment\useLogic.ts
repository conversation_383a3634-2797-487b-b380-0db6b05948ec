/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect, useMemo, useState } from "react";
import { useMutation, useQuery } from "@apollo/client";
import { CreatBooking } from "gql/mutations/createRental";
import { useSnackbar } from "notistack";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import moment from "moment";
import {
  Installment_Payment_Status,
  Payment_Status,
} from "gql/queries/customerRentals";

import { CustomerEditRent } from "gql/mutations/UpdateRent";
import {
  Tamara_Create_Checkout,
  Tamara_Create_Extension_Checkout,
  Tamara_Create_Installment_Checkout,
} from "gql/mutations/tamara";
import { setCouponData, setCouponInvalid } from "store/coupons/action";
import { Extension_Payment_Status } from "gql/queries/extensions";
import { Installment_Cash_Payment } from "gql/mutations/installments";
import {
  TpaymentMethods,
  installmentStatuses,
  loyalityTypes,
} from "utilities/enums";
import { setShowInstallmmentBreakdownModal } from "store/installments/action";
import { setPriceTemp, setRentalDetails } from "store/boooking/action";
import { GoToLogin } from "store/authentication/action";

import {
  Checkout_Id,
  Installment_Checkout_Id,
} from "gql/queries/customerRentals";
import { Extension_Checkout_Id } from "gql/queries/extensions";
import { SelectPaymentMethod } from "store/cars/action";
import { setCreatingOnlineExtensionRequset } from "store/extensions/action";
import useRentalType from "../heplers/rentalType";
import { toggleYakeenCompleteMissingModal } from "store/user/action";
import { TuserProfile } from "store/user/reducer";
import { setFullyPaidByWallet } from "store/wallet/action";
import useFirebase from "useFirebase";
import { Pay_By_Wallet } from "gql/mutations/wallet";
import { verifyFursanMembershipId } from "store/loyality/action";

function useLogic(
  extensionId?: string,
  isInInstallmentBreakDown?: boolean,
  isIntallmentDetailsModal?: boolean,
  refetchRentalDetails?: () => void,
  valueToPay?: number,
  isCouponInvalid?: any,
  setIsCouponInvalid?: any,
  refetchRentalAboutPrice?: () => void
) {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const router = useRouter();
  const {
    id: hyperPayPaymentId,
    bookingId: booking_id,
    checkout_id,
    rent_pay_id,
    paymentStatus: tamara_payment_status,
    extensionId: extension_id,
    isInstallmentParam,
    car,
    isRentToOwn: _isRentToOwn,
    PayNow,
  } = router.query || {};
  const rentalData = useSelector((state: RootStateOrAny) => state.booking);
  const { packages } =
    useSelector((state: RootStateOrAny) => state?.cars?.about_rent_price) || {};
  const { coupon_invalid } =
    useSelector((state: RootStateOrAny) => state.coupons) || {};
  const { isRentToOwn } = useRentalType();
  const { remoteConfigValues } = useFirebase();
  const { is_yakeen_feature_enabled, skip_integrity } = remoteConfigValues || {
    is_yakeen_feature_enabled: { _value: "false" },
    skip_integrity: { _value: "false" },
  };

  //State
  const [isSuccessOpen, setIsSuccessOpen] = useState(false);
  const [integrity, setIntegrity] = useState<string>();
  const [invaildPickupPopupOpen, setInvaildPickupPopupOpen] = useState(false);
  const [invaildDropoffPopupOpen, setInvaildDropoffPopupOpen] = useState(false);
  const [ErrorOpen, setErrorOpen] = useState(false);
  const [ErrorMessage, setErrorMessage] = useState("");
  const [isPaymentPopupOpen, setIsPaymentPopupOpen] = useState(false);
  const [tamaraErrors, setTamaraErrors] = useState();
  const [isCheckoutPopupOpen, setIsCheckoutPopupOpen] = useState(false);
  const [isTamaraFailPopupOpen, setIsTamaraFailPopupOpen] = useState(false);
  const [isPaymentStatusPopupOpen, setIsPaymentStatusPopupOpen] =
    useState(false);
  const [carNotAvailablePopupOpen, setCarNotAvailablePopupOpen] =
    useState(false);
  const [termsConditionModalOpen, setTermsConditionModalOpen] = useState(false);
  const [bookingId, setBookingId] = useState();
  const [paymentBrandId, setPaymentBrandId] = useState<string>();
  const [checkoutId, setCheckoutId] = useState();
  const [AllyRate, setAllyRatePopup] = useState(false);
  const [cancelRent, setCancelRent] = useState(false);

  const [paymentStatus, setPaymentStatus] = useState<any>();
  const [paymentError, setPaymentError] = useState("");
  const [PaymentErrors, setPaymentErrors] = useState();

  const [isPendingRentalPopupOpen, setIsPendingRentalPopupOpen] =
    useState(false);
  const [hasCalledPaymentStatus, setHasCalledPaymentStatus] = useState(false);

  const [pendingRentalHandler, setPendingRentalHandler] = useState<any>();

  //Store
  const state = useSelector((state: RootStateOrAny) => state.authentication);
  const { user_address, date_time, confirmed_delivery_location } = useSelector(
    (state: RootStateOrAny) => state.search_data
  );
  const { tamara_used } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const { is_creating_online_extension_requset } =
    useSelector((state: RootStateOrAny) => state.extensions) || {};

  const {
    insuranceId,
    paymentType,
    extra_services,
    car_data,
    selectedCarBranch,
    handover_in_another_branch,
    deliveryType,
    about_rent_price,
    paymentMethod: _paymentMethod,
    rent_to_own_plan,
    branch,
  } = useSelector((state: RootStateOrAny) => state.cars) || {};
  const { selected_package_data } =
    useSelector((state: RootStateOrAny) => state.installments) || {};

  const { id } =
    useSelector(
      (state: RootStateOrAny) => state.authentication.login_data?.user
    ) || {};
  const city = useSelector(
    (state: RootStateOrAny) => state.search_data.user_address
  );
  const { rentalDetails, rental_about_price } =
    useSelector((state: RootStateOrAny) => state.booking) || {};
  const { pay_with_installments, show_installment_breakdown_modal } =
    useSelector((state: RootStateOrAny) => state.installments) || {};
  const { pay_with_wallet } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};
  const paymentMethod = _paymentMethod as TpaymentMethods;
  const { id: couponId } =
    useSelector((state: RootStateOrAny) => state?.coupons?.coupon_data) || {};
  const { balance, is_paid_by_wallet } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};
  const { profile } =
    useSelector(
      (state: {
        user: { is_yakeen_modal_open: boolean; profile: TuserProfile };
      }) => state.user
    ) || {};

  const { couponErrorMessage } = !booking_id
    ? about_rent_price || {}
    : rental_about_price || {};
  const { membership_id, is_fursan_membershipId_verified, is_fursan_selected } =
    useSelector((state: RootStateOrAny) => state.loyality) || {};

  const isPaidByWallet = is_paid_by_wallet === "Success";

  const isCash = useMemo(() => {
    return paymentMethod === "CASH" ? true : false;
  }, [paymentMethod]);

  const isOnline = useMemo(() => {
    return paymentMethod !== "CASH" ? true : false;
  }, [paymentMethod]);

  const isTamara = useMemo(() => {
    return paymentMethod === "TAMARA" ? true : false;
  }, [paymentMethod]);

  const sucessInstallmentPaid = rental_about_price?.installments?.filter(
    (i: { status: string }) =>
      i.status === installmentStatuses.paid.value ||
      i.status === installmentStatuses.partially_refunded.value
  )?.length;

  const rentalHasInstallment =
    // !isRentToOwn &&
    !booking_id
      ? about_rent_price?.installmentsBreakdown
      : Boolean(rental_about_price?.installments?.length);

  const isThereNotCollected = rental_about_price?.installments?.find(
    (i: { status: string }) =>
      i.status === installmentStatuses.not_collected.value
  );

  const isThereInstallmentToPay =
    // !isRentToOwn &&
    rental_about_price?.installments &&
    rental_about_price?.installments?.filter(
      (i: { status: string }) =>
        i.status === installmentStatuses.due.value ||
        i.status === installmentStatuses.overdue.value ||
        (i.status === installmentStatuses.upcoming.value &&
          !isThereNotCollected)
    )?.length
      ? true
      : false;

  //GQL
  const [createRental, { loading }] = useMutation(CreatBooking, {
    errorPolicy: "all",
  });
  const [payCashInstallment] = useMutation(Installment_Cash_Payment, {
    errorPolicy: "all",
  });
  const [tamaraCreateCheckout] =
    !extensionId && !extension_id
      ? !rentalHasInstallment
        ? useMutation(Tamara_Create_Checkout, {
            errorPolicy: "all",
          })
        : useMutation(Tamara_Create_Installment_Checkout, {
            errorPolicy: "all",
          })
      : useMutation(Tamara_Create_Extension_Checkout, {
          errorPolicy: "all",
        });
  const { loading: loadingPaymentStatus, refetch: getPaymentStatus } =
    extension_id || extensionId
      ? useQuery(Extension_Payment_Status, {
          fetchPolicy: "no-cache",
          errorPolicy: "all",
          skip: true,
          onError: (err) => {
            setPaymentError(err.message);
          },
        })
      : rentalHasInstallment || isRentToOwn || isInstallmentParam
      ? useQuery(Installment_Payment_Status, {
          fetchPolicy: "no-cache",
          errorPolicy: "all",
          skip: true,
          onError: (err) => {
            setPaymentError(err.message);
          },
        })
      : useQuery(Payment_Status, {
          fetchPolicy: "no-cache",
          errorPolicy: "all",
          skip: true,
          onError: (err) => {
            setPaymentError(err.message);
          },
        });
  const { loading: loadingCheckoutID, refetch: getCheckoutId } = !extensionId
    ? rentalHasInstallment
      ? useQuery(Installment_Checkout_Id, {
          fetchPolicy: "cache-first",
          errorPolicy: "all",
          skip: true,
        })
      : useQuery(Checkout_Id, {
          fetchPolicy: "cache-first",
          errorPolicy: "all",
          skip: true,
        })
    : useQuery(Extension_Checkout_Id, {
        fetchPolicy: "cache-first",
        errorPolicy: "all",
        skip: true,
      });
  const [updateRent] = useMutation(CustomerEditRent, {
    errorPolicy: "all",
  });

  const [fullyPayWithWallet] = useMutation(Pay_By_Wallet, {
    errorPolicy: "all",
  });

  //Variables
  const { isIntegratedRental, rentalIntegrationStatus } =
    paymentStatus?.getPaymentStatus?.rental || {};
  const { status, errors } =
    paymentStatus?.getPaymentStatus ||
    paymentStatus?.extensionGetPaymentStatus ||
    paymentStatus?.installmentGetPaymentStatus ||
    {};

  useEffect(() => {
    if (errors?.length) {
      setPaymentError(errors[0]);
    }
  }, [errors]);

  const isWalletCoversPayment: boolean = valueToPay ? false : true;
  const showCancelReservation =
    booking_id &&
    ((rentalDetails?.status.toLowerCase() == "confirmed" &&
      // Negative value indicates that pickup time has not come  yet
      moment(Date.now())
        .utc()
        .diff(
          moment(
            `${rentalDetails?.pickUpDate} ${rentalDetails?.pickUpTime}`,
            "YYYY-MM-DD HH:mm:ss"
          ).utc(),
          "hours"
        ) < 0) ||
      rentalDetails?.status == "pending" ||
      (rentalDetails?.status == "car_received" &&
        isRentToOwn &&
        !rentalDetails?.installments?.filter((item) => item.status == "overdue")
          ?.length));

  const isPayable =
    booking_id &&
    (rentalDetails?.payable ||
      rentalDetails?.installments?.find((i) => i.payable) ||
      rental_about_price?.installments?.find((i) => i.payable));

  const showRentActionBtn =
    (isPayable &&
      rentalHasInstallment &&
      (!sucessInstallmentPaid ||
        isIntallmentDetailsModal ||
        isInInstallmentBreakDown) &&
      rental_about_price?.installments?.find((i) => i.payable)) ||
    (!rentalHasInstallment && isPayable) ||
    (!booking_id && !rent_pay_id);

  // functions
  function closePopupHandler() {
    setIsPaymentStatusPopupOpen(false);
    dispatch(setShowInstallmmentBreakdownModal(false));
    dispatch(setCreatingOnlineExtensionRequset(false));
    router.replace(
      `${router.pathname}?car=${car}&bookingId=${booking_id || bookingId}`
    );
  }

  function getPaymentStatusModalLocalizedContent(
    isIntegrated: boolean,
    integratedStatus: string
  ) {
    const isIntegratedRental = isIntegrated && !tamara_used;
    const isRegularRental = !isInstallmentParam && !rentalHasInstallment;
    const isTamaraPaid =
      tamara_payment_status === "approved" && !isInstallmentParam;
    const isPending =
      status?.includes("pending") || integratedStatus?.includes("pending");
    const isPaid =
      status === "paid" ||
      status === "partially_refunded" ||
      isTamaraPaid ||
      isPaidByWallet ||
      integratedStatus === "paid";
    const isNotExtensionRequest =
      !extensionId && !extension_id && !is_creating_online_extension_requset;

    const statusLocalizationKey = determineStatusLocalizationKey(
      isPaid,
      isPending
    );
    const rentalTypeLocalizationKey = determineRentalTypeLocalizationKey(
      isRentToOwn,
      isRegularRental
    );
    const localizedIntegratedStatus = t(
      `rental.integrated.status.${statusLocalizationKey}`
    );
    const localizedIntegratedBody = t(
      `rental.integrated.${statusLocalizationKey}`
    );
    const localizedSuccessBody = t(`${rentalTypeLocalizationKey}.Success`);
    const localizedIntegratedButton = t(
      `rental.integrated.btn.${statusLocalizationKey}`
    );
    const localizedNonIntegratedButton = t(
      `${rentalTypeLocalizationKey}.btn.${statusLocalizationKey}`
    );

    function determineStatusLocalizationKey(isPaid, isPending) {
      if (isPaid || isPaidByWallet) return "Success";
      if (isPending) return "Pending";
      return isRentToOwn ? "RentError" : "Error";
    }

    function determineRentalTypeLocalizationKey(isRentToOwn, isRegularRental) {
      return isRentToOwn
        ? "renttoown"
        : isRegularRental
        ? "rental"
        : "installment";
    }

    function getLocalizedFailureBody(type, ErrorMessage) {
      const _paymentError = ErrorMessage || paymentError;
      const messages = {
        en: {
          regular: `Dear customer.. your payment process failed ${
            _paymentError ? `due to ${_paymentError}` : ""
          }${
            !ErrorMessage
              ? '. Meanwhile you have created unpaid pending booking to confirm your booking please try to pay by pressing "Pay Now"'
              : ""
          }`,
          installment: `Dear customer.. your payment process failed ${
            _paymentError ? `due to ${_paymentError}` : ""
          }, please try to pay by pressing Pay Now`,
          rentToOwn: `Dear customer.. your payment was declined ${
            _paymentError ? `due to ${_paymentError}` : ""
          }, and your car booking is pending. Please pay within 2 hours to avoid booking cancellation.`,
          extension: `Dear customer.. your payment process was unsuccessful ${
            _paymentError ? `due to ${_paymentError}` : ""
          }, try to pay again by pressing clicking on Pay Now`,
        },
        ar: {
          regular: `عميلنا العزيز .. عملية الدفع غير ناجحة ${
            _paymentError ? `${_paymentError} بسبب` : ""
          } ، علماً بأنك انشأت حجز حالته انتظار ولتأكيد حجزك يمكنك محاولة الدفع مرة أخرى بالضغط على ادفع الان`,
          installment: `عميلنا العزيز .. نأسف بإبلاغكم أن عملية الدفع غير ناجحة ${
            _paymentError ? `${_paymentError} بسبب` : ""
          } ، نأمل إعادة محاولة الدفع مرة أخرى بالضغط على ادفع الان`,
          rentToOwn: `عميلنا العزيز .. عملية الدفع غير ناجحة ${
            _paymentError ? `${_paymentError} بسبب` : ""
          }، علماً بأن  حجز السيارة في حاله إنتظار يرجى الدفع في غضون ساعتين تفاديا لإلغاء الحجز`,
          extension: `عميلنا العزيز .. عملية الدفع غير ناجحة ${
            _paymentError ? `${_paymentError} بسبب` : ""
          }، يمكنك محاولة الدفع مرة أخرى من خلال النقر على ادفع الان`,
        },
      };
      return messages[i18n.language][type];
    }

    function getBodyContent(errorMessage) {
      if (isIntegratedRental) {
        return localizedIntegratedBody;
      }
      if (isNotExtensionRequest) {
        if (isPaid) return localizedSuccessBody;
        if (isPending)
          return t(`${rentalTypeLocalizationKey}.${statusLocalizationKey}`);
        if (isRentToOwn)
          return getLocalizedFailureBody("rentToOwn", ErrorMessage);
        return isRegularRental
          ? getLocalizedFailureBody("regular", ErrorMessage)
          : getLocalizedFailureBody("installment", ErrorMessage);
      }
      return status !== "paid"
        ? isPending
          ? t("extension.Pending")
          : getLocalizedFailureBody("extension", ErrorMessage)
        : localizedSuccessBody;
    }

    const seeRentalDetailsButton = () => {
      if (
        (paymentError || errors?.length) &&
        !isPending &&
        isNotExtensionRequest &&
        (isRegularRental || (!isRegularRental && !sucessInstallmentPaid))
      ) {
        return {
          action: () => {
            router.push(
              `${router.pathname}?car=${car}&bookingId=${
                booking_id || bookingId
              }`
            );
            setIsPaymentStatusPopupOpen(false);
          },
          text: t("See Rental Details"),
        };
      }
      return null;
    };
    function togglePaymentHandler() {
      setIsPaymentStatusPopupOpen(false);
      dispatch(SelectPaymentMethod());
      setIsPaymentPopupOpen(true);
      dispatch(setCreatingOnlineExtensionRequset(false));

      router.push(
        `${router.pathname}?car=${car}&bookingId=${booking_id || bookingId}`
      );
    }
    const action = () => {
      if (isPaid || isPending) {
        return closePopupHandler();
      } else {
        if (isInstallmentParam) {
          return () => {
            dispatch(SelectPaymentMethod());
            router.push(
              `${router.pathname}?car=${car}&bookingId=${
                booking_id || bookingId
              }`
            );
            setIsPaymentStatusPopupOpen(false);
            dispatch(setShowInstallmmentBreakdownModal(false));
          };
        } else {
          return togglePaymentHandler();
        }
      }
    };

    return {
      title: () =>
        isIntegratedRental
          ? localizedIntegratedStatus
          : t(
              `${
                !isInstallmentParam ? "rental" : "installment"
              }.status.${statusLocalizationKey}`
            ),
      body: getBodyContent,
      button: () =>
        isIntegratedRental
          ? localizedIntegratedButton
          : localizedNonIntegratedButton,
      action: action,
      seeRentalDetailsButton,
    };
  }

  function getPaymentStatusHandler(
    checkoutIdParam: any,
    cb?: any,
    skip?: boolean
  ) {
    if (skip) {
      setIsPaymentStatusPopupOpen(true);
      if (cb) cb();
      return;
    }
    if (
      !loadingPaymentStatus &&
      (checkoutId ||
        checkoutIdParam ||
        ((extensionId || extension_id) && (checkoutId || checkoutIdParam)))
    ) {
      getPaymentStatus({
        checkoutId: checkoutIdParam || checkoutId,
        rentalId:
          !extensionId && !extension_id ? booking_id || bookingId : undefined,
        rentalExtensionId:
          extensionId || extension_id ? extensionId || extension_id : undefined,
      })
        .then((res) => {
          if (res.data) {
            setPaymentStatus(res.data);
            if (res?.data?.errors) {
              setPaymentErrors(res?.data?.installmentGetPaymentStatus?.errors);
            }
          }
        })
        .finally(() => {
          setIsPaymentStatusPopupOpen(true);
          if (cb) cb();
        });
    }
  }

  async function getCheckOutIdFunction(rental_id?: string, cb?: () => void) {
    if (!loadingCheckoutID) {
      return getCheckoutId({
        paymentBrand: paymentMethod,
        rentalId: !extensionId
          ? booking_id || bookingId || rental_id
          : undefined,
        rentalExtensionId: extensionId ? extensionId : undefined,
        withWallet: pay_with_wallet,
      })
        .then((res) => {
          if (res?.errors) {
            const error = res.errors.find(
              (i) =>
                i.message?.includes("pending") ||
                i.message?.includes("قيد الانتظار") // handles localized BE error
            );
            if (error) {
              setErrorMessage(error.message);
              setIsPaymentStatusPopupOpen(true);
            }
          } else {
            const checkoutData =
              res?.data?.getCheckoutId ||
              res?.data?.installmentGetCheckoutId ||
              res?.data?.extensionGetCheckoutId;

            setCheckoutId(checkoutData?.checkoutId);
            // Store the integrity value to pass to the Checkout component
            if (checkoutData?.integrity && !skip_integrity) {
              setIntegrity(checkoutData?.integrity);
            }
          }
        })
        .finally(() => {
          cb && cb();
        });
    }
  }

  function onlinePaymentHandler() {
    if (!booking_id && !bookingId) {
      return submitRentHandler(
        isTamara ? payWithTamaraHandler : getCheckOutIdFunction
      );
    }

    if (checkRentalIsCoveredByWalletHandler(booking_id)) return;

    if (bookingId || booking_id) {
      if (!isTamara) {
        return (() => {
          getCheckOutIdFunction("", () => {
            if (!is_creating_online_extension_requset && !booking_id) {
              setIsSuccessOpen(true);
            }
          });
        })();
      }
      return payWithTamaraHandler();
    }
  }

  function profileNotCompletedHandler() {
    if (
      profile &&
      !profile?.customerProfile?.isYakeenVerified &&
      !profile.customerProfile?.yakeenTriesLeft &&
      !profile?.isCustomerProfileCompleted
    ) {
      router.push("my-account");
      return true;
    }
    return false;
  }

  function yakeenCompleteMissingDataHandler() {
    if (
      profile &&
      profile?.customerProfile?.isYakeenVerified &&
      !profile?.isCustomerProfileCompleted &&
      is_yakeen_feature_enabled?._value == "true"
    ) {
      dispatch(toggleYakeenCompleteMissingModal(true));
      return true;
    }
    return false;
  }

  function validateUserAuthenticationHandler() {
    if (!state?.login_data?.token) {
      dispatch(GoToLogin(true));
      return true;
    }
    return false;
  }

  function validateCouponHandler() {
    if (coupon_invalid) return false;
    if (couponErrorMessage) {
      dispatch(setCouponInvalid(true));
      setIsCouponInvalid(true);
      return true;
    }
    return false;
  }

  function openInstallmentBreakdownModal() {
    if (
      !show_installment_breakdown_modal &&
      !isIntallmentDetailsModal &&
      ((booking_id && isThereInstallmentToPay && !isRentToOwn) ||
        (!booking_id && rentalHasInstallment && !isRentToOwn))
    ) {
      dispatch(setShowInstallmmentBreakdownModal(true));
      return true;
    }
    return false;
  }

  function cashRentalsHandler() {
    if (isCash) {
      if (booking_id) {
        if (isInInstallmentBreakDown || isIntallmentDetailsModal) {
          payCashInstallmentHandler();
          return true;
        }
        UpdateRent();
        return true;
      }
      submitRentHandler();
      return true;
    }
    return false;
  }

  function onlineRentalsHandler() {
    if (isOnline) {
      onlinePaymentHandler();
      return true;
    }
    return false;
  }

  const isInstallmentPayment =
    (isThereInstallmentToPay && booking_id) ||
    (rentalHasInstallment && !booking_id);
  const isNotInstallmentPayment =
    (!isThereInstallmentToPay && booking_id) ||
    (!rentalHasInstallment && !booking_id);

  function clickHandler() {
    if (profileNotCompletedHandler()) return;
    if (yakeenCompleteMissingDataHandler()) return;
    if (validateUserAuthenticationHandler()) return;
    if (
      ((isInstallmentPayment && show_installment_breakdown_modal) ||
        isNotInstallmentPayment) &&
      validateCouponHandler()
    ) {
      return;
    }
    if (openInstallmentBreakdownModal()) return;
    if (cashRentalsHandler()) return;
    if (onlineRentalsHandler()) return;
  }

  const showChangePaymentMethod = useMemo(() => {
    if (
      !isRentToOwn &&
      rentalHasInstallment &&
      !isInInstallmentBreakDown &&
      !isIntallmentDetailsModal
    ) {
      return false;
    }
    return true;
  }, [
    booking_id,
    rentalDetails,
    isCash,
    rentalHasInstallment,
    isInInstallmentBreakDown,
    isIntallmentDetailsModal,
  ]);
  function changePymentMethodHandler() {
    dispatch(setPriceTemp(valueToPay));
    setIsPaymentPopupOpen(true);
  }

  useEffect(() => {
    dispatch(setPriceTemp(valueToPay));
  }, [valueToPay]);

  function buttonText() {
    if (
      !isRentToOwn &&
      ((isThereInstallmentToPay && booking_id) ||
        (rentalHasInstallment && !booking_id))
    ) {
      // Installment payment
      if (!isInInstallmentBreakDown && !isIntallmentDetailsModal) {
        return t("Proceed.rent");
      } else if (isInInstallmentBreakDown || isIntallmentDetailsModal) {
        return t(
          isThereInstallmentToPay
            ? "rental.btn.pay" // Using a consistent key that exists in both language files
            : isOnline && isWalletCoversPayment
            ? "Rent"
            : isCash
            ? "Pay upon receipt"
            : "rental.btn.pay" // Using a consistent key that exists in both language files
        );
      }
    } else {
      // Normal payment
      if (booking_id) {
        if (isCash && rentalDetails?.status == "pending")
          return t("update Rent");
        if (isOnline && !rentalDetails?.isPaid) {
          return t(
            isWalletCoversPayment && pay_with_wallet ? "Rent" : "rental.btn.pay" // Using a consistent key that exists in both language files
          );
        } else {
          return t("rental.btn.pay"); // Using a consistent key that exists in both language files
        }
      } else if (!booking_id) {
        return t(
          isOnline && isWalletCoversPayment
            ? "Rent"
            : isCash
            ? "Pay upon receipt"
            : "rental.btn.pay" // Using a consistent key that exists in both language files
        );
      }
    }
  }

  function buttonIconUrl() {
    if (!valueToPay && pay_with_wallet) {
      return "";
    }
    if (
      (booking_id && isCash && !rentalData?.cash_option) ||
      (!isRentToOwn &&
        rentalHasInstallment &&
        !isInInstallmentBreakDown &&
        !isIntallmentDetailsModal)
    ) {
      return "";
    }
    return balance && isWalletCoversPayment && pay_with_wallet
      ? `/assets/icons/wallet.svg`
      : paymentMethod
      ? `/assets/icons/${
          paymentMethod === "MADA"
            ? "mada"
            : paymentMethod === "CREDIT_CARD"
            ? "visamaster"
            : isTamara
            ? "tamara"
            : "cash"
        }.svg`
      : "";
  }
  function isDisabled() {
    if (!rent_to_own_plan?.id && isRentToOwn && !booking_id) return true;
    if (rentalHasInstallment && !isThereInstallmentToPay && booking_id) {
      return true;
    }
  }

  function createRentErrorHandler(res: any) {
    res.errors.map((err: any) => {
      if (
        [
          "car_invalid_pickup_datetime",
          "car_invalid_dropoff_datetime",
          "COUPON_NOT_APPLIED",
          "blocked_customer",
          "partially_blocked_customer",
          "id_expired",
          "license_expired",
          "id_license_expired",
          "passport_expired",
          "passport_license_expired",
        ].includes(err.extensions.code)
      ) {
        enqueueSnackbar(err.message, {
          variant: "error",
          preventDuplicate: true,
        });
      } else if (
        err.extensions.code == "basic_profile_incomplete" ||
        err.extensions.code == "license_profile_incomplete"
      ) {
        enqueueSnackbar(err.message, {
          variant: "error",
          preventDuplicate: true,
        });
        setTimeout(() => {
          router.push("my-account");
        }, 1000);
      } else if (err.extensions.code == "AGE_NOT_ALLOWED") {
        setCarNotAvailablePopupOpen(true);
      } else {
        setErrorMessage(err.message);
        setErrorOpen(true);
      }
      setIsPaymentPopupOpen(false);
      if (err.extensions.code == "car_invalid_pickup_datetime") {
        setInvaildPickupPopupOpen(true);
        setTimeout(() => {
          setInvaildPickupPopupOpen(false);
        }, 10000);
      }
      if (err.extensions.code == "car_invalid_dropoff_datetime") {
        setInvaildDropoffPopupOpen(true);
        setTimeout(() => {
          setInvaildDropoffPopupOpen(false);
        }, 10000);
      }
    });
  }

  function storeDataHandler(rentalId, carId) {
    dispatch(setCouponData());
    dispatch(
      setRentalDetails({
        rentalId,
        carId,
      })
    );
  }

  function fullyPayWithWalletHandler(_rentalId: any) {
    fullyPayWithWallet({ variables: { rentalId: _rentalId } }).then(() => {
      dispatch(setFullyPaidByWallet("Success"));
      // setIsSuccessOpen(true);
    });
  }

  function createRentSuccessHandler(res: any, actionFunction: any) {
    dispatch(verifyFursanMembershipId(false));
    const rentalId = res?.data?.createRental?.rental?.id;
    const carId = res.data.createRental.rental?.carId;

    setBookingId(rentalId);
    storeDataHandler(rentalId, carId);
    if (isCash) setIsSuccessOpen(true);

    if (checkRentalIsCoveredByWalletHandler(rentalId)) return;

    if (isOnline) {
      if (isTamara) {
        return tamaraCreateCheckout({
          variables: {
            rentalId: !extensionId && !extension_id ? rentalId : undefined,
            rentalExtensionId:
              extensionId || extension_id
                ? extensionId || extension_id
                : undefined,
            requestPlatform: "website",
            locale: i18n.language,
            withWallet: pay_with_wallet,
          },
        }).then((res) => {
          const { checkoutUrl: checkout_url } =
            !extensionId && !extension_id
              ? !rentalHasInstallment
                ? res?.data?.tamaraCreateCheckoutSession || {}
                : res?.data?.tamaraCreateInstallmentCheckoutSession
              : res?.data?.tamaraCreateExtensionCheckoutSession;
          if (checkout_url) {
            router.replace(checkout_url);
            setIsPaymentPopupOpen(false);
          } else {
            setIsPaymentPopupOpen(false);
            setIsTamaraFailPopupOpen(true);
            setTamaraErrors(
              (res?.data?.tamaraCreateCheckoutSession?.errors as any) ||
                (res.errors?.map((error) => error.message) as any)
            );
          }
        });
      } else {
        if (actionFunction) {
          return actionFunction(rentalId);
        }
      }
    }
  }

  async function payWithTamaraHandler(rentalId?: string) {
    return tamaraCreateCheckout({
      variables: {
        rentalId:
          !extensionId && !extension_id ? rentalId || booking_id : undefined,
        rentalExtensionId:
          extensionId || extension_id ? extensionId || extension_id : undefined,
        requestPlatform: "website",
        locale: i18n.language,
        withWallet: pay_with_wallet,
      },
    }).then((res) => {
      if (res?.errors?.length) {
        setTamaraErrors(res.errors?.map((error) => error.message) as any);
        setIsTamaraFailPopupOpen(true);
      } else {
        const { checkoutUrl: checkout_url } =
          !extensionId && !extension_id
            ? !rentalHasInstallment
              ? res?.data?.tamaraCreateCheckoutSession || {}
              : res?.data?.tamaraCreateInstallmentCheckoutSession
            : res?.data?.tamaraCreateExtensionCheckoutSession;
        if (checkout_url) {
          router.replace(checkout_url);
          setIsPaymentPopupOpen(false);
        } else {
          setIsPaymentPopupOpen(false);
          setIsTamaraFailPopupOpen(true);
          setTamaraErrors(
            (res?.data?.tamaraCreateCheckoutSession?.errors as any) ||
              (res.errors?.map((error) => error.message) as any)
          );
        }
      }
    });
  }

  async function payCashInstallmentHandler() {
    return payCashInstallment({ variables: { rentalId: booking_id } }).then(
      (res) => {
        if (!res.errors?.length) {
          enqueueSnackbar(`${t("Payment Done Successfully")}`);
          setIsSuccessOpen(true);
        } else {
          res.errors.map((err) => {
            enqueueSnackbar(err.message, {
              variant: "error",
              preventDuplicate: true,
            });
          });
        }
      }
    );
  }

  //LifeCycles
  useEffect(() => {
    if (checkoutId) {
      setIsCheckoutPopupOpen(true);
    }
  }, [checkoutId]);

  useEffect(() => {
    if (
      is_creating_online_extension_requset &&
      booking_id &&
      (extensionId || extension_id) &&
      !hyperPayPaymentId
    ) {
      onlinePaymentHandler();
    }
  }, [is_creating_online_extension_requset, extensionId, extension_id]);

  useEffect(() => {
    if (
      hyperPayPaymentId &&
      !isInInstallmentBreakDown &&
      !paymentStatus &&
      checkout_id &&
      !hasCalledPaymentStatus
    ) {
      setHasCalledPaymentStatus(true);
      getPaymentStatusHandler(checkout_id || checkoutId);
    }
  }, [hyperPayPaymentId, checkout_id, !hasCalledPaymentStatus]);

  function checkRentalIsCoveredByWalletHandler(rentalId) {
    if (isWalletCoversPayment && pay_with_wallet) {
      if (booking_id) fullyPayWithWalletHandler(rentalId); //skips pay by wallet muatation when creating a new rent
      dispatch(setFullyPaidByWallet("Success"));
      // setIsSuccessOpen(true);
      return true;
    }
    return false;
  }

  const submitRentHandler = (actionFunction?: () => void) => {
    // Validate delivery location
    if (deliveryType !== "no_delivery" && !confirmed_delivery_location) {
      enqueueSnackbar(t("Please choose a delivery location") as string, {
        variant: "error",
      });
      return;
    }

    // Early exit if bookingId or booking_id exists
    if (bookingId || booking_id) {
      return;
    }

    setIsCheckoutPopupOpen(false);

    // Helper function to determine delivery latitude
    const getDeliverLat = () => {
      if (deliveryType === "no_delivery") return null;
      return (
        confirmed_delivery_location?.airport?.centerLat ||
        confirmed_delivery_location?.lat ||
        user_address?.pick_up?.lat
      );
    };

    // Helper function to determine delivery longitude
    const getDeliverLng = () => {
      if (deliveryType === "no_delivery") return null;
      return (
        confirmed_delivery_location?.airport?.centerLng ||
        confirmed_delivery_location?.lng ||
        user_address?.pick_up?.lng
      );
    };

    // Prepare variables for the rental creation mutation
    const rentalVariables = {
      carId: car_data?.id,
      handoverBranch:
        handover_in_another_branch?.checked && !isRentToOwn
          ? handover_in_another_branch?.branchId
          : undefined,
      deliverLat: getDeliverLat(),
      deliverLng: getDeliverLng(),
      deliverAddress:
        deliveryType !== "no_delivery"
          ? confirmed_delivery_location?.address
          : "",
      deliverType:
        deliveryType !== "no_delivery" ? deliveryType : "no_delivery",
      pickUpDate: date_time?.pickUpDate,
      pickUpTime: date_time?.pickUpDate
        ? `${date_time.pickup_time}:00`
        : undefined,
      dropOffDate:
        date_time?.dropOffDate && !isRentToOwn
          ? date_time.dropOffDate
          : undefined,
      dropOffTime:
        date_time?.dropOffDate && !isRentToOwn
          ? `${date_time.return_time}:00`
          : undefined,
      isUnlimited: extra_services?.some((e) => e?.id === "isUnlimited"),
      allyExtraServices: extra_services
        ?.filter((e) => e.type === "allyExtraService")
        .map((i) => i.id),
      branchExtraServices: extra_services
        ?.filter((e) => e.type === "branchExtraService")
        .map((i) => i.id),
      insuranceId,
      paymentMethod: determinePaymentMethod(),
      userId: id,
      dropOffBranchId: !isRentToOwn ? determineDropOffBranchId() : undefined,
      dropOffCityId: !isRentToOwn
        ? handover_in_another_branch?.checked
          ? handover_in_another_branch?.option?.cityId
          : city?.return.id
        : undefined,
      pickUpCityId: city.pick_up.id,
      withInstallment: !isRentToOwn && pay_with_installments,
      withWallet: pay_with_wallet,
      ownCarPlanId: rent_to_own_plan?.id,
      couponId: !isCouponInvalid && !couponErrorMessage ? couponId : undefined,
      loyaltyType: Boolean(
        membership_id && is_fursan_membershipId_verified && is_fursan_selected
      )
        ? loyalityTypes.ALFURSAN
        : undefined,
    };

    // Execute the rental creation mutation
    createRental({ variables: rentalVariables }).then((res) => {
      const { pendingRentalId } = res?.data?.createRental || {};
      if (pendingRentalId) {
        setPendingRentalHandler(() => () => {
          router.push(`${router.pathname}?&bookingId=${pendingRentalId}`);
        });
        setIsPendingRentalPopupOpen(true);
        return;
      }
      if (res.errors?.length) {
        createRentErrorHandler(res);
      } else {
        createRentSuccessHandler(res, actionFunction);
      }
    });
  };

  // Example helper function for determining payment method
  const determinePaymentMethod = () => {
    if (paymentMethod) {
      return isCash ? "CASH" : "ONLINE";
    }
    if (
      about_rent_price?.availablePaymentMethods?.includes("and") ||
      about_rent_price?.availablePaymentMethods.toLowerCase() === "all"
    ) {
      return "ONLINE";
    }
    return about_rent_price?.availablePaymentMethods?.toUpperCase();
  };

  // Example helper function for determining drop-off branch ID
  const determineDropOffBranchId = () => {
    return handover_in_another_branch?.checked
      ? handover_in_another_branch.branchId
      : selectedCarBranch?.id || branch?.branchId || car_data?.branch?.id;
  };

  const UpdateRent = () => {
    updateRent({
      variables: {
        pickUpDate: date_time?.pickUpDate
          ? date_time.pickUpDate
          : moment(rentalData.rentalDetails.pickUpDate).format("DD/MM/yyyy"),
        pickUpTime: date_time?.pickUpDate
          ? `${date_time.pickup_time}:00`
          : rentalData.rentalDetails.pickUpTime,
        dropOffDate: date_time?.dropOffDate
          ? date_time.dropOffDate
          : moment(rentalData.rentalDetails.dropOffDate).format("DD/MM/yyyy"),
        dropOffTime: date_time?.dropOffDate
          ? `${date_time.return_time}:00`
          : rentalData.rentalDetails.dropOffTime,
        rentalId: booking_id,
        isUnlimited: extra_services?.find((e: any) => e?.id === "isUnlimited")
          ? true
          : undefined,
        allyExtraServices: extra_services
          ?.filter((e: any) => e.type === "allyExtraService")
          .map((i: any) => i.id),
        branchExtraServices: extra_services
          ?.filter((e: any) => e.type === "branchExtraService")
          .map((i: any) => i.id),
        couponId,
      },
    }).then((res) => {
      if (res?.errors?.length) {
        res?.errors?.map((err) =>
          enqueueSnackbar(err.message, { variant: "error" })
        );
      } else {
        refetchRentalDetails();
        enqueueSnackbar("Rent Updated Successfully", { variant: "success" });
      }
    });
  };

  return {
    UpdateRent,
    PaymentErrors,
    submitRentHandler,
    getPaymentStatusModalLocalizedContent,
    showRentActionBtn,
    rentalIntegrationStatus,
    isIntegratedRental,
    showCancelReservation,
    loading,
    isSuccessOpen,
    setIsSuccessOpen,
    setIsCouponInvalid,
    isCouponInvalid,
    invaildPickupPopupOpen,
    setTermsConditionModalOpen,
    termsConditionModalOpen,
    isPaymentStatusPopupOpen,
    isTamaraFailPopupOpen,
    isCheckoutPopupOpen,
    isPaymentPopupOpen,
    setIsPaymentPopupOpen,
    ErrorMessage,
    ErrorOpen,
    setErrorOpen,
    invaildDropoffPopupOpen,
    AllyRate,
    setAllyRatePopup,
    cancelRent,
    setCancelRent,
    checkoutId,
    integrity,
    bookingId,
    paymentType,
    hyperPayPaymentId,
    booking_id,
    t,
    rentalData,
    setInvaildPickupPopupOpen,
    setInvaildDropoffPopupOpen,
    setIsPaymentStatusPopupOpen,
    extension_id,
    setIsCheckoutPopupOpen,
    paymentBrandId,
    setPaymentBrandId,
    setCheckoutId,
    getPaymentStatusHandler,
    setIsTamaraFailPopupOpen,
    tamaraErrors,
    dispatch,
    rentalHasInstallment,
    sucessInstallmentPaid,
    isInstallmentParam,
    isDisabled,
    buttonText,
    clickHandler,
    payWithTamaraHandler,
    selected_package_data,
    carNotAvailablePopupOpen,
    setCarNotAvailablePopupOpen,
    changePymentMethodHandler,
    i18n,
    buttonIconUrl,
    paymentMethod,
    rentalDetails,
    isCash,
    isOnline,
    showChangePaymentMethod,
    isThereInstallmentToPay,
    pendingRentalHandler,
    isPendingRentalPopupOpen,
    setIsPendingRentalPopupOpen,
  };
}

export default useLogic;
