import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";

import Swiper<PERSON><PERSON>, {
  Navigation,
  Pagination,
  Scrollbar,
  A11y,
  Autoplay,
} from "swiper";

import "swiper/swiper-bundle.css";
import { useTranslation } from "react-i18next";

SwiperCore.use([Navigation, Pagination, Scrollbar, A11y, Autoplay]);

export default function SwiperCarousel(props) {
  const { i18n } = useTranslation();
  return (
    <div className="swiper-wrap" style={{ position: "relative" }}>
      <Swiper
        loop={false}
        {...props}
        style={{ direction: i18n.language === "ar" ? "rtl" : "ltr" }}
      >
        {props?.slides?.map((slide) => {
          return <SwiperSlide key={Math.random()}>{slide}</SwiperSlide>;
        })}
      </Swiper>
      {/* Arrows styles are applied to the wrapper that holds the swiper */}
    </div>
  );
}
