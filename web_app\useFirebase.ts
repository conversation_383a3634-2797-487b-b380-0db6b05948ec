// firebaseConfig.js
import { initializeApp } from "firebase/app";
import {
  getRemoteConfig,
  fetchAndActivate,
  getAll,
} from "firebase/remote-config";
import { useEffect, useMemo, useState } from "react";

const firebaseConfig =
  process.env.NEXT_PUBLIC_FIREBASE_CONFIG &&
  JSON.parse(process.env.NEXT_PUBLIC_FIREBASE_CONFIG);

function useFirebase() {
  const app = firebaseConfig && initializeApp(firebaseConfig);
  const remoteConfig = useMemo(() => {
    if (app) {
      return getRemoteConfig(app);
    }
    return {};
  }, [app]) as any;
  if (app) remoteConfig.settings.minimumFetchIntervalMillis = 30000;

  const [remoteConfigValues, setRemoteConfigValues] = useState<any>();

  useEffect(() => {
    if (firebaseConfig) {
      fetchAndActivate(remoteConfig).then((activated) => {
        setRemoteConfigValues(getAll(remoteConfig));
      });
    }
  }, [remoteConfig]);

  return { remoteConfig, remoteConfigValues };
}

export default useFirebase;
