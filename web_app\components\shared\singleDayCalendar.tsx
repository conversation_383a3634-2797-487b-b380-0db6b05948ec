/* eslint-disable react-hooks/exhaustive-deps */
import { Calendar, DateObject } from "react-multi-date-picker";
import { memo, useEffect } from "react";
import arabic_en from "react-date-object/locales/arabic_en";
import arabic_ar from "react-date-object/locales/arabic_ar";
import gregorian_en from "react-date-object/locales/gregorian_en";
import gregorian_ar from "react-date-object/locales/gregorian_ar";
import arabic from "react-date-object/calendars/arabic";
import gregorian from "react-date-object/calendars/gregorian";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";
import TimePicker from "./timePicker";

function SingleDayCalendar({
  switchChecked,
  generateAvailableShifts,
  dayShifts,
  days,
  setDays,
  time,
  setTime,
  handleButtonClick,
  settoolTipOpen,
  minDate,
  maxDate,
}) {
  const { i18n, t } = useTranslation();
  const { pathname } = useRouter() || {};

  function calendarLocale() {
    if (i18n.language === "ar" && switchChecked) {
      return arabic_ar;
    } else if (i18n.language === "en" && switchChecked) {
      return arabic_en;
    } else if (i18n.language === "ar" && !switchChecked) {
      return gregorian_ar;
    } else {
      return gregorian_en;
    }
  }

  useEffect(() => {
    if (generateAvailableShifts && days) {
      generateAvailableShifts(null, null, days);
    }
  }, [days]);

  return (
    <div className="d-flex flex-column">
      <div className="d-flex flex-wrap-md">
        <Calendar
          value={days}
          onChange={(e) => {
            setDays(e);
          }}
          calendar={switchChecked ? arabic : gregorian}
          locale={calendarLocale()}
          minDate={Boolean(minDate) && new DateObject(minDate)}
          maxDate={Boolean(maxDate) && new DateObject(maxDate)}
        />
        <div className="d-flex flex-column w-100">
          <div
            id="return_time"
            style={{ visibility: !days ? "hidden" : "visible" }}
          >
            <h5 className="color-4 mt-2">{t("Return time") as string}</h5>
            <TimePicker
              textColor="var(--color-4)"
              fontSize="20px"
              time={time}
              setTime={setTime}
              dropOffShifts={dayShifts}
              setReturnTime={undefined}
              pickUpShifts={undefined}
              reservationDays={undefined}
              isPickUp={undefined}
              roundedTime={undefined}
              setDisabledApply={undefined}
            />
          </div>
          <div className="d-flex flex-column mt-4">
            <button
              className="mb-2"
              type="button"
              id="cancel"
              onClick={() => settoolTipOpen(false)}
            >
              {t("Cancel") as string}
            </button>

            <button
              type="button"
              id="apply"
              onClick={handleButtonClick}
              disabled={
                (pathname === "/car-details" && !dayShifts?.length) || !days
              }
            >
              {t("Apply") as string}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default memo(SingleDayCalendar);
