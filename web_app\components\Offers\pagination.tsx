import { Pagination } from "@material-ui/lab";
import PerPage from "components/shared/PerPage";
import React from "react";

export default function PaginationComponent({
  offersRes,
  limit,
  setPage,
  setLimit,
}) {
  return (
    <div className="d-flex justify-content-around mt-5">
      <Pagination
        style={{
          direction: "ltr",
        }}
        count={Math.ceil(
          offersRes?.businessRentalOffers.metadata?.totalCount / limit
        )}
        page={offersRes?.businessRentalOffers.metadata?.currentPage}
        onChange={(_e, value) => {
          setPage(value);
        }}
      />
      <PerPage
        specialPagination={[5, 10, 20, 40, 100]}
        handlePerPageChange={(value) => setLimit(value)}
        perPage={limit}
        setPage={setPage}
      />
    </div>
  );
}
