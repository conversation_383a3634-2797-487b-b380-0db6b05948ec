/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { ticket_type } from "constants/constants";

export function TicketType({ loading, type, setSelecteType, SelectedType, error }) {
  const { formatMessage } = useIntl();

  const options = ticket_type(formatMessage);

  React.useEffect(() => {
    if (!SelectedType) {
      onClear();
    }
  }, [SelectedType]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };

  return (
    <Select
      className={`dropdown-select ${error ? "selection-error" : ""}`}
      options={options}
      isClearable
      ref={selectInputRef}
      loadOptions={loading}
      defaultValue={options.find((optn) => `${optn.value}` === `${SelectedType}`)}
      value={options.find((optn) => `${optn.value}` === `${SelectedType}`)}
      placeholder={formatMessage({ id: type })}
      onChange={(selection) => {
        setSelecteType(selection?.value);
      }}
      noOptionsMessage={() => {
        if (loading) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
    />
  );
}
TicketType.propTypes = {
  type: PropTypes.string,
  loading: PropTypes.bool,
  SelectedType: PropTypes.string,
  setSelecteType: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
};
