import { Container, Grid, TextField } from "@material-ui/core";
import Layout from "components/shared/layout";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import TermsAr from "./termsar";
import TermsEn from "./termsen";

const Div = styled.div`
  padding: 70px 0 50px 0;
  &,
  > div,
  > div > div {
    min-height: calc(100vh - 250px);
  }
`;

const Card = styled.div`
  background: #fff;
  z-index: 9;
  border-radius: var(--radius-2);
`;

const Hashtag = styled.div`
  color: var(--color-4);
  @media (min-width: 961px) {
    position: absolute;
    bottom: 0;
    transform: translateY(-70px);
  }
  @media (max-width: 960px) {
    margin-top: 35px;
  }
`;

export default function MyTerms() {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const componentname = router.asPath.replace(`${router.pathname}`, "");

  return (
    <Layout>
      <Div>
        <Container>
          <Grid
            container
            direction="row-reverse"
            justifyContent="space-between"
            alignItems="center"
          >
            {i18n.language == "ar" ? <TermsAr /> : <TermsEn />}
          </Grid>
        </Container>
      </Div>
    </Layout>
  );
}
