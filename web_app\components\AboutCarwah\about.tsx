/* eslint-disable @next/next/no-img-element */
import { Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  background-color: var(--color-4);
  border-radius: var(--radius-2);
  padding-top: 30px;
  padding-bottom: 40px;
  .section-fancy-title,
  h5,
  .counters {
    padding: 0 1.75rem;
    @media (max-width: 960px) {
      > div {
        margin-bottom: 15px;
      }
    }
  }

  .counters {
    margin-top: 70px;
    @media (max-width: 960px) {
      * {
        text-align: center;
      }
    }
    h6 {
      margin-top: 15px;
    }
  }
  @media (max-width: 960px) {
    padding-bottom: 55px;
    h5 {
      margin-top: 55px !important;
    }
  }
`;

export default function About() {
  const { t, i18n } = useTranslation();

  return (
    <Div t={t}>
      <Grid container justifyContent="space-between">
        <Grid item xs={12} md={3}>
          <div className="px-4">
            <h4>{t("Carwah Origin Story ") as string}</h4>
          </div>
          <img
            src="/assets/images/about/aboutcarwah.png"
            alt="Carwah Car Rental - كروة لأيجار السيارات"
            style={{ width: "100%", marginTop: "30px" }}
          />
        </Grid>
        <Grid item xs={12} md={9}>
          <h5 className="mb-3">
            {
              t(
                "Carwah was founded on the idea of making car rental easier for customers. One of the company's co-founders came up with the idea while waiting in line for an airport car rental. He realized that there were so many different car rental companies to choose from and it was difficult to determine which one offered the car that would meet his needs or have the best price. He believed there had to be a better way to rent a car than simply relying on name recognition and leaving the decision-making process up to the car rental companies. He wanted to create a platform that would give customers control over their car rental options. The result was an online platform that featured multiple car rental companies in one place and made the rental process more focused on the needs of the consumer rather than the name of the car rental company."
              ) as string
            }
          </h5>
          <h5 className="mb-3">
            {
              t(
                "Carwah officially launched its application on June 24, 2018 - a historic day for women in Saudi Arabia as it marked the day when women were granted the right to drive. The company's approach is simplicity. It offers an easy-to-navigate application that is available on all platforms and links customers with its allies (car rental companies). With Carwah, booking a car rental has never been easier."
              ) as string
            }
          </h5>
          <h5 className="mt-4 text-center" style={{ fontWeight: "bold" }}>
            {
              t(
                "Every present has a future, and carwah is the future of car rental."
              ) as string
            }
          </h5>
        </Grid>
      </Grid>
    </Div>
  );
}
