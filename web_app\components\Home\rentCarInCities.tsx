/* eslint-disable react/jsx-key */
import { Container, Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import CityCard from "./cityCard";
import { useState } from "react";

const Div = styled(Container)``;

export default function RentCarInCities({ cityData, cities }) {
  const { t, i18n } = useTranslation();
  const [citiesNo, setCitiesNo] = useState(4);

  //Randomize cities
  const citiesToBeShown = cities?.cities
    ?.filter((c) => c.nameEn !== cityData.nameEn)
    .map((value) => ({ value, sort: Math.random() }))
    .sort((a, b) => a.sort - b.sort)
    .map(({ value }) => value)
    .splice(0, citiesNo);

  return (
    <Div className="py-4 mb-5">
      <h2 className="font-27px medium text-uppercase mb-4">
        {t("Rent a car in these cities")}
      </h2>
      <Grid container justifyContent="space-between" spacing={5}>
        {citiesToBeShown?.map((i) => {
          return (
            <Grid item xs={12} md={3}>
              <CityCard
                img={i.icon}
                city={i18n.language === "en" ? i.nameEn : i.nameAr}
                link={i.nameEn}
              />
            </Grid>
          );
        })}
        {citiesNo === 4 ? (
          <span
            className="px-4 cursor-pointer"
            style={{ textDecoration: "underline" }}
            onClick={() => setCitiesNo(cities?.cities.length)}
          >
            {t("Show More")}
          </span>
        ) : (
          <span
            className="px-4"
            style={{ cursor: "pointer", textDecoration: "underline" }}
            onClick={() => setCitiesNo(4)}
          >
            {t("Show Less")}
          </span>
        )}
      </Grid>
    </Div>
  );
}
