import { useTranslation } from "react-i18next";
import MessagePopup from "./messagePopup";

export default function TamaraFailPopup({
  isOpen,
  setIsOpen,
  setIsPaymentPopupOpen,
  tamaraErrors
}) {
  const { t } = useTranslation();
  if (isOpen) {
    return (
      <MessagePopup
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        body={
          <>
          <p className="text-center">
              {t("Dear customer.. you can't pay using <PERSON> right now")}
          </p>
            <p className="text-center">
              {
                tamaraErrors?.map(error=>error).join(", ")
              }
            
            </p>
            <div className="d-flex justify-content-center mt-3 cursor-pointer">
              <div
                className="button text-center text-white radius-1 py-2 px-4 mt-3"
                onClick={() => {
                  setIsPaymentPopupOpen(true);
                  setIsOpen(false);
                }}
                style={{ background: "var(--color-3)" }}
              >
                {t("rental.btn.Error")}
              </div>
            </div>
          </>
        }
        title={t("Failed payment")}
      />
    );
  }
  return null;
}
