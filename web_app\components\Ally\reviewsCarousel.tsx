/* eslint-disable react/jsx-key */
import React, { useEffect, useLayoutEffect } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import styled from "styled-components";
import Swiper from "components/shared/carousel";
import ReviewCard from "./reviewCard";

const Div = styled.div`
  transform: ${(props) => (props.language === "ar" ? "rotateZ(180deg)" : null)};
  .swiper-button-next {
    @media (max-width: 1024px) {
      right: -15px;
    }
    @media (min-width: 1025px) {
      right: -50px;
    }
  }
  .swiper-button-prev {
    @media (max-width: 1024px) {
      left: -10px;
    }
    @media (min-width: 1025px) {
      left: -50px;
    }
  }
  .swiper-button-prev,
  .swiper-button-next {
    color: var(--color-1);
    width: 35px;
    &:after {
      font-size: 18px;
      transform: ${(props) =>
        props.language === "en"
          ? "translateX(-17px)"
          : (props) => (props.language === "ar" ? "translateX(17px)" : null)};
    }
    &:before {
      content: "";
      width: 100%;
      height: calc(100% * 5);
      opacity: 1;
      /* background-color: var(--color-3); */
      /* border-radius: var(--radius-1); */
    }
  }
  .swiper-button-next {
    @media (min-width: 961px) {
      position: absolute;
      left: -350px;
      bottom: ${(props) => (props.language === "en" ? 0 : "auto")};
      top: ${(props) => (props.language === "ar" ? 0 : "auto")};
      right: auto;
    }
  }
  .swiper-button-prev {
    @media (min-width: 961px) {
      position: absolute;
      left: -395px;
      bottom: ${(props) => (props.language === "en" ? 0 : "auto")};
      top: ${(props) => (props.language === "ar" ? 0 : "auto")};
      right: auto;
    }
  }
  @media (max-width: 900px) {
    margin-top: 50px;
  }

  .swiper-wrap {
    .swiper-container {
      padding: 15px 0px;
    }
  }
  .swiper-slide {
    float: right;
  }
`;

export default function ReviewsCarousel() {
  const { t, i18n } = useTranslation();

  useEffect(() => {
    let clean = false;
    if (!clean) {
      // Moved next and prev arrows outside of swiper container for styling
      const next = document.querySelector(
        "#reviews-carousel .swiper-button-next"
      );
      const prev = document.querySelector(
        "#reviews-carousel .swiper-button-prev"
      );
      const wrap = document.querySelector("#reviews-carousel .swiper-wrap");
      wrap.appendChild(next);
      wrap.appendChild(prev);
    }
    return (clean = true);
  }, []);

  return (
    <Div id="reviews-carousel" language={i18n.language}>
      <Swiper
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 25,
          },
          900: {
            slidesPerView: 1,
            spaceBetween: 25,
          },
          1024: {
            slidesPerView: 1,
            spaceBetween: 25,
          },
        }}
        navigation
        // pagination={{ clickable: true }}
        // scrollbar={{ draggable: true }}
        onSwiper={(swiper) => null}
        onSlideChange={() => null}
        loop
        slides={[
          <ReviewCard
            data={{
              img: "/assets/images/ally/review.png",
              name: "Jack Jack",
              description:
                "Our vision is to be a one-stop site for consumers in the GCC looking for car rentals, with a consumer-focused platform that creates value for every company and shareholder that collaborates with us.",
            }}
          />,
        ]}
      />
    </Div>
  );
}
