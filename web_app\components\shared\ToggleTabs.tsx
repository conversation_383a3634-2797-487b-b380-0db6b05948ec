import React from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";

const Div = styled.div`
  text-transform: uppercase;
  background-color: #e6e6e6;
  padding: 3px;
  border-radius: 50px;
  color: #9c9c9c;
  width: fit-content;
  margin: 0 auto;

  .active {
    color: var(--color-3);
    transition: all ease 0.3s;
    position: relative;
    color: #ffffff;
    background-color: ${(props) => props.activeTabColor || "var(--color-2)"};
    font-weight: bold;
    display: inline-block;
    border-radius: 30px;
  }
  span {
    padding: 12px 50px 16px 50px;
    font-size: 16px;
    line-height: 18px;
  }
`;

export default function ToggleTabs({
  tabs,
  activeTab,
  activeTabColor,
}: {
  tabs: { title: string; clickAction: () => void; routerQuery: string }[];
  activeTab: string;
  activeTabColor?: string;
}) {
  const { i18n } = useTranslation();
  const { query } = useRouter() || {};

  return (
    <Div
      className="d-flex justify-content-center medium"
      language={i18n.language}
      activeTabColor={activeTabColor}
    >
      {tabs?.length
        ? tabs.map(({ title, clickAction, routerQuery }, index) => {
            return (
              <span
                key={`tab-${index}`}
                className={`${
                  activeTab === routerQuery && "active"
                } cursor-pointer`}
                onClick={clickAction}
              >
                {title}
              </span>
            );
          })
        : null}
    </Div>
  );
}
