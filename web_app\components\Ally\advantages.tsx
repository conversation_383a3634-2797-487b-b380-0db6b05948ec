import { Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Item from "./advantageItem";

const Div = styled.div`
  padding: 30px 0;
  @media (max-width: 960px) {
    .section-fancy-title {
      margin-bottom: 30px;
    }
  }
`;

export default function Advantages() {
  const { t } = useTranslation();

  const advantagesMockData = [
    {
      title: t("Allies"),
      description: t(
        "Benefit from our strategic partnerships to get the best services."
      ),
    },
    {
      title: t("Customers"),
      description: t(
        "See all information about the customer before he visits your branch."
      ),
    },
    {
      title: t("Evaluation"),
      description: t("Get the evaluation and booking history of the customer."),
    },
    {
      title: t("Behavior"),
      description: t(
        "Discover and track the customer driving behavior with our Carwah`s GPS."
      ),
    },
    {
      title: t("Finance"),
      description: t(
        "The ability to get financial information of each car and the company overview."
      ),
    },
    {
      title: t("Operation"),
      description: t(
        "Easy renting process (book- receive - handover) through smartphones."
      ),
    },
    {
      title: t("Training"),
      description: t(
        "Train allies and its employees on how to use Carwah system."
      ),
    },
  ];

  return (
    <Div>
      <Grid container>
        <Grid item xs={12} md={3}>
          <div className="section-fancy-title">
            <h2>
              <span className="bold">{t("Allies")}</span> {t("Advantages")}
            </h2>
          </div>
        </Grid>
        {advantagesMockData.map((item, index) => {
          return (
            <Grid item key={index} xs={6} md={3}>
              <Item data={{ ...item, count: `0${index + 1}` }} />
            </Grid>
          );
        })}
      </Grid>
    </Div>
  );
}
