/* eslint-disable react-hooks/exhaustive-deps */
import { InfoOutlined } from "@material-ui/icons";
import { useRouter } from "next/router";
import MessagePopup from "./messagePopup";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import {
  setCreatingOnlineExtensionRequset,
  setIsExtensionRequestCreatedSuccessfully,
} from "store/extensions/action";
import { setFullyPaidByWallet } from "store/wallet/action";
import { useEffect } from "react";
import { setShowInstallmmentBreakdownModal } from "store/installments/action";

export default function PaymentStatusPopup({
  isOpen,
  setIsOpen,
  localizedContent,
  paid,
  extensionId,
  bookingId,
  setCheckoutId,
  PaymentErrors,
  ErrorMessage,
}) {
  const router = useRouter();
  const {
    car,
    bookingId: booking_id,
    extensionId: extension_id,
  } = router.query || {};
  const dispatch = useDispatch();
  const { is_creating_online_extension_requset } =
    useSelector((state: RootStateOrAny) => state.extensions) || {};

  const { is_paid_by_wallet } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};

  useEffect(() => {
    return () => {
      setCheckoutId();
    };
  }, []);

  if (isOpen) {
    return (
      <MessagePopup
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        onClose={() => {
          dispatch(setShowInstallmmentBreakdownModal(false));
          setCheckoutId();
          if (
            (extensionId || extension_id) &&
            !router.asPath.includes("#update") &&
            paid
          ) {
            dispatch(setIsExtensionRequestCreatedSuccessfully(true));
          }
          if (is_paid_by_wallet === "Success") {
            dispatch(setFullyPaidByWallet("Error"));
          }
          if (
            is_creating_online_extension_requset &&
            !extension_id &&
            !window.location.hash
          ) {
            dispatch(setCreatingOnlineExtensionRequset(false));
          } else {
            router.push(
              `${router.pathname}?car=${car}&bookingId=${
                booking_id || bookingId
              }`
            );
          }
        }}
        body={
          <>
            <div className="text-center" title="حالة الدفع">
              <InfoOutlined
                className="color-3 mb-2"
                style={{ width: "40px", height: "40px" }}
              />
            </div>
            <h4 className="mb-3 text-center">{localizedContent?.title()}</h4>
            <p className="text-center">
              {PaymentErrors?.length
                ? PaymentErrors?.map((error) => error).join(", ")
                : localizedContent?.body(ErrorMessage)}
            </p>
            <div
              className="text-center w-100 radius-1 p-2 cursor-pointer mt-4 border-0 text-white"
              style={{ background: "var(--color-2)" }}
              onClick={() => {
                if (is_paid_by_wallet === "Success") {
                  dispatch(setFullyPaidByWallet("Error"));
                  router.push(
                    `${router.pathname}?car=${car}&bookingId=${
                      booking_id || bookingId
                    }`
                  );
                } else {
                  localizedContent?.action();
                }
              }}
            >
              {localizedContent?.button()}
            </div>
            {localizedContent?.seeRentalDetailsButton() ? (
              <div
                className="text-center w-100 radius-1 p-2 cursor-pointer color-2 mt-2 "
                style={{ border: "solid 2px var(--color-2)" }}
                onClick={localizedContent?.seeRentalDetailsButton()?.action}
              >
                {localizedContent?.seeRentalDetailsButton()?.text}
              </div>
            ) : null}
          </>
        }
        title=""
      />
    );
  }
  return null;
}
