/* eslint-disable react-hooks/exhaustive-deps */
import { useLazyQuery } from "@apollo/client";
import { ListRentToOwnCars_Query } from "gql/queries/rentToOwn";
import { SimpleCars_Query } from "gql/queries/simpleCars";
import { useRouter } from "next/router";
import { useSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { carsListAction } from "store/cars/action";

const useCarsListEffects = ({
  carsList,
  rentToOwnCarsList,
  simpleCarsError,
  listRentToOwnCarsError,
  simpleCarsLoading,
  listRentToOwnCarsLoading,
  setLoadingCarsList,
}) => {
  const dispatch = useDispatch();
  const router = useRouter(); // Add this line
  const { enqueueSnackbar } = useSnackbar();
  const fiveYearsAgo = new Date().getFullYear() - 4; // Add this line
  const currentYear = new Date().getFullYear() + 1; // Add this line

  useEffect(() => {
    if (carsList?.simpleCars) {
      dispatch(carsListAction(carsList?.simpleCars));
    }
    if (rentToOwnCarsList?.listRentToOwnCars) {
      dispatch(carsListAction(rentToOwnCarsList?.listRentToOwnCars));
    }
  }, [carsList, rentToOwnCarsList]);

  useEffect(() => {
    if (simpleCarsError) {
      enqueueSnackbar(simpleCarsError.message, { variant: "error" });
    }
    if (listRentToOwnCarsError) {
      enqueueSnackbar(listRentToOwnCarsError.message, { variant: "error" });
    }
  }, [simpleCarsError, listRentToOwnCarsError]);

  useEffect(() => {
    setLoadingCarsList(simpleCarsLoading || listRentToOwnCarsLoading);
  }, [simpleCarsLoading, listRentToOwnCarsLoading]);
  return { fiveYearsAgo, currentYear, router };
};

export function useCarsList(payload?: { isRentToOwn: boolean }) {
  const { isRentToOwn } = payload || {};
  const [
    simpleCars,
    { data: carsList, loading: simpleCarsLoading, error: simpleCarsError },
  ] = useLazyQuery(SimpleCars_Query, {
    fetchPolicy: "no-cache",
    nextFetchPolicy: "no-cache",
  });

  const [
    listRentToOwnCars,
    {
      data: rentToOwnCarsList,
      loading: listRentToOwnCarsLoading,
      error: listRentToOwnCarsError,
    },
  ] = useLazyQuery(ListRentToOwnCars_Query, {
    fetchPolicy: "no-cache",
    nextFetchPolicy: "no-cache",
  });

  const {
    user_address,
    search_makeModel,
    search_model,
    confirmed_delivery_location,
    filters = {},
    selection_index,
    current_location,
    isUnlimited,
    canHandoverInBranch,
    isInstantConfirmation,
    date_time,
    fullInsurance,
  } = useSelector((state: RootStateOrAny) => state.search_data);

  const { carsListCurrentPage } =
    useSelector((state: RootStateOrAny) => state.cars) || {};

  const { coupon_data } =
    useSelector((state: RootStateOrAny) => state?.coupons) || {};

  const [loadingCarsList, setLoadingCarsList] = useState(false);

  const { isNewCar } = useSelector(
    (state: RootStateOrAny) => state?.search_data
  );

  const deliveryTypeBooking = selection_index === 2;

  const formatItem = (payload: any, key?: string) => {
    if (!payload || payload?.id == -1) return undefined;
    if (payload?.value && !key) {
      return payload?.value;
    } else {
      return payload?.map((e) => e.value)?.length
        ? payload?.map((e) => e.value)
        : undefined;
    }
  };

  const { fiveYearsAgo, currentYear, router } = useCarsListEffects({
    carsList,
    rentToOwnCarsList,
    simpleCarsError,
    listRentToOwnCarsError,
    simpleCarsLoading,
    listRentToOwnCarsLoading,
    setLoadingCarsList,
  });

  const getCarList = async (providedData: any) => {
    const params = new URLSearchParams(window?.location?.search);
    const variables = {
      page: carsListCurrentPage,
      limit: 12,
      canDelivery: deliveryTypeBooking ? true : false,
      pickUpLocationId: user_address?.pick_up?.id,
      airportId: user_address?.pick_up?.airport?.id,
      dropOffLocationId: user_address?.return?.id || user_address?.pick_up?.id,
      pickUpDeliverLat: deliveryTypeBooking
        ? confirmed_delivery_location?.airport?.centerLat ||
          confirmed_delivery_location?.lat ||
          user_address?.pick_up?.lat
        : undefined,
      pickUpDeliverLng: deliveryTypeBooking
        ? confirmed_delivery_location?.airport?.centerLng ||
          confirmed_delivery_location?.lng ||
          user_address?.pick_up?.lng
        : undefined,
      filter: search_makeModel,
      carModelName: search_model,
      isUnlimited,
      canHandoverInBranch,
      isInstantConfirmation,
      make: formatItem(filters.make),
      model: formatItem(filters.model),
      insuranceType: formatItem(filters.insuranceType),
      dailyPriceFrom: formatItem(filters.dailyPriceFrom),
      dailyPriceTo:
        formatItem(filters.dailyPriceTo) === "+300"
          ? undefined
          : formatItem(filters.dailyPriceTo),
      vehicleType: formatItem(filters.vehicleType),
      transmission: formatItem(filters.transmission),
      yearFrom: !filters.yearFrom ? fiveYearsAgo : formatItem(filters.yearFrom),
      yearTo: !filters.yearTo ? currentYear : formatItem(filters.yearTo),
      extraServices: formatItem(filters.extraServices, "extraServices"),
      userLat: current_location?.lat,
      userLng: current_location?.lng,
      couponId: coupon_data?.id,
      pickEndDate: date_time?.dropOffDate,
      pickEndTime: date_time?.return_time,
      pickStartDate: date_time?.pickUpDate,
      pickStartTime: date_time?.pickup_time,
      bannerId: params?.get("bannerId") || undefined,
      insuranceType: fullInsurance ? 2 : undefined,
    };

    if (!isRentToOwn) {
      return simpleCars({
        variables,
      }).then(() => {
        if (providedData?.isHome) {
          router.push("car-search#car-grid");
        }
      });
    } else {
      return listRentToOwnCars({
        variables: {
          filter: search_makeModel,
          page: carsListCurrentPage || 1,
          limit: 12,
          isNewCar: isNewCar,
          pickUpLocationId: user_address?.pick_up?.id,
        },
      });
    }
  };

  return {
    getCarList,
    loadingCarsList,
  };
}
