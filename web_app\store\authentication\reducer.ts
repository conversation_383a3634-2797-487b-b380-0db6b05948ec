import { AUTH_DATA, RETURNLOGIN, LOGOUT, UNSEENEXTENSION } from "./action";

export default function AuthenticationReducer(state = {}, action) {
  switch (action.type) {
    case AUTH_DATA:
      return { ...state, login_data: action.payload };
    case RETURNLOGIN:
      return { ...state, Islogin: action.payload };
    case LOGOUT:
      return { login_data: { token: null, unseenExtension: false } };
    case UNSEENEXTENSION:
      return {
        ...state,
        unseenExtension: true,
        rentalId: action.payload.rentalId,
        carId: action.payload.carId,
      };
    default:
      return state;
  }
}
