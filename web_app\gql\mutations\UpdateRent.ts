import { gql } from "@apollo/client";
export const CustomerEditRent = gql`
  mutation customerUpdatePendingRental(
    $dropOffDate: String
    $dropOffTime: String
    $pickUpDate: String
    $pickUpTime: String
    $rentalId: ID!
    $allyExtraServices: [ID!]
    $branchExtraServices: [ID!]
    $isUnlimited: Boolean
  ) {
    customerUpdatePendingRental(
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      pickUpDate: $pickUpDate
      pickUpTime: $pickUpTime
      rentalId: $rentalId
      allyExtraServices: $allyExtraServices
      branchExtraServices: $branchExtraServices
      isUnlimited: $isUnlimited
    ) {
      errors
      status
    }
  }
`;
