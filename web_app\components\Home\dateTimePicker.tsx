/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */
import React, { useEffect, useState } from "react";
import { Calendar, DateObject } from "react-multi-date-picker";
import styles from "./_style.module.scss";
import { RootStateOrAny, useDispatch } from "react-redux";
import { setDateTimeAction, setRentalMonthsAction } from "store/search/action";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

import TimePicker from "components/shared/timePicker";
import arabic_en from "react-date-object/locales/arabic_en";
import gregorian_en from "react-date-object/locales/gregorian_en";
import arabic from "react-date-object/calendars/arabic";
import gregorian from "react-date-object/calendars/gregorian";
import DatePanel from "react-multi-date-picker/plugins/date_panel";
import { useRouter } from "next/router";
import {
  calendarDaysConvert,
  isEligibleForInstallments,
} from "utilities/helpers";
import { useSnackbar } from "notistack";
import moment from "moment";
import "moment/locale/en-in";
import {
  defaultTime,
  monthsAr,
  two_hours_plusArr,
  weekDaysAr,
} from "utilities/enums";
import { setPayWithInstallments } from "store/installments/action";

export default function DateTimePicker({
  settoolTipOpen,
  switchChecked,
  setChanged,
  pickUpShifts,
  dropOffShifts,
  generateAvailableShifts,
  minDate,
  maxDate,
  hidePickUpTime,
  hideReturnTime,
  isSingleDaySelection,
}: {
  settoolTipOpen: React.Dispatch<React.SetStateAction<boolean>>;
  switchChecked: boolean;
  setChanged?: React.Dispatch<React.SetStateAction<boolean>>;
  pickUpShifts?: any[];
  dropOffShifts?: any[];
  generateAvailableShifts: (
    reservation_days_start: any,
    reservation_days_end: any,
    days: any
  ) => void;
  minDate?: any;
  maxDate?: any;
  hidePickUpTime?: boolean;
  hideReturnTime?: boolean;
  isSingleDaySelection?: boolean;
}) {
  //Hooks
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const snackbar = useSnackbar();
  const { pathname } = router || {};
  const isRentalPage = router?.query?.car;

  //Store
  const dispatch = useDispatch();
  const dateTime = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );
  const selectionIndex = useSelector(
    (state: RootStateOrAny) => state.search_data.selection_index
  );
  const rentalMonths = useSelector(
    (state: RootStateOrAny) => state.search_data.rental_months
  );

  const { deliveryType } =
    useSelector((state: RootStateOrAny) => state.cars) || {};

  //State
  const [reservationDays, setReservationDays] = useState<
    DateObject | DateObject[]
  >(
    !isSingleDaySelection
      ? dateTime?.reservation_days?.length
        ? ([
            new DateObject(dateTime?.reservation_days[0]),
            new DateObject(dateTime?.reservation_days[1]),
          ] as any) // Retrieve calendar dates from redux
        : [new DateObject(), new DateObject().add(3, "days")] // sets default 3 days
      : dateTime?.reservation_days?.[0]
  );

  const [pickupTime, setPickupTime] = useState(
    dateTime?.pickup_time ? dateTime.pickup_time : defaultTime
  );
  const [returnTime, setReturnTime] = useState(
    dateTime?.return_time ? dateTime.return_time : defaultTime
  );

  const [disabledApply, setDisabledApply] = useState(false);

  function handleButtonClick() {
    if (isSingleDaySelection) {
      handleSingleDaySelection();
    } else {
      handleMultiDaySelection();
    }
    settoolTipOpen(false);
  }

  function handleSingleDaySelection() {
    const pickUpDate = calendarDaysConvert(
      reservationDays,
      switchChecked ? true : null
    );
    const dropOffDate = calendarDaysConvert(
      new DateObject(reservationDays as DateObject).add(3, "days"),
      switchChecked ? true : null
    );
    dispatch(
      setDateTimeAction({
        reservation_days: [
          new DateObject(reservationDays as DateObject),
          new DateObject(reservationDays as DateObject).add(3, "days"),
        ],
        pickUpDate,
        dropOffDate,
        selected_days_count: 3,
        pickup_time: pickUpShifts
          ? !isRentalPage
            ? pickupTime
            : pickUpShifts?.includes(pickupTime)
            ? pickupTime
            : pickUpShifts?.[0]
          : pickupTime,
        return_time: dropOffShifts
          ? !isRentalPage
            ? returnTime
            : dropOffShifts?.includes(returnTime)
            ? returnTime
            : dropOffShifts?.[0]
          : returnTime,
      })
    );
  }

  function handleMultiDaySelection() {
    const isValidSelection =
      reservationDays &&
      (reservationDays as any).length === 2 &&
      reservationDays[0] !== reservationDays[1];

    if (isValidSelection) {
      const pickUpDate = calendarDaysConvert(
        reservationDays[0],
        switchChecked ? true : null
      );
      const dropOffDate = calendarDaysConvert(
        reservationDays[1],
        switchChecked ? true : null
      );

      const selected_days_count = moment(dropOffDate, "DD/MM/YYYY").diff(
        moment(pickUpDate, "DD/MM/YYYY"),
        "days"
      );

      dispatch(
        setDateTimeAction({
          reservation_days: reservationDays,
          pickUpDate,
          dropOffDate,
          selected_days_count,
          pickup_time: !isRentalPage
            ? pickupTime
            : pickUpShifts?.includes(pickupTime)
            ? pickupTime
            : pickUpShifts?.[0],
          return_time: !isRentalPage
            ? returnTime
            : dropOffShifts?.includes(returnTime)
            ? returnTime
            : dropOffShifts?.[0],
        })
      );

      handleMultiDaySelectionLogic(selected_days_count);
    } else {
      showSnackbarInfo();
    }
  }

  function handleMultiDaySelectionLogic(selected_days_count) {
    if (selected_days_count >= 30 && selectionIndex === 1) {
      dispatch(setRentalMonthsAction(selected_days_count / 30));
    }

    if (setChanged) {
      setChanged(true);
    }

    handleInstallments(selected_days_count);

    settoolTipOpen(false);
  }

  function handleInstallments(selected_days_count) {
    const isInstallmentEligible =
      isEligibleForInstallments(selected_days_count);
    dispatch(setPayWithInstallments(isInstallmentEligible));
  }

  function showSnackbarInfo() {
    snackbar.enqueueSnackbar(t("Please Select a date") as string, {
      variant: "info",
      preventDuplicate: true,
    });
  }

  function calendarLocale() {
    if (switchChecked) {
      return arabic_en;
    } else {
      return gregorian_en;
    }
  }

  //LifeCycle
  useEffect(() => {
    if (pathname.includes("car-details")) {
      if (isSingleDaySelection) {
        generateAvailableShifts(
          new DateObject(reservationDays as DateObject),
          new DateObject(reservationDays as DateObject),
          null
        );
      } else {
        generateAvailableShifts(
          new DateObject(reservationDays?.[0]) || reservationDays,
          new DateObject(reservationDays?.[1]) || reservationDays,
          null
        );
      }
    }
  }, [reservationDays, deliveryType]);

  useEffect(() => {
    if (
      reservationDays?.[0] &&
      reservationDays?.[1] &&
      reservationDays[0].format() == reservationDays[1].format()
    ) {
      setDisabledApply(true);
    } else {
      setDisabledApply(false);
    }
  }, [reservationDays]);

  return (
    <div className={styles.dateTimePicker}>
      <Calendar
        weekDays={i18n.language == "ar" ? weekDaysAr : undefined}
        months={i18n.language == "ar" ? monthsAr : undefined}
        value={reservationDays}
        onChange={(e: DateObject[]) => {
          setReservationDays(e);
        }}
        range={!isSingleDaySelection}
        calendar={switchChecked ? arabic : gregorian}
        locale={calendarLocale()}
        plugins={
          !isSingleDaySelection ? [<DatePanel eachDaysInRange />] : undefined
        }
        onFocusedDateChange={(e, c) => {
          if (selectionIndex === 1) {
            if (!isSingleDaySelection) {
              setReservationDays([
                c,
                new DateObject(c).add(30 * Number(rentalMonths), "days"),
              ]);
            } else {
              setReservationDays(c);
            }
          }
        }}
        minDate={minDate || new DateObject()}
        maxDate={maxDate}
      />
      {reservationDays ? (
        <div id="time-pickers">
          {!hidePickUpTime && (
            <div id="pickup_time">
              <h4>{t("Pick-up time") as string}</h4>
              <TimePicker
                textColor="var(--color-4)"
                fontSize="20px"
                time={pickupTime}
                setTime={setPickupTime}
                setReturnTime={setReturnTime}
                pickUpShifts={pickUpShifts}
                reservationDays={reservationDays}
                isPickUp
                roundedTime={`
                ${two_hours_plusArr[0]}:${
                  Number(two_hours_plusArr[1]) < 30 &&
                  Number(two_hours_plusArr[1]) < 30
                    ? "30"
                    : "00"
                }`?.trim()}
                setDisabledApply={setDisabledApply}
                dropOffShifts={undefined}
              />
            </div>
          )}
          {!hideReturnTime && (
            <div id="return_time">
              <h4>{t("Return time") as string}</h4>
              <TimePicker
                textColor="var(--color-4)"
                fontSize="20px"
                time={returnTime}
                setTime={setReturnTime}
                dropOffShifts={dropOffShifts}
                reservationDays={reservationDays}
                setReturnTime={undefined}
                pickUpShifts={undefined}
                isPickUp={undefined}
                roundedTime={undefined}
                setDisabledApply={undefined}
              />
            </div>
          )}
          <div className="d-flex flex-column mt-4">
            <button
              className="mb-2"
              type="button"
              id="cancel"
              onClick={() => settoolTipOpen(false)}
            >
              {t("Cancel") as string}
            </button>

            <button
              type="button"
              id="apply"
              onClick={handleButtonClick}
              disabled={
                (pathname === "/car-details" && !pickUpShifts?.length) ||
                (pathname === "/car-details" && !dropOffShifts?.length) ||
                disabledApply
              }
            >
              {t("Apply") as string}
            </button>
          </div>
        </div>
      ) : null}
    </div>
  );
}
