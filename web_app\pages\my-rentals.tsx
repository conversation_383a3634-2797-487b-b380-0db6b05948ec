import Head from "next/head";
import { useRouter } from "next/router";
import MyRentalsView from "components/MyRentals";
import AuthProtected from "components/shared/authProtected";

function MyRentalsPage({ locale }) {
  //Hooks
  const router = useRouter();

  return (
    <div>
      <Head>
        <link
          rel="canonical"
          href={`https://carwah.com.sa/${locale}${router?.pathname}`}
        />
        <link
          rel="alternate"
          hrefLang={locale === "en" ? "ar" : "en"}
          href={`https://carwah.com.sa/${locale === "en" ? "ar" : "en"}${
            router?.pathname
          }`}
        />
      </Head>
      <AuthProtected>
        <MyRentalsView />
      </AuthProtected>
    </div>
  );
}

export async function getStaticProps(context: any = {}) {
  const { locale } = context;

  return {
    props: {
      locale,
    },
  };
}

export default MyRentalsPage;
