import { Controller } from "react-hook-form";
import { Input } from ".";
import { useTranslation } from "react-i18next";

function NationalId({ watch, control, errors }) {
  const { t } = useTranslation();

  return (
    <div className="mt-4">
      <label className="color-19 text-start w-100 mb-2">
        {watch("status") === "resident"
          ? (t("Iqama ID") as string)
          : watch("status") === "citizen"
          ? (t("National ID") as string) || watch("status") === "visitor"
          : watch("status") === "visitor"
          ? ""
          : (t("Gulf National ID") as string)}

        <span className="color-9">*</span>
      </label>
      <Controller
        name={"nid"}
        control={control}
        rules={{
          required: true,
          pattern:
            watch("status") === "resident"
              ? /^2\d{9}$/
              : watch("status") === "citizen"
              ? /^1\d{9}$/
              : watch("status") === "gulf_citizen"
              ? /^\d{1,20}$/
              : null,
          maxLength: 50,
        }}
        render={({ field, fieldState }) => {
          return <Input className="hide-arrows d-block" {...field} />;
        }}
      />
      {errors?.nid?.type === "required" && (
        <p className="color-9">{t("This field is required") as string}</p>
      )}
      {errors?.nid?.type === "pattern" && (
        <p className="color-9">
          {watch("status") === "citizen"
            ? (t("ID must start with 1, and consist of 10 digits") as string)
            : watch("status") === "resident"
            ? (t("ID must start with 2, and consist of 10 digits") as string)
            : (t("This field is required") as string)}
        </p>
      )}
    </div>
  );
}

export default NationalId;
