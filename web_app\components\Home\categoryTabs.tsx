import React from "react";
import { useSelector, useDispatch, RootStateOrAny } from "react-redux";
import { Grid } from "@material-ui/core";
import {
  setDateTimeAction,
  setSelectionIndexAction,
} from "store/search/action";
import Tabs from "./tabs";
import useLogic from "./RentalPeriod/useLogic";
import { setPayWithInstallments } from "store/installments/action";
import { default_date_time } from "store/search/reducer";

export default function TypeTabs({ t }) {
  const dispatch = useDispatch();

  const selectionIndex = useSelector(
    (state: RootStateOrAny) => state.search_data.selection_index
  );
  const { monthlyDateHandler, rental_months } = useLogic();

  return (
    <Grid item xs={12} className="mb-3">
      <Tabs
        items={[
          { index: 0, text: t("Daily") },
          { index: 1, text: t("Monthly") },
          {
            index: 2,
            text: t("Delivery"),
            note: t("Additional fees may apply"),
          },
        ]}
        selectionIndex={selectionIndex}
        onClickHandler={(index: 0 | 1 | 2) => {
          dispatch(setSelectionIndexAction(index));
          if (index === 0) {
            dispatch(setDateTimeAction(default_date_time));
            dispatch(setPayWithInstallments(false));
          }
          if (index === 1) {
            monthlyDateHandler(rental_months);
          } else {
            monthlyDateHandler(undefined);
          }
        }}
      />
    </Grid>
  );
}
