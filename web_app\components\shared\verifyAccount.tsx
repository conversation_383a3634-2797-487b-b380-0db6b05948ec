/* eslint-disable @next/next/no-img-element */
import styled from "styled-components";
import VerifyByEmail from "./verifyByEmail";
import VerifyByPhone from "./verifyByPhone";

const Button = styled.button`
  background: var(--color-3);
  border-radius: var(--radius-3);
  padding: 10px 18px;
  height: 60px;
`;

const DigitsCodeWrapper = styled.div`
  flex-direction: ${(props) =>
    props.language === "en" ? "row" : "row-reverse"};
  input {
    border: none;
    border-radius: var(--radius-1);
    background: var(--color-6);
    width: 50px;
    height: 50px;
    text-align: center !important;
    font-weight: 600;
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    &[type="number"] {
      -moz-appearance: textfield; /* Firefox */
    }
  }
`;

export default function VerifyAccount({
  verifyBy,
  t,
  isPhoneVerified,
  setIsPhoneVerified,
  setIsSigninPopupOpen,
  mobileData,
  setMobileData,
}) {
  return verifyBy === "phone" ? (
    <VerifyByPhone
      t={t}
      setIsSigninPopupOpen={setIsSigninPopupOpen}
      Button={Button}
      isPhoneVerified={isPhoneVerified}
      setIsPhoneVerified={setIsPhoneVerified}
      DigitsCodeWrapper={DigitsCodeWrapper}
      mobileData={mobileData}
      setMobileData={setMobileData}
    />
  ) : (
    <VerifyByEmail
      t={t}
      setIsSigninPopupOpen={setIsSigninPopupOpen}
      Button={Button}
    />
  );
}
