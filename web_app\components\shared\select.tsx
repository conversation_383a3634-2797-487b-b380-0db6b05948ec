import { MenuItem, Select } from "@material-ui/core";
import React from "react";
import parse from "html-react-parser";

export default function SelectComponent({
  id,
  defaultValue,
  value,
  options,
  select<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  disabled,
  ...props
}: {
  id?: string;
  defaultValue?: string;
  value?: string;
  options: { value: string; text: string }[];
  selectChangeHandler?: Function;
  disabled?: boolean;
  style?: any;
}) {
  return (
    <div id={id}>
      <Select
        className="select"
        defaultValue={defaultValue && defaultValue}
        value={value && value}
        disableUnderline
        onChange={selectChangeHandler ? (e) => selectChangeHandler(e) : null}
        disabled={disabled}
        {...props}
      >
        {options.map((option, index) => {
          return (
            <MenuItem key={index} value={option.value}>
              {parse(option.text)}
            </MenuItem>
          );
        })}
      </Select>
    </div>
  );
}
