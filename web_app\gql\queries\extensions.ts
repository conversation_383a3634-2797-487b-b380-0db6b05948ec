import { gql } from "@apollo/client";

const Rental_Extension_Request_Price = gql`
  query RentalExtensionRequestPrice(
    $dropOffDate: String!
    $dropOffTime: String!
    $paymentMethod: PaymentMethod
    $rentalId: ID!
    $withWallet: Boolean
  ) {
    rentalExtensionRequestPrice(
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      paymentMethod: $paymentMethod
      rentalId: $rentalId
      withWallet: $withWallet
    ) {
      extensionDays
      pricePerDay
      totalBookingPrice
      totalRemainingPrice
      walletAmount
    }
  }
`;

const Created_Rental_Extension_Request_Price = gql`
  query RentalDateExtensionRequest($id: ID!) {
    rentalDateExtensionRequest(id: $id) {
      extensionDays
      pricePerDay
      totalBookingPrice
      totalRemainingPrice
      walletAmount
    }
  }
`;

const Extension_Checkout_Id = gql`
  query ExtensionGetCheckoutId(
    $paymentBrand: RentalPaymentBrand
    $rentalExtensionId: ID!
    $withWallet: Boolean
  ) {
    extensionGetCheckoutId(
      paymentBrand: $paymentBrand
      rentalExtensionId: $rentalExtensionId
      withWallet: $withWallet
    ) {
      checkoutId
      integrity
    }
  }
`;
const Extension_Payment_Status = gql`
  query ExtensionGetPaymentStatus(
    $checkoutId: String!
    $rentalExtensionId: ID!
  ) {
    extensionGetPaymentStatus(
      checkoutId: $checkoutId
      rentalExtensionId: $rentalExtensionId
    ) {
      status
      rentalDateExtensionRequest {
        rental {
          isIntegratedRental
          rentalIntegrationStatus
        }
      }
      errors
    }
  }
`;

export {
  Rental_Extension_Request_Price,
  Created_Rental_Extension_Request_Price,
  Extension_Checkout_Id,
  Extension_Payment_Status,
};
