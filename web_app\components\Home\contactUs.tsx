import { Grid } from "@material-ui/core";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import ArrowForwardIcon from "@material-ui/icons/ArrowForward";
import ArrowBackIcon from "@material-ui/icons/ArrowBack";
import PopupForm from "./popupForm";
import ToAppPopup from "components/shared/toAppPopup";

const Div = styled.div`
  padding: 30px 0;
  .btn-wrap {
    display: flex;
    justify-content: flex-end;
    .contact-us-btn {
      width: 100%;
      /* max-width: 270px; */
      font-size: 17px;
      font-weight: 500;
      padding: 18px 20px;
      border: none;
      color: var(--color-4);
      background-color: var(--color-2);
      border-radius: var(--radius-1);
      display: grid;
      grid-template-columns: 1fr 20px;
      > span {
        text-align: start;
      }
      &:focus {
        outline: none;
      }
    }
  }

  #newsletter {
    border-radius: 15px;
    margin-top: 60px;
    > div {
      width: 100%;
      height: 100%;
      border-radius: inherit;
      @media (min-width: 960px) {
        padding: 50px 70px;
      }
      @media (max-width: 959px) {
        padding: 45px 30px;
        background-position: right;
      }
      background: url(/assets/images/contactus-bg.png);
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      h6 {
        text-transform: uppercase;
        color: var(--color-4);
        font-weight: 500;
        letter-spacing: 2px;
        margin-bottom: 30px;
      }
    }
    .subscribe-btn {
      pointer-events: none;
      width: 90%;
      max-width: 240px;
      margin: auto;
      font-size: 17px;
      font-weight: 500;
      padding: 18px 20px;
      border: none;
      color: var(--color-4);
      background-color: var(--color-1);
      border-radius: var(--radius-1);
      display: grid;
      grid-template-columns: 1fr 20px;
      > span {
        text-align: start;
      }
      &:focus {
        outline: none;
      }
    }
    input {
      width: 100%;
      border: none;
      border-bottom: solid 1px;
      background: none;
      padding: 10px 0;
      font-size: 22px;
      text-transform: capitalize;
      color: #92959a;
      @media (max-width: 959px) {
        margin-bottom: 20px;
      }
    }
  }
`;
export default function ContactUs() {
  const { t, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenToAppPopup, setIsOpenToAppPopup] = useState(false);

  return (
    <>
      <div
        onClick={() => {
          if (!isOpenToAppPopup) {
            setIsOpenToAppPopup(true);
          }
        }}
      >
        <Div>
          <Grid
            container
            justifyContent="space-between"
            alignItems="center"
            style={{ flexWrap: "wrap" }}
            spacing={2}
          >
            <Grid item xs={12} md={6}>
              <div className="section-fancy-title">
                <h2>{`${t("Contact Us")} ${t("For Any Questions")}`}</h2>
              </div>
            </Grid>
            <Grid item xs={12} md={3} className="btn-wrap cursor-pointer">
              <button
                className="contact-us-btn "
                type="button"
                // onClick={() => setIsOpen(true)}
              >
                <span>{t("Contact Us") as string}</span>
                {i18n.language === "ar" ? (
                  <ArrowBackIcon />
                ) : (
                  <ArrowForwardIcon />
                )}
              </button>
            </Grid>
          </Grid>
          {/* <div id="newsletter">
            <div>
              <h6>{t("You Can Follow Us")}</h6>
              <form>
                <Grid container alignItems="flex-end">
                  <Grid item xs={12} md={9}>
                    <input
                      type="email"
                      placeholder={t("Enter your email here")}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <button className="subscribe-btn" type="submit">
                      <span>{t("Subscribe")}</span>
                      {i18n.language === "ar" ? (
                        <ArrowBackIcon />
                      ) : (
                        <ArrowForwardIcon />
                      )}
                    </button>
                  </Grid>
                </Grid>
              </form>
            </div>
          </div> */}
        </Div>
      </div>
      <PopupForm isOpen={isOpen} setIsOpen={setIsOpen} />
      <ToAppPopup
        isOpenToAppPopup={isOpenToAppPopup}
        setIsOpenToAppPopup={setIsOpenToAppPopup}
      />
    </>
  );
}
