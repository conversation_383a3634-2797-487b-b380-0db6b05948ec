import Layout from "components/shared/layout";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  height: calc(100vh - 324px);
`;

function Error({ statusCode }) {
  const { t } = useTranslation();
  return (
    <Layout>
      <Div className="d-flex justify-content-center align-items-center">
        {statusCode ? (
          `${statusCode}`
        ) : (
          <h1 className="text-capitalize">{t("page not found")}</h1>
        )}
      </Div>
    </Layout>
  );
}

Error.getInitialProps = ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
};

export default Error;
