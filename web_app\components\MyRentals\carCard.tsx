/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @next/next/no-img-element */
import { useLazyQuery } from "@apollo/client";
import { Grid } from "@material-ui/core";
import AllyData from "components/CarDetails/allyData";
import Popup from "components/shared/popup";
import { Branch_Data } from "gql/queries/car";
import moment from "moment";
import { useRouter } from "next/router";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { setRentalBranchData } from "store/boooking/action";
import styled from "styled-components";
import MenuItem from "@material-ui/core/MenuItem";
import RateAlly from "components/shared/RateAlly";
import Tooltip from "components/shared/tooltip";
import CancelReasons from "components/shared/CancelReasons";
import { convertToEnglishNumbers } from "utilities/helpers";
import Link from "next/link";
import { installmentStatuses } from "utilities/enums";
import RiyalSymbol from "components/shared/RiyalSymbol";
const Div = styled(Grid)`
  position: relative;
  .alert {
    text-align: ${(props) => (props.lang === "ar" ? "right" : "left")};
  }
  .year {
    color: var(--color-12);
  }
  .price {
    display: flex;
    justify-content: end;
  }
  .price-label {
    color: var(--color-4);
    background-color: var(--color-2);
    padding: ${(props) =>
      props.lang === "ar" ? "7px 15px 15px 15px" : "10px 15px 10px 15px"};
    border-radius: ${(props) =>
      props.lang === "ar" ? "0 var(--radius-2)" : "var(--radius-2) 0"};
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    align-items: center;
    gap: 5px;
    min-width: 120px;

    span {
      display: block;
    }
    div:last-child {
      font-size: 22px;
      font-weight: bold;
    }
    div:first-child {
      font-size: 25px;
      width: max-content;
      text-align: start;
      /* transform: translateY(-4px); */
    }
  }
  border: ${(props) =>
    props.IsRed ? "3px solid red " : "3px solid var(--color-2) "};
  background-color: var(--color-4);
  cursor: pointer;
  border-radius: 22px;
  /* padding: 25px; */
  margin-bottom: 60px !important;
  h6:first-child {
    font-size: 15px;
    color: var(--color-12);
    font-weight: lighter;
    margin-bottom: 5px;
  }
  h6:last-child {
    font-size: 15px;
    font-weight: bold;
  }
  .car-box {
    text-transform: uppercase;
    border: solid 1px var(--color-10);
    border-radius: var(--radius-2);
    padding: 15px 20px;
    > div:first-child {
      border-bottom: solid 1px var(--color-10);
      margin-bottom: 15px;
      padding-bottom: 15px;
    }
    .booking-status {
      border-radius: var(--radius-1);
      padding: 3px 7px;
      &.confirmed {
        color: var(--color-3);
        background-color: #e5f4f8;
      }
      &.pending {
        background-color: #f2f3f5;
      }
      &.car_received {
        background-color: #fef0e3;
      }
      &.invoiced {
        background: #dff3e7;
      }
      &.cancelled {
        background: #fee6e6;
      }
      &.closed {
        background: #fee6e6;
      }
      span {
        font-size: 9px;
      }
    }
  }
  .booking-details {
    text-transform: capitalize;
    .booking-issue {
      position: relative;
      padding: 12px 20px;
      border-radius: var(--radius-2);
      p.error {
        color: var(--color-9);
        display: inline;
        &:after {
          content: "";
          background-color: var(--color-9);
          opacity: 0.07;
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          border-radius: var(--radius-2);
        }
      }
    }
    .grid {
      display: grid;
      margin-bottom: 20px;
      grid-template-columns: 1fr 1fr;
      grid-row-gap: 10px;
      grid-column-gap: 10px;
      @media (max-width: 900px) {
        margin-top: -10px;
        grid-template-columns: 1fr 1fr 1fr;
        margin-bottom: 5px;
      }
    }
  }
  > div:last-child {
    button {
      color: var(--color-3);
      border: none;
      font-size: 18px;
      font-weight: 600;
      padding: 15px 20px;
      border-radius: var(--radius-2);
      &:focus {
        outline: none;
      }
      &.branch-details {
        background-color: var(--color-13);
      }
      &.rent-to-own-logo {
        background-color: var(--color-7);
      }
      &.dots {
        background: transparent;
        border: solid 1px var(--color-10);
      }
    }
    .price-box {
      padding: 15px 20px;
      border-radius: var(--radius-2);
      .total-price {
        color: var(--color-3);
        font-size: 24px;
      }
      .daily-price {
        color: var(--color-14);
        font-size: 18px;
      }
    }
  }
  .price-more {
    @media (max-width: 768px) {
      margin-top: -50px;
    }
  }
`;

export default function CarCard(props) {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const [getBranchData] = useLazyQuery(Branch_Data);
  const [openPopup, setOpenPopup] = useState(false);
  const dispatch = useDispatch();
  const [openMenu, setOpenMenu] = useState(false);
  const [AllyRate, setAllyRatePopup] = useState(false);
  const [cancelReasonOpen, setCancelReasonOpen] = useState(false);

  const handleClose = () => {
    setOpenMenu(null);
  };

  return (
    <>
      <Div
        container
        className="car-card"
        justifyContent="space-between"
        lang={i18n.language}
        IsRed={
          props.status == "car_received" &&
          props.installments.filter((item) => item.status == "overdue")?.length
        }
        onClick={(e) => {
          e.stopPropagation();
          router.push(`/car-details?car=${props.carId}&bookingId=${props.id}`);
        }}
      >
        {/* {props.hasPendingExtensionRequests && (
          <Grid
            container
            item
            md={12}
            className="alert alert-warning mb-2"
            onClick={() =>
              router.push(
                `/car-details?car=${props.carId}&bookingId=${props.id}`
              )
            }
          >
            {i18n.language == "ar"
              ? `يوجد لديك طلب تمديد ${props.pendingExtensionRequest.requestNo} قيد الانتظار`
              : `You have a pending extension request ${props.pendingExtensionRequest.requestNo}`}
          </Grid>
        )} */}

        <Grid
          container
          item
          lg={8}
          xs={12}
          spacing={2}
          onClick={() =>
            router.push(`/car-details?car=${props.carId}&bookingId=${props.id}`)
          }
          style={{ padding: "24px" }}
        >
          <Grid item lg={4} xs={12}>
            <div className="car-box">
              <Grid container justifyContent="space-between">
                <Grid item>
                  <img
                    className="w-100 mb-2"
                    src={
                      props?.carImage?.length
                        ? props?.carImage
                        : `/assets/images/car.png`
                    }
                    alt="model"
                  />
                  <h5 className="bold">
                    {props?.[`${i18n.language}MakeName`]}{" "}
                    {props?.[`${i18n.language}ModelName`]}
                  </h5>
                  <h5 className="year">{props?.year}</h5>
                </Grid>
              </Grid>
              <Grid
                container
                justifyContent="space-between"
                alignItems="center"
              >
                <Grid item>
                  <h6>{t("Booking Id") as string}</h6>
                  <h6>{props?.bookingNo}</h6>
                </Grid>
                <Grid item className={`${props?.status} booking-status`}>
                  <span
                    className="font-weight-bold"
                    style={{
                      color:
                        props?.status == "pending"
                          ? "#9EA6A9"
                          : props?.status == "invoiced"
                          ? "#27AE60"
                          : props.status == "confirmed"
                          ? "#7AB3C5"
                          : props.status == "cancelled" ||
                            props.status == "closed"
                          ? "#F85959"
                          : "#FA9C3F",
                    }}
                  >
                    {props?.statusLocalized}
                  </span>
                </Grid>
              </Grid>
            </div>
          </Grid>
          <Grid
            item
            container
            justifyContent="space-between"
            className="w-auto booking-details"
            md={12}
            lg={8}
          >
            <Grid
              className="w-auto"
              item
              container
              direction="column"
              justifyContent="space-between"
            >
              <div className="grid">
                <div>
                  <h6>{t("Trip Date") as string}</h6>

                  <h6>
                    {convertToEnglishNumbers(
                      moment(props.pickUpDate)
                        .locale(i18n.language)
                        .format("Do MMMM YYYY")
                    )}
                    {props?.isRentToOwn ? null : " - "}
                    {props?.isRentToOwn
                      ? null
                      : convertToEnglishNumbers(
                          moment(props.dropOffDate)
                            .locale(i18n.language)
                            .format("Do MMMM YYYY")
                        )}
                  </h6>
                </div>
                <div>
                  <h6>{t("Duration") as string}</h6>
                  <h6>
                    <span>{props?.numberOfDays}</span>
                    <span>{` ${t("days")} `}</span>
                  </h6>
                </div>
                <div>
                  <h6>{t("Pick-up Car Location") as string}</h6>
                  <h6>
                    <img src="/assets/icons/location.svg" />
                    {"  "}
                    {props[`${i18n.language}PickUpCityName`]}
                  </h6>
                </div>
              </div>
            </Grid>
            <Grid className="w-100" item container direction="column">
              {props.hasPendingExtensionRequests ? (
                <div
                  className="alert alert-warning"
                  onClick={() =>
                    router.push(
                      `/car-details?car=${props.carId}&bookingId=${props.id}`
                    )
                  }
                >
                  {i18n.language == "ar"
                    ? `يوجد لديك طلب تمديد ${props.pendingExtensionRequest.requestNo} قيد الانتظار`
                    : `You have a pending extension request ${props.pendingExtensionRequest.requestNo}`}
                </div>
              ) : props?.status == "pending" &&
                (props.isPaid ||
                  props?.installments?.[0]?.status == "paid" ||
                  props?.installments?.[0]?.status == "partially_refunded" ||
                  props?.paymentMethod?.toLowerCase() == "cash") ? (
                <div className="alert alert-primary w-100">
                  {t("Your rental will be confirmed soon.") as string}
                  <button
                    onClick={() =>
                      router.push(
                        `/car-details?car=${props.carId}&bookingId=${props.id}`
                      )
                    }
                    className="btn btn-link"
                  >
                    {t("Details") as string}
                  </button>
                </div>
              ) : props.status == "confirmed" ? (
                <div className="alert alert-dark w-100">
                  {t("The car will be delivered on time") as string}
                  <button
                    className="btn btn-link"
                    onClick={() =>
                      router.push(
                        `/car-details?car=${props.carId}&bookingId=${props.id}`
                      )
                    }
                  >
                    {t("Details") as string}
                  </button>
                </div>
              ) : props.status == "car_received" &&
                props.installments.filter((item) => item.status == "overdue")
                  ?.length ? (
                <div className="alert alert-danger">
                  {
                    t(
                      "You have an overdue installment. Please settle it promptly."
                    ) as string
                  }
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(
                        `/car-details?car=${props.carId}&bookingId=${props.id}&PayNow=true`
                      );
                    }}
                    className="btn btn-link"
                  >
                    {t("Pay Now") as string}
                  </button>
                </div>
              ) : props.status == "car_received" &&
                props.installments.filter((item) => item.status == "due")
                  ?.length ? (
                <div className="alert alert-warning">
                  {t("You have a due installment") as string}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(
                        `/car-details?car=${props.carId}&bookingId=${props.id}&PayNow=true`
                      );
                    }}
                    className="btn btn-link"
                  >
                    {t("Pay Now") as string}
                  </button>
                  {/* <Invoice /> */}
                </div>
              ) : null}
            </Grid>
          </Grid>
        </Grid>
        <Grid
          item
          className="w-100 price-more"
          container
          md={4}
          direction="column"
          justifyContent="space-between"
          alignItems="flex-end"
          style={{ position: "relative" }}
        >
          <Grid
            className="w-auto gap-10px mb-1"
            item
            container
            alignItems="flex-start"
            justifyContent="flex-end"
            style={{ padding: "24px 0" }}
          >
            <div className="d-flex" style={{ gap: "10px" }}>
              {props?.isRentToOwn ? (
                <div>
                  <button className="rent-to-own-logo">
                    <img
                      src="/assets/images/rentToOwnDark.svg"
                      width={"100px"}
                    />
                  </button>
                </div>
              ) : null}

              <div>
                {props?.status === "confirmed" ||
                props?.status === "car_received" ? (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      getBranchData({
                        variables: { id: props?.dropOffBranchId },
                      }).then((res) => {
                        if (res?.data) {
                          dispatch(setRentalBranchData(res?.data?.branch));
                          setOpenPopup(true);
                        }
                      });
                    }}
                    className="branch-details"
                  >
                    {t("Branch Details") as string}
                  </button>
                ) : null}
              </div>
            </div>
            <div>
              {(props.status == "invoiced" && !props.allyRentalRate) ||
              props.status == "pending" ||
              (props.status == "confirmed" &&
                moment(Date.now())
                  .utc()
                  .diff(
                    moment(
                      `${props?.pickUpDate} ${props?.pickUpTime}`,
                      "YYYY-MM-DD HH:mm:ss"
                    ).utc(),
                    "hours"
                  ) < 0) ||
              (props.status == "car_received" &&
                props?.isRentToOwn &&
                !props?.installments?.filter((item) => item.status == "overdue")
                  ?.length) ? (
                <button
                  aria-controls="simple-menu"
                  aria-haspopup="true"
                  className="dots"
                  onClick={(e) => {
                    e.stopPropagation();
                    setOpenMenu(!openMenu);
                  }}
                >
                  <img src={`/assets/images/dots.svg`} alt="dots" />
                </button>
              ) : null}

              <div style={{ position: "relative", background: "red" }}>
                <Tooltip
                  isOpen={openMenu}
                  // position={i18n.language === "en" ? "right" : "left"}
                  color="#fff"
                >
                  {props.status == "invoiced" && !props.allyRentalRate ? (
                    <MenuItem
                      style={{ color: "#000" }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleClose();
                        setAllyRatePopup(true);
                      }}
                    >
                      {t("Rate Ally") as string}
                    </MenuItem>
                  ) : null}

                  {props.status == "pending" ||
                  props.status == "confirmed" ||
                  (props.status == "car_received" &&
                    props?.isRentToOwn &&
                    !props.installments.filter(
                      (item) => item.status == "overdue"
                    )?.length) ? (
                    <MenuItem
                      style={{ color: "#000" }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleClose();
                        setCancelReasonOpen(true);
                      }}
                    >
                      {t("Cancel Reservation") as string}
                    </MenuItem>
                  ) : null}
                </Tooltip>
              </div>
            </div>
            <div onClick={(e) => e.stopPropagation()}>
              <CancelReasons
                isOpen={cancelReasonOpen}
                setIsOpen={setCancelReasonOpen}
                rentalId={props.id}
                status={props.status}
                title={t("Cancel request")}
                isRentToOwn={props?.isRentToOwn}
              />
            </div>
            <div onClick={(e) => e.stopPropagation()}>
              <RateAlly
                isOpen={AllyRate}
                setIsOpen={setAllyRatePopup}
                title={t("Rate Ally")}
                rentalId={props.id}
              />
            </div>
          </Grid>

          <div
            className="d-flex justify-content-end align-items-center"
            style={{ gap: "10px" }}
          >
            <div className="price">
              <div className="font-18px price-label align-items-baseline">
                <div style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                  <RiyalSymbol />
                </div>
                <div style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                  {(props?.installments?.length &&
                  props?.status == "pending" &&
                  !props?.isRentToOwn
                    ? props?.installments[0].amount
                    : props?.isRentToOwn || props?.installments?.length
                    ? props?.installments
                        ?.filter(
                          (i) =>
                            i.status === installmentStatuses.paid.value ||
                            i.status ===
                              installmentStatuses.partially_refunded.value ||
                            i.status === installmentStatuses.not_collected.value
                        )
                        ?.reduce((total: any, current: any) => {
                          return total + current?.amount;
                        }, 0)
                    : props?.totalBookingPrice
                  )?.toFixed(2)}
                </div>
              </div>
            </div>
          </div>
        </Grid>
      </Div>
      <Popup isOpen={openPopup} setIsOpen={setOpenPopup}>
        <AllyData />
      </Popup>
    </>
  );
}
