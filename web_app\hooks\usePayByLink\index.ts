import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { useQuery } from "@apollo/client";
import {
  PaymentLinkDetails_Query,
  PaymentLinkCheckout_Query,
} from "gql/queries/paymentLink";
import { differenceInHours, differenceInMinutes, addHours } from "date-fns";

// Payment state enum
export enum PaymentState {
  LOADING = "loading",
  PENDING = "pending",
  FORM = "form",
  GET_STATUS = "get_status",
  ERROR = "error",
  INVALID_TOKEN = "invalid_token",
}

// Interface for the payment data
export interface PaymentData {
  bookingNumber: string;
  amount: number;
  currency: string;
  customerName: string;
  validUntil: string;
  installmentNumber?: string;
  extensionNumber?: string;
}

// Using a module-level variable to prevent duplicate initialization
// This works even in strict mode which double-invokes effects
const INITIALIZED_HOOKS = new Set<string>();

export const usePayByLink = () => {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const { token, status } = router.query;
  const hookId = useRef(`paylink-${Date.now()}`).current;

  // Track user activity and inactivity
  const lastActivityTime = useRef(Date.now());
  const inactivityTimeout = useRef<NodeJS.Timeout | null>(null);
  const wasInactive = useRef(false);

  const [paymentView, setPaymentView] = useState<PaymentState>(
    PaymentState.LOADING
  );
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [checkoutId, setCheckoutId] = useState<string>("");
  const [paymentBrand, setPaymentBrand] = useState<"MADA" | "CREDIT_CARD">(
    "CREDIT_CARD"
  );
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);

  // Setup the GraphQL queries with useQuery
  const {
    loading: detailsLoading,
    data: detailsData,
    error: detailsError,
    refetch: refetchDetails,
  } = useQuery(PaymentLinkDetails_Query, {
    fetchPolicy: "network-only",
    errorPolicy: "all",
    skip: !token || status === "get_status", // Skip the query if there's no token
    variables: token ? { token } : undefined,
  });

  const {
    loading: checkoutLoading,
    data: checkoutData,
    error: checkoutError,
    refetch: refetchCheckout,
  } = useQuery(PaymentLinkCheckout_Query, {
    fetchPolicy: "network-only",
    errorPolicy: "all",
    skip:
      !token ||
      !paymentBrand ||
      status === "get_status" ||
      !detailsData ||
      (detailsData?.paymentLinkDetails?.validForPayment === false &&
        detailsData?.paymentLinkDetails?.payable?.paymentStatus !==
          "pending_transaction" &&
        detailsData?.paymentLinkDetails?.payable?.paymentStatusCode !==
          "pending_transaction" &&
        detailsData?.paymentLinkDetails?.payable?.paymentStatusCode !==
          "pending_payment" &&
        !(
          detailsData?.paymentLinkDetails?.payable?.numberOfExtension &&
          detailsData?.paymentLinkDetails?.payable?.hasPendingPaymentTransaction
        )),
    variables: token && paymentBrand ? { token, paymentBrand } : undefined,
  });

  // Handle payment status (for redirects from payment gateway)
  const handlePaymentStatus = useCallback(
    (status: string) => {
      if (status === "get_status") {
        setPaymentView(PaymentState.GET_STATUS);
      } else {
        setPaymentView(PaymentState.ERROR);
        setErrorMessage(t("Payment failed. Please try again."));
      }
    },
    [t]
  );

  // Initialize payment process
  useEffect(() => {
    // Skip if router not ready or already initialized
    if (!router.isReady || INITIALIZED_HOOKS.has(hookId)) {
      return;
    }

    // Mark as initialized using the external Set
    INITIALIZED_HOOKS.add(hookId);

    // If we have a status parameter, handle payment status
    if (status) {
      handlePaymentStatus(status as string);
      return;
    }

    // No need to manually fetch details - useQuery will handle it with the skip option
  }, [router.isReady, token, status, handlePaymentStatus, hookId, detailsData]);

  // Process payment details and fetch checkout ID if needed
  useEffect(() => {
    // Skip if no data
    if (!detailsData?.paymentLinkDetails) {
      return;
    }

    const paymentLinkDetails = detailsData.paymentLinkDetails;

    if (
      !paymentLinkDetails.validForPayment &&
      paymentLinkDetails?.payable?.paymentStatus !== "pending_transaction" &&
      paymentLinkDetails?.payable?.paymentStatusCode !==
        "pending_transaction" &&
      paymentLinkDetails?.payable?.paymentStatusCode !== "pending_payment" &&
      !(
        paymentLinkDetails?.payable?.numberOfExtension &&
        paymentLinkDetails?.payable?.hasPendingPaymentTransaction
      )
    ) {
      setPaymentView(PaymentState.INVALID_TOKEN);
      setErrorMessage("انتهت صلاحية رابط الدفع");
      return;
    }

    // Extract relevant data
    const payable = paymentLinkDetails.payable;

    // Check payment status and set view to PENDING if applicable
    if (
      [payable?.paymentStatus, payable?.paymentStatusCode]?.includes(
        "pending_transaction"
      ) ||
      [payable?.paymentStatus, payable?.paymentStatusCode]?.includes(
        "pending_payment"
      ) ||
      payable?.hasPendingPaymentTransaction
    ) {
      setPaymentView(PaymentState.PENDING);
    }

    // Calculate remaining time until expiration (24 hours from creation)
    const createdAt = new Date(paymentLinkDetails.createdAt);
    const expiresAt = addHours(createdAt, 24);
    const now = new Date();

    let validUntil = "";
    const hoursRemaining = differenceInHours(expiresAt, now);
    const minutesRemaining = differenceInMinutes(expiresAt, now) % 60;

    if (i18n.language === "ar") {
      validUntil = `${hoursRemaining} ساعة و ${minutesRemaining} دقيقة`;
    } else {
      validUntil = `${hoursRemaining} hours and ${minutesRemaining} minutes`;
    }
    // Set payment data
    setPaymentData({
      bookingNumber: !payable?.extensionDays ? payable?.bookingNo : "",
      installmentNumber: !payable?.extensionDays
        ? payable?.installmentNumber
        : "",
      extensionNumber: payable?.extensionDays ? payable?.numberOfExtension : "",
      amount: payable?.totalAmountDue || payable?.amountDue || 0,
      currency: "SAR",
      customerName: payable.customerName || "",
      validUntil,
    });

    // No need to manually fetch checkout - useQuery will handle it with the skip option
  }, [detailsData, token, paymentBrand, i18n.language, checkoutId]);

  // Process checkout data when it's loaded
  useEffect(() => {
    if (!checkoutData?.paymentLinkCheckout) {
      return;
    }

    const { checkoutId: id } = checkoutData.paymentLinkCheckout;
    if (status === "get_status") {
      setPaymentView(PaymentState.GET_STATUS);
      return;
    }
    if (id) {
      setCheckoutId(id);
      setPaymentView(PaymentState.FORM);
    } else {
      setPaymentView(PaymentState.ERROR);
      setErrorMessage(t("Failed to get checkout ID"));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checkoutData, t]);

  // Handle errors from the details query
  useEffect(() => {
    if (detailsError) {
      setPaymentView(PaymentState.ERROR);
      setErrorMessage(t("An error occurred while processing your payment"));
    }
  }, [detailsError, t]);

  // Method to update payment brand and fetch new checkout ID
  const updatePaymentBrand = useCallback((brand: "MADA" | "CREDIT_CARD") => {
    setPaymentBrand(brand);
  }, []);

  // Set up user activity tracking
  useEffect(() => {
    // Function to handle user activity
    const handleUserActivity = () => {
      const currentTime = Date.now();
      const timeSinceLastActivity = currentTime - lastActivityTime.current;

      // If user was inactive for more than 15 minutes (900000 ms), refetch data
      if (wasInactive.current && timeSinceLastActivity > 900000) {
        refetchDetails();
        // Only refetch checkout if we have the required parameters
        if (token && paymentBrand) {
          refetchCheckout();
        }
      }

      // Reset inactivity flag and update last activity time
      wasInactive.current = false;
      lastActivityTime.current = currentTime;

      // Clear any existing timeout
      if (inactivityTimeout.current) {
        clearTimeout(inactivityTimeout.current);
      }

      // Set a new timeout to mark as inactive after 15 minutes
      inactivityTimeout.current = setTimeout(() => {
        wasInactive.current = true;
      }, 900000); // 15 minutes
    };

    // Add event listeners for user activity
    window.addEventListener("mousemove", handleUserActivity);
    window.addEventListener("keydown", handleUserActivity);
    window.addEventListener("click", handleUserActivity);
    window.addEventListener("touchstart", handleUserActivity);
    window.addEventListener("scroll", handleUserActivity);

    // Initialize activity tracking
    handleUserActivity();

    // Cleanup on unmount
    return () => {
      INITIALIZED_HOOKS.delete(hookId);
      window.removeEventListener("mousemove", handleUserActivity);
      window.removeEventListener("keydown", handleUserActivity);
      window.removeEventListener("click", handleUserActivity);
      window.removeEventListener("touchstart", handleUserActivity);
      window.removeEventListener("scroll", handleUserActivity);

      if (inactivityTimeout.current) {
        clearTimeout(inactivityTimeout.current);
      }
    };
  }, [hookId, refetchDetails, refetchCheckout, token, paymentBrand]);

  return {
    paymentView,
    errorMessage,
    checkoutId,
    paymentData,
    paymentBrand,
    updatePaymentBrand,
    isLoading: detailsLoading || checkoutLoading,
    checkoutData,
  };
};

export default usePayByLink;
