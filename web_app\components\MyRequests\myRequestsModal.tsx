import Popup from "components/shared/popup";
import styled from "styled-components";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import React from "react";
import { makeStyles } from "@material-ui/core/styles";
import List from "@material-ui/core/List";
import ListItem from "@material-ui/core/ListItem";
import ListItemIcon from "@material-ui/core/ListItemIcon";
import ListItemSecondaryAction from "@material-ui/core/ListItemSecondaryAction";
import ListItemText from "@material-ui/core/ListItemText";
import Checkbox from "@material-ui/core/Checkbox";
import IconButton from "@material-ui/core/IconButton";
import CommentIcon from "@material-ui/icons/Comment";
import { useMutation, useQuery, useLazyQuery } from "@apollo/client";
import { Snackbar } from "@material-ui/core";
import CloseIcon from "@material-ui/icons/Close";

import { CancelRequest_Mutation } from "gql/mutations/cancelRequest";
const useStyles = makeStyles((theme) => ({
  root: {
    width: "100%",
    backgroundColor: theme.palette.background.paper,
  },
}));
const DIV = styled.div`
  border-top: 1px solid #000;
  display: flex;
  flex-direction: row-reverse;
  .close-btn {
    min-width: 100px;
    max-width: 120px;
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
    margin-top: 15px;
    margin-left: 2px;
  }
  .confirm-btn {
    min-width: 100px;
    max-width: 120px;
    color: #fff;
    background-color: #198754;
    border-color: #198754;
    margin-top: 15px;
    margin-left: 2px;
  }
`;
const AdditionalNotesWrapper = styled.div`
  textarea {
    width: 100%;
    height: 150px;
    resize: none;
    border-radius: var(--radius-3);
    border-color: var(--color-7);
    padding: 15px;
  }
`;
export default function MyrequestesModal({
  setOpened,
  open,
  requestId,
  cancelreasosnes,
  refetch,
}) {
  const classes = useStyles();
  const { t, i18n } = useTranslation();
  const [snackBarClosed, setSnackBarClosed] = useState(true);

  const [cancelRequest] = useMutation(CancelRequest_Mutation);

  const [checked, setChecked] = React.useState([]);
  const [cancelreason, setCancelReason] = useState("");
  const [snackBarMessage, setSnackBarMessage] = useState("");

  const handleToggle = (value) => () => {
    const currentIndex = checked.indexOf(+value);
    const newChecked = [...checked];

    if (currentIndex === -1) {
      newChecked.push(+value);
    } else {
      newChecked.splice(currentIndex, 1);
    }

    setChecked(newChecked);
  };
  const CancelRequest = () => {
    cancelRequest({
      variables: {
        businessRentalId: requestId,
        cancelledReason: cancelreason,
        cancelledReasons: checked,
      },
    }).then(() => {
      setSnackBarMessage(t("request.has.been.canceled.successfully"));
      setSnackBarClosed(false);

      refetch();
    });
  };
  function handleCloseSnackBar() {
    setSnackBarClosed(true);
  }
  const action = (
    <>
      <IconButton
        size="small"
        aria-label="close"
        color="inherit"
        onClick={handleCloseSnackBar}
      >
        <CloseIcon fontSize="small" />
      </IconButton>
    </>
  );
  return (
    <Popup
      isOpen={open}
      setIsOpen={() => setOpened(false)}
      title={t("Cancel.request")}
      isWidth={true}
    >
      <div className="">
        <List className={classes.root}>
          {cancelreasosnes?.map((reasone, idx) => {
            const labelId = `checkbox-list-label-${reasone.id}`;

            return (
              <ListItem
                key={reasone.id}
                role={undefined}
                dense
                button
                onClick={handleToggle(reasone.id)}
                style={{ textAlign: i18n.language == "ar" ? "right" : "left" }}
              >
                <ListItemIcon>
                  <Checkbox
                    edge="start"
                    checked={checked.indexOf(+reasone.id) !== -1}
                    tabIndex={-1}
                    disableRipple
                    inputProps={{ "aria-labelledby": labelId }}
                  />
                </ListItemIcon>
                <ListItemText
                  id={labelId}
                  primary={`${reasone[`${i18n.language}Body`]}`}
                />
              </ListItem>
            );
          })}
        </List>
      </div>

      <AdditionalNotesWrapper>
        <textarea
          maxLength={100}
          placeholder={t("Other")}
          onChange={(e) => setCancelReason(e.target.value)}
        />
      </AdditionalNotesWrapper>

      <DIV>
        <button
          className="close-btn btn btn-danger"
          onClick={() => setOpened(false)}
        >
          {t("close")}
        </button>
        <button
          className="confirm-btn btn btn-info"
          disabled={!checked.length}
          onClick={() => CancelRequest()}
        >
          {t("Confirm")}
        </button>
      </DIV>
      <Snackbar
        open={!snackBarClosed}
        autoHideDuration={6000}
        onClose={handleCloseSnackBar}
        message={snackBarMessage}
        action={action}
      />
    </Popup>
  );
}
