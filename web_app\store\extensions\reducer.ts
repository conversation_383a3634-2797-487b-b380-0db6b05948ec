import {
  CREATING_EXTENSION,
  PAY_EXTENSION,
  SUCCESSFUL_EXTENSION_CREATED,
  CLEAR_EXTENSION_DATA,
  EXTENSION_PRICE,
  EXTENSION_WITH_WALLET_ACTION,
} from "./action";

export default function ExtensionsReducer(state = {}, action) {
  switch (action.type) {
    case CREATING_EXTENSION:
      return { ...state, is_creating_online_extension_requset: action.payload };
    case PAY_EXTENSION:
      return { ...state, is_extension_requset_paid: action.payload };
    case SUCCESSFUL_EXTENSION_CREATED:
      return {
        ...state,
        is_extension_requset_created_successfully: action.payload,
      };
    case EXTENSION_PRICE:
      return {
        ...state,
        total_remaining_price: action.payload,
      };
    case EXTENSION_WITH_WALLET_ACTION:
      return {
        ...state,
        extension_With_wallet_action: action.payload,
      };
    case <PERSON><PERSON>AR_EXTENSION_DATA:
      return {
        is_extension_requset_created_successfully: undefined,
        is_creating_online_extension_requset: undefined,
        is_extension_requset_paid: undefined,
        total_remaining_price: undefined,
        extension_With_wallet_action: undefined,
      };
    default:
      return state;
  }
}
