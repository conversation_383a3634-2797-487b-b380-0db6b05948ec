.payment-status-modal {
  padding: 20px;
  text-align: center;

  .status-content {
    max-width: 400px;
    margin: 0 auto;
  }

  .status-header {
    margin-bottom: 24px;

    .status-icon {
      font-size: 64px;
      margin-bottom: 16px;
      display: block;

      &.success {
        color: #28a745;
      }

      &.error {
        color: #dc3545;
      }

      &.pending {
        color: #ffc107;
        animation: pulse 2s infinite;
      }

      &.default {
        color: #6c757d;
      }

      &.loading {
        color: #007bff;
      }
    }

    .status-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;

      &.success {
        color: #28a745;
      }

      &.error {
        color: #dc3545;
      }

      &.pending {
        color: #ffc107;
      }

      &.default {
        color: #6c757d;
      }
    }
  }

  .error-details,
  .success-details,
  .pending-details {
    margin-bottom: 24px;
    text-align: left;

    .alert {
      margin: 0;
      border-radius: 8px;
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.5;

      strong {
        font-weight: 600;
      }
    }
  }

  .status-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;

    .btn {
      min-width: 120px;
      padding: 10px 20px;
      font-weight: 500;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }

      .material-icons {
        font-size: 18px;
      }
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.status-icon.loading {
  animation: spin 1s linear infinite;
}

// Responsive design
@media (max-width: 768px) {
  .payment-status-modal {
    padding: 16px;

    .status-header {
      margin-bottom: 20px;

      .status-icon {
        font-size: 48px;
        margin-bottom: 12px;
      }

      .status-title {
        font-size: 16px;
      }
    }

    .error-details,
    .success-details,
    .pending-details {
      margin-bottom: 20px;

      .alert {
        padding: 10px 12px;
        font-size: 13px;
      }
    }

    .status-actions {
      flex-direction: column;
      align-items: stretch;

      .btn {
        min-width: auto;
        width: 100%;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .payment-status-modal {
    .error-details,
    .success-details,
    .pending-details {
      text-align: right;
    }

    .status-actions {
      .btn {
        flex-direction: row-reverse;
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .payment-status-modal {
    .status-icon {
      &.success {
        color: #006600;
      }

      &.error {
        color: #cc0000;
      }

      &.pending {
        color: #cc8800;
      }
    }

    .status-title {
      &.success {
        color: #006600;
      }

      &.error {
        color: #cc0000;
      }

      &.pending {
        color: #cc8800;
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .payment-status-modal {
    color: #e2e8f0;

    .status-header {
      .status-title {
        &.default {
          color: #a0aec0;
        }
      }
    }

    .error-details,
    .success-details,
    .pending-details {
      .alert {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: #e2e8f0;

        &.alert-success {
          background-color: rgba(40, 167, 69, 0.2);
          border-color: rgba(40, 167, 69, 0.3);
        }

        &.alert-danger {
          background-color: rgba(220, 53, 69, 0.2);
          border-color: rgba(220, 53, 69, 0.3);
        }

        &.alert-info {
          background-color: rgba(0, 123, 255, 0.2);
          border-color: rgba(0, 123, 255, 0.3);
        }
      }
    }
  }
}

// Print styles
@media print {
  .payment-status-modal {
    .status-actions {
      display: none;
    }

    .status-icon {
      color: #000 !important;
    }

    .status-title {
      color: #000 !important;
    }
  }
}
