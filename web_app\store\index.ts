// /* eslint-disable import/no-anonymous-default-export */

import { composeWithDevTools } from "redux-devtools-extension";
import { createStore, applyMiddleware } from "redux";
import { persistStore, persistReducer } from "redux-persist"; // defaults to localStorage for web

import rootReducer from "./rootReducer";
import session from "redux-persist/lib/storage/session";

const persistConfig = {
  key: "root",
  storage: session,
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

let store = createStore(
  persistedReducer,
  composeWithDevTools(applyMiddleware())
);
let persistor = persistStore(store);

export function useStore() {
  return { store, persistor };
}
