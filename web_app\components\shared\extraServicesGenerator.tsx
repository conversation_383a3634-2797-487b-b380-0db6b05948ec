import React from "react";
import { useTranslation } from "react-i18next";
import RiyalSymbol from "./RiyalSymbol";

export default function useExtraServicesGenerator() {
  const { t, i18n } = useTranslation();

  function getAllExtraServices(data) {
    if (data) {
      return [
        {
          id: "allowedKM",
          extraService: {
            iconUrl: "/assets/icons/allowedKM.png",
            title: t("Allowed kilometer per day"),
          },
          serviceValue: data?.distanceByDay,
          subtitle: `${data?.distanceByDay} ${t("km/d")}`,
          hide: !data?.distanceByDay,
        },
        {
          id: "isUnlimitedFree",
          extraService: {
            iconUrl: "/assets/icons/unlimited.png",
            title: t("Unlimited Kilometer Free"),
          },
          serviceValue: 0,
          hide: !data?.isUnlimitedFree,
        },
        {
          id: "unlimitedKMCost",
          extraService: {
            iconUrl: "/assets/icons/unlimitedCost.png",
            title: t("Unlimited Kilometer Cost"),
          },
          serviceValue: data?.unlimitedFeePerDay,
          subtitle: `${data?.unlimitedFeePerDay} ${t("SR/day")}`,
          hide: data?.isUnlimitedFree || !data?.isUnlimited,
        },
        {
          id: "extraKMCost",
          extraService: {
            iconUrl: "/assets/icons/extraKMCost.png",
            title: t("Extra Kilometer Cost"),
          },
          serviceValue: data?.additionalDistanceCost,
          subtitle: (
            <div className="d-flex gap-5px">
              <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                <RiyalSymbol />
              </span>
              <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                {data?.additionalDistanceCost} / KM
              </span>
            </div>
          ),
          hide: !data?.additionalDistanceCost,
        },
        {
          id: "fullInsurance",
          extraService: {
            iconUrl: "/assets/icons/insurance.png",
            title: t("Full Insurance Per Day"),
          },
          serviceValue: data?.carInsurances?.find((i) => i.insuranceId == 2)
            ?.value,
          subtitle: `${
            data?.carInsurances?.find((i) => i.insuranceId == 2)?.value
          } ${t("SR/day")}`,
          hide: !data?.carInsurances?.find((i) => i.insuranceId == 2),
        },
        {
          id: "standardInsurance",
          extraService: {
            iconUrl: "/assets/icons/insurance.png",
            title: t("Standard Insurance Deductible"),
          },
          serviceValue: data?.carInsurances?.find((i) => i.insuranceId == 1)
            ?.value,
          subtitle: (
            <div className="d-flex gap-5px">
              <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                <RiyalSymbol />
              </span>
              <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                {data?.carInsurances?.find((i) => i.insuranceId == 1)?.value}
              </span>
            </div>
          ),
          hide: !data?.carInsurances?.find((i) => i.insuranceId == 1),
        },

        ...data?.branch?.allyCompany?.allyExtraServicesForAlly?.filter(
          (i) => i.isActive == true
        ),
        ...data?.branch?.branchExtraServices?.filter((i) => i.isActive == true),
        {
          id: "canHandoverInAntherCity",
          extraService: {
            iconUrl: "/assets/icons/GPS.png",
            title: t("Car Return In Another City"),
          },
          serviceValue: 0,
          hide: !data?.branch?.allyCompany?.canHandoverInAntherCity,
        },
      ];
    }
  }
  function getExtraServicesPrice(data: any = {}) {
    if (data) {
      return [
        {
          id: "isUnlimited",
          extraService: {
            iconUrl: "/assets/icons/unlimited.png",
            title: t("Unlimited Kilometers"),
            description:
              i18n.language === "en"
                ? "This service is available to some of our allies, and it does not count the extra kilometers, and there may be additional fees for this service that are added to the rental value for each day"
                : "هذه الخدمة متاحة لدى بعض حلفائنا وهي عدم احتساب الكيلومترات الزائدة وقد توجد رسوم إضافية على هذه الخدمة تضاف لقيمة التأجير لكل يوم",
          },
          serviceValue: data?.isUnlimitedFree ? 0 : data?.unlimitedFeePerDay,
          totalServiceValue: data?.isUnlimitedFree
            ? 0
            : data?.totalUnlimitedFee,
          subtitle: `${data?.unlimitedFeePerDay} ${t("SR/day")}`,
          hide: !data?.isUnlimited,
        },
        ...(data?.allyExtraServices?.map((i: any) => {
          return {
            ...i,
            type: "allyExtraService",
          };
        }) || []),
        ...(data?.branchExtraServices?.map((i: any) => {
          return {
            ...i,
            type: "branchExtraService",
          };
        }) || []),
      ];
    }
  }

  function getCarExtraServices(data) {
    if (data) {
      return [
        {
          id: "isUnlimited",
          extraService: {
            iconUrl: "/assets/icons/unlimited.png",
            title: t("Unlimited Kilometers"),
            description:
              i18n.language === "en"
                ? "This service is available to some of our allies, and it does not count the extra kilometers, and there may be additional fees for this service that are added to the rental value for each day"
                : "هذه الخدمة متاحة لدى بعض حلفائنا وهي عدم احتساب الكيلومترات الزائدة وقد توجد رسوم إضافية على هذه الخدمة تضاف لقيمة التأجير لكل يوم",
          },
          serviceValue: data?.unlimitedFeePerDay,
          subtitle: `${data?.unlimitedFeePerDay} ${t("SR/day")}`,
          hide: !data?.isUnlimited,
        },
        ...data?.branch?.allyCompany?.allyExtraServicesForAlly
          ?.filter((i) => i.isActive == true)
          ?.map((i) => {
            return {
              ...i,
              type: "allyExtraService",
            };
          }),
        ...data?.branch?.branchExtraServices
          ?.filter((i) => i.isActive == true)
          ?.map((i) => {
            return {
              ...i,
              type: "branchExtraService",
            };
          }),
      ];
    }
  }
  return { getAllExtraServices, getCarExtraServices, getExtraServicesPrice };
}
