/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @next/next/no-img-element */
/* eslint-disable react/jsx-key */
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { Grid } from "@material-ui/core";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { GoToLogin } from "store/authentication/action";
import { useRouter } from "next/router";
import Card from "@material-ui/core/Card";
import CardContent from "@material-ui/core/CardContent";
import DeleteIcon from "@material-ui/icons/Delete";
import AddIcon from "@material-ui/icons/Add";
import BorderColorIcon from "@material-ui/icons/BorderColor";
import { DeleteBooking, EditBooking, RemoveList } from "store/boooking/action";
import Link from "next/link";
import "moment/locale/en-in";
import "moment/locale/ar";
import Swal from "sweetalert2";
import { useMutation, useQuery, useLazyQuery } from "@apollo/client";
import { CreateBusinessRental_Mutation } from "gql/mutations/businessRental";
import { useState } from "react";
import { useSnackbar } from "notistack";

const Hashtag = styled.div`
  color: #000;
  margin: 20px 0px;
`;
const PRIVACY = styled.p`
  font-size: ${(props) => (props.language === "ar" ? "12px" : "11px")};
  margin-bottom: 3px !important;
  text-align: center !important ;
`;
const DIV = styled.div`
  .button:focus {
    outline: none;
  }
  .MuiCard-root {
    border: 1px solid #ccc;
    border-radius: 12px;
    line-height: 1.3;
  }
  .privacy-link {
    color: #7ab3c5;
  }
  .actions {
    alig-self: center !important;
  }
  .delete-btn {
    color: #000;
    border: none;
    background: none;
  }
  .edit-btn:focus {
    outline: none;
    border: none;
  }
  .delete-btn:focus {
    outline: none;
    border: none;
  }
  .edit-btn {
    color: #000;
    border: none;
    background: none;
  }
  .add-btn {
    border: 1px solid #000;
    border-radius: 8px;
    background: none;
    padding: 10px;
  }
  .request-title {
    font-weight: bold;
  }
  button.order {
    width: 100%;
    padding: 10px 0px;
    border: none;
    box-shadow: none;
    border-radius: var(--radius-3);
    background-color: var(--color-3);
    color: var(--color-4);
    font-weight: 500;
  }
  .title {
    color: var(--color-3);
  }
  .content {
    padding: 20px 0px;
  }
  .btn-link {
    color: #000;
  }
`;
const Steppeing = styled.div`
  min-width: 300px;
  .container {
    width: 100%;
    margin-top: 10px;
  }
  .progressbar {
    counter-reset: step;
  }
  .progressbar li {
    list-style: none;
    display: inline-block;
    width: 30.33%;
    position: relative;
    cursor: pointer;
  }
  .progressbar li:before {
    content: "";
    counter-increment: step;
    width: 8px;
    height: 8px;
    border: 1px solid #ddd;
    border-radius: 100%;
    display: block;
    right: 0;
    background-color: #fff;
    position: relative;
    z-index: 9;
  }
  .progressbar li:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #eff1f5;
    top: 2px;
    left: ${(props) => (props.language == "en" ? "-100%" : "100%")};
  }
  .progressbar li:first-child:after {
    content: none;
  }
  .progressbar li.active {
    color: var(--color-2);
  }
  .progressbar li.active:before {
    border-color: var(--color-2);
  }

  .progressbar li.active:before {
    background-color: var(--color-2) !important;
  }
  .progressbar li span {
    transform: ${(props) =>
      props.language == "en" ? "translateX(-30px)" : "translateX(30px)"};
    display: inline-block;
    width: 100%;
    text-align: center;
  }
  .progressbar li.active {
    color: var(--color-2);
  }
  .progressbar li.end {
    color: #7ab3c5;
  }
  .progressbar li.active:before {
    border-color: var(--color-2);
  }
  .progressbar li.end:before {
    background-color: #7ab3c5 !important;
  }
  .progressbar li.active:before {
    background-color: var(--color-2) !important;
  }
`;
const Head = styled.div`
  @media (max-width: 768px) {
    flex-direction: column;
    ul,
    .container {
      padding: 0;
      margin: 0;
    }
  }
`;
const TITLE = styled.div`
  min-width: 170px;
`;
export default function Step3() {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const state = useSelector((state: RootStateOrAny) => state.authentication);
  const { enqueueSnackbar } = useSnackbar();
  const BookingState = useSelector((state: RootStateOrAny) => state.booking);
  const [
    createBusinessRental,
    { data: businessRentalRes, loading: businessRentalResLoading },
  ] = useMutation(CreateBusinessRental_Mutation, { errorPolicy: "all" });
  const dispatch = useDispatch();
  const DeleteBookingList = (index) => {
    Swal.fire({
      titleText: t("Are you sure you want to delete this car?"),

      icon: "warning",
      showCancelButton: true,

      // buttons: [t("cancel"), t("delete")],
      confirmButtonText: t("Yes"),
      cancelButtonText: t("No"),
      cancelButtonColor: "red",
    }).then((result) => {
      if (result.isConfirmed) {
        dispatch(DeleteBooking(index));
        Swal.fire({
          icon: "success",
          title: t("Done successfully"),
          showConfirmButton: false,
          timer: 1500,
        });
      }
    });
    // dispatch(DeleteBooking(index))
  };
  const handelCreateRent = () => {
    for (let i = 0; i < BookingState.list.length; i++) {
      createBusinessRental({
        variables: {
          additionalNotes: BookingState.list[i].additionalNotes,
          carModelId: BookingState.list[i].carModelId,
          carVersionId: BookingState.list[i].carVersionId,
          insuranceId: BookingState.list[i].insuranceId,
          makeId:
            +BookingState.list[i].makeId == 999
              ? null
              : BookingState.list[i].makeId,
          numberOfCars: BookingState.list[i].numberOfCars,
          otherCarName:
            +BookingState.list[i].makeId == 999
              ? BookingState.list[i].car_name
              : null,
          numberOfMonths: BookingState.list[i].numberOfMonths,
          pickUpCityId:
            +BookingState.list[i].pickUpCityId == 999
              ? undefined
              : BookingState.list[i].pickUpCityId,
          otherCityName:
            +BookingState.list[i].pickUpCityId == 999
              ? BookingState.list[i].city_name
              : undefined,
          pickUpDatetime: BookingState.list[i].pickUpDatetime,
          userId: +BookingState.list[i].userId,
        },
      }).then((res) => {
        if (!res?.errors) {
          dispatch(RemoveList());
          router.push("/carwah-business#success");
        } else {
          enqueueSnackbar(res.errors[0].message, { variant: "error" });
        }
      });
    }
  };

  return (
    <div>
      <Head className="d-flex justify-content-between">
        <TITLE>
          <h2 className="font-27px bold mb-3">{t("Request_Summary")}</h2>
        </TITLE>
        <Steppeing language={i18n.language}>
          <div className="container">
            <ul className="progressbar text-align-localized">
              <li
                className="end"
                onClick={() => router.push("carwah-business#step1")}
              >
                <span>{t("step1") as string}</span>
              </li>
              <li
                className="end"
                onClick={() => {
                  if (
                    BookingState?.list?.length &&
                    BookingState?.BookingList?.length
                  ) {
                    router.push("carwah-business#step2");
                  }
                }}
              >
                <span>{t("step2") as string}</span>
              </li>
              <li
                className="active"
                onClick={() => router.push("carwah-business#step3")}
              >
                <span>{t("step3") as string}</span>
              </li>
            </ul>
          </div>
        </Steppeing>
      </Head>

      <DIV>
        <Grid
          container
          direction="row"
          justifyContent="center"
          alignItems="center"
          xs={12}
          md={12}
          className="form-wrapper mt-4"
        >
          {BookingState.BookingList?.map((booking, index) => (
            <Grid xs={10} md={10} className="mb-3">
              <Card variant="outlined" className="shadow-sm">
                <CardContent className="d-flex">
                  <Grid item md={8} xs={8}>
                    <p className="request-title">{`${t("car_no")} ${
                      index + 1
                    }`}</p>
                    <p>
                      {!booking?.car_name || booking.makeId != 999 ? (
                        <span>
                          {booking.makeId?.[`${i18n.language}Name`]}{" "}
                          {booking.carModelId?.[`${i18n.language}Name`]}{" "}
                          {booking.carVersionId}
                          {", "}
                        </span>
                      ) : (
                        <span>{booking.car_name},</span>
                      )}
                      {booking?.car_name ? (
                        <span>{booking?.car_name},</span>
                      ) : null}

                      <span> {t("count") + " " + booking.numberOfCars} </span>
                    </p>
                    <p>
                      <span>
                        {booking.pickUpCityId?.[`${i18n.language}Name`]},{" "}
                        {booking.insuranceType?.[`${i18n.language}Name`]},
                      </span>
                      <span className="d-inline-block">
                        {booking.numberOfMonths == 1
                          ? t("month")
                          : booking.numberOfMonths == 2
                          ? t("2.month")
                          : booking.numberOfMonths >= 3 &&
                            booking.numberOfMonths <= 10
                          ? ` ${booking.numberOfMonths} ` + t("months")
                          : booking.numberOfMonths > 10
                          ? ` ${booking.numberOfMonths} ` + t("b.month")
                          : null}
                        ,
                      </span>
                      <span>{booking.pickUpDatetime}</span>
                    </p>
                  </Grid>
                  <Grid
                    item
                    md={4}
                    xs={4}
                    style={{ alignSelf: "center" }}
                    className="actions"
                  >
                    <button
                      className="delete-btn"
                      onClick={() => DeleteBookingList(index)}
                    >
                      <img
                        className="d-block"
                        src="/assets/images/deleteicon.svg"
                      />
                      {t("Delete")}
                    </button>
                    <button
                      className="edit-btn"
                      onClick={() => {
                        dispatch(EditBooking(index));
                        setTimeout(
                          () => router.push("carwah-business#step2"),
                          1000
                        );
                      }}
                    >
                      <img
                        className="d-block"
                        src="/assets/images/editicon.svg"
                      />
                      {t("btn.edit")}
                    </button>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
        {
          <Grid
            container
            justifyContent="center"
            direction="row"
            className="mb-3"
          >
            <Grid item md={10} xs={10}>
              <button
                className="add-btn"
                onClick={() => router.push("carwah-business#step2")}
                disabled={BookingState.list?.length >= 10}
                title={
                  BookingState.list?.length >= 10 &&
                  t("Max. no of requests is 10 per time")
                }
                style={{ float: i18n.language == "en" ? "right" : "left" }}
              >
                <AddIcon /> {t("add")}
              </button>
            </Grid>
          </Grid>
        }

        <Grid
          container
          direction="row"
          justifyContent="center"
          alignItems="center"
          xs={12}
          md={12}
          className="form-wrapper"
        >
          <Grid item xs={10} md={10}>
            <PRIVACY language={i18n.language}>
              {t("By_submitting_you_agree_to_the")}{" "}
              <Link href="terms-conditions" prefetch={false}>
                <a className="privacy-link">{t("Terms_and_Conditions")}</a>
              </Link>{" "}
              {t("and")}
              <Link href="privacy-policy" prefetch={false}>
                <a className="privacy-link">{t("Privacy_Policy")}</a>
              </Link>
            </PRIVACY>
          </Grid>
          <Grid item xs={10} md={10}>
            <button
              className="order"
              onClick={() => {
                !state?.login_data?.token
                  ? dispatch(GoToLogin(true))
                  : handelCreateRent();
              }}
            >
              {t("Submit_Request")}
            </button>
          </Grid>
          <Grid item xs={8} md={8}>
            <button
              className="btn btn-link w-100"
              onClick={() => router.push("carwah-business#step2")}
            >
              {t("back")}
            </button>
          </Grid>
        </Grid>
      </DIV>
    </div>
  );
}
