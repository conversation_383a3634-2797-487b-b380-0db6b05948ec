import { useRouter } from "next/router";
import { RootStateOrAny, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import useExtraServicesGenerator from "components/shared/extraServicesGenerator";
import { installmentStatuses } from "utilities/enums";
import { useState, useMemo, useCallback } from "react";

// Types for better organization
type InstallmentData = {
  status: string;
  amount: number;
  amountDue: number;
  payable?: boolean;
};

type InstallmentsInvoiceData = {
  paidInstallmentsCount: number;
  notPaidInstallmentsCount: number;
  totalInstallmentsCount: number;
  totalPaidAmount: number;
  nextInstallmentToPay?: number;
  nextInstallmentAmountToPay?: number;
  upcomingInstallmentToPay?: number;
  upcomingInstallmentAmountToPay?: number;
  totalnextInstallmentToPay: number;
};

function useLogic(isIntallmentDetailsModal = false) {
  // Hooks
  const { t, i18n } = useTranslation();
  const { getExtraServicesPrice } = useExtraServicesGenerator();
  const router = useRouter();
  const bookingId = router?.query.bookingId;
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  // Redux state selectors
  const wallet = useSelector((state: RootStateOrAny) => state.wallet) || {};
  const booking = useSelector((state: RootStateOrAny) => state.booking) || {};
  const cars = useSelector((state: RootStateOrAny) => state.cars) || {};
  const coupons = useSelector((state: RootStateOrAny) => state?.coupons) || {};
  const installmentsState =
    useSelector((state: RootStateOrAny) => state.installments) || {};

  // Extract wallet data
  const { pay_with_wallet, balance } = wallet;
  const { pay_with_installments } = installmentsState;
  const { coupon_data } = coupons;

  // Extract booking data
  const { rental_about_price: rentalAboutPrice, rentalDetails } = booking || {};

  // Extract cars data
  const { about_rent_price: aboutRentPrice } = cars || {};

  // Extract about rent price details
  const {
    dailyPrice,
    numberOfDays,
    priceBeforeDiscount,
    discountValue,
    discountType,
    discountPercentage,
    priceBeforeInsurance,
    insuranceValue,
    deliveryPrice,
    handoverPrice,
    addsPrice,
    allyExtraServices,
    branchExtraServices,
    isUnlimited: isUnlimited_aboutRentPrice,
    isUnlimitedFree: isUnlimitedFree_aboutRentPrice,
    unlimitedFeePerDay: unlimitedFeePerDay_aboutRentPrice,
    totalUnlimitedFee: totalUnlimitedFee_aboutRentPrice,
    priceBeforeTax,
    valueAddedTaxPercentage,
    taxValue,
    totalPrice,
    totalBookingPrice,
    pricePerDay,
    couponCode,
    couponDiscount,
    isCouponApplied,
    couponErrorMessage: aboutPriceCouponErrorMessage,
    totalAmountDue,
    installmentsBreakdown,
    rentToOwnInstallmentBreakdown,
    walletAmount,
    remainingDueInstallmentsAmount,
  } = aboutRentPrice || {};

  // Extract rental about price details
  const {
    installments: installmentsFromRentalAbout,
    totalAmountDue: totalAmountDue_rentalAboutPric,
    walletAmount: walletAmountDetails,
    remainingDueInstallmentsAmount: remainingDueInstallmentsAmountDetails,
    remainingInstallmentsAmount: remainingInstallmentsAmountDetails,
    totalInstallmentsAmount,
    couponErrorMessage: rentalAboutPriceCouponErrorMessage,
    ownCarDetails,
    totalExtensionsDays,
    totalExtensionsPrice,
    priceBeforeTax: priceBeforeTax_rentalAbouPrice,
    priceBeforeInsurance: priceBeforeInsurance_rentalAboutPrice,
    totalWalletPaidAmount: totalWalletPaidAmount_rentalAboutPrice,
  } = rentalAboutPrice || {};

  // Extract rental details
  const {
    isRentToOwn: isRentToown_details,
    isPaid,
    rentalExtraServices,
    paymentStatusCode,
    paymentStatusMessage,
    installmentsFullyPaid,
    paidInstallmentsCount,
    totalAmountDue: totalAmountDueFromRentalDetails,
    totalWalletPaidAmount: totalWalletPaidFromRentalDetails,
    payable,
  } = rentalDetails || {};

  // Derived values
  const isUnlimited = isUnlimited_aboutRentPrice || rentalDetails?.isUnlimited;
  const isUnlimitedFree =
    isUnlimitedFree_aboutRentPrice || rentalDetails?.isUnlimitedFree;
  const unlimitedFeePerDay =
    unlimitedFeePerDay_aboutRentPrice || rentalDetails?.unlimitedFeePerDay;
  const totalUnlimitedFee =
    totalUnlimitedFee_aboutRentPrice || rentalDetails?.totalUnlimitedFee;

  // Determine which installments data to use
  const installments = bookingId
    ? installmentsFromRentalAbout
    : installmentsBreakdown;
  const hasInstallment = Boolean(installments && installments.length);

  // Determine rent-to-own flag
  const isRentToOwn =
    Boolean(rentToOwnInstallmentBreakdown) || Boolean(isRentToown_details);

  // Preserve original naming for paid installments
  const sucessInstallmentPaid =
    bookingId &&
    rentalAboutPrice?.installments?.filter(
      (i) =>
        i.status === installmentStatuses.paid.value ||
        i.status === installmentStatuses.partially_refunded.value
    )?.length;

  // Helper: Calculate installments invoice data
  const getInstallmentsInvoiceData = useCallback(
    (data: InstallmentData[] = []): InstallmentsInvoiceData => {
      if (!data?.length)
        return {
          paidInstallmentsCount: 0,
          notPaidInstallmentsCount: 0,
          totalInstallmentsCount: 0,
          totalPaidAmount: 0,
          totalnextInstallmentToPay: 0,
        };

      const paidInstallments = data.filter(
        (i) =>
          i.status === installmentStatuses.paid.value ||
          i.status === installmentStatuses.partially_refunded.value
      );
      const paidInstallmentsValues = paidInstallments.map((i) => i.amount);
      const paidInstallmentsCount = paidInstallments.length;
      const notPaidInstallmentsCount = data.filter(
        (i) =>
          i.status !== installmentStatuses.paid.value &&
          i.status !== installmentStatuses.partially_refunded.value
      ).length;
      const totalInstallmentsCount = data?.length;
      const totalPaidAmount = paidInstallmentsValues.reduce(
        (total, current) => total + current,
        0
      );

      const dueInstallment = data.find(
        (i) =>
          i.status === installmentStatuses.due.value ||
          i.status === installmentStatuses.overdue.value
      );
      const upcomingInstallment = data.find(
        (i) => i.status === installmentStatuses.upcoming.value
      );

      const totalnextInstallmentToPay = data
        .filter(
          (i) =>
            i.status === installmentStatuses.due.value ||
            i.status === installmentStatuses.overdue.value
        )
        .map((i) => i.amount)
        .reduce((total, current) => total + current, 0);

      return {
        paidInstallmentsCount,
        notPaidInstallmentsCount,
        totalInstallmentsCount,
        totalPaidAmount,
        nextInstallmentToPay: dueInstallment?.amountDue,
        nextInstallmentAmountToPay: dueInstallment?.amount,
        upcomingInstallmentToPay: upcomingInstallment?.amountDue,
        upcomingInstallmentAmountToPay: upcomingInstallment?.amount,
        totalnextInstallmentToPay,
      };
    },
    []
  );

  // Check if due amount should be shown
  const showDueAmount = useCallback(() => {
    return Boolean(
      !bookingId ||
        (bookingId &&
          (booking?.rentalDetails?.payable ||
            booking?.rentalDetails?.installments?.find((i) => i.payable)))
    );
  }, [booking, bookingId]);

  // Calculate installments invoice data
  const installmentsInvoiceData = useMemo(
    () => getInstallmentsInvoiceData(installments),
    [installments, getInstallmentsInvoiceData]
  );

  // Extract values from invoice data
  const {
    nextInstallmentToPay,
    nextInstallmentAmountToPay,
    upcomingInstallmentToPay,
    upcomingInstallmentAmountToPay,
    totalPaidAmount,
  } = installmentsInvoiceData;

  const totalInstallmentsPaidAmount =
    hasInstallment && bookingId ? totalPaidAmount : 0;

  // Compute due amount based on various conditions
  const computeDueAmount = () => {
    if (
      hasInstallment &&
      (isIntallmentDetailsModal || !sucessInstallmentPaid)
    ) {
      return nextInstallmentToPay !== undefined
        ? nextInstallmentToPay
        : upcomingInstallmentToPay;
    } else if (bookingId) {
      if (hasInstallment && !isRentToOwn) {
        return remainingDueInstallmentsAmountDetails;
      } else {
        return isPaid
          ? totalAmountDueFromRentalDetails
          : totalAmountDue_rentalAboutPric;
      }
    }
    return totalAmountDue;
  };

  const computedDueAmount = computeDueAmount() || 0;

  // Compute total due amount based on context
  const computedTotalDueAmount = hasInstallment
    ? nextInstallmentToPay
    : bookingId
    ? totalAmountDue_rentalAboutPric
    : totalAmountDue || 0;

  return {
    // Rental details from booking.rentalDetails
    rentalExtraServices,
    paymentStatusCode,
    paymentStatusMessage,
    pay_with_installments,
    getInstallmentsInvoiceData,

    // Amounts from rental_about_price (booking)
    totalAmountDue_rentalAboutPric,
    totalAmountDueFromRentalDetails,

    // Installments and flags
    hasInstallment,
    sucessInstallmentPaid,

    // Price breakdown from about_rent_price (cars)
    dailyPrice,
    numberOfDays,
    priceBeforeDiscount,
    discountValue,
    discountType,
    discountPercentage,
    priceBeforeInsurance,
    insuranceValue,
    deliveryPrice,
    handoverPrice,
    addsPrice,
    allyExtraServices,
    branchExtraServices,
    isUnlimited,
    isUnlimitedFree,
    unlimitedFeePerDay,
    totalUnlimitedFee,
    priceBeforeTax,
    valueAddedTaxPercentage,
    taxValue,
    totalPrice,
    pricePerDay,
    couponCode,
    couponDiscount,
    isCouponApplied,
    couponErrorMessage: bookingId
      ? rentalAboutPriceCouponErrorMessage
      : aboutPriceCouponErrorMessage,
    totalAmountDue, // from about_rent_price

    // Car extra details from cars
    handover_in_another_branch:
      cars.handover_in_another_branch || rentalDetails?.handoverPrice,
    deliveryType: !bookingId ? cars.deliveryType : rentalDetails?.deliverType,
    insuranceType: !bookingId
      ? cars.insuranceType
      : rentalDetails?.insuranceName,
    delivery: !bookingId ? cars.delivery : rentalDetails?.deliveryPrice,

    coupon_data,

    // Local state
    setIsPopupOpen,
    isPopupOpen,

    // Translation and extra services
    i18n,
    t,
    getExtraServicesPrice,

    // Router info
    bookingId,
    rentalData: booking,

    // Installments data
    installments,
    walletAmount,
    installmentsFullyPaid: rentalDetails?.installmentsFullyPaid,
    paidInstallmentsCount: rentalDetails?.paidInstallmentsCount,
    walletAmountDetails,
    balance,
    pay_with_wallet,
    remainingDueInstallmentsAmount,
    remainingDueInstallmentsAmountDetails,
    remainingInstallmentsAmountDetails,
    installmentsDetails: installmentsFromRentalAbout,
    totalWalletPaidFromRentalDetails: rentalDetails?.totalWalletPaidAmount,

    // Due amounts (keys preserved exactly as in original code)
    dueAmoutOutside: computedDueAmount,
    toalDueAmoutOutside: computedTotalDueAmount,

    // Rent-to-own and installments breakdown
    isRentToOwn,
    rentToOwnInstallmentBreakdown,
    totalInstallmentsPaidAmount,
    totalInstallmentsAmount,
    upcomingInstallmentToPay,
    nextInstallmentToPay,

    // Additional rental_about_price details
    ownCarDetails,
    totalExtensionsDays,
    totalExtensionsPrice,
    priceBeforeTax_rentalAbouPrice,
    priceBeforeInsurance_rentalAboutPrice,
    totalWalletPaidAmount_rentalAboutPrice,
    nextInstallmentAmountToPay,
    upcomingInstallmentAmountToPay,
    showDueAmount,
    totalBookingPrice,
    payable:
      rentalDetails?.payable ||
      rentalDetails?.installments?.find((i) => i.payable),
  };
}

export default useLogic;
