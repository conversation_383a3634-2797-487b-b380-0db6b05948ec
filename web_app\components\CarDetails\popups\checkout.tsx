/* eslint-disable react-hooks/exhaustive-deps */
import { useRouter } from "next/router";
import { useEffect, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { rentalPaymentBrand } from "utilities/enums";
import MessagePopup from "./messagePopup";
import ErrorPopup from "./error";
import { useDispatch } from "react-redux";
import { v4 as uuidv4 } from "uuid";
import Script from "next/script";
import useFirebase from "useFirebase";

// Add TypeScript declarations for HyperPay
declare global {
  interface Window {
    wpwl?: any;
    wpwlOptions?: {
      style: string;
      locale: string;
      labels: {
        cardNumber: string;
        cvv: string;
        expiryDate: string;
      };
      iframeStyles: {
        [key: string]: {
          [key: string]: string;
        };
      };
      onReady: () => void;
      onError: (error: any) => void;
      brandDetection: boolean;
      showCVVHint: boolean;
      brandDetectionPriority: string[];
      enableBrandDetection: boolean;
      requireCvv: boolean;
      showLabels: boolean;
      brandDetectionType: string;
      threeDSStyles?: {
        backgroundColor: string;
        borderRadius: string;
        boxShadow: string;
        fontFamily: string;
        buttonColor: string;
        textColor: string;
        headerColor: string;
        headerTextColor: string;
        width: string;
        maxWidth: string;
        height: string;
        maxHeight: string;
        overflow: string;
      };
      threeDSInitiated?: () => boolean;
      onBeforeSubmitCard?: (e: any) => boolean;
      onValidation?: (type: string, valid: boolean, element: any) => boolean;
    };
  }
}

export default function Checkout({
  isOpen,
  setIsOpen,
  paymentBrandId,
  extensionId,
  checkoutId,
  setCheckoutId,
  bookingId,
  isInstallment,
  setIsPaymentStatusPopupOpen,
  getPaymentStatusHandler,
  integrity: _integrity,
}) {
  const { i18n, t } = useTranslation();
  const router = useRouter();
  const [nonce] = useState(() => uuidv4());
  const [errorOpen, setErrorOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [formKey, setFormKey] = useState(0);
  const { remoteConfigValues } = useFirebase();
  const { skip_integrity } = remoteConfigValues || {
    skip_integrity: { _value: "false" },
  };
  const {
    car,
    checkout_id,
    id: hyperPayPaymentId,
    paymentBrandId: payment_brand_id,
    bookingId: booking_id,
  } = router.query || {};

  const integrity = !skip_integrity ? _integrity : null;

  // Cleanup function to remove existing payment widgets
  const cleanupPaymentForm = useCallback(() => {
    const existingForm = document.querySelector(".paymentWidgets");
    if (existingForm) {
      existingForm.remove();
    }
    const scripts = document.querySelectorAll('script[id^="payment-"]');
    scripts.forEach((script) => script.remove());

    // Set wpwlOptions to an empty object instead of deleting it
    if (window.wpwlOptions) {
      window.wpwlOptions = {
        style: "plain",
        locale: "en",
        labels: {
          cardNumber: "",
          cvv: "",
          expiryDate: "",
        },
        iframeStyles: {},
        onReady: () => {},
        onError: () => {},
        brandDetection: false,
        showCVVHint: false,
        brandDetectionPriority: [],
        enableBrandDetection: false,
        requireCvv: false,
        showLabels: false,
        brandDetectionType: "",
      };
    }

    // Remove any existing style tag for 3DS
    const styleTag = document.getElementById("hyperpay-3ds-styles");
    if (styleTag) {
      styleTag.remove();
    }
  }, []);

  // Initialize form when opened
  useEffect(() => {
    if (isOpen && (checkoutId || checkout_id)) {
      cleanupPaymentForm();

      // Set up wpwlOptions before loading the widget
      window.wpwlOptions = {
        style: "plain",
        locale: i18n.language,
        labels: {
          cardNumber: t("Card Number"),
          cvv: t("CVV"),
          expiryDate: t("Expiry Date"),
        },
        iframeStyles: {
          ".wpwl-control": {
            height: "60px",
            padding: "0 20px",
            "border-radius": "15px",
            border: "1px solid rgba(0, 0, 0, 0.2)",
            "background-color": "#FFFFFF",
            "box-sizing": "border-box",
            width: "100%",
          },
          ".wpwl-control-cvv": {
            width: "120px !important",
            display: "inline-block !important",
            "margin-left": "20px !important",
          },
          ".wpwl-button-pay": {
            width: "100%",
            height: "55px",
            "background-color": "#7ab3c5 !important",
            color: "white !important",
            "border-radius": "15px !important",
            "font-size": "18px !important",
            "font-weight": "bold !important",
            "margin-top": "20px !important",
            "text-transform": "none !important",
            "font-family": "system-ui, system-ui",
            transition: "background-color 0.3s ease",
          },
          ".wpwl-button-pay:hover": {
            "background-color": "#6ba6b8 !important",
          },
          ".wpwl-button-pay:disabled": {
            "background-color": "#f8f8f8 !important",
            color: "#cccccc !important",
          },
        },
        onReady: function () {
          const form = document.querySelector(".paymentWidgets");
          if (form && i18n.language === "ar") {
            form.setAttribute("dir", "rtl");
          }

          // Style 3D Secure popup
          // This adds a style tag to the head to style the 3D Secure iframe
          const styleTag = document.createElement("style");
          styleTag.id = "hyperpay-3ds-styles";
          styleTag.innerHTML = `
            /* Style the 3D Secure container only */
            .wpwl-3ds-container, #threeDsecure, .wpwl-3ds-holder, .wpwl-3ds-content, iframe[id^='3ds'], .wpwl-3ds-dialog {
              border-radius: 20px !important;
              box-shadow: 0px 9px 30px rgba(77, 81, 86, 0.2) !important;
              overflow: hidden !important;
              max-width: 450px !important;
              max-height: 90vh !important;
              background-color: white !important;
              display: flex !important;
              flex-direction: column !important;
            }
            
            /* Hide scrollbars only on 3DS elements */
            .wpwl-3ds-container::-webkit-scrollbar,
            #threeDsecure::-webkit-scrollbar, 
            .wpwl-3ds-holder::-webkit-scrollbar, 
            .wpwl-3ds-content::-webkit-scrollbar,
            iframe[id^='3ds']::-webkit-scrollbar {
              width: 0 !important;
              height: 0 !important;
              display: none !important;
            }
            
            /* Only apply to 3DS elements */
            .wpwl-3ds-container, 
            #threeDsecure, 
            .wpwl-3ds-holder, 
            .wpwl-3ds-content,
            iframe[id^='3ds'],
            .wpwl-3ds-dialog {
              -ms-overflow-style: none !important;
              scrollbar-width: none !important;
              overflow: hidden !important;
            }
            
            /* Style the 3D Secure header */
            .wpwl-3ds-header, .wpwl-3ds-header #main_header {
              background-color: #7ab3c5 !important;
              color: white !important;
              font-family: system-ui, system-ui !important;
              font-weight: bold !important;
              padding: 15px 20px !important;
              border-top-left-radius: 15px !important;
              border-top-right-radius: 15px !important;
              text-align: center !important;
              flex-shrink: 0 !important;
            }
            
            /* Style the submit button in the 3D Secure iframe */
            .wpwl-3ds-container button[type="submit"], 
            .wpwl-3ds-container .wpwl-button-submit, 
            #threeDsecure #submitButton {
              background-color: #7ab3c5 !important;
              color: white !important;
              border: none !important;
              border-radius: 15px !important;
              padding: 12px 25px !important;
              font-family: system-ui, system-ui !important;
              font-weight: bold !important;
              cursor: pointer !important;
              margin-top: 15px !important;
              transition: background-color 0.3s ease !important;
            }
            
            .wpwl-3ds-container button[type="submit"]:hover, 
            .wpwl-3ds-container .wpwl-button-submit:hover, 
            #threeDsecure #submitButton:hover {
              background-color: #6ba6b8 !important;
            }
            
            /* Style input fields in the 3D Secure form only */
            .wpwl-3ds-container input[type="password"], 
            .wpwl-3ds-container input[type="text"], 
            .wpwl-3ds-input,
            #threeDsecure input {
              height: 50px !important;
              border-radius: 15px !important;
              border: 1px solid rgba(0, 0, 0, 0.2) !important;
              padding: 0 15px !important;
              font-family: system-ui, system-ui !important;
              width: 100% !important;
              max-width: 300px !important;
              margin: 10px auto !important;
              display: block !important;
            }
            
            /* Style labels in the 3D Secure form only */
            .wpwl-3ds-container label, 
            .wpwl-3ds-label,
            #threeDsecure label {
              font-family: system-ui, system-ui !important;
              font-weight: 500 !important;
              margin-bottom: 5px !important;
              display: block !important;
              text-align: center !important;
            }
            
            /* Style the content area */
            .wpwl-3ds-content, #content_div {
              padding: 20px !important;
              background-color: white !important;
              overflow: hidden !important;
              flex: 1 !important;
              display: flex !important;
              flex-direction: column !important;
              align-items: center !important;
              justify-content: center !important;
              max-height: calc(90vh - 60px) !important;
            }
            
            /* Style form elements inside 3DS container only */
            .wpwl-3ds-container form,
            #threeDsecure form {
              display: flex !important;
              flex-direction: column !important;
              align-items: center !important;
              margin: 0 !important;
              padding: 0 !important;
              overflow: hidden !important;
            }
            
            /* Style the simulation notice text */
            .simulation-text, #page_text {
              color: #555 !important;
              font-family: system-ui, system-ui !important;
              text-align: center !important;
              margin: 15px 0 !important;
              font-size: 14px !important;
            }
            
            /* Fix for MiGS 3DS popup */
            .SIMULATION {
              width: 100% !important;
              max-width: 450px !important;
              height: auto !important;
              max-height: 90vh !important;
              overflow: hidden !important;
              border-radius: 20px !important;
              box-shadow: 0px 9px 30px rgba(77, 81, 86, 0.2) !important;
            }
            
            .SIMULATION iframe {
              width: 100% !important;
              border: none !important;
              overflow: hidden !important;
            }
            
            /* Fix for dialog dimensions to prevent scrolling */
            .wpwl-3ds-dialog {
              width: auto !important;
              max-width: 450px !important;
              height: auto !important;
              max-height: 90vh !important;
              margin: 0 auto !important;
              padding: 0 !important;
              overflow: hidden !important;
            }
            
            /* Extra fix for iframe containers */
            #threeDsecureIframe, #redirectTo3DSPage {
              width: 100% !important;
              height: 100% !important;
              min-height: 350px !important;
              max-height: 90vh !important;
              overflow: hidden !important;
              border: none !important;
            }
          `;
          document.head.appendChild(styleTag);

          // Ensure validation functions exist
          if (window.wpwl) {
            // Create validation objects if they don't exist
            if (!window.wpwl.validation) window.wpwl.validation = {};
            if (!window.wpwl.validation.de) window.wpwl.validation.de = {};

            // Add fallback validation functions
            if (!window.wpwl.validation.de.validatePciIframes) {
              window.wpwl.validation.de.validatePciIframes = function () {
                return true;
              };
            }

            if (!window.wpwl.validation.de.validateCardAsync) {
              window.wpwl.validation.de.validateCardAsync = function () {
                return Promise.resolve(true);
              };
            }

            if (!window.wpwl.validation.de.validateCard) {
              window.wpwl.validation.de.validateCard = function () {
                return true;
              };
            }

            if (!window.wpwl.validation.de.validateInput) {
              window.wpwl.validation.de.validateInput = function () {
                return true;
              };
            }

            // Add function to fix iframe dimensions when 3DS popup appears
            const originalExecutePayment = window.wpwl.executePayment;
            if (originalExecutePayment) {
              window.wpwl.executePayment = function () {
                // Add event listener to fix 3DS popup dimensions
                setTimeout(() => {
                  const threeDSPopup = document.querySelector(
                    ".wpwl-3ds-container"
                  );
                  if (threeDSPopup) {
                    threeDSPopup.setAttribute(
                      "style",
                      "max-width: 450px !important; max-height: 90vh !important; overflow: hidden !important; border-radius: 20px !important;"
                    );

                    const iframe = threeDSPopup.querySelector("iframe");
                    if (iframe) {
                      iframe.setAttribute(
                        "style",
                        "width: 100% !important; height: 100% !important; overflow: hidden !important; border: none !important;"
                      );
                    }
                  }
                }, 500);

                return originalExecutePayment.apply(this, arguments);
              };
            }
          }
        },
        onError: function (error) {
          console.error("Payment form error:", error);
          setErrorMessage(error?.message || "Payment form error");
          setErrorOpen(true);
        },
        threeDSInitiated: function () {
          // Set styles when 3DS popup appears
          setTimeout(() => {
            const threeDSPopup = document.querySelector(".wpwl-3ds-container");
            if (threeDSPopup) {
              threeDSPopup.setAttribute(
                "style",
                "max-width: 450px !important; max-height: 90vh !important; overflow: hidden !important; border-radius: 20px !important;"
              );
            }
          }, 100);
          return true;
        },
        onBeforeSubmitCard: function (e) {
          return true;
        },
        onValidation: function (type, valid, element) {
          return true;
        },
        // Add styles for 3D Secure popup
        threeDSStyles: {
          backgroundColor: "#ffffff",
          borderRadius: "20px",
          boxShadow: "0px 9px 30px rgba(77, 81, 86, 0.2)",
          fontFamily: "system-ui, system-ui",
          buttonColor: "#7ab3c5",
          textColor: "#333333",
          headerColor: "#7ab3c5",
          headerTextColor: "#ffffff",
          width: "100%",
          maxWidth: "450px",
          height: "auto",
          maxHeight: "90vh",
          overflow: "hidden",
        },
        brandDetection: true,
        showCVVHint: true,
        brandDetectionPriority: ["VISA", "MASTER", "MADA"],
        enableBrandDetection: true,
        requireCvv: true,
        showLabels: true,
        brandDetectionType: "regex",
      };

      if (checkoutId || checkout_id) {
        // Load the widget script
        const script = document.createElement("script");
        script.id = "payment-widget";
        script.src = `${
          process.env.NEXT_PUBLIC_OPPWA_URL
        }/v1/paymentWidgets.js?checkoutId=${checkoutId || checkout_id}`;
        script.async = true;
        script.setAttribute("crossorigin", "anonymous");
        script.setAttribute("nonce", nonce);

        if (integrity) {
          script.setAttribute("integrity", integrity);
        }

        document.body.appendChild(script);
      }

      setFormKey((prev) => prev + 1);
    }

    return () => {
      // Clean up the style tag when the component unmounts
      const styleTag = document.getElementById("hyperpay-3ds-styles");
      if (styleTag) {
        styleTag.remove();
      }
    };
  }, [
    isOpen,
    checkoutId,
    checkout_id,
    cleanupPaymentForm,
    i18n.language,
    t,
    nonce,
    integrity,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupPaymentForm();
    };
  }, [cleanupPaymentForm]);

  useEffect(() => {
    if (checkoutId || (checkout_id && !hyperPayPaymentId)) {
      setIsOpen(true);
    }
  }, [checkout_id, checkoutId]);

  return (
    <>
      {isOpen && (checkoutId || checkout_id) ? (
        <MessagePopup
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          body={
            <>
              <div
                key={formKey}
                className={`text-align-localized`}
                style={{
                  direction: "ltr",
                  minHeight: "200px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <form
                  action={`${window.location.origin}/${
                    i18n.language
                  }/car-details?car=${car}&bookingId=${
                    booking_id || bookingId
                  }${
                    extensionId ? `&extensionId=${extensionId}` : ""
                  }&checkout_id=${checkoutId}${
                    isInstallment ? `&isInstallmentParam=valid` : ""
                  }`}
                  className="paymentWidgets"
                  data-brands={`${
                    paymentBrandId === rentalPaymentBrand.CREDIT_CARD ||
                    payment_brand_id === rentalPaymentBrand.CREDIT_CARD
                      ? "VISA MASTER"
                      : "MADA"
                  }`}
                />
              </div>
            </>
          }
          onClose={() => {
            cleanupPaymentForm();
            getPaymentStatusHandler(
              checkoutId,
              () => {
                setIsPaymentStatusPopupOpen &&
                  setIsPaymentStatusPopupOpen(true);
              },
              !checkout_id
            );
          }}
        />
      ) : null}
      <ErrorPopup
        isOpen={errorOpen}
        setIsOpen={setErrorOpen}
        ErrorMessage={errorMessage || t("Payment processing error occurred")}
        buttonText={t("Close")}
        buttonClickHandler={() => setErrorOpen(false)}
      />
    </>
  );
}
