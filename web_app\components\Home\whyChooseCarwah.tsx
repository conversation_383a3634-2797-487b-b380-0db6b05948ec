import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Cards from "./cards";

const Div = styled.div`
  padding-top: 2rem;
  div.section-fancy-title {
    margin-bottom: 30px;
  }
  padding-bottom: 4rem;
`;

export default function WhyChooseCarwah() {
  const { t } = useTranslation();
  return (
    <Div>
      <div className="section-fancy-title">
        <h2>{`${t("Why choose")} ${t("Carwah")}`}</h2>
      </div>
      <div>
        <Cards />
      </div>
    </Div>
  );
}
