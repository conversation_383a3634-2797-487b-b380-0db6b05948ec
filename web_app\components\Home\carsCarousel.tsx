/* eslint-disable react/jsx-key */
import React, { useEffect, useLayoutEffect } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Swiper from "components/shared/carousel";
import CarCard from "components/shared/carCard";

const Div = styled.div`
  @media (min-width: 960px) {
    position: absolute;
    right: 60px;
    top: 15px;
    width: 355px;
    height: 95%;
    .swiper-wrap {
      height: 95%;
      .swiper-container {
        height: 100%;
        .swiper-wrapper {
          padding: 30px 0;
        }
      }
      .swiper-button-prev {
        position: absolute;
        top: 15px;
        left: calc(50% - 10px);
        transform: rotateZ(90deg);
        &:after {
          color: var(--color-3);
          font-size: 24px;
          font-weight: 900;
        }
      }
      .swiper-button-next {
        position: absolute;
        top: auto;
        bottom: -40px !important;
        left: calc(50% - 10px);
        right: auto;
        transform: rotateZ(90deg);
        &:after {
          color: var(--color-3);
          font-size: 24px;
          font-weight: 900;
        }
      }
    }
  }
  @media (max-width: 959px) {
    width: 90%;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 20px;
    .swiper-button-prev,
    .swiper-button-next {
      &:after {
        color: var(--color-3);
        font-size: 24px;
        font-weight: 900;
      }
    }
  }
`;

export default function MapCarousel() {
  const { t } = useTranslation();

  useEffect(() => {
    let clean = false;
    if (!clean) {
      // Moved next and prev arrows outside of swiper container for styling
      const next = document.querySelector("#map-carousel .swiper-button-next");
      const prev = document.querySelector("#map-carousel .swiper-button-prev");
      const wrap = document.querySelector("#map-carousel .swiper-wrap");
      wrap.appendChild(next);
      wrap.appendChild(prev);
    }
    return (clean = true);
  }, []);

  return (
    <Div id="map-carousel">
      <Swiper
        navigation
        onSwiper={(swiper) => null}
        onSlideChange={() => null}
        loop={false}
        breakpoints={{
          1024: {
            direction: "vertical",
            slidesPerView: 1.4,
          },
        }}
        slides={[
          <CarCard
            car={{
              img: `/assets/images/accent.svg`,
              model: t("Hyundai Accent"),
              year: 2018,
              // similarType: t("Sedan"),
              location: { city: t("Riyadh"), district: t("Alyasamin") },
              price: { before: 70, after: 56 },
            }}
          />,
          <CarCard
            car={{
              img: `/assets/images/accent.svg`,
              model: t("Hyundai Accent"),
              year: 2018,
              // similarType: t("Sedan"),
              location: { city: t("Riyadh"), district: t("Alyasamin") },
              price: { before: 70, after: 56 },
            }}
          />,
          <CarCard
            car={{
              img: `/assets/images/accent.svg`,
              model: t("Hyundai Accent"),
              year: 2018,
              // similarType: t("Sedan"),
              location: { city: t("Riyadh"), district: t("Alyasamin") },
              price: { before: 70, after: 56 },
            }}
          />,
          <CarCard
            car={{
              img: `/assets/images/accent.svg`,
              model: t("Hyundai Accent"),
              year: 2018,
              // similarType: t("Sedan"),
              location: { city: t("Riyadh"), district: t("Alyasamin") },
              price: { before: 70, after: 56 },
            }}
          />,
        ]}
      />
    </Div>
  );
}
