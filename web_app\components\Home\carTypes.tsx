import { Grid } from "@material-ui/core";
import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import CarTypesCarousel from "./carTypesCarousel";
import Select from "components/shared/select";

const Div = styled.div`
  position: relative;
  padding: 30px 0;
  margin: 20px 0 40px 0;
  @media (max-width: 900px) {
    margin: 0 0 30px 0 !important;
  }

  .section-title {
    margin-bottom: 30px;
    z-index: 1;

    h4 {
      font-size: 22px;
      font-weight: bold;
      line-height: 1.2;
      margin: 0;
      color: #333;
      font-weight: 400;
    }

    .highlight {
      /* color: var(--color-2); */
      display: block;
      font-weight: bold;
      margin-top: 5px;
      position: relative;

      &:after {
        content: "";
        position: absolute;
        bottom: -15px;
        right: ${(props) => (props.language === "ar" ? "0" : "auto")};
        left: ${(props) => (props.language === "ar" ? "auto" : "0")};
        width: 20px;
        border-radius: 10px;
        height: 4px;
        background-color: var(--color-2);
      }
    }
  }

  h6 {
    text-align: center !important;
    font-size: 19px;
    line-height: 24px;
    margin-top: 15px !important;
  }
`;

export default function CarTypes() {
  const { t, i18n } = useTranslation();

  return (
    <Div language={i18n.language}>
      <Grid container justifyContent="space-around" spacing={2}>
        <Grid item xs={12} sm={2} md={2} lg={2}>
          <div className="section-title">
            <h4>
              {t("Search By").toString()}
              <span className="highlight">{t("Vehicle Type").toString()}</span>
            </h4>
          </div>
        </Grid>
        <Grid item xs={12} sm={10} md={10} lg={10}>
          <CarTypesCarousel />
        </Grid>
      </Grid>
    </Div>
  );
}
