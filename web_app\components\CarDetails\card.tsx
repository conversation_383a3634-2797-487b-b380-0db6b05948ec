/* eslint-disable @next/next/no-img-element */
import WhiteCard from "components/shared/whiteCard";
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import { memo, useState } from "react";
import Tooltip from "components/shared/tooltip";
import { ShareSocial } from "react-share-social";
import { ClickAwayListener } from "@material-ui/core";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  .icon {
    max-width: 18px;
  }
  > div {
    /* padding: 20px; */
    > div {
      width: 100%;
    }
  }
  .price-wrapper {
    .price-label {
      color: var(--color-4);
      background-color: var(--color-2);
      padding: ${(props) =>
        props.lang === "ar" ? "7px 15px 15px 15px" : "10px 15px 10px 15px"};
      border-radius: ${(props) =>
        props.lang === "ar" ? "0 var(--radius-2)" : "var(--radius-2) 0"};
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: row;
      -ms-flex-direction: row;
      flex-direction: row;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      align-items: center;
      gap: 5px;
      min-width: 120px;

      span {
        display: block;
      }
      > div {
        align-items: center;
        div:last-child {
          font-size: 24px;
          font-weight: bold;
        }
        div:first-child {
          font-size: 25px;
          width: max-content;
          text-align: start;
          /* transform: translateY(-4px); */
        }
      }
    }
  }
  .flex-column-mobile {
    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
  @media (max-width: 768px) {
    h4 {
      font-size: 1.3rem !important;
    }
  }
`;
const style = {
  root: {
    background: "var(--color-5)",
    borderRadius: 3,
    border: 0,
    padding: 0,
    color: "white",
  },
  copyContainer: {
    display: "none",
  },
};

function Card({ carData }) {
  //Hooks
  const { t, i18n } = useTranslation();
  const { carPrice, carMonthsPrice } =
    useSelector((state: RootStateOrAny) => state.cars.selectedCarBranch) || {};
  const { pricePerDay, pricePerMonth } =
    useSelector((state: RootStateOrAny) => state.booking?.rentalDetails) || {};

  //Store
  const selectionIndex = useSelector(
    (state: RootStateOrAny) => state.search_data.selection_index
  );
  //State
  const [isTTopen, setIsTTopen] = useState(false);

  const SingleFeatureComponent = ({ src, title, value }) => {
    return (
      <div className="d-flex align-items-center" style={{ gap: "12px" }}>
        <div>
          <img
            className="icon"
            src={src}
            alt="icon"
            style={{ width: "30px" }}
          />
        </div>
        <div className="text-align-localized">
          <span className="color-12 font-12px">{title}</span>
          <span className="text-capitalize" style={{ marginTop: "-5px" }}>
            <br />
            {value}
          </span>
        </div>
      </div>
    );
  };
  return (
    <Div className="rounded" lang={i18n.language}>
      <WhiteCard>
        <div className="px-3 pt-2">
          <div className="d-flex justify-content-between align-items-start">
            <div>
              <h4>
                <span className="bold">{carData?.carMakeName}</span>{" "}
                <span className="bold">
                  {carData?.carModelName}
                  {carData?.carVersionName}
                </span>{" "}
                {/* <br /> */}
                <span>{carData?.year}</span>
              </h4>
              <h3 className="font-15px my-2 color-16">{`${t(
                "or similar"
              )}`}</h3>
            </div>
            <ClickAwayListener
              touchEvent={"onTouchStart"}
              mouseEvent={"onMouseDown"}
              onClickAway={() => {
                setIsTTopen(false);
              }}
            >
              <div className="position-relative">
                <img
                  src="/assets/icons/share.svg"
                  alt="share icon"
                  className="cursor-pointer"
                  onClick={() => setIsTTopen((prev: boolean) => !prev)}
                />
                {typeof window != "undefined" ? (
                  <Tooltip
                    anchor={i18n.language === "en" ? "left" : "right"}
                    isOpen={isTTopen}
                    style={{
                      borderRadius: "var(--radius-3) !important",
                    }}
                  >
                    <h4 className="mt-2 mb-2" style={{ color: "white" }}>
                      {t("Share car now!") as string}
                    </h4>
                    <ShareSocial
                      url={window.location.href}
                      socialTypes={[
                        "linkedin",
                        "facebook",
                        "twitter",
                        "whatsapp",
                        "telegram",
                        "mailru",
                        "ok",
                        "hatena",
                        "instapaper",
                        "line",
                        "reddit",
                      ]}
                      onSocialButtonClicked={() => setIsTTopen(false)}
                      style={style}
                    />
                  </Tooltip>
                ) : null}
              </div>
            </ClickAwayListener>
          </div>
        </div>
        <div className="separator mb-1" />
        <div className="d-flex justify-content-between flex-column-mobile">
          <div
            className="footer d-flex px-3 mb-0"
            style={{
              gap: "10px",
              flexWrap: "wrap",
              transform: "translateY(-5px)",
            }}
          >
            <SingleFeatureComponent
              src="/assets/images/features/feature1.svg"
              title={t("transmission")}
              value={
                i18n.language === "en"
                  ? carData?.transmission
                  : carData?.transmission == "auto"
                  ? "أتوماتيك"
                  : "مانيوال"
              }
            />
            <SingleFeatureComponent
              src="/assets/images/features/feature2.svg"
              title={t("vehicleType")}
              value={carData?.vehicleTypeName}
            />
            <SingleFeatureComponent
              src="/assets/images/features/feature3.svg"
              title={t("km.per.day")}
              value={carData?.distanceByDay}
            />
            <SingleFeatureComponent
              src="/assets/images/features/feature3.svg"
              title={t("km.away")}
              value={`${Number(carData?.distanceBetweenCarUser || 0).toFixed(
                0
              )} ${t("km")}`}
            />
          </div>
          <div>
            <div
              className="d-flex justify-content-end align-items-center price-wrapper"
              style={{ gap: "20px" }}
            >
              <div className="font-18px price-label">
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    gap: "2px",
                  }}
                >
                  <div className="d-flex gap-5px align-items-baseline">
                    <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                      <RiyalSymbol />
                    </span>
                    <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                      {selectionIndex !== 1
                        ? carPrice || pricePerDay
                        : carMonthsPrice || pricePerMonth}
                    </span>
                  </div>
                </div>
                <div>
                  {selectionIndex !== 1 ? ` ${t("/day")} ` : ` ${t("/month")} `}
                </div>
              </div>
            </div>
          </div>
        </div>
      </WhiteCard>
    </Div>
  );
}

export default memo(Card);
