/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { memo } from "react";
import { isEligibleForInstallments } from "utilities/helpers";
import { Div } from "./styled";
import useLogic from "./useLogic";
import RiyalSymbol from "components/shared/RiyalSymbol";

function RentalPackages() {
  const { i18n, t, selectedPackage, savedPackages, packageSelectionHandler } =
    useLogic();

  return (
    <Div lang={i18n.language}>
      <div className="p-4 position-relative">
        <div className="d-flex gap-10px justify-content-between align-items-center">
          <img
            src={`/assets/images/${
              i18n.language == "en" ? "packagesEn" : "packages"
            }.svg`}
            alt="rental packages"
          />
          <div className="seperate-line" />
          <div>
            <h6 className="bold">{t("Rental Packages") as string}</h6>
            <p className="font-14px color-11">
              {t("Select your preferred package") as string}
            </p>
          </div>
        </div>
        <div className="packages-container mt-4">
          {savedPackages?.map((packageData) => {
            const isPackageSelected =
              selectedPackage &&
              packageData.selected &&
              packageData.name === selectedPackage.name;

            return (
              <div
                key={packageData.name}
                style={{
                  border: isPackageSelected
                    ? "3px solid var(--color-3)"
                    : "3px solid transparent",
                }}
                onClick={() => packageSelectionHandler(packageData)}
              >
                <div
                  className="d-flex justify-content-around px-1 cursor-pointer position-relative"
                  style={{
                    marginTop:
                      packageData?.highlyRequested || packageData?.smallestRate
                        ? "-7px"
                        : "",
                  }}
                >
                  <div className="d-flex align-items-center gap-5px">
                    <h6
                      className="font-15px bold"
                      style={{
                        color: isPackageSelected ? "var(--color-3)" : " ",
                      }}
                    >
                      {packageData.name}
                    </h6>
                  </div>
                  <div className="color-16 font-13px text-center">
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        gap: "5px",
                        alignItems: "baseline",
                      }}
                    >
                      <RiyalSymbol
                        style={{
                          color: "black",
                          order: i18n.language === "ar" ? 1 : 0,
                        }}
                      />
                      <span
                        className="bold"
                        style={{
                          color: "black",
                          order: i18n.language === "ar" ? 0 : 1,
                        }}
                      >
                        {packageData.price}
                      </span>
                    </div>
                    <span className="font-13px">{t("/day") as string}</span>
                  </div>
                </div>

                <div className="badge">
                  {packageData?.highlyRequested ? (
                    <span className="cursor-pointer high-req">
                      {t("High Req.") as string}
                    </span>
                  ) : !packageData?.highlyRequested &&
                    packageData?.smallestRate ? (
                    <span className="cursor-pointer smallest-rate">
                      {t("Save more") as string}
                    </span>
                  ) : null}
                  {isEligibleForInstallments(packageData?.daysCount) ? (
                    <span className="cursor-pointer">
                      {t("Installment") as string}
                    </span>
                  ) : null}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </Div>
  );
}

export default memo(RentalPackages);
