/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @next/next/no-img-element */
import { Box, Grid } from "@material-ui/core";
import React, { memo, useEffect } from "react";
import styled from "styled-components";
import LocationInputs from "./locationInputs";
import DateTimeInputs from "./dateTimeInputs";
import CategoryTabs from "./categoryTabs";
import DeliveryInputs from "./deliveryInputs";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import ToAppPopup from "components/shared/toAppPopup";

import PopupComponent from "components/shared/popup";

import Filters from "./filters";
import { useRouter } from "next/router";
import { useCarsList } from "hooks/useCarList";
import { setfiltersUsedInSearch } from "store/search/action";
import RentalPeriod from "./RentalPeriod";
import useRentalType from "components/CarDetails/heplers/rentalType";
import { setPayWithInstallments } from "store/installments/action";

//Styled Components
const Div = styled.div`
  position: ${(props) => "relative"};
  /* top: ${(props) => (!props.isCollapsed ? "" : "72px")}; */
  left: 0;
  z-index: 9;
  #inputs-wrap {
    > div:first-child {
      border-radius: 20px !important;
      background-color: var(--color-4);
      padding: 20px 15px 20px 20px;
      @media (max-width: 1200px) {
        /* scale: 0.95; */
      }
      @media (max-width: 570px) {
        padding: 15px 5px 25px 10px;
        gap: 10px;
        justify-content: center !important;
      }
      border-radius: ${(props) => (props.isCpollased ? "var(--radius-2)" : "")};
      > div:nth-child(4) {
        @media (max-width: 960px) {
          margin-top: -10px;
        }
      }
    }
    .input:not([type="checkbox"]) {
      border-radius: var(--radius-3);
      border: solid 2px var(--color-7);
      input:not([type="checkbox"]) {
        width: 100%;
        border: none;
        padding: 25px 10px;
        box-sizing: border-box;
        font-weight: 500;
        text-transform: uppercase;
        &::placeholder {
          color: #bdc7cb;
        }
        @media (max-width: 570px) {
          padding: 18px 15px !important;
          position: relative;
          z-index: 9;
          font-size: 0.9rem !important;
        }
        @media (min-width: 570px) {
          font-size: 0.9rem !important;
        }
        @media (min-width: 960px) {
          /* font-size: 0.85rem !important; */
        }
      }
      /* img {
        transform: ${(props) => (props) =>
        props.i18n.language === "en"
          ? "translateX(-20px)"
          : "translateX(20px)"};
      } */
    }
    > div {
      > div {
        position: relative;
        width: 100%;
        @media (max-width: 570px) {
          max-width: 100%;
          /* margin-bottom: 15px !important; */
          font-size: 15px;
          display: flex;

          align-items: center;
          .input-wrap {
            flex-wrap: nowrap !important;
            justify-content: center;
          }
        }
      }
      img.exchange {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 1;
        transform: translate(-50%, -50%);
        cursor: pointer;
        @media (max-width: 570px) {
          width: 25px;
        }
      }
      .counter {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        z-index: 999;
        > div {
          position: relative;
          span {
            position: absolute;
            left: 50%;
            font-weight: bold;
            font-size: 13px;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }

        @media (max-width: 768px) {
          display: none;
        }
      }
    }
  }

  #switch {
    margin-top: 10px;
    span.MuiSwitch-thumb {
      background: var(--color-4);
    }
    .MuiSwitch-colorSecondary.Mui-checked,
    .MuiSwitch-track {
      opacity: 1;
    }
    display: flex;
    align-items: center;
  }
  #action-arrow {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: var(--color-2);
    height: max-content;
    padding: ${(props) =>
      !props.isMobile
        ? props.home
          ? "25px 0px"
          : ""
        : props.home
        ? "5px 20px 10px 20px"
        : ""};
    margin-top: ${(props) => (!props.isMobile && props.home ? "-8px" : "0px")};
    border-radius: ${(props) =>
      !props.isMobile && props.home ? "20px" : "5px"};
    gap: 10px;
    max-width: ${(props) => (props.home && !props.isMobile ? "95px" : "100%")};
    img {
      display: block;
      margin: 0 auto;
      width: 30px;
    }
    div {
      font-size: 1rem;
      cursor: pointer;
      color: white;
      font-weight: bold;
      border-radius: var(--radius-1);
      text-align: center;
      padding: ${(props) =>
        props.i18n.language === "en"
          ? !props.home
            ? "10px 30px"
            : ""
          : !props.home
          ? "5px 30px 10px 30px"
          : ""};
    }
    @media (max-width: 900px) {
      img {
        padding: 12px 6px !important;
        border-radius: 5px !important;
      }
      div {
        font-size: 0.85rem !important;
      }
    }
  }
  button.order {
    width: 100%;
    padding: 10px 0px;
    border: none;
    box-shadow: none;
    border-radius: var(--radius-3);
    background-color: var(--color-3);
    color: var(--color-4);
    font-weight: 500;
  }
  .filters-btn {
    @media (max-width: 960px) {
      padding: 0px 30px !important;
    }
  }
`;

// ---------------------------------------------//
function SearchBox({
  home,
  fetchCars,
  setFetchCars,
}: {
  home?: boolean;
  fetchCars: boolean;
  setFetchCars: (fetchCars: boolean) => void;
}) {
  //Hooks
  const { t, i18n } = useTranslation();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const router = useRouter();
  const { getCarList } = useCarsList();
  const dispatch = useDispatch();
  const { isRentToOwn } = useRentalType();

  //State
  const [isOpenToAppPopup, setIsOpenToAppPopup] = useState(false);
  const [isDeliveryPolicyOpen, setIsDeliveryPolicyOpen] = useState(false);

  //Store
  const userAddress = useSelector(
    (state: RootStateOrAny) => state.search_data.user_address
  );
  const dateTime = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );
  const confirmedDeliveryLocation = useSelector(
    (state: RootStateOrAny) => state.search_data.confirmed_delivery_location
  );
  const stored_fitlers = useSelector(
    (state: RootStateOrAny) => state.search_data.filters
  );
  const selectionIndex = useSelector(
    // refers to active tab
    (state: RootStateOrAny) => state.search_data.selection_index
  );
  const rentalMonths = useSelector(
    (state: RootStateOrAny) => state.search_data.rental_months
  );

  function findCarHandler() {
    dispatch(setPayWithInstallments(Boolean(rentalMonths >= 2)));

    dispatch(
      setfiltersUsedInSearch({
        ...stored_fitlers,
        airport: {
          label: userAddress?.pick_up?.airport?.name,
          value: userAddress?.pick_up?.airport?.id,
          action: "airport",
        },
      })
    );
    if (home) {
      router.push(
        `car-search${
          router?.query?.bannerId ? `?bannerId=${router.query.bannerId}` : ""
        }#car-grid`
      );
    } else {
      setFetchCars(!fetchCars);
      router.push(
        `car-search${
          router?.query?.bannerId ? `?bannerId=${router.query.bannerId}` : ""
        }#car-grid`
      );
    }
  }

  //JSX
  return (
    <>
      <Div
        selectionIndex={selectionIndex}
        i18n={i18n}
        isCollapsed={isCollapsed}
        isMobile={window?.innerWidth < 960}
        home={home}
      >
        <div
          style={{
            maxWidth: isCollapsed ? "initial" : "",
            padding: isCollapsed ? "20px 0px 0 0px" : "",
            marginTop: !isCollapsed ? "0" : "",
          }}
        >
          <Grid
            container
            id="inputs-wrap"
            justifyContent="space-between"
            // spacing={8}
          >
            <Grid
              item
              // xs={12}
              xs={home && !isCollapsed && window?.innerWidth > 960 ? 10 : 12}
              md={home && !isCollapsed && window?.innerWidth > 960 ? 11 : 12}
              container
              justifyContent="space-between"
              spacing={home && !isCollapsed ? 2 : 0}
            >
              <CategoryTabs t={t} />

              <LocationInputs />
              <DateTimeInputs />

              <Grid container alignItems="center">
                <Grid
                  className="input-wrap"
                  container
                  justifyContent="flex-end"
                  // alignItems="center"
                  style={{ gap: "10px", marginTop: "20px" }}
                >
                  {!isRentToOwn ? (
                    <Grid
                      className="cursor-pointer filters-btn"
                      style={{
                        // width: "fit-content",
                        border: "solid 1px var(--color-2)",
                        color: "var(--color-2)",
                        borderRadius: "var(--radius-1)",
                        padding:
                          i18n.language == "ar"
                            ? "0 50px 5px 50px"
                            : "10px 50px",
                        fontSize: "1rem",
                        textTransform: "lowercase",
                        alignSelf: "stretch",
                        display: "flex",
                        alignItems: "center",
                        // fontWeight: "bold",
                        // float: i18n.language == "ar" ? "left" : "right",
                      }}
                      onClick={() => {
                        // if (!isCollapsed) {
                        //   window.scrollTo(0, 0);
                        // }
                        setIsCollapsed(!isCollapsed);
                      }}
                    >
                      {t("filter") as string}

                      {/* {!isCollapsed ? (
                        <AddIcon style={{ width: "16px" }} />
                      ) : (
                        <RemoveIcon style={{ width: "16px" }} />
                      )} */}
                    </Grid>
                  ) : null}
                  {(window?.innerWidth < 960 || !home) && (
                    <Grid id="action-arrow">
                      <label title={t("Find a car")}>
                        <div
                          onClick={findCarHandler}
                          // src={`/assets/images/rightArrow.svg`}
                          // alt="arrow"
                          className={`${
                            !userAddress?.pick_up ||
                            !userAddress?.return ||
                            !dateTime?.pickup_time ||
                            !dateTime?.return_time ||
                            (selectionIndex === "2" &&
                              !confirmedDeliveryLocation)
                              ? "dimmed"
                              : ""
                          }`}
                        >
                          {t("Find a car") as string}
                        </div>
                      </label>
                    </Grid>
                  )}
                </Grid>

                {selectionIndex === 1 ? (
                  <Grid
                    item
                    xs={12}
                    md={8}
                    className={`${
                      !dateTime && !dateTime?.reservation_days ? "dimmed" : ""
                    }`}
                  >
                    <RentalPeriod />
                  </Grid>
                ) : null}
                <Grid container id="inputs-wrap" justifyContent="space-between">
                  {selectionIndex === 2 ? (
                    <div className="w-100 p-0 mt-3">
                      <DeliveryInputs rentalDetailsLocation={undefined} />
                      <p
                        onClick={() => setIsDeliveryPolicyOpen(true)}
                        className="text-decoration-underline px-1 mt-4 cursor-pointer"
                      >
                        {t("Delivery Terms & Conditions") as string}
                      </p>

                      <PopupComponent
                        isOpen={isDeliveryPolicyOpen}
                        title={t("Delivery Policy")}
                        setIsOpen={setIsDeliveryPolicyOpen}
                        maxWidth="md"
                      >
                        <p>{t("Delivery Policy Text") as string}</p>
                        <div className="text-center">
                          <button
                            className="primary-btn mt-4"
                            style={{
                              width: "fit-content",
                              padding: "7px 55px 7px",
                            }}
                            onClick={() => setIsDeliveryPolicyOpen(false)}
                          >
                            {t("Ok") as string}
                          </button>
                        </div>
                      </PopupComponent>
                    </div>
                  ) : null}
                </Grid>
              </Grid>
            </Grid>
            {home && !isCollapsed && window?.innerWidth > 960 ? (
              <Grid
                item
                xs={12}
                md={1}
                id="action-arrow"
                onClick={findCarHandler}
                className="cursor-pointer"
              >
                <img
                  src={`/assets/images/rightArrow.svg`}
                  alt="arrow"
                  style={{
                    transform: i18n.language === "ar" ? "scaleX(-1)" : "",
                  }}
                  className={`${
                    !userAddress?.pick_up ||
                    !userAddress?.return ||
                    !dateTime?.pickup_time ||
                    !dateTime?.return_time ||
                    (selectionIndex === "2" && !confirmedDeliveryLocation)
                      ? "dimmed"
                      : ""
                  }`}
                />

                <div>{t("Search") as string}</div>
              </Grid>
            ) : null}
          </Grid>
        </div>
        {isCollapsed ? (
          <Filters
            isCollapsed={isCollapsed}
            setIsCollapsed={setIsCollapsed}
            home={home}
            fetchCars={fetchCars}
            setFetchCars={setFetchCars}
            getCarList={getCarList}
          />
        ) : null}
      </Div>
      <ToAppPopup
        isOpenToAppPopup={isOpenToAppPopup}
        setIsOpenToAppPopup={setIsOpenToAppPopup}
      />
    </>
  );
}

export default memo(SearchBox);
