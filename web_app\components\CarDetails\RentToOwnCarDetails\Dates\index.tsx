/* eslint-disable @next/next/no-img-element */
import WorkingHours from "components/shared/WorkingHours";
import CalendarTooltip from "components/shared/calendarTooltip";
import { Dispatch, SetStateAction } from "react";
import styled from "styled-components";
import { DateTimeCombined } from "utilities/helpers";
import useCustomHook from "./useCustomHook";
import { useRouter } from "next/router";

const Div = styled.div`
  margin-bottom: 24px;
  .padding-30px {
    padding: 30px;
  }
  background-color: #ffffff;
  border-radius: var(--radius-2);
  h2 {
    font-size: 18px;
  }
  padding-bottom: 20px;
  .wrapper {
    padding: 0 20px;
    @media (max-width: 768px) {
      padding: 0;
    }
  }
  > div {
    width: 92%;
    @media (max-width: 768px) {
      width: 99%;
    }
  }
`;

const Wrapper = styled.div`
  position: relative;
  background-color: #f6f6f6;
  border-radius: 50px;
  padding: 20px 25px 24px 25px;
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 16px;
  margin: 0 auto;
  margin-top: 25px;
  margin-bottom: 25px;
  img {
    transform: ${(props) => (props.lang === "en" ? "rotate(180deg)" : "")};
  }
`;

function Dates({
  calendarTooltipOpen,
  setCalendarTooltipOpen,
}: {
  isRentToOwn?: boolean;
  calendarTooltipOpen: boolean;
  setCalendarTooltipOpen: Dispatch<SetStateAction<boolean>>;
}) {
  const { t, i18n, pickUpDate, pickup_time, generateAvailabilityDates } =
    useCustomHook();
  const router = useRouter();

  return (
    <Div lang={i18n.language}>
      <h2 className="padding-30px pt-4 pb-2">
        {t("Select the pick-up date") as string}
      </h2>
      <hr />

      <Wrapper lang={i18n.language}>
        <div>{DateTimeCombined(pickUpDate, pickup_time, i18n.language)}</div>
        {!Boolean(router.query.bookingId) && (
          <div
            className="cursor-pointer d-flex align-items-center"
            onClick={(e) => {
              e.stopPropagation();
              setCalendarTooltipOpen(true);
            }}
          >
            <span
              className="color-3 mx-2 font-16px"
              style={{ marginTop: "-2px" }}
            >
              {t("Edit") as string}
            </span>
            <img
              src="/assets/icons/blueMoreArrowRounded.svg"
              alt="more arrow"
            />
          </div>
        )}
        <CalendarTooltip
          {...{
            isOpen: calendarTooltipOpen,
            setIsOpen: setCalendarTooltipOpen,
            isSingleDaySelection: true,
            hideReturnTime: true,
            minDate: generateAvailabilityDates().minDate,
            maxDate: generateAvailabilityDates().maxDate,
          }}
        />
      </Wrapper>

      <div
        style={{
          backgroundColor: "#F6F6F6",
          margin: "0 auto",
          borderRadius: "40px",
          paddingRight: "10px",
          paddingLeft: "10px",
        }}
      >
        <WorkingHours />
      </div>
    </Div>
  );
}

export default Dates;
