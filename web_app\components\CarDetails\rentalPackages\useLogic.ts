/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from "react";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { DateObject } from "react-multi-date-picker";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { setPayWithInstallments } from "store/installments/action";
import { setSelectedPackage } from "store/installments/action";
import { setDateTimeAction, setRentalMonthsAction } from "store/search/action";
import { defaultPackagesDays } from "utilities/enums";
import { isEligibleForInstallments } from "utilities/helpers";

function useLogic() {
  const { t, i18n } = useTranslation();
  const { about_rent_price } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const { packages = [] } = about_rent_price || {};
  const dispatch = useDispatch();
  const dateTime = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );
  const { pickup_time, return_time, pickUpDate } = dateTime || {};
  const [savedPackages, setSavedPackages] = useState([]);
  const [customizedPackageState, setCustomPackageState] = useState<any>([]);
  const selectionIndex = useSelector(
    (state: RootStateOrAny) => state.search_data.selection_index
  );
  const [installmentSet, setInstallmentSet] = useState(false);
  const { pay_with_installments } =
    useSelector((state: RootStateOrAny) => state.installments) || {};
  const selectedPackage = packages?.find((p: any) => p.selected);

  useEffect(() => {
    const findCustomPackage = () =>
      packages?.find(
        (packageItem) => !defaultPackagesDays.includes(packageItem.daysCount)
      );

    const isDifferent = (newPackages) => {
      return JSON.stringify(newPackages) !== JSON.stringify(savedPackages);
    };

    const insertOrUpdateCustomPackage = (customPackage) => {
      if (!customPackage || Object.keys(customPackage).length === 0) {
        return packages; // Return original packages if customPackage is invalid
      }

      // Ensure to only remove custom packages, correctly identifying them
      const filteredPackages = packages.filter(
        (i) => !(i.isCustom && i.daysCount === customPackage.daysCount)
      );

      const whereToInsert = filteredPackages.findIndex(
        (item) => item.daysCount >= customPackage.daysCount
      );

      // Insert the custom package at the correct position or append it
      return whereToInsert >= 0
        ? [
            ...filteredPackages.slice(0, whereToInsert),
            customPackage,
            ...filteredPackages.slice(whereToInsert),
          ]
        : [...filteredPackages, customPackage];
    };

    const customizedPackage = findCustomPackage();

    // Ensure that we are updating the state based on the latest info
    if (customizedPackage && !customizedPackageState) {
      setCustomPackageState(customizedPackage);
    }

    const newPackages = customizedPackageState
      ? insertOrUpdateCustomPackage(customizedPackageState)
      : packages;

    // Only update if the packages are actually different
    if (isDifferent(newPackages)) {
      setSavedPackages(newPackages);
    }
  }, [packages, customizedPackageState, defaultPackagesDays]);
  useEffect(() => {
    dispatch(setPayWithInstallments(false));
    setInstallmentSet(false);
  }, []);
  useEffect(() => {
    if (
      selectedPackage &&
      selectedPackage.daysCount % 3 === 0 &&
      selectedPackage.daysCount >= 60 &&
      !installmentSet
    ) {
      dispatch(setPayWithInstallments(true));
      setInstallmentSet(true);
    }
  }, [about_rent_price]);
  function packageSelectionHandler(packageData) {
    dispatch(
      setDateTimeAction({
        pickUpDate,
        dropOffDate: moment(pickUpDate, "DD/MM/YYYY")
          .add(packageData.daysCount, "days")
          .format("DD/MM/YYYY"),
        selected_days_count: packageData.daysCount,
        pickup_time,
        return_time,
        reservation_days: [
          new DateObject({ date: pickUpDate, format: "DD/MM/YYYY" }),
          new DateObject({ date: pickUpDate, format: "DD/MM/YYYY" }).add(
            +packageData.daysCount,
            "days"
          ),
        ],
      })
    );
    dispatch(setSelectedPackage(packageData));
    if (packageData.daysCount >= 30 && selectionIndex == 1) {
      dispatch(setRentalMonthsAction(packageData.daysCount / 30));
    }
    if (isEligibleForInstallments(packageData.daysCount)) {
      dispatch(setPayWithInstallments(true));
    } else {
      dispatch(setPayWithInstallments(false));
    }
  }

  return {
    i18n,
    t,
    selectedPackage,
    savedPackages,
    pay_with_installments,
    packageSelectionHandler,
  };
}

export default useLogic;
