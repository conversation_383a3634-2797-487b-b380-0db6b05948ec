import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  background-color: var(--color-4);
  border-radius: var(--radius-4);
  direction: ${(props) => (props.language === "ar" ? "rtl" : null)};
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
`;

export default function WhiteCard({ children }) {
  const { i18n } = useTranslation();
  return (
    <Div lang={i18n.language} className="white-card">
      {children}
    </Div>
  );
}
