import { gql } from "@apollo/client";

const Business_Rental_Offers = gql`
  query businessRentalOffers($id: ID, $page: Int, $limit: Int) {
    businessRentalOffers(id: $id, limit: $limit, page: $page) {
      collection {
        additionalKilometer
        allyClass
        allyCompanyArName
        allyCompanyEnName
        allyCompanyId
        allyCompanyName
        rate
        allyRate
        businessRentalId
        carInsuranceFull
        carInsuranceStandard
        createdAt
        id
        kilometerPerMonth
        offerPrice
        policyAndConditions
        status
        statusLocalized
        businessRental {
          id
          makeName
          modelName
          year
          makeId
          otherCarName
          insuranceName
          status
          statusLocalized
          bookingNo
        }
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

export { Business_Rental_Offers };
