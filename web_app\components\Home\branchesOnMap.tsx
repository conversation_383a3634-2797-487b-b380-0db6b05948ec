import React, { useState } from "react";
import styled from "styled-components";
import BranchesMapBox from "./branchesMapBox";
import Carousel from "./carsCarousel";
import BranchesMap from "./branchesMap";
import ToAppPopup from "components/shared/toAppPopup";

const Div = styled.div`
  pointer-events: none;
  position: relative;
  overflow: hidden;
  @media (min-width: 960px) {
    height: 600px;
  }
  padding: 0 25px;
  @media (orientation: portrait) {
  }
`;

export default function BranchesOnMap() {
  const [isShowCarousel, setIsShowCarousel] = useState(false);
  const [isOpenToAppPopup, setIsOpenToAppPopup] = useState(false);

  return (
    <>
      <div
        onClick={() => {
          if (!isOpenToAppPopup) {
            setIsOpenToAppPopup(true);
          }
        }}
      >
        <Div>
          <BranchesMapBox />
          <BranchesMap
            isShowCarousel={isShowCarousel}
            setIsShowCarousel={setIsShowCarousel}
          />
          {isShowCarousel ? <Carousel /> : null}
        </Div>
      </div>
      <ToAppPopup
        isOpenToAppPopup={isOpenToAppPopup}
        setIsOpenToAppPopup={setIsOpenToAppPopup}
      />
    </>
  );
}
