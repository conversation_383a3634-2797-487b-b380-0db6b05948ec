import { useMutation } from "@apollo/client";
import {
  Login_Mutation,
  RequestPasscode_Mutation,
} from "gql/mutations/authentication";
import i18n from "localization";
import { useRouter } from "next/router";
import { useSnackbar } from "notistack";
import { ChangeEvent, useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { authDataAction } from "store/authentication/action";
import styled from "styled-components";
import { fnBrowserDetect } from "utilities/helpers";
import RequestLoader from "./requestLoader";

/* eslint-disable @next/next/no-img-element */

const WrongCode = styled.div`
  background: var(--color-9);
  color: var(--color-4);
  position: absolute;
  top: 18px;
  left: 10px;
  width: calc(100% - 20px);
  padding: 7px 10px;
  border-radius: var(--radius-3);
`;

export default function VerifyByPhone({
  t,
  isPhoneVerified,
  setIsPhoneVerified,
  But<PERSON>,
  DigitsCodeWrapper,
  mobileData,
  setMobileData,
}) {
  const [requestPasscode, { loading: requestPassCodeLoading }] = useMutation(
    RequestPasscode_Mutation
  );

  const [login, { loading: loadingLogin }] = useMutation(Login_Mutation, {
    context: {
      headers: {
        "x-user-agent": fnBrowserDetect(),
        "x-app-version": "website",
      },
    },
  });

  const [counter, setCounter] = useState(60);
  const [showWrongCode, setShowWrongCode] = useState(false);
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  const router = useRouter();

  const input1Ref = useRef(null);
  const input2Ref = useRef(null);
  const input3Ref = useRef(null);
  const input4Ref = useRef(null);

  function resendPasscode() {
    async function Mutation() {
      const res = await requestPasscode({
        variables: {
          input: {
            mobile: mobileData.number,
            deviceToken: mobileData.deviceToken,
            viaWhatsapp: mobileData?.viaWhatsapp,
          },
        },
      });
      if (res?.data?.requestPasscode?.status?.includes("success")) {
        const code = res.data.requestPasscode.status
          .replace("success", "")
          .trim();
        setMobileData({
          ...mobileData,
          code,
        });
        setCounter(60);
      } else {
        enqueueSnackbar(res.data.requestPasscode.errors[0], {
          variant: "error",
        });
        setCounter(60);
      }
    }
    Mutation();
  }

  useEffect(() => {
    const { code } = mobileData;
    if (code && code.length === 4 && input1Ref.current) {
      input1Ref.current.value = Number(code[0]);
      input2Ref.current.value = Number(code[1]);
      input3Ref.current.value = Number(code[2]);
      input4Ref.current.value = Number(code[3]);
    }
  }, [mobileData]);

  useEffect(() => {
    setTimeout(() => {
      if (counter > 0) {
        setCounter(counter - 1);
      }
    }, 1000);
  }, [counter]);

  useEffect(() => {
    if (input1Ref.current && !input1Ref.current.value) {
      input1Ref.current.focus();
    } else if (input4Ref.current && input4Ref.current.value) {
      input4Ref.current.focus();
    }
  }, []);

  async function Mutaion(passcode) {
    try {
      const res = await login({
        variables: {
          input: {
            mobile: mobileData.number,
            deviceToken: mobileData.deviceToken,
            passcode,
          },
        },
      });
      if (res.data.login.status === "success") {
        dispatch(authDataAction(res.data.login));
        sessionStorage.setItem(
          "authDataAction",
          JSON.stringify(res.data.login)
        );
        if (res.data && !res.data.login.basicProfileCompleted) {
          setIsPhoneVerified(true);
          return router.push("/my-account");
        } else {
          setIsPhoneVerified(true);
          window.location.reload();
        }
      } else if (res.data.login.errors) {
        setShowWrongCode(true);
      }
    } catch (err) {
      throw err;
    }
  }
  function submitFormHandler(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const num1 = formData.get("num1");
    const num2 = formData.get("num2");
    const num3 = formData.get("num3");
    const num4 = formData.get("num4");
    if (num1 && num2 && num3 && num4) {
      Mutaion(`${num1}${num2}${num3}${num4}`);
    }
  }

  return (
    <>
      <RequestLoader loading={requestPassCodeLoading || loadingLogin} />
      <div id="verify-phone">
        {!isPhoneVerified ? (
          <form
            onSubmit={(e) => {
              submitFormHandler(e);
            }}
          >
            {showWrongCode && (
              <WrongCode>{t("Enter the correct verification code")}</WrongCode>
            )}
            <h6 className="font-27px mt-4 mb-2">{t("Enter 4-digits code")}</h6>
            <div className="mt-5 mb-2">
              <p className="color-19 d-flex gap-5px justify-content-center">
                {t("We’ve sent you code to")}
                <span className="color-1" style={{ direction: "ltr" }}>
                  +{mobileData?.number}
                </span>
              </p>
              <DigitsCodeWrapper
                language={i18n.language}
                className="d-flex justify-content-center gap-25px mt-4"
              >
                <input
                  inputMode="numeric"
                  pattern="\d*"
                  type="text"
                  name="num1"
                  ref={input1Ref}
                  style={{ direction: "ltr" }}
                  maxLength={1}
                  onChange={(e) => {
                    e.target.value = e.target.value.replace(/[^\d]/, "");
                    if (e.target.value.length > 1) {
                      const digits = e.target.value.split('').slice(0, 4);
                      input1Ref.current.value = digits[0] || '';
                      input2Ref.current.value = digits[1] || '';
                      input3Ref.current.value = digits[2] || '';
                      input4Ref.current.value = digits[3] || '';
                      input4Ref.current.focus();
                    } else if (e.target.value) {
                      input2Ref.current.focus();
                    }
                  }}
                  onPaste={(e) => {
                    e.preventDefault();
                    const pasteData = e.clipboardData.getData("text").trim();
                    if (/^\d{4}$/.test(pasteData)) {
                      input1Ref.current.value = pasteData[0];
                      input2Ref.current.value = pasteData[1];
                      input3Ref.current.value = pasteData[2];
                      input4Ref.current.value = pasteData[3];
                      input4Ref.current.focus();
                    }
                  }}
                  autoComplete="one-time-code"
                />
                <input
                  inputMode="numeric"
                  pattern="\d*"
                  type="text"
                  name="num2"
                  ref={input2Ref}
                  style={{ direction: "ltr" }}
                  maxLength={1}
                  onChange={(e) => {
                    e.target.value = e.target.value.replace(/[^\d]/, "");
                    if (e.target.value) {
                      input3Ref.current.focus();
                    }
                  }}
                  onKeyDown={(e) => {
                    if (
                      e.key === "Backspace" &&
                      !(e.target as HTMLInputElement).value
                    ) {
                      input1Ref.current.focus();
                    }
                  }}
                />
                <input
                  inputMode="numeric"
                  pattern="\d*"
                  type="text"
                  name="num3"
                  ref={input3Ref}
                  style={{ direction: "ltr" }}
                  maxLength={1}
                  onChange={(e) => {
                    e.target.value = e.target.value.replace(/[^\d]/, "");
                    if (e.target.value) {
                      input4Ref.current.focus();
                    }
                  }}
                  onKeyDown={(e) => {
                    if (
                      e.key === "Backspace" &&
                      !(e.target as HTMLInputElement).value
                    ) {
                      input2Ref.current.focus();
                    }
                  }}
                />
                <input
                  inputMode="numeric"
                  pattern="\d*"
                  type="text"
                  name="num4"
                  ref={input4Ref}
                  style={{ direction: "ltr" }}
                  maxLength={1}
                  onChange={(e) => {
                    e.target.value = e.target.value.replace(/[^\d]/, "");
                  }}
                  onKeyDown={(e) => {
                    if (
                      e.key === "Backspace" &&
                      !(e.target as HTMLInputElement).value
                    ) {
                      input3Ref.current.focus();
                    }
                  }}
                />
              </DigitsCodeWrapper>
              {counter > 0 ? (
                <p className="color-19 d-flex gap-5px justify-content-center mb-4 mt-4">
                  {t("Resend code in")}
                  <span style={{ marginTop: "-2px" }}>{`0:${counter < 10 ? `0${counter}` : counter
                    }`}</span>
                </p>
              ) : (
                <div className="text-center">
                  <span
                    className="cursor-pointer color-3 medium d-inline-block mb-2 mt-4 "
                    onClick={() => {
                      resendPasscode();
                    }}
                  >
                    {t("Resend")}
                  </span>
                </div>
              )}
            </div>
            <Button
              type="submit"
              className="font-17px color-4 border-0 medium w-100 mt-4"
            >
              {t("Login")}
            </Button>
          </form>
        ) : (
          <div>
            <div className="d-flex flex-column justify-content-center align-items-center mt-5">
              <img src="/assets/images/verified.svg" alt="verified" />
              <h4 className="color-3 bold mt-4">
                {t("Verification Completed")}
              </h4>
              <p className="d-inline-block color-19 mb-5">
                {t("Your phone is verified")}
              </p>
            </div>
            <Button className="font-17px color-4 border-0 medium w-100 mt-4 w-auto">
              {t("Done")}
            </Button>
          </div>
        )}
      </div>
    </>
  );
}
