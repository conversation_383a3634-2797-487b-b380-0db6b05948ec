import { gql } from "@apollo/client";
export const RateAllyMutation=gql`
mutation AllyRate($allyComment:String,$allyRate:Int!,$rentalId:ID!) {
    allyRate(allyComment: $allyComment, allyRate: $allyRate, rentalId: $rentalId) {
      errors
      rental {
        # addsPrice
        # allyCompanyId
        # allyName
        # allyRate
        # allyRentalRate
        # arAllyName
        # arDropOffCityName
        # arMakeName
        # arModelName
        # arPickUpCityName
        # arVersionName
        # assignedAt
        # assignedBy
        # assignedByName
        # assignedTo
        # assignedToName
        # bookingNo
        # branchAreaId
        # branchAreaNameAr
        # branchAreaNameEn
        # branchDistrictNameAr
        # branchDistrictNameEn
        # branchId
        # branchName
        # cancelledAt
        # cancelledReason
        # carId
        # carImage
        # closedAt
        # couponCode
        # couponDiscount
        # couponId
        # couponType
        # createdAt
        # customerBookingLat
        # customerBookingLng
        # customerDob
        # customerLocale
        # customerMobile
        # customerName
        # customerProfileImage
        # customerRate
        # customerRentalRate
        # dailyPrice
        # deliverAddress
        # deliverLat
        # deliverLng
        # deliverType
        # deliveryDistance
        # deliveryPrice
        # discountPercentage
        # discountType
        # discountValue
        # dropOffBranchId
        # dropOffCityId
        # dropOffCityName
        # dropOffDate
        # dropOffTime
        # enAllyName
        # enDropOffCityName
        # enMakeName
        # enModelName
        # enPickUpCityName
        # enVersionName
        # handoverAddress
        # handoverAntherCity
        # handoverDistance
        # handoverLat
        # handoverLng
        # handoverPrice
        # hasPendingExtensionRequests
        # hasUnseenExtensionConfirmation
        # id
        # insuranceId
        # insuranceIncluded
        # invoicePic
        # invoicedAt
        # is24Passed
        # isExtendable
        # isIntegratedRental
        # isPaid
        # isPassed
        # isUnlimited
        # isUnlimitedFree
        # makeName
        # modelName
        # newGrandTotal
        # note
        # numberOfDays
        # payable
        # paymentBrand
        # paymentMethod
        # paymentStatus
        # paymentStatusCode
        # paymentStatusMessage
        # pickUpCityId
        # pickUpCityName
        # pickUpDate
        # pickUpTime
        # priceBeforeDiscount
        # priceBeforeInsurance
        # priceBeforeTax
        # pricePerDay
        # refundable
        # refundedAmount
        # refundedAt
        # refundedBy
        # rentalIntegrationErrorMessage
        # rentalIntegrationResponse
        # rentalIntegrationStatus
        # status
        # statusLocalized
        # subStatus
        # subStatusLocalized
        # suggestedPrice
        # taxNote
        # taxValue
        # totalBookingPrice
        # totalExtraServicesPrice
        # totalInsurancePrice
        # totalUnlimitedFee
        # unlimitedFeePerDay
        userId
        valueAddedTaxPercentage
        versionName
        year
      }
      status
    }
  }
  `;