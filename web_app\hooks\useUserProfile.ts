/* eslint-disable react-hooks/exhaustive-deps */
import { useQuery } from "@apollo/client";
import { Get_Profile } from "gql/queries/profile";
import { useEffect } from "react";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { profileDataAction } from "store/user/action";

function useUserProfile() {
  const dispatch = useDispatch();
  const authentication = useSelector(
    (state: RootStateOrAny) => state.authentication
  );
  const { token } = authentication?.login_data || {};
  const { data, refetch } = useQuery(Get_Profile, { skip: !Boolean(token) });
  const { profile } = data || {};

  useEffect(() => {
    if (profile) {
      dispatch(profileDataAction(profile));
    }
  }, [profile]);

  return { refetch };
}

export default useUserProfile;
