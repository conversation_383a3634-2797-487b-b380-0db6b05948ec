# Payment Integration Guide for Carwah Dashboard

This guide provides step-by-step instructions for integrating the new payment system into the Carwah dashboard.

## Overview

The payment system has been designed to handle all payment scenarios for agency users in the dashboard, including:
- Regular rental payments
- Extension payments  
- Installment payments
- Wallet payments
- Agency deactivation checks

## Quick Start

### 1. Environment Setup

Add these environment variables to your `.env` file:

```env
# Tamara Configuration
REACT_APP_TAMARA_PUBLIC_KEY=0aa13b77-e140-4e1c-9139-663443e4ecd1
REACT_APP_TAMARA_CDN=https://cdn-sandbox.tamara.co/widget-v2/tamara-widget.js

# Hyperpay Configuration  
REACT_APP_HYPERPAY_URL=https://eu-test.oppwa.com/v1/paymentWidgets.js
REACT_APP_OPPWA_URL=https://eu-test.oppwa.com

# Base URL for payment result handling
REACT_APP_BASE_URL=https://your-domain.com

# Firebase Configuration for Remote Config (skip_integrity feature)
REACT_APP_FIREBASE_CONFIG='{"apiKey":"AIzaSyAgCFEKgEAXYKVJn998vKkGRJBcEKW7VxQ","authDomain":"carwah-204411.firebaseapp.com","databaseURL":"https://carwah-204411.firebaseio.com","projectId":"carwah-204411","storageBucket":"carwah-204411.appspot.com","messagingSenderId":"727054343094","appId":"1:727054343094:web:813aad1634fa7322e63f17","measurementId":"G-PB128Q57F1"}'
```

### 2. Basic Integration

For a simple payment button in any component:

```jsx
import PaymentButton from 'components/PaymentButton/PaymentButton';

function MyComponent() {
  return (
    <PaymentButton
      rentalId="123"
      totalAmount={500}
      onPaymentSuccess={(method) => console.log('Paid with:', method)}
      onPaymentError={(error) => console.error('Payment failed:', error)}
    >
      Pay Now
    </PaymentButton>
  );
}
```

### 3. Full Modal Integration

For complete control over the payment flow:

```jsx
import PaymentFlow from 'components/PaymentFlow/PaymentFlow';

function BookingComponent() {
  const [paymentOpen, setPaymentOpen] = useState(false);
  
  return (
    <>
      <button onClick={() => setPaymentOpen(true)}>
        Pay Booking
      </button>
      
      <PaymentFlow
        isOpen={paymentOpen}
        onClose={() => setPaymentOpen(false)}
        rentalId="123"
        totalAmount={500}
        walletBalance={100}
        onPaymentSuccess={(method) => {
          // Handle success
          setPaymentOpen(false);
        }}
        onPaymentError={(error) => {
          // Handle error
          console.error(error);
        }}
      />
    </>
  );
}
```

## Integration Points

### 1. Booking Creation (AgencyBookingAddEdit.js)

The payment system is already integrated into the booking creation flow:

```jsx
// In AgencyBookingAddEdit.js
const handleSaveAndPay = async () => {
  // Create booking first
  const booking = await createBooking();
  
  // Open payment modal
  setCreatedRentalId(booking.id);
  setPaymentModalOpen(true);
};
```

### 2. Extension Requests

For extension payments, use the `extensionId` prop:

```jsx
<PaymentFlow
  rentalId="123"
  extensionId="ext-456"
  totalAmount={150}
  // ... other props
/>
```

### 3. Installment Payments

For installment payments, use the `isInstallment` prop:

```jsx
<PaymentFlow
  rentalId="123"
  isInstallment={true}
  totalAmount={200}
  // ... other props
/>
```

### 4. Booking Details Display

Payment notes are automatically added to booking details when paid by agency:

```jsx
// In BookingDetails.js - automatically shows:
// "(Through Agency [Agency-Name])" in the notes section
```

## GraphQL Integration

### Required Mutations

Ensure these GraphQL mutations are available:

```graphql
# Regular payments
mutation GetCheckoutId($rentalId: ID!, $paymentBrand: RentalPaymentBrand, $withWallet: Boolean)
mutation GetPaymentStatus($checkoutId: String!, $rentalId: ID!)
mutation TamaraCreateCheckoutSession($rentalId: ID!, $locale: String, $withWallet: Boolean)
mutation PayByWallet($rentalId: ID!)

# Extension payments  
mutation ExtensionGetCheckoutId($rentalExtensionId: ID!, $paymentBrand: RentalPaymentBrand, $withWallet: Boolean)
mutation ExtensionGetPaymentStatus($checkoutId: String!, $rentalExtensionId: ID!)
mutation TamaraCreateExtensionCheckout($rentalExtensionId: ID!, $locale: String, $withWallet: Boolean)
mutation ExtensionPayByWallet($extensionRequestId: ID!)

# Installment payments
mutation InstallmentGetCheckoutId($rentalId: ID!, $paymentBrand: RentalPaymentBrand, $withWallet: Boolean)
mutation InstallmentGetPaymentStatus($checkoutId: String!, $rentalId: ID!)
mutation TamaraCreateInstallmentCheckout($rentalId: ID!, $locale: String, $withWallet: Boolean)
```

### Agency Status Query

```graphql
query Agency($id: ID!) {
  agency(id: $id) {
    id
    isActive
    name
    # ... other fields
  }
}
```

## Component API Reference

### PaymentFlow Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `isOpen` | boolean | Yes | Controls modal visibility |
| `onClose` | function | Yes | Called when modal closes |
| `rentalId` | string | Yes | ID of the rental |
| `extensionId` | string | No | ID for extension payments |
| `isInstallment` | boolean | No | Flag for installment payments |
| `totalAmount` | number | Yes | Total payment amount |
| `walletBalance` | number | No | Available wallet balance |
| `isAgencyDeactivated` | boolean | No | Agency deactivation status |
| `onPaymentSuccess` | function | Yes | Success callback |
| `onPaymentError` | function | Yes | Error callback |

### PaymentButton Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `rentalId` | string | Yes | ID of the rental |
| `extensionId` | string | No | ID for extension payments |
| `installmentId` | string | No | ID for installment payments |
| `totalAmount` | number | Yes | Total payment amount |
| `walletBalance` | number | No | Available wallet balance |
| `onPaymentSuccess` | function | No | Success callback |
| `onPaymentError` | function | No | Error callback |
| `disabled` | boolean | No | Disable button |
| `size` | string | No | Button size (sm, md, lg) |
| `color` | string | No | Button color |
| `className` | string | No | Additional CSS classes |
| `children` | node | No | Button content |

## Error Handling

The system provides comprehensive error handling:

### Agency Deactivation
```jsx
// Automatically prevents payments and shows error message
"Agency is deactivated. Payment is not allowed."
```

### Payment Failures
```jsx
// Uses web app status messages
"Dear customer.. the payment process was unsuccessful, try to pay again"
```

### Network Issues
```jsx
// Automatic retry mechanisms and manual status checking
```

## Testing

### Unit Tests
```bash
npm test src/components/PaymentFlow/__tests__/PaymentFlow.test.js
```

### Integration Testing
1. Test with deactivated agency
2. Test all payment methods (Mada, Credit Card, Tamara)
3. Test extension and installment flows
4. Test wallet integration
5. Test error scenarios

### Manual Testing Checklist

- [ ] Regular rental payment with Mada
- [ ] Regular rental payment with Credit Card  
- [ ] Regular rental payment with Tamara
- [ ] Extension payment flow
- [ ] Installment payment flow
- [ ] Wallet payment (full and partial)
- [ ] Agency deactivation prevention
- [ ] Payment status polling
- [ ] Error handling and retry
- [ ] Mobile responsiveness
- [ ] Arabic/English localization

## Deployment Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] GraphQL mutations tested
- [ ] Payment gateway credentials verified
- [ ] Agency status checking working
- [ ] All tests passing

### Post-deployment
- [ ] Monitor payment success rates
- [ ] Check error logs for issues
- [ ] Verify agency payment notes
- [ ] Test with real payment amounts
- [ ] Monitor performance metrics

## Troubleshooting

### Common Issues

1. **Payment widget not loading**
   - Check OPPWA_URL environment variable
   - Verify network connectivity to payment gateway
   - Check browser console for script errors

2. **Tamara widget issues**
   - Verify TAMARA_CDN and TAMARA_PUBLIC_KEY
   - Check Tamara service status
   - Ensure proper locale configuration

3. **Agency deactivation not working**
   - Verify useAgencyStatus hook
   - Check agency query implementation
   - Ensure proper Redux state management

4. **Payment status not updating**
   - Check GraphQL endpoint availability
   - Verify polling configuration
   - Monitor for timeout issues

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will provide detailed console logs for:
- Payment widget loading
- GraphQL mutations
- Status polling
- Error details

## Firebase Remote Config

### Skip Integrity Feature
The payment system integrates with Firebase Remote Config to support the `skip_integrity` feature from the web app:

```jsx
// Automatically handled in PaymentFlow component
const { remoteConfigValues } = useFirebase();
const { skip_integrity } = remoteConfigValues;
```

### Configuration Steps
1. **Firebase Console**: Set `skip_integrity` parameter in Remote Config
2. **Environment**: Add Firebase config to `.env`
3. **Automatic**: System fetches and applies setting automatically

### When to Use
- **Development**: When CDN integrity hashes change frequently
- **Testing**: To bypass SRI checks during testing
- **Production**: Only when specifically needed for compatibility

### Monitoring
Check browser console for integrity-related logs:
```
"Using integrity check for payment widget"
"Skipping integrity check for payment widget (skip_integrity: true)"
```

## Support

For issues or questions:
1. Check the component README files
2. Review the example implementations
3. Run the test suite for debugging
4. Check browser console for errors
5. Verify environment variables and GraphQL endpoints
6. Check Firebase Remote Config values in console

## Migration from Old System

If migrating from an existing payment system:

1. **Replace payment buttons** with `PaymentButton` component
2. **Update payment modals** to use `PaymentFlow` component  
3. **Add agency status checks** using `useAgencyStatus` hook
4. **Update GraphQL mutations** to support extensions and installments
5. **Test thoroughly** with all payment scenarios

The new system is designed to be backward compatible while providing enhanced functionality for agency users.
