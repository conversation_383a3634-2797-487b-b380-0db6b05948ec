/* eslint-disable @next/next/no-img-element */
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  background-color: white;
  border-radius: var(--radius-2);
  box-shadow: 5px 10px 10px 3px rgba(0, 0, 0, 0.05);
  cursor: pointer;

  position: relative;
  .months {
    display: flex;
    justify-content: end;
    color: white;
    div {
      display: flex;
      align-items: center;
      gap: 5px;
      background-color: #2a292f;
      position: absolute;
      top: 0;
      left: ${(props) => (props.lang === "ar" ? "0" : "")};
      right: ${(props) => (props.lang === "en" ? "0" : "")};
      padding: 2px 20px 10px 20px;
      border-radius: ${(props) => (props.lang === "en" ? "0 15px" : "15px 0")};
    }
    span:first-child {
      font-size: 28px;
      font-weight: bold;
    }
    span:last-child {
      transform: translateY(5px) !important;
    }
  }
  hr {
    margin: 0;
  }
`;

const CarInfo = styled.div`
  display: flex;
  justify-content: start;
  gap: 50px;
  flex-wrap: wrap;
  align-items: center;
  padding: 15px 30px;
  div {
    transform: translateY(20px);
  }
  h2 {
    font-weight: bold;
  }
  h6 {
    margin-top: 10px !important;
    color: #a1a1a1;
  }
  img {
    width: 250px;
  }
  @media (max-width: 768px) {
    margin-top: 5px !important;
    gap: 0px !important;
    img {
      width: 80%;
    }
    div {
      transform: translateY(0px) !important;
    }
  }
`;

const FeatureCardStyled = styled.div`
  display: flex;
  gap: 10px;
  background-color: #f6f6f6;
  border-radius: var(--radius-3);
  padding: 20px;
  flex-grow: 0.2;
  h6 {
    font-size: 14px;
    text-transform: "capitalize";
  }
  h5 {
    font-size: 16px;
    font-weight: bold;
  }
  img {
    width: 24px;
  }
`;

function FeatureCard({
  title,
  subtitle,
  icon,
}: {
  title: string;
  subtitle: string;
  icon: string;
}) {
  return (
    <FeatureCardStyled>
      <img src={icon} alt="icon" />
      <div>
        <h6 className="mb-1">{title}</h6>
        <h5>{subtitle}</h5>
      </div>
    </FeatureCardStyled>
  );
}

const FeaturedCards = styled.div`
  display: flex;
  gap: 15px;
  padding: 30px;
  flex-wrap: wrap;
  background: rgb(255, 255, 255);
  background: rgb(255, 255, 255);
  /* background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(186, 186, 186, 0.25) 200%
  ); */
  padding-bottom: 45px;
  /* margin-bottom: 20px; */
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  flex-wrap: wrap;
  .label {
    background-color: #e5f4f8;
    color: var(--color-3);
    border-radius: 50px;
    font-weight: bold;
    margin: 20px;
    padding: ${(props) =>
      props.lang === "ar" ? "7px 10px 14px 10px" : "10px"};
    padding-bottom: ${(props) =>
      props.lang === "ar" ? "10px !important" : ""};
    &.payment {
      background-color: #f6f6f6 !important;
      color: #000000 !important;
      &.green {
        background-color: #4eda4e !important;
        color: darkgreen !important;
      }
    }
    @media (max-width: 768px) {
      margin: 0 10px 20px 10px;
    }
  }
`;

const PricePerMonth = styled.div`
>span{
      font-size: 20px;
    }
  > div {
    border-radius: ${(props) => (props.lang === "ar" ? "0 15px" : "15px 0")};
    padding: ${(props) =>
      props.lang === "ar" ? "10px 10px 15px 10px" : "10px"};
    background-color: var(--color-2);
    display: flex;
    color: white;
    width: max-content;
    // text-align: start;
    align-items: center;
    gap: 2px;
    // transform: translateY(-5px);
    // margin-top: 8px;
    span.price {
      font-size: 26px;
      // font-weight: bold;
    }
    // span:last-child {
    //   font-size: 26px;
    // }
    
    @media (max-width: 768px) {
      position: relative;
    }
      > div{
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }
`;

function CarGroupingCard(props: any) {
  const {
    id,
    carImages,
    year,
    ownCarDetail,
    carMakeName,
    carModelName,
    branch,
    transmissionName,
    additionalDistanceCost,
    distanceByMonth,
    carThumb,
    isUnlimited,
    isUnlimitedFree,
  } = props || {};
  const { ownCarPlans, km, color, availableAfterDays } = ownCarDetail || {};
  const { t, i18n } = useTranslation();
  const router = useRouter();
  return (
    <Div
      lang={i18n.language}
      onClick={() => {
        router.push(`/car-details/?car=${id}&isRentToOwn=true`);
      }}
    >
      <div className="months">
        <div>
          <span>{`${ownCarPlans?.[0]?.noOfMonths}`}</span>
          <span>{t("month") as string}</span>
        </div>
      </div>
      <CarInfo lang={i18n.language}>
        <img src={carThumb ? carThumb : carImages?.[0]} alt="car image" />
        <div>
          <h2>{`${carMakeName} ${carModelName}`}</h2>
          <h6>{year}</h6>
        </div>
      </CarInfo>
      <hr />
      <FeaturedCards>
        <FeatureCard
          title={t("City")}
          subtitle={branch?.area?.name}
          icon="/assets/icons/city.svg"
        />
        <FeatureCard
          title={t("KM")}
          subtitle={`${km} ${t("km")}`}
          icon="/assets/icons/km.svg"
        />
        <FeatureCard
          title={t("Color")}
          subtitle={color?.name}
          icon="/assets/icons/color.svg"
        />
        <FeatureCard
          title={t("transmission")}
          subtitle={transmissionName}
          icon="/assets/icons/transmission.svg"
        />
        {isUnlimited && isUnlimitedFree ? (
          <>
            <FeatureCard
              title={t("Unlimited kilometers")}
              subtitle={t(`is unlimited`)}
              icon="/assets/icons/allowedkm.svg"
            />
            <FeatureCard
              title={t("extra.km")}
              subtitle={`${t("Free")}`}
              icon="/assets/icons/additionalkm.svg"
            />
          </>
        ) : (
          <>
            <FeatureCard
              title={t("Additional KM")}
              subtitle={`${distanceByMonth} ${t("KM/month")}`}
              icon="/assets/icons/allowedkm.svg"
            />
            <FeatureCard
              title={t("km.per.day")}
              subtitle={additionalDistanceCost}
              icon="/assets/icons/additionalkm.svg"
            />
          </>
        )}
      </FeaturedCards>
      <CardFooter lang={i18n.language}>
        <div
          className="label"
          style={
            availableAfterDays
              ? { background: "var(--color-2)", color: "#fff" }
              : null
          }
        >
          {availableAfterDays
            ? (t("Pre-booking") as string)
            : (t("Available") as string)}
        </div>
        <div>
          <div
            className={`label payment ${
              !ownCarPlans?.[0]?.firstInstallment ? "green" : ""
            }`}
          >
            {ownCarPlans?.[0]?.firstInstallment ? (
              <div className="d-flex gap-5px align-items-baseline">
                {t("1st payment")}
                <RiyalSymbol
                  style={{ order: i18n.language === "ar" ? 1 : 0 }}
                />
                <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                  {ownCarPlans?.[0]?.firstInstallment}
                </span>
              </div>
            ) : (
              `${t("No 1st payment")}`
            )}
          </div>
        </div>
        <PricePerMonth lang={i18n.language}>
          <div>
            <div className="d-flex gap-5px bold align-items-baseline">
              <RiyalSymbol
                className="price"
                style={{ order: i18n.language === "ar" ? 1 : 0 }}
              />
              <span
                className="price"
                style={{ order: i18n.language === "ar" ? 0 : 1 }}
              >
                {ownCarPlans?.[0]?.monthlyInstallment}
              </span>
            </div>

            <span style={{ fontSize: "18px" }}>{t("/month") as string}</span>
          </div>
        </PricePerMonth>
      </CardFooter>
    </Div>
  );
}

export default CarGroupingCard;
