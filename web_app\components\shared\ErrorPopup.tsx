import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { Button } from "@material-ui/core";
import { Dispatch, SetStateAction } from "react";
import { useRouter } from "next/router";
import MessagePopup from "components/CarDetails/popups/messagePopup";

const Div = styled.div`
  * {
    text-align: center !important;
  }
  h4 {
    margin-bottom: 10px !important;
  }
  .btn {
    text-align: center;
    margin-top: 20px;
    background-color: var(--color-3);
    color: var(--color-4);
    padding: 10px 20px 12px 20px;
    font-weight: bold;
    &:hover {
      background-color: var(--color-3);
    }
  }
`;
export default function ErrorPopup(props: {
  buttonText: string;
  clickHandler: () => void;
  text: string;
  title: string;
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}) {
  const { t } = useTranslation();
  const { push } = useRouter();

  const { buttonText, clickHandler, text, title, isOpen, setIsOpen } = props;

  if (isOpen) {
    return (
      <MessagePopup
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        body={
          <Div>
            <h5 className="text-center mb-2">
              {/* {t("request") as string} */}
              {title}
            </h5>
            <p className="text-center">
              {/* {t("creating") as string} */}
              {text}
            </p>
            <div className="w-100 d-flex justify-content-center">
              <Button
                // onClick={() => push(`/car-search#car-grids`)}
                onClick={clickHandler}
              >
                {/* {t("view") as string} */}
                {buttonText}
              </Button>
            </div>
          </Div>
        }
        title=""
      />
    );
  }
  return null;
}
