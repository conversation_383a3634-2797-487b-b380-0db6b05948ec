/* eslint-disable @next/next/no-img-element */
// components/PayByLink/index.tsx
import React, { useRef, useEffect, useState, useCallback } from "react";
import {
  Box,
  Typography,
  <PERSON>ton,
  Card,
  CircularProgress,
} from "@material-ui/core";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";
import styled from "styled-components";
import LockIcon from "@material-ui/icons/Lock";
import TimerIcon from "@material-ui/icons/Timer";
import CreditCardIcon from "@material-ui/icons/CreditCard";
import CheckCircleIcon from "@material-ui/icons/CheckCircle";
import usePayByLink, { PaymentState } from "hooks/usePayByLink";
import SelectComponent from "components/shared/select";
import { useQuery } from "@apollo/client";
import { PaymentLinkStatus_Query } from "gql/queries/paymentLinkStatus";
import RiyalSymbol from "components/shared/RiyalSymbol";
import { v4 as uuidv4 } from "uuid";
import Head from "next/head";
import useFirebase from "useFirebase";
// Declare the wpwl global type with a simpler approach
// Global types are already declared elsewhere, avoiding conflicts

// Define WpwlOptions interface to ensure TypeScript compatibility

const PageContainer = styled.div`
  position: relative;
  /* min-height: 100vh; */
  padding: 0;
  margin: 0;
  direction: ${(props) => (props.lang === "ar" ? "rtl" : "ltr")};
  overflow-x: hidden;
  background-image: url("/assets/payment/overlay.svg");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  justify-content: space-between;
  .page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
    @media (max-width: 1024px) {
      flex-direction: column-reverse;
    }
    .logo-pos-container {
      display: flex;
      flex-direction: column;
      width: 50%;
      justify-content: center;
    }
    .logo-container {
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 200px !important;
      }
    }
    .pos-container {
      text-align: end;
      img {
        width: 80% !important;
      }
    }
    .payment-card-container {
      padding: 5%;
      width: 50%;
    }
    @media (max-width: 920px) {
      .payment-card-container {
        margin-top: 20px;
      }
      width: 100% !important;
      > div {
        width: 100% !important;
        padding: 32px 0 !important;
      }
    }
  }
`;

const PaymentCard = styled(Card)`
  padding: 40px 50px;
  box-shadow: 0px 9px 30px rgba(77, 81, 86, 0.1);
  border-radius: 20px;
  position: relative;
  background-color: #ffffff;
  z-index: 2;
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: calc(100% - 40px);
    min-height: auto;
    margin: 20px;
    padding: 20px;
  }
`;

const Divider = styled.div`
  width: 100%;
  height: 1px;
  background: #e9e9e9;
  margin: 20px 0;
`;

const TotalBox = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 15px;
  padding: 15px 20px;
  margin: 30px 0;
  width: 100%;
`;

const StatusMessage = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-family: "system-ui", system-ui;
  font-size: 16px;
  line-height: 25px;
  color: ${(props) => props.color};
  flex-direction: row;
  justify-content: flex-start;
  width: 100%;

  svg {
    margin-left: 10px;
  }
`;

const TermsText = styled(Typography)`
  text-align: center;
  font-size: 16px;
  margin-top: 30px;
  font-family: "system-ui", system-ui;
  line-height: 36px;

  span {
    color: #7ab3c5;
    cursor: pointer;
  }
`;

const LangSwitcher = styled.div`
  position: absolute;
  top: 20px;
  right: ${(props) => (props.lang === "ar" ? "auto" : "20px")};
  left: ${(props) => (props.lang === "ar" ? "20px" : "auto")};
  z-index: 10;
  display: flex;
  align-items: center;

  .select {
    border: none;
    font-family: ${(props) =>
      props.lang === "en"
        ? "var(--font-En) !important"
        : "var(--font-ar) !important"};
  }
`;

const PaymentMethodSelector = styled(Box)(({ theme }) => ({
  margin: "20px 0",
  width: "100%",

  ".payment-method-label": {
    marginBottom: "10px",
    fontWeight: 500,
  },

  ".payment-methods": {
    display: "flex",
    justifyContent: "space-between",
    gap: "10px",
    width: "100%",
    alignItems: "center",
  },

  ".payment-method": {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    padding: "12px",
    borderRadius: "8px",
    border: "1px solid #eee",
    cursor: "pointer",
    flex: 1,
    transition: "all 0.2s ease",
    width: "100%",

    "&.selected": {
      borderColor: "#7AB3C5",
      backgroundColor: "rgba(122, 179, 197, 0.05)",
    },

    img: {
      marginBottom: "8px",
    },
  },
}));

const SafariBanner = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ff9500 0%, #ff6b00 100%);
  color: white;
  padding: 12px 20px;
  text-align: center;
  font-size: 14px;
  line-height: 1.4;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: "system-ui", system-ui;

  .close-button {
    position: absolute;
    top: 50%;
    right: ${(props) => (props.lang === "ar" ? "auto" : "15px")};
    left: ${(props) => (props.lang === "ar" ? "15px" : "auto")};
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }

  .banner-content {
    max-width: calc(100% - 60px);
    margin: 0 auto;
  }

  .browser-list {
    font-weight: 600;
    margin-top: 4px;
  }

  @media (max-width: 768px) {
    font-size: 13px;
    padding: 10px 15px;

    .banner-content {
      max-width: calc(100% - 50px);
    }
  }
`;

const BannerSpacer = styled.div`
  height: ${(props) => (props.show ? "80px" : "0")};
  transition: height 0.3s ease;

  @media (max-width: 768px) {
    height: ${(props) => (props.show ? "70px" : "0")};
  }
`;

const PayByLink: React.FC = () => {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const [formKey, setFormKey] = useState(0);
  const [nonce] = useState(() => uuidv4());
  const { remoteConfigValues } = useFirebase();
  const { skip_integrity } = remoteConfigValues || {
    skip_integrity: { _value: "false" },
  };

  // Safari mobile detection
  const isSafariMobile = () => {
    if (typeof window === "undefined") return false;
    const userAgent = navigator.userAgent;
    const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
    const isMobile = /iPhone|iPad|iPod/i.test(userAgent);
    return isSafari && isMobile;
  };

  // Safari browser detection (desktop and mobile)
  const isSafari = () => {
    if (typeof window === "undefined") return false;
    const userAgent = navigator.userAgent;
    return /^((?!chrome|android).)*safari/i.test(userAgent);
  };

  const [showSafariBanner, setShowSafariBanner] = useState(false);

  // Check for Safari mobile on component mount
  useEffect(() => {
    setShowSafariBanner(isSafariMobile());
  }, []);

  // Function to close Safari banner
  const closeSafariBanner = () => {
    setShowSafariBanner(false);
  };

  // Handle navigation back from GET_STATUS view with full page reload
  const handleNavigateHome = () => {
    // Clear all payment-related data and force full page reload
    if (typeof window !== "undefined") {
      // Clear any payment widget data
      if (window.wpwl) {
        delete window.wpwl;
      }
      if (window.wpwlOptions) {
        delete window.wpwlOptions;
      }

      // Remove all payment scripts
      const scripts = document.querySelectorAll(
        'script[src*="paymentWidgets"]'
      );
      scripts.forEach((script) => script.remove());

      // Reload the PayByLink page with the original token (remove status parameter)
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.delete("status"); // Remove status parameter to show payment form again

      // Force full page navigation back to payment form
      window.location.href =
        window.location.origin +
        window.location.pathname +
        "?token=" +
        router.query.token;
    }
  };

  // Setup GraphQL query for payment link status
  const { loading: paymentStatusLoading, data: paymentStatusData } = useQuery(
    PaymentLinkStatus_Query,
    {
      fetchPolicy: "network-only",
      errorPolicy: "all",
      skip:
        !router.isReady ||
        !(router.query.status === "get_status" && router.query.token), // Skip if no status=success and token
      variables:
        router.isReady &&
        router.query.status === "get_status" &&
        router.query.token
          ? { token: router.query.token as string }
          : undefined,
    }
  );

  // Use the custom hook to handle payment logic
  const {
    paymentView,
    checkoutId,
    paymentData,
    paymentBrand,
    updatePaymentBrand,
    isLoading,
    checkoutData,
  } = usePayByLink();

  // Extract integrity value from checkoutData (PCI DSS v4.0 compliance)
  const integrity = !skip_integrity
    ? checkoutData?.paymentLinkCheckout?.integrity
    : null;

  const paymentStatus: "paid" | "pending" | "failed" =
    paymentStatusData?.getPaymentLinkStatus?.status;

  // Add a more robust script cleanup function
  const removeExistingPaymentScript = useCallback(() => {
    // Remove all existing payment widget scripts
    const existingScripts = document.querySelectorAll(
      '#payment-widget, #payment-widget-fallback, script[id^="payment-"]'
    );
    existingScripts.forEach((script) => {
      script.remove();
    });

    // Wait a tick to ensure DOM is updated
    return new Promise((resolve) => setTimeout(resolve, 10));
  }, []);

  // Cleanup function to remove existing payment widgets
  const cleanupPaymentForm = useCallback(() => {
    const existingForm = document.querySelector(".paymentWidgets");
    if (existingForm) {
      existingForm.remove();
    }

    // Use the more robust script removal
    removeExistingPaymentScript();

    // Reset wpwlOptions to a clean state instead of deleting it
    if (window.wpwlOptions) {
      window.wpwlOptions = {} as any;
    }
  }, [removeExistingPaymentScript]);

  // Reset submission state when payment brand changes
  useEffect(() => {
    setIsSubmitting(false);
  }, [paymentBrand]);

  // No need for this effect anymore since we're using useQuery with skip option

  // Initialize form when payment state or brand changes
  useEffect(() => {
    // Only load script when we have both checkoutId AND integrity from backend
    if (paymentView === PaymentState.FORM && checkoutId && integrity) {
      cleanupPaymentForm();

      // Set up wpwlOptions before loading the widget
      window.wpwlOptions = {
        style: "plain",
        locale: i18n.language,
        labels: {
          cardNumber: i18n.language === "ar" ? "رقم البطاقة" : t("Card Number"),
          cvv: i18n.language === "ar" ? "رمز التحقق" : t("CVV"),
          expiryDate:
            i18n.language === "ar" ? "تاريخ الانتهاء" : t("Expiry Date"),
        },
        iframeStyles: {
          ".wpwl-control": {
            height: "60px",
            padding: "0 20px",
            "border-radius": "15px",
            border: "1px solid rgba(0, 0, 0, 0.2)",
            "background-color": "#FFFFFF",
            "box-sizing": "border-box",
            width: "100%",
          },
          ".wpwl-control-cvv": {
            width: "120px !important",
            display: "inline-block !important",
            "margin-left": "20px !important",
          },
          ".wpwl-button-pay": {
            width: "100%",
            height: "55px",
            "background-color": "#7ab3c5 !important",
            color: "white !important",
            "border-radius": "15px !important",
            "font-size": "18px !important",
            "font-weight": "bold !important",
            "margin-top": "20px !important",
            "text-transform": "none !important",
            "font-family": "system-ui, system-ui",
            transition: "background-color 0.3s ease",
          },
          ".wpwl-button-pay:hover": {
            "background-color": "#6ba6b8 !important",
          },
          ".wpwl-button-pay:disabled": {
            "background-color": "#f8f8f8 !important",
            color: "#cccccc !important",
          },
        },
        onReady: function () {
          const form = document.querySelector(".paymentWidgets");
          if (form && i18n.language === "ar") {
            form.setAttribute("dir", "rtl");
          }

          // Override executePayment to handle Safari redirect issues
          if (window.wpwl && window.wpwl.executePayment) {
            const originalExecutePayment = window.wpwl.executePayment;

            if (originalExecutePayment) {
              window.wpwl.executePayment = function () {
                // Safari-specific handling for redirects
                if (isSafari()) {
                  // For Safari, we need to handle redirects more explicitly
                  const form = document.querySelector(".paymentWidgets form");
                  if (form) {
                    // Ensure the form target is set to handle Safari redirect policies
                    form.setAttribute("target", "_self");

                    // Add event listener for form submission in Safari
                    form.addEventListener("submit", function (e) {
                      // Allow the form to submit naturally in Safari
                      setTimeout(() => {
                        // Handle any post-submission logic if needed
                        setIsSubmitting(true);
                      }, 100);
                    });
                  }
                }

                return originalExecutePayment.apply(this, arguments);
              };
            }
          }
        },
        onError: function (error) {
          setIsSubmitting(false);
          console.error("Payment error:", error);
        },
        onBeforeSubmitCard: function (e) {
          setIsSubmitting(true);

          // Minimal Safari-specific handling - only ensure form action is set
          if (isSafari()) {
            const form = document.querySelector(".paymentWidgets form");
            if (form && !form.getAttribute("action")) {
              const redirectUrl = `${window.location.origin}/${
                i18n.language
              }/pay-by-link?${
                router.query.token ? `token=${router.query.token}&` : ""
              }status=get_status`;
              form.setAttribute("action", redirectUrl);
            }
          }

          return true;
        },
        // Add handling for 3DS iframe appearance
        threeDSInitiated: function () {
          // Basic 3DS handling without browser-specific styles
          return true;
        },
        onValidation: function (type, valid, element) {
          return true;
        },
        // Add styles for 3D Secure popup
        threeDSStyles: {
          backgroundColor: "#ffffff",
          borderRadius: "20px",
          boxShadow: "0px 9px 30px rgba(77, 81, 86, 0.2)",
          fontFamily: "system-ui, system-ui",
          buttonColor: "#7ab3c5",
          textColor: "#333333",
          headerColor: "#7ab3c5",
          headerTextColor: "#ffffff",
          width: "100%",
          maxWidth: "450px",
          height: "auto",
          maxHeight: "90vh",
          overflow: "hidden",
        },

        requireCvv: true,
        brandDetection: true,
        showCVVHint: true,
        brandDetectionPriority: ["VISA", "MASTER", "MADA"],
        enableBrandDetection: true,
        showLabels: true,
        brandDetectionType: "regex",
      };

      // Load the widget script with integrity (PCI DSS v4.0 compliance)
      const script = document.createElement("script");
      script.id = "payment-widget";
      script.src = `${process.env.NEXT_PUBLIC_OPPWA_URL}/v1/paymentWidgets.js?checkoutId=${checkoutId}`;
      script.async = true;
      script.setAttribute("crossorigin", "anonymous");
      script.setAttribute("nonce", nonce);

      // Double-check no existing script with the same ID exists before adding
      const existingScript = document.getElementById("payment-widget");
      if (existingScript) {
        existingScript.remove();
      }

      if (integrity) {
        script.setAttribute("integrity", integrity);
      }

      document.body.appendChild(script);
      setFormKey((prev) => prev + 1);
    } else if (paymentView === PaymentState.FORM && checkoutId && !integrity) {
      console.log(
        "PayByLink - Waiting for integrity from backend. CheckoutId available but no integrity yet."
      );
    } else if (paymentView === PaymentState.FORM && !checkoutId && integrity) {
      console.log(
        "PayByLink - Waiting for checkoutId from backend. Integrity available but no checkoutId yet."
      );
    }
  }, [
    paymentView,
    checkoutId,
    paymentBrand,
    cleanupPaymentForm,
    i18n.language,
    t,
    nonce,
    integrity, // This ensures script reloads when integrity changes
  ]);

  // Add form validation check
  useEffect(() => {
    if (typeof window !== "undefined" && window.wpwl) {
      const checkFormValidity = () => {
        const form = document.querySelector(".paymentWidgets");
        if (form) {
          const cardNumber = form.querySelector(
            ".wpwl-control-card-number"
          ) as HTMLInputElement;
          const cvv = form.querySelector(
            ".wpwl-control-cvv"
          ) as HTMLInputElement;
          const expiry = form.querySelector(
            ".wpwl-control-expiry"
          ) as HTMLInputElement;
          const cardHolder = form.querySelector(
            ".wpwl-control-card-holder"
          ) as HTMLInputElement;

          const isValid =
            cardNumber?.value?.trim() !== "" &&
            cvv?.value?.trim() !== "" &&
            expiry?.value?.trim() !== "" &&
            cardHolder?.value?.trim() !== "";

          setIsFormValid(!!isValid);
        }
      };

      // Add event listeners to form fields
      const form = document.querySelector(".paymentWidgets");
      if (form) {
        form.addEventListener("input", checkFormValidity);
        form.addEventListener("change", checkFormValidity);
      }

      // Initial check
      checkFormValidity();

      // Cleanup
      return () => {
        if (form) {
          form.removeEventListener("input", checkFormValidity);
          form.removeEventListener("change", checkFormValidity);
        }
      };
    }
  }, [paymentBrand, checkoutId]);

  // Function to handle language change
  const handleLanguageChange = (e) => {
    const locale = e?.target?.value || e;
    i18n.changeLanguage(locale);
    sessionStorage.setItem("locale", locale);
    router
      .push(
        {
          query: router.query,
        },
        router.asPath,
        { locale }
      )
      .then(() => {
        window.location.reload(); // Force components to reload after language change
      });
  };

  // Add a useEffect to force the payment form to reload when payment brand changes
  useEffect(() => {
    if (paymentView === PaymentState.FORM && checkoutId) {
      // Use the robust script removal function

      removeExistingPaymentScript().then(() => {
        // Ensure no existing script with the same ID exists
        const existingScript = document.getElementById("payment-widget");
        if (existingScript) {
          existingScript.remove();
        }

        const scriptElement = document.createElement("script");
        scriptElement.id = "payment-widget";
        scriptElement.src = `${process.env.NEXT_PUBLIC_OPPWA_URL}/v1/paymentWidgets.js?checkoutId=${checkoutId}`;
        scriptElement.async = true;
        // Add PCI DSS v4.0 required attributes
        scriptElement.setAttribute("crossorigin", "anonymous");
        scriptElement.setAttribute("nonce", nonce);

        // Always set integrity since we only reload when integrity is available

        // Set the exact integrity value from backend without any modifications
        if (integrity) {
          scriptElement.setAttribute("integrity", integrity);
        }

        scriptElement.onload = () => {
          document.body.appendChild(scriptElement);
          document.body.appendChild(scriptElement);
        };
      });
    }
  }, [
    paymentBrand,
    paymentView,
    checkoutId,
    nonce,
    integrity,
    removeExistingPaymentScript,
  ]);

  const renderLoading = () => (
    <Box textAlign="center" py={4}>
      <CircularProgress />
      <Typography
        variant="body1"
        style={{ marginTop: 16, textAlign: "center" }}
      >
        {t("Loading payment information...") as string}
      </Typography>
    </Box>
  );

  const renderGetStatus = () => {
    if (paymentStatusLoading) {
      return renderLoading();
    }
    return (
      <Box
        textAlign="center"
        py={4}
        width="100%"
        display="flex"
        flexDirection="column"
        alignItems="center"
        paddingX={6}
      >
        <Box mb={3}>
          <img
            src={`/assets/icons/${
              paymentStatus === "paid"
                ? "circleCheck"
                : paymentStatus?.includes("pending")
                ? "infoOrange"
                : "notChecked"
            }.svg`}
            alt="Success"
            width={60}
            height={60}
          />
        </Box>

        <Typography
          variant="h4"
          style={{
            color:
              paymentStatus === "paid"
                ? "#7AB3C5"
                : paymentStatus?.includes("pending")
                ? "#FA9C3F"
                : "#F85959",
            fontWeight: 700,
            fontSize: "30px",
            marginBottom: "16px",
            textAlign: "center",
            fontFamily:
              i18n.language === "ar"
                ? "Helvetica Neue LT Arabic"
                : "Helvetica Neue",
          }}
        >
          {paymentStatus === "paid" && (t("thank_you") as string)}
          {paymentStatus?.includes("pending") &&
            (t("Pending payment") as string)}
          {paymentStatus === "failed" && (t("Failed payment") as string)}
        </Typography>

        {paymentStatusData?.getPaymentLinkStatus && (
          <Typography
            variant="body1"
            style={{
              marginTop: 8,
              marginBottom: 40,
              textAlign: "center",
              color:
                paymentStatus === "paid"
                  ? "#7AB3C5"
                  : paymentStatus?.includes("pending")
                  ? "#FA9C3F"
                  : "#F85959",
              fontSize: "20px",
              fontWeight: 700,
              fontFamily:
                i18n.language === "ar"
                  ? "Helvetica Neue LT Arabic"
                  : "Helvetica Neue",
            }}
          >
            {paymentStatus === "paid"
              ? (t("Payment completed successfully") as string)
              : paymentStatus === "failed"
              ? (t(
                  "Dear customer.. your payment process failed please try to pay again"
                ) as string)
              : (t(
                  "Dear customer.. Your payment process is currently pending with the payment gateway. The payment status will be updated once the payment is completed."
                ) as string)}
          </Typography>
        )}
        <Box sx={{ marginTop: "0rem" }} />
        <Button
          variant="contained"
          style={{
            backgroundColor:
              paymentStatus === "paid"
                ? "#7AB3C5"
                : paymentStatus?.includes("pending")
                ? "#FA9C3F"
                : "transparent",
            color: paymentStatus === "failed" ? "#F85959" : "white",
            border: paymentStatus === "failed" ? "solid 1px #F85959" : "",
            borderRadius: "15px",
            padding: "12px 32px",
            width: "100%",
            fontSize: "18px",
            marginBottom: "16px",
            fontWeight: "bold",
            textTransform: "none",
          }}
          onClick={() =>
            paymentStatus === "failed" ? handleNavigateHome() : router.push("/")
          }
        >
          {paymentStatus === "failed"
            ? (t("Pay Again") as string)
            : (t("Home Page") as string)}
        </Button>
      </Box>
    );
  };

  const renderInvalidToken = () => (
    <Box
      style={{
        width: "100%",
        minHeight: "300px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/payment/blur.svg')`,
        backgroundPosition: "center",
        backgroundSize: "cover",
        borderRadius: "20px",
        padding: "40px 20px",
        textAlign: "center",
      }}
    >
      <Box mb={3}>
        <LockIcon style={{ color: "white", fontSize: 48 }} />
      </Box>
      <Typography
        variant="h5"
        style={{
          color: "white",
          fontWeight: 500,
          marginBottom: "16px",
        }}
      >
        {t("Payment link has been expired") as string}
      </Typography>
      <Typography
        variant="body1"
        style={{
          color: "#E0E0E0",
          maxWidth: "400px",
          marginBottom: "40px",
          textAlign: "center",
        }}
      >
        {
          t(
            "To ensure the security of your transactions, the validity period of the payment link is limited to a period. Please contact customer service."
          ) as string
        }
      </Typography>
      <Button
        variant="contained"
        style={{
          backgroundColor: "#7AB3C5",
          color: "white",
          borderRadius: "15px",
          padding: "12px 32px",
          fontSize: "16px",
          fontWeight: 500,
          textTransform: "none",
        }}
        onClick={handleNavigateHome}
      >
        {t("Home Page") as string}
      </Button>
    </Box>
  );

  const PaymentMethodSelectorComponent = ({
    selectedPaymentMethod,
    onSelectPaymentMethod,
  }: {
    selectedPaymentMethod: string;
    onSelectPaymentMethod: (method: string) => void;
  }) => {
    const { t } = useTranslation();

    return (
      <PaymentMethodSelector>
        <Typography
          variant="body1"
          className="payment-method-label"
          style={{ marginBottom: "20px" }}
        >
          {t("Cards") as string}
        </Typography>
        <Box className="payment-methods">
          <Box
            className={`payment-method ${
              selectedPaymentMethod === "MADA" ? "selected" : ""
            }`}
            onClick={() => onSelectPaymentMethod("MADA")}
          >
            <img
              src="/assets/icons/mada.svg"
              alt="MADA"
              width={65}
              height={24}
            />
          </Box>
          <Box
            className={`payment-method ${
              selectedPaymentMethod === "CREDIT_CARD" ? "selected" : ""
            }`}
            onClick={() => onSelectPaymentMethod("CREDIT_CARD")}
          >
            <Box sx={{ display: "flex" }}>
              <div style={{ marginRight: "8px" }}>
                <img
                  src="/assets/icons/visa.svg"
                  alt="VISA"
                  width={40}
                  height={24}
                />
              </div>
              <img
                src="/assets/icons/master.svg"
                alt="MasterCard"
                width={40}
                height={24}
              />
            </Box>
          </Box>
        </Box>
      </PaymentMethodSelector>
    );
  };

  const renderPaymentForm = () => {
    if (!paymentData) return null;

    const { amount } = paymentData;

    return (
      <>
        <Box
          style={{
            textAlign: "center",
            display: "flex",
            flexDirection: "row",
            gap: 10,
            width: "100%",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box display="flex" justifyContent="center">
            <CreditCardIcon style={{ fontSize: 35, color: "#000" }} />
            <Typography
              variant="h5"
              style={{
                fontWeight: 600,
                fontSize: "25px",
                lineHeight: "34px",
                color: "#000000",
                letterSpacing: "0.2px",
                fontFamily: "system-ui, system-ui",
              }}
            >
              {t("Carwah Payment") as string}
            </Typography>
          </Box>

          <Typography
            style={{
              backgroundColor: "#F8F8F8",
              borderRadius: "41px",
              height: "49px",
              textAlign: "center",
              border: "none",
              fontFamily: "system-ui, system-ui",
              width: "fit-content",
              padding: "12px 24px",
              display: "flex",
              alignItems: "center",
            }}
          >
            {`${paymentData?.bookingNumber ? t("Booking Number") : ""} ${
              paymentData?.bookingNumber || ""
            }`}
            {`${
              paymentData?.installmentNumber ? t("Installment Number") : ""
            } ${paymentData?.installmentNumber || ""}`}
            {`${paymentData?.extensionNumber ? t("Extension Number") : ""} ${
              paymentData?.extensionNumber || ""
            }`}
          </Typography>
        </Box>
        <TotalBox>
          <Typography
            style={{
              fontWeight: "bold",
              fontSize: "20px",
              fontFamily: "system-ui, system-ui",
            }}
          >
            {t("total") as string}
            <br />
            <span style={{ fontSize: "14px", fontWeight: "normal" }}>
              {t("includes_vat") as string}
            </span>
          </Typography>

          <Box display="flex" alignItems="center">
            <Typography
              style={{
                color: "#B0B0B0",
                fontSize: "28px",
                lineHeight: "1.2",
                order: i18n.language === "ar" ? 1 : 0,
              }}
            >
              <RiyalSymbol />
            </Typography>

            <Typography
              style={{
                fontWeight: "bold",
                color: "#7AB3C5",
                fontSize: "28px",
                marginLeft: "5px",
                marginRight: "5px",
                fontFamily: "system-ui, system-ui",
                order: i18n.language === "ar" ? 0 : 1,
              }}
            >
              {amount.toLocaleString()}
            </Typography>
          </Box>
        </TotalBox>
        <TermsText
          style={{
            marginBottom: "20px",
            width: "100%",
            textAlign: "center",
            fontSize: "15px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {t("By continuing you indicate your acceptance of the")}
          <span
            onClick={() =>
              window.open(
                `${window.location.origin}/${i18n.language}/terms-conditions`,
                "_blank"
              )
            }
            className="mx-1"
            style={{ cursor: "pointer", textDecoration: "underline" }}
          >
            {t("Terms & Conditions") as string}
          </span>
        </TermsText>
        <Divider />

        <PaymentMethodSelectorComponent
          selectedPaymentMethod={paymentBrand}
          onSelectPaymentMethod={updatePaymentBrand}
        />

        {/* HyperPay Form Integration */}
        <div
          key={formKey}
          className="text-align-localized"
          style={{
            direction: "ltr",
            marginBottom: "-10px",
            marginTop: "40px",
            minHeight: "200px",
            width: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "column",
          }}
        >
          <form
            ref={formRef}
            action={`${window.location.origin}/${i18n.language}/pay-by-link?${
              router.query.token ? `token=${router.query.token}&` : ""
            }status=get_status`}
            className="paymentWidgets"
            data-brands={
              paymentBrand === "CREDIT_CARD" ? "VISA MASTER" : paymentBrand
            }
          ></form>

          {/* {errorMessage && (
            <Typography
              style={{
                color: "red",
                marginTop: "10px",
                textAlign: "center",
                fontFamily: "system-ui, system-ui",
              }}
            >
              {errorMessage}
            </Typography>
          )} */}
        </div>

        <StatusMessage color="#F85959">
          <TimerIcon />
          {t("Payment link validity")} {paymentData.validUntil}
        </StatusMessage>

        <StatusMessage color="#6FCFA1">
          <CheckCircleIcon />
          {String(t("Payment data is secure with us 100%"))}
        </StatusMessage>
      </>
    );
  };

  const renderPending = () => {
    return (
      <Box
        textAlign="center"
        py={4}
        width="100%"
        display="flex"
        flexDirection="column"
        alignItems="center"
        paddingX={6}
      >
        <Box mb={3}>
          <img
            src={`/assets/icons/infoOrange.svg`}
            alt="Success"
            style={{
              width: "50px",
              height: "50px",
            }}
          />
        </Box>

        <Typography
          variant="h4"
          style={{
            color: "#FA9C3F",
            fontWeight: 700,
            fontSize: "35px",
            marginBottom: "16px",
            textAlign: "center",
            fontFamily:
              i18n.language === "ar"
                ? "Helvetica Neue LT Arabic"
                : "Helvetica Neue",
          }}
        >
          {t("Pending payment") as string}
        </Typography>
        <Typography
          variant="body1"
          style={{
            marginTop: 8,
            marginBottom: 30,
            color: "var(--color-2)",
            textAlign: "center",
            fontSize: "20px",
            fontWeight: 700,
            fontFamily:
              i18n.language === "ar"
                ? "Helvetica Neue LT Arabic"
                : "Helvetica Neue",
          }}
        >
          {
            t(
              "Dear customer.. You have a pending payment. Please wait 30 minutes and then try to pay again."
            ) as string
          }
        </Typography>

        <Button
          variant="contained"
          style={{
            backgroundColor: "var(--color-2)",

            color: "white",
            borderRadius: "15px",
            padding: "12px 32px",
            width: "100%",
            fontSize: "18px",
            marginBottom: "16px",
            fontWeight: "bold",
            textTransform: "none",
          }}
          onClick={handleNavigateHome}
        >
          {t("Home Page") as string}
        </Button>
      </Box>
    );
  };

  return (
    <>
      {/* Safari Mobile Browser Banner */}
      {showSafariBanner && (
        <SafariBanner lang={i18n.language}>
          <button
            className="close-button"
            onClick={closeSafariBanner}
            aria-label={t("Close") as string}
          >
            ×
          </button>
          <div className="banner-content">
            <div>
              {
                t(
                  "If you face any issues, kindly use a supported browser such as:"
                ) as string
              }
            </div>
            <div className="browser-list">
              {t("Chrome, Firefox, Edge") as string}
            </div>
          </div>
        </SafariBanner>
      )}

      {/* Spacer to push content down when banner is shown */}
      <BannerSpacer show={showSafariBanner} />

      <PageContainer lang={i18n.language}>
        {/* Add nonce to any custom scripts */}
        <script
          nonce={nonce}
          dangerouslySetInnerHTML={{
            __html: `
            var wpwlOptions = {
              style: "plain"
            };
          `,
          }}
        />

        {/* Language Switcher */}
        <LangSwitcher lang={i18n.language}>
          <SelectComponent
            key={i18n.language}
            id="select-language"
            defaultValue="en"
            value={i18n.language}
            options={[
              { value: "en", text: "En" },
              { value: "ar", text: "عربي" },
            ]}
            selectChangeHandler={handleLanguageChange}
          />
        </LangSwitcher>

        {/* No Script tags here - they're now loaded dynamically in the useEffect */}

        <Box className="page-wrapper">
          <Box className="logo-pos-container">
            <Box className="logo-container">
              <img
                src="/assets/images/logo.svg"
                alt="Carwah Logo"
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "contain",
                }}
              />
            </Box>
            <Box className="pos-container">
              <img
                src="/assets/payment/pos.svg"
                alt="Payment Terminal"
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "contain",
                  transform: i18n.language === "en" ? "scaleX(-1)" : "none",
                }}
              />
            </Box>
          </Box>
          <Box className="payment-card-container">
            <PaymentCard>
              {(paymentView === PaymentState.LOADING || isLoading) &&
                renderLoading()}
              {paymentView === PaymentState.FORM &&
                !isLoading &&
                renderPaymentForm()}
              {paymentView === PaymentState.GET_STATUS && renderGetStatus()}
              {paymentView === PaymentState.PENDING && renderPending()}
              {paymentView === PaymentState.ERROR && renderInvalidToken()}
              {paymentView === PaymentState.INVALID_TOKEN &&
                renderInvalidToken()}
            </PaymentCard>
          </Box>
        </Box>
      </PageContainer>
    </>
  );
};

export default PayByLink;
