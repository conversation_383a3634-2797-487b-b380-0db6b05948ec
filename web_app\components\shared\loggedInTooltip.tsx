/* eslint-disable @next/next/no-img-element */
import { Box, ClickAwayListener, Typography } from "@material-ui/core";
import SelectComponent from "./select";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";
import styled from "styled-components";
import Tooltip from "./tooltip";
import { useState } from "react";
import { ArrowBackIos } from "@material-ui/icons";
import { setCouponData } from "store/coupons/action";
import { LogOutAction } from "store/authentication/action";
import { claerUserDataAction } from "store/user/action";
import { useDispatch } from "react-redux";

const AvatarWrapper = styled.div`
  transition: all 1s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  &:hover {
    img.arrow {
      background: #dddddd;
      padding: 0px;
      border-radius: 50px;
    }
  }
`;
const TooltipWrapper = styled.div`
  h6,
  svg {
    font-size: 16px !important;
  }
  .active-item {
    h6,
    svg {
      font-weight: bold !important;
      color: var(--color-2);
    }
  }
  img.arrow {
    margin-top: 5px;
  }
  &:hover {
    img.arrow {
      background: #dddddd;
      padding: 0px;
      border-radius: 50px;
    }
    color: var(--color-2);
    font-weight: bold;
  }
`;

function LoggedInTooltip({
  loginData: {
    user: { firstName, lastName, profileImage },
  },
}: {
  loginData: any;
}) {
  const { i18n, t } = useTranslation();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const dispatch = useDispatch();
  const [settingsMenuOpen, setSettingsMenuOpen] = useState(false);

  async function selectChangeHandler(e) {
    const locale = e?.target?.value || e;
    localeChangeHandler(locale);
    setTimeout(() => {
      window.scrollTo(0, 0);
      // window.location.reload();
    }, 500);
  }
  function localeChangeHandler(e) {
    const locale = e?.target?.value || e;
    i18n.changeLanguage(locale);
    sessionStorage.setItem("locale", locale);
    router
      .push(
        {
          query: router.query,
        },
        router.asPath,
        { locale }
      )
      .then(() => {
        window.location.reload(); // Force swiper carousel to reload after language change
      });
  }

  return (
    <ClickAwayListener
      onClickAway={() => setIsOpen(false)}
      touchEvent={"onTouchStart"}
      mouseEvent={"onMouseDown"}
    >
      <Box
        style={{
          padding: "2px 15px 5px 15px",
          color: "#4E4E4E",
          background: "#F0F0F0",
          border: "solid 1px rgba(156,156,156, 0.5)",
          borderRadius: "50px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          flexDirection: i18n.language === "en" ? "row-reverse" : "row",
          position: "relative",
        }}
        sx={{
          width: { xs: "95%", sm: "95%", lg: "auto" },
        }}
      >
        <div className="position-relative" style={{ cursor: "pointer" }}>
          <TooltipWrapper
            onClick={() => {
              selectChangeHandler(i18n.language === "ar" ? "en" : "ar");
            }}
            style={{
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              transform:
                i18n.language === "en" ? "translateY(-3px)" : "translateY(0px)",
              padding: "0 5px",
            }}
          >
            {i18n.language === "ar" ? "EN" : "عربي"}
          </TooltipWrapper>
        </div>
        <Box
          sx={{
            margin: "0px 5px",
            color: "#DDDDDD",
            display: { xs: "none", sm: "block", lg: "block" },
          }}
        >
          |
        </Box>
        <AvatarWrapper onClick={() => setIsOpen((prev) => !prev)}>
          <img
            style={
              profileImage
                ? {
                    margin: "2px 5px 0 5px",
                    borderRadius: "50px",
                    width: "30px",
                    height: "29px",
                    objectFit: "cover",
                  }
                : {}
            }
            src={!profileImage ? "/assets/icons/Person.svg" : profileImage}
            alt="avatar"
            loading="lazy"
          />
          <Typography
            style={{
              fontWeight: "bold",
              textOverflow: "ellipsis",
              overflow: "hidden",
              whiteSpace: "nowrap",
              maxWidth: "25vw",
              padding: "0 5px",
            }}
            variant="subtitle2"
          >{`${firstName} ${
            window?.innerWidth > 768 ? lastName || "" : ""
          }`}</Typography>
          <img
            className="arrow"
            src="/assets/icons/downArrow.svg"
            alt="arrow"
            loading="lazy"
          />
        </AvatarWrapper>
        <TooltipWrapper>
          <Tooltip
            {...{
              isOpen,
              anchor: "right",
              style: {
                width: "75%",
                top: "50px",
                border: "none",
                padding: "20px",
                display: "flex",
                gap: "15px",
                flexDirection: "column",
                right: i18n.language === "en" ? "0px" : "unset",
              },
            }}
          >
            <Box
              display={"flex"}
              justifyContent={"space-between"}
              alignItems={"center"}
              sx={{ color: "white" }}
              className={router.asPath === "/my-account" ? "active-item" : null}
              onClick={() => {
                router.push("/my-account");
              }}
            >
              <Typography variant="subtitle2">
                {t("My account") as string}
              </Typography>
              <ArrowBackIos
                style={{
                  fontSize: "16px",
                  marginTop: "5px",
                  marginLeft: i18n.language === "ar" ? "5px" : "auto",
                  marginRight: i18n.language === "en" ? "5px" : "auto",
                  transform: i18n.language === "en" ? "scaleX(-1)" : "none",
                }}
              />
            </Box>
            <Box
              style={{
                backgroundColor: "#F2F3F5",
                opacity: 0.3,
                height: "1px",
                width: "100%",
              }}
            />
            <Box
              display={"flex"}
              justifyContent={"space-between"}
              alignItems={"center"}
              sx={{ color: "white" }}
              className={
                router.pathname === "/my-rentals" ? "active-item" : null
              }
              onClick={() => {
                router.push("/my-rentals?tab=current");
              }}
            >
              <Typography variant="subtitle2">
                {t("My rentals") as string}
              </Typography>
              <ArrowBackIos
                style={{
                  fontSize: "16px",
                  marginTop: "5px",
                  marginLeft: i18n.language === "ar" ? "5px" : "auto",
                  marginRight: i18n.language === "en" ? "5px" : "auto",
                  transform: i18n.language === "en" ? "scaleX(-1)" : "none",
                }}
              />
            </Box>
            <Box
              display={"flex"}
              justifyContent={"space-between"}
              alignItems={"center"}
              sx={{ color: "white" }}
              className={
                router.asPath.includes("my-account?tab=wallet")
                  ? "active-item"
                  : null
              }
              onClick={() => {
                router.push("/my-account?tab=wallet");
              }}
            >
              <Typography variant="subtitle2">
                {t("My Wallet") as string}
              </Typography>
              <ArrowBackIos
                style={{
                  fontSize: "16px",
                  marginTop: "5px",
                  marginLeft: i18n.language === "ar" ? "5px" : "auto",
                  marginRight: i18n.language === "en" ? "5px" : "auto",
                  transform: i18n.language === "en" ? "scaleX(-1)" : "none",
                }}
              />
            </Box>
            <Box
              display={"flex"}
              justifyContent={"space-between"}
              alignItems={"center"}
              sx={{ color: "#F85959" }}
              onClick={() => {
                dispatch(setCouponData(null));
                dispatch(LogOutAction());
                sessionStorage.removeItem("authDataAction");
                router.push("/");
                dispatch(claerUserDataAction());
              }}
            >
              <Typography variant="subtitle2">
                {t("Logout") as string}
              </Typography>
              <img
                src="/assets/icons/logout.svg"
                style={{
                  fontSize: "16px",
                  marginTop: "5px",
                  transform: i18n.language === "en" ? "scaleX(-1)" : "none",
                }}
                alt="logout"
              />
            </Box>
          </Tooltip>
        </TooltipWrapper>
      </Box>
    </ClickAwayListener>
  );
}

export default LoggedInTooltip;
