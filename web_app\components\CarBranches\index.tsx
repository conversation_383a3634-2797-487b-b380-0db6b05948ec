/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @next/next/no-img-element */
import { Container, Grid } from "@material-ui/core";
import Layout from "components/shared/layout";
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import RequestLoader from "components/shared/requestLoader";
import { Star } from "@material-ui/icons";
import { useQuery } from "@apollo/client";
import { AlliesBranchesByCar_Query } from "gql/queries/car";
import { Pagination } from "@material-ui/lab";
import { useState } from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { carsSlectedBranchAction, setClearCar } from "store/cars/action";
import { useSnackbar } from "notistack";
import SortBy from "./sortBy";
import useExtraServicesGenerator from "components/shared/extraServicesGenerator";
import { setPayWithInstallments } from "store/installments/action";
import RiyalSymbol from "components/shared/RiyalSymbol";

//Style
const Div = styled.div`
  padding: 40px 0;
  h3 {
    font-size: 1.2rem;
  }
`;

const Card = styled.div`
  position: relative;
  cursor: pointer;
  border-radius: var(--radius-4);
  background-color: var(--color-4);
  /* padding: 20px 20px 0 20px; */
  width: 100%;
  min-height: 200px;
  margin-bottom: 30px;
  @media (max-width: 768px) {
    div,
    #location,
    #km-away {
      font-size: 14px !important;
    }
  }
  .inner-card {
    width: 100%;
    border: solid 1px var(--color-21);
    border-radius: var(--radius-4);
    padding: 15px 10px;
    /* margin-bottom: 50px; */
    > div {
      display: flex;
      gap: 10px;
    }
    .ally-class {
      border-radius: var(--radius-1);
      font-weight: bold;
      padding: 1px 10px 2px 10px;
      background-color: var(--color-13);
      color: var(--color-3);
    }
    .branch-rate {
      display: flex;
      gap: 8px;
      font-weight: bold;
      svg {
        fill: var(--color-2);
      }
    }
    #location,
    #km-away {
      margin-top: 15px;
      padding: 0 8px;
      .icon-wrapper {
        display: flex;
        align-items: center;
        gap: 18px;
      }
    }
    #availability {
      display: flex;
      margin-top: 15px;
      justify-content: space-between;
      align-items: center;
      > div {
        display: flex;
        gap: 15px;
        .airport {
          background-color: var(--color-5);
          color: var(--color-4);
          padding: ${(props) =>
            props.lang === "ar"
              ? "7px 20px 14px 20px "
              : "10px 20px 10px 20px"};
          border-radius: var(--radius-1);
          display: flex;
          align-items: center;
          gap: 5px;
        }
        .delivery {
          background-color: var(--color-3);
          color: var(--color-4);
          padding: 7px 15px 10px 15px;
          border-radius: var(--radius-1);
        }
      }
      span.status {
        color: var(--color-3);
      }
    }
    @media (max-width: 767px) {
      margin-bottom: 0 !important;
      padding: 5px !important;
    }
  }
  .price-label {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: var(--color-2);
    color: var(--color-4);
    background-color: var(--color-2);
    padding: ${(props) =>
      props.lang === "ar" ? "7px 15px 15px 15px" : "10px 15px 10px 15px"};
    border-radius: ${(props) =>
      props.lang === "ar" ? "0 var(--radius-2)" : "var(--radius-2) 0"};
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    align-items: center;
    gap: 5px;
    min-width: 120px;

    span {
      display: block;
    }
    > div {
      align-items: center;
      div:last-child {
        font-size: 24px;
        font-weight: bold;
      }
      div:first-child {
        font-size: 25px;
        width: max-content;
        text-align: start;
        /* transform: translateY(-4px); */
      }
      span:last-child {
        font-size: 24px;
      }
    }
    > span {
      font-size: 18px;
    }
  }
  .car-image {
    height: 150px;
    object-fit: contain;
    @media (max-width: 900px) {
      height: 120px !important;
    }
  }
  #allies-features {
    margin-top: 10px;
    @media (min-width: 768px) {
      display: flex;
      flex-wrap: wrap;
      height: fit-content;
      gap: 0px 40px;
      > div {
        flex-basis: calc(50% - 20px);
        box-sizing: border-box;
      }
    }
    @media (max-width: 767px) {
      /* margin-bottom: 50px; */
      width: 100%;
    }
  }
`;

const ExtraService = styled.div`
  font-size: 14px;
  img {
    width: 20px;
    height: 20px;
    object-fit: contain;
  }
  > div {
    display: flex;
    gap: 20px;
    margin-bottom: 22px;
    align-items: center;
    height: 0px;
    justify-content: space-between;
    > div {
      display: flex;
      /* gap: 10px; */
    }
  }
  span.text {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    align-items: center;
  }
`;

export default function CarBranches({ carId }) {
  //State
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState();
  //Hooks
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const { getAllExtraServices } = useExtraServicesGenerator();
  const sort_by = router?.query?.sort_by;
  const { enqueueSnackbar } = useSnackbar();
  //Store
  const dispatch = useDispatch();
  const searchData = useSelector((state: RootStateOrAny) => state.search_data);
  const carsList = useSelector(
    (state: RootStateOrAny) => state.cars?.carsList?.collection
  );

  //Variables
  const selectedCarData = carsList?.length
    ? carsList.find((i) => i.carId === carId)
    : "";
  //Functions
  function handlePageChange(event, value) {
    setPage(Number(value));
  }

  //GQL
  const { data: alliesBranchesData, loading: isLoadingAlliesBranchesData } =
    useQuery(AlliesBranchesByCar_Query, {
      variables: {
        make: selectedCarData?.makeId || searchData?.filters?.make,
        model: selectedCarData?.carModelId || searchData?.filters?.model,
        version: selectedCarData?.carVersionId || searchData?.filters?.version,
        yearFrom: selectedCarData?.year,
        yearTo: selectedCarData?.year,
        limit: 3,
        page,
        canDelivery: searchData.selection_index === 2 ? true : false,
        pickUpLocationId: searchData?.user_address?.pick_up?.id,
        dropOffLocationId: searchData?.user_address?.return?.id,
        dailyPriceFrom: 0,
        sortBy: sortBy || sort_by,
        userLat: searchData?.current_location?.lat,
        userLng: searchData?.current_location?.lng,
        pickEndDate: searchData?.date_time?.dropOffDate,
        pickEndTime: searchData?.date_time?.return_time,
        pickStartDate: searchData?.date_time?.pickUpDate,
        pickStartTime: searchData?.date_time?.pickup_time,
        extraServices:
          searchData?.filters?.extraServices?.map((e) => e.value) || undefined,
        insuranceType: searchData?.fullInsurance ? 2 : undefined,
      },
    });

  const alliesCarsList = alliesBranchesData?.cars;

  return (
    <Layout>
      <RequestLoader loading={isLoadingAlliesBranchesData} />
      {!isLoadingAlliesBranchesData && !alliesBranchesData ? (
        <Div lang={i18n.language} className="grey-background">
          <Container>
            <div className="d-none">
              {/* {enqueueSnackbar(t("Check your connection!") as string, {
                variant: "error",
                preventDuplicate: true,
              })} */}
            </div>
          </Container>
        </Div>
      ) : (
        <Div className="grey-background" lang={i18n.language}>
          <Container>
            <div className="d-lg-flex align-items-center justify-content-between">
              <div
                className="d-flex align-items-center"
                style={{ gap: "20px", flexWrap: "wrap" }}
              >
                <img
                  style={{ height: "90px" }}
                  src={
                    selectedCarData?.logo
                      ? selectedCarData.logo
                      : "/assets/images/car.png"
                  }
                  alt="car"
                />
                <div style={{ width: "max-content" }}>
                  <p className="font-18px text-capitalize">
                    <span className="bold">
                      {selectedCarData?.makeName} {selectedCarData?.modelName}{" "}
                      {selectedCarData?.versionName}
                    </span>
                    <br />
                    {selectedCarData?.year}
                    <br />
                  </p>
                  <p className="color-11">{t("or similar") as string}</p>
                </div>
              </div>
            </div>
          </Container>
          <div className="separator my-3" />
          <Container>
            <div className="d-flex justify-content-between align-items-center">
              <h3 className="my-4 pb-2 bold">
                {t("Choose from Allies below") as string}
              </h3>
              <SortBy sortBy={sortBy} setSortBy={setSortBy} sort_by={sort_by} />
            </div>
            {alliesCarsList?.collection.map((item) => {
              return (
                <Link
                  href={`/car-details?car=${item?.id}`}
                  key={item?.id}
                  prefetch={false}
                >
                  <Card
                    key={item?.id}
                    lang={i18n.language}
                    onClick={() => {
                      dispatch(
                        carsSlectedBranchAction({
                          ...item?.branch,
                          carMonthsPrice: item?.carMonthsPrice,
                          carPrice: item?.carPrice,
                        })
                      );
                    }}
                  >
                    <Grid
                      container
                      spacing={2}
                      alignItems="baseline"
                      style={{ padding: "20px 20px 0 20px" }}
                    >
                      <Grid
                        item
                        md={4}
                        lg={4}
                        sm={12}
                        style={{ width: "100%" }}
                      >
                        <div className="inner-card">
                          <div id="rate">
                            <div>
                              <div className="ally-class">
                                {item?.branch?.allyCompany?.allyClass}
                              </div>
                            </div>
                            <div className="branch-rate">
                              <span>
                                {item?.branch?.allyCompany?.allyRate?.name}
                              </span>
                              <span>|</span>
                              <span>{item?.branch?.allyCompany?.rate}/5</span>
                              <Star />
                            </div>
                          </div>
                          <div id="location">
                            <div className="icon-wrapper">
                              <img
                                src="/assets/images/carBranches/locationPin.svg"
                                alt="icon"
                              />
                              <h6 className="bold">
                                {item?.branch?.area?.name}
                                {item?.branch?.region?.name
                                  ? `, ${item?.branch?.region?.name}`
                                  : ""}
                                {item?.branch?.districtName
                                  ? `, ${item?.branch?.districtName}`
                                  : ""}
                              </h6>
                            </div>
                          </div>
                          <div id="km-away">
                            <div className="icon-wrapper">
                              <img
                                src="/assets/images/carBranches/locationArrow.svg"
                                alt="icon"
                              />
                              <h6 className="bold">
                                {`${t("km.away")} ${
                                  item?.branch?.distanceBetweenBranchUser
                                } ${t("km")}`}
                              </h6>
                            </div>
                          </div>
                          <div id="availability">
                            <div>
                              {item?.branch?.deliverToAirport ? (
                                <div className="airport">
                                  <img
                                    src="/assets/images/carBranches/airplane.svg"
                                    alt="icon"
                                  />
                                  <span>{t("Airport") as string}</span>
                                </div>
                              ) : null}
                              {item?.branch?.canDelivery ? (
                                <div className="delivery">
                                  <span>{t("Delivery") as string}</span>
                                </div>
                              ) : null}
                            </div>
                            {item?.branch?.branchState === "open" ? (
                              <span className="status">
                                {t("Open Now") as string}
                              </span>
                            ) : (
                              <span className="status">
                                {t("Closed Now") as string}
                              </span>
                            )}
                          </div>
                        </div>
                      </Grid>
                      <Grid item lg={8} md={8} sm={12} id="allies-features">
                        {getAllExtraServices(item).map((i) => {
                          if (!i?.hide) {
                            return (
                              <ExtraService key={i?.id} className="bold">
                                <div>
                                  <div className="gap-5px">
                                    {i?.extraService?.iconUrl ? (
                                      <img
                                        src={i?.extraService?.iconUrl}
                                        alt="icon"
                                      />
                                    ) : null}
                                    <span className="text text-align-localized">
                                      {i?.extraService?.title ||
                                        i?.allyExtraService?.extraService
                                          ?.title}
                                    </span>
                                  </div>
                                  {i?.serviceValue == 0 ? (
                                    <span style={{ color: "#6FCFA1" }}>
                                      {t("available") as string}
                                    </span>
                                  ) : (
                                    <div className="text-uppercase d-flex gap-2px ">
                                      {typeof i?.subtitle === "string"
                                        ? i.subtitle
                                            .replace("SAR", "﷼")
                                            .replace("ريال", "﷼")
                                            .split("/")
                                            .map((item, index, arr) => (
                                              <div
                                                className="d-flex gap-2px "
                                                key={`${i?.id}-${index}`}
                                              >
                                                {arr.length > 1 &&
                                                index === 1 ? (
                                                  <>
                                                    <span>/</span>
                                                    <span>{item}</span>
                                                  </>
                                                ) : (
                                                  item
                                                    .split(" ")
                                                    .map((part, i) => (
                                                      <span
                                                        key={i}
                                                        className="riyal-symbol"
                                                        style={{
                                                          order:
                                                            i18n.language ===
                                                            "en"
                                                              ? i === 0
                                                                ? 1
                                                                : 0
                                                              : index,
                                                        }}
                                                      >
                                                        {part}
                                                      </span>
                                                    ))
                                                )}
                                              </div>
                                            ))
                                        : i?.subtitle}
                                    </div>
                                  )}
                                </div>
                                <div className="separator" />
                              </ExtraService>
                            );
                          }
                          return null;
                        })}
                      </Grid>
                    </Grid>
                    <div
                      style={{
                        width: "100%",
                        display: "flex",
                        justifyContent: "end",
                      }}
                    >
                      <div className="price-label">
                        <div
                          className="bold"
                          style={{
                            display: "flex",
                            gap: "5px",
                            direction: "ltr",
                            alignItems: "baseline",
                          }}
                        >
                          <div className="bold" style={{ order: 0 }}>
                            <RiyalSymbol />
                          </div>
                          <div style={{ order: 1 }}>
                            {searchData.selection_index === 1
                              ? Math.round(item?.carMonthsPrice)
                              : item?.carPrice}
                          </div>
                        </div>
                        <div>
                          {searchData.selection_index === 1
                            ? (t("/month") as string)
                            : (t("/day") as string)}
                        </div>
                      </div>
                    </div>
                  </Card>
                </Link>
              );
            })}
            <Pagination
              page={alliesCarsList?.metadata?.page}
              onChange={handlePageChange}
              count={alliesCarsList?.metadata?.totalPages}
              showFirstButton
              showLastButton
              className="d-flex justify-content-center"
            />
          </Container>
        </Div>
      )}
    </Layout>
  );
}
