/* eslint-disable react-hooks/exhaustive-deps */
import { Head } from "./head";

import { useQuery } from "@apollo/client";
import { CircularProgress, Container } from "@material-ui/core";
import Layout from "components/shared/layout";
import { Business_Rental_Offers } from "gql/queries/offers";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Card from "./card";
import PaginationComponent from "./pagination";

const Div = styled.div`
  padding: 30px 0;
  background: #f5f5f5;
`;

const CardsContainer = styled.div`
  padding: 50px 0 10px 0;
`;

export default function Offers({ id }) {
  const { t } = useTranslation();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const {
    data: offersRes,
    loading,
    refetch,
  } = useQuery(Business_Rental_Offers, {
    variables: { id: id, skip: !id, page, limit },
  });

  useEffect(() => {
    let clean = false;
    if (!clean) refetch();
    return (clean = true);
  }, []);

  return (
    <Layout>
      <Div>
        <Head t={t} offersRes={offersRes} />
        <CardsContainer>
          <Container>
            <div className="d-flex flex-column gap-30px">
              {offersRes?.businessRentalOffers && !loading ? (
                offersRes.businessRentalOffers.collection.map((item, index) => {
                  return (
                    <Card
                      key={item.id}
                      index={index}
                      {...item}
                      refetch={refetch}
                    />
                  );
                })
              ) : (
                <p className="text-center mb-2">
                  {loading ? <CircularProgress /> : t("No.Recordes")}
                </p>
              )}
            </div>
            {offersRes?.businessRentalOffers &&
            offersRes.businessRentalOffers.metadata.totalPages ? (
              <PaginationComponent
                offersRes={offersRes}
                limit={limit}
                setPage={setPage}
                setLimit={setLimit}
              />
            ) : null}
          </Container>
        </CardsContainer>
      </Div>
    </Layout>
  );
}
