import { useLazyQuery } from "@apollo/client";
import { AboutRentPrice_Query } from "gql/queries/car";
import { useEffect } from "react";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { setAboutRentPrice } from "store/cars/action";
import { useSnackbar } from "notistack";
import useRentalType from "components/CarDetails/heplers/rentalType";

export function useAboutRentPrice() {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  const {
    user_address,
    date_time,
    confirmed_delivery_location,
    login_data,
    coupon_data,
    cars,
    installments,
    wallet,
  } = useSelector((state: RootStateOrAny) => ({
    ...state.search_data,
    ...state.authentication,
    ...state.coupons,
    cars: state.cars || {},
    installments: state.installments || {},
    wallet: state.wallet || {},
  }));
  const { isRentToOwn } = useRentalType();
  const {
    handover_in_another_branch,
    deliveryType,
    insuranceId,
    paymentType,
    extra_services,
    car_data,
    about_rent_price,
    rent_to_own_plan,
    paymentMethod: paymentBrand,
  } = cars;

  const [aboutRentPrice, { data: aboutRentPriceData, loading, error }] =
    useLazyQuery(AboutRentPrice_Query, {
      fetchPolicy: "no-cache",
      nextFetchPolicy: "no-cache",
      errorPolicy: "all",
    });

  const getDeliveryCoordinates = () => {
    if (deliveryType === "no_delivery")
      return { deliverLat: undefined, deliverLng: undefined };
    const { airport, lat, lng } = confirmed_delivery_location || {};
    return {
      deliverLat: airport?.centerLat || lat || user_address?.pick_up.lat,
      deliverLng: airport?.centerLng || lng || user_address?.pick_up.lng,
    };
  };

  const getServiceIds = (serviceType) => {
    return (
      extra_services?.filter((e) => e.type === serviceType).map((i) => i.id) ||
      []
    );
  };

  const getPaymentMethod = () => {
    if (paymentBrand)
      return paymentBrand.toUpperCase() === "CASH" ? "CASH" : "ONLINE";
    if (
      about_rent_price?.availablePaymentMethods?.toLowerCase() === "all" ||
      about_rent_price?.availablePaymentMethods?.includes("and")
    ) {
      return "ONLINE";
    }
    return about_rent_price?.availablePaymentMethods?.toUpperCase() || "ONLINE";
  };

  const getRentPrice = () => {
    if (!car_data?.id || (!insuranceId && !isRentToOwn)) return;
    const { deliverLat, deliverLng } = getDeliveryCoordinates();
    const allyExtraServices = getServiceIds("allyExtraService");
    const branchExtraServices = getServiceIds("branchExtraService");
    const paymentMethod = getPaymentMethod();

    aboutRentPrice({
      variables: {
        carId: car_data?.id,
        handoverBranch: handover_in_another_branch?.checked
          ? handover_in_another_branch.branchId
          : undefined,
        deliverLat,
        deliverLng,
        deliveryType: deliveryType === "no_delivery" ? undefined : deliveryType,
        pickUpDate: date_time?.pickUpDate,
        pickUpTime: date_time?.pickup_time
          ? `${date_time.pickup_time}:00`
          : undefined,
        dropOffDate: date_time?.dropOffDate,
        dropOffTime: date_time?.return_time
          ? `${date_time.return_time}:00`
          : undefined,
        isUnlimited: !!extra_services?.find((e) => e?.id === "isUnlimited"),
        allyExtraServices,
        branchExtraServices,
        insuranceId: insuranceId || undefined,
        paymentMethod,
        couponId: coupon_data?.id,
        payWithInstallments: !isRentToOwn && installments.pay_with_installments,
        withWallet:
          (login_data && paymentType.toUpperCase() !== "CASH") ||
          about_rent_price?.availablePaymentMethods?.toUpperCase() !== "CASH"
            ? wallet.pay_with_wallet
            : undefined,
        ownCarPlanId: rent_to_own_plan?.id,
        paymentBrand:
          paymentBrand?.toUpperCase() !== "CASH" ? paymentBrand : undefined,
      },
    });
  };

  useEffect(() => {
    if (aboutRentPriceData) {
      dispatch(setAboutRentPrice(aboutRentPriceData?.aboutRentPrice));
    } else if (error) {
      enqueueSnackbar(error.message, {
        variant: "error",
        preventDuplicate: true,
      });
    }
  }, [aboutRentPriceData, error, dispatch, enqueueSnackbar]);

  return { getRentPrice, loading };
}
