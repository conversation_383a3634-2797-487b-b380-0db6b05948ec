import { gql } from "@apollo/client";

const COUPON_AVAILABILITY_QUERY = gql`
  query CouponAvailability($couponCode: ID!) {
    couponAvailability(couponCode: $couponCode) {
      localizedPaymentMethod
      id
      code
      expireAt
      generalTerms
      isActive
      areas {
        name
        id
      }
      maxLimitValue
      minRentPrice
      minRentDays
      numOfUsagesPerUser
      discountType
      discountValue
      discountTypeKey
      minRentPrice
      numOfDays
    }
  }
`;

export { COUPON_AVAILABILITY_QUERY };
