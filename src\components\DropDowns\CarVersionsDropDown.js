/* eslint-disable no-restricted-globals */
/* eslint-disable eqeqeq */
/* eslint-disable no-unused-expressions */
/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
import React, { memo, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { CarVersions } from "gql/queries/GetCarVersions.gql";

function CarVersionsDropDown({
  loading,
  setSelectedVersion,
  selectVersion,
  error,
  valueAttribute,
  multiple,
  setCarVersionList,
  branchIds,
  coupon,
  ...props
}) {
  const { data: carVersions, loading: gettingVersions } = useQuery(CarVersions, {
    variables: {
      limit: 1000,
      branchIds,
    },
  });
  const { locale, formatMessage } = useIntl();
  const [options, setOptions] = useState([]);

  useEffect(() => {
    if (carVersions && setCarVersionList) {
      setCarVersionList(carVersions?.carVersions?.collection?.length);
    }
  }, [carVersions]);

  useEffect(() => {
    if (!options?.length) {
      const alloptions =
        carVersions?.carVersions.collection
          ?.map((x) => ({
            value: x[valueAttribute || "enName"],
            label: `${x.carModel.make[`${locale}Name`]} ${x.carModel[`${locale}Name`]} ${
              x[`${locale}Name`]
            } ${x.year}`,
          }))
          ?.filter((i) => !options?.includes(i.value)) || [];
      if (alloptions.length) {
        setOptions([{ value: "all", label: formatMessage({ id: "widgets.all" }) }, ...alloptions]);
      }
    }
  }, [carVersions]);

  useEffect(() => {
    if (!selectVersion) {
      onClear();
    }
  }, [selectVersion]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };

  return (
    <Select
      isMulti={multiple}
      className={`dropdown-select ${multiple ? "multiple" : ""} ${error ? "selection-error" : ""}`}
      options={options}
      isClearable
      ref={selectInputRef}
      loadOptions={gettingVersions || loading}
      value={
        multiple
          ? options?.filter((optn, index) =>
              coupon && !selectVersion?.length && branchIds?.length
                ? optn.value != "all"
                : selectVersion?.includes(+optn.value),
            )
          : options?.find((optn) => `${optn.value}` === `${selectVersion}`)
      }
      placeholder={formatMessage({ id: "car.version" })}
      onChange={(selection) => {
        if (multiple) {
          const versionsids = [];
          if (selection == null && multiple) {
            setSelectedVersion();
            return;
          }
          if (selection[0].value == "all" || selection[selection.length - 1].value == "all") {
            options.map(
              (onselectoion) =>
                onselectoion.value != "all" && versionsids.push(+onselectoion?.value),
            );
          }
          
          selection?.map((onselectoion) => versionsids.push(+onselectoion?.value));
          console.log(versionsids,'versionsids')
          if (versionsids.length) {
            const versions = versionsids.filter((id) => !isNaN(id));
            setSelectedVersion([...versions]);
          } else {
            setSelectedVersion([]);
          }
        } else {
          if (selection?.value == "all") {
            setSelectedVersion("null");
            return;
          }
          setSelectedVersion(+selection?.value);
        }
      }}
      noOptionsMessage={() => {
        if (gettingVersions) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
      {...props}
    />
  );
}
CarVersionsDropDown.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectVersion: PropTypes.string,
  setSelectedVersion: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
export default memo(CarVersionsDropDown);
