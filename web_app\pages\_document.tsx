/* eslint-disable @next/next/no-css-tags */
/* eslint-disable @next/next/no-title-in-document-head */
/* eslint-disable react/display-name */
/* eslint-disable @next/next/no-document-import-in-page */
import Document, { Html, Head, Main, NextScript } from "next/document";
// Import styled components ServerStyleSheet
import { ServerStyleSheet } from "styled-components";
import { v4 as uuidv4 } from "uuid";

export default class MyDocument extends Document {
  static async getInitialProps(ctx: DocumentContext) {
    // Step 1: Create an instance of ServerStyleSheet
    const sheet = new ServerStyleSheet();

    const originalRenderPage = ctx.renderPage;

    try {
      // Step 2: Retrieve styles from components in the page
      ctx.renderPage = () =>
        originalRenderPage({
          enhanceApp: (App) => (props) =>
            sheet.collectStyles(<App {...props} />),
        });

      // Step 3: Get the initial props from the parent Document
      const initialProps = await Document.getInitialProps(ctx);

      // Step 4: Extract the styles as <style> tags
      const styleTags = sheet.getStyleElement();

      // Generate a nonce for CSP
      const nonce = uuidv4();

      // Step 5: Return the proper structure with html prop and other required props
      return {
        ...initialProps,
        styleTags,
        nonce,
      };
    } finally {
      sheet.seal();
    }
  }

  render() {
    const { nonce } = this.props as any;

    return (
      <Html>
        <Head>
          <meta name="robots" content="all" />
          <meta name="apple-itunes-app" content="app-id=1387161215" />
          <meta name="google-play-app" content="app-id=com.carwah.customer" />

          {/* Global style to fix 3D Secure iframe scrollbars */}

          <link
            rel="apple-touch-icon"
            href="https://play-lh.googleusercontent.com/MHLbpTa2U4wwZDLJ7Iu4WiWcXkQyhAtBsNAoHFoHyoKBntFz0K0NMVzq6W2nZKqVTFM=w240-h480-rw"
          />
          <link
            rel="android-touch-icon"
            href="https://play-lh.googleusercontent.com/MHLbpTa2U4wwZDLJ7Iu4WiWcXkQyhAtBsNAoHFoHyoKBntFz0K0NMVzq6W2nZKqVTFM=w240-h480-rw"
          />

          <meta
            name="keywords"
            content="car rental, car rental in Riyadh, car rental in Saudi Arabia, car rental in airport, cheap car rental, car rental deliveryايجار سيارة, ايجار, إيجار سيارة في الرياض , ايجار سيارة توصيل, ايجار سيارة في مطار الرياض, ايجار سيارة في مطار جدة ايجار سيارة رخيصة
            , حجز , booking , المملكة , Kingdom , السعودية , Saudi , عروض , offers , أفضل , Best , جدة , Jeddah , الرياض , Riyadh , شهري , Monthly , الباقات , packages , الخدمات , Services , السيارات , vehicle , Car , ايجار , rental , كروة , carwah , موقع , website"
          />
          <meta
            name="description"
            content="Carwah your first choice for car rental. Choose from our large fleet of cars from 900 branches in 42 cities and airports across Saudi Arabia. كروة خيارك الأول لتأجير السيارات. اختر من بين أسطولنا الكبير من السيارات من بين 900 فرع في 42 مدينة ومطار في جميع أنحاء المملكة العربية السعودية."
          />
          {/* og tags */}
          <meta property="og:type" content="article" />
          <meta
            property="og:title"
            content="Carwah Website - Car Rental | موقع كروة - ايجار السيارات"
          />
          <meta
            property="og:description"
            content="Carwah your first choice for car rental. Choose from our large fleet of cars from 900 branches in 42 cities and airports across Saudi Arabia. كروة خيارك الأول لتأجير السيارات. اختر من بين أسطولنا الكبير من السيارات من بين 900 فرع في 42 مدينة ومطار في جميع أنحاء المملكة العربية السعودية."
          />
          <meta property="og:image" content="/favicon.ico" />
          <meta
            property="og:site_name"
            content="Carwah Car Rental - كروة لأيجار السيارات"
          />
          {/* twitter cards */}
          <meta
            name="twitter:title"
            content="Carwah Website - Car Rental | موقع كروة - ايجار السيارات"
          />
          <meta
            name="twitter:description"
            content="Carwah your first choice for car rental. Choose from our large fleet of cars from 900 branches in 42 cities and airports across Saudi Arabia. كروة خيارك الأول لتأجير السيارات. اختر من بين أسطولنا الكبير من السيارات من بين 900 فرع في 42 مدينة ومطار في جميع أنحاء المملكة العربية السعودية."
          />
          <meta name="twitter:image" content="/favicon.ico" />
          <meta name="twitter:site" content="@Carwah_sa" />
          <meta name="twitter:creator" content="@Carwah_sa" />
          <link rel="icon" href="/favicon.ico" />
          <link href="/assets/fonts/style.css" rel="stylesheet" />
          <link
            rel="stylesheet"
            href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css"
            integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh"
            crossOrigin="anonymous"
          />
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" />
          {/* <link
            href="https://fonts.googleapis.com/css2?family=Jost&family=Roboto&display=swap"
            rel="stylesheet"
          />
          <link
            href="https://fonts.googleapis.com/css2?family=Jost:wght@400;500;600;700&display=swap"
            rel="stylesheet"
          /> */}
          <meta
            httpEquiv="Content-Security-Policy"
            content={`
              default-src 'self';
              style-src 'self' ${
                process.env.NEXT_PUBLIC_OPPWA_URL
              } https://fonts.googleapis.com https://stackpath.bootstrapcdn.com https://*.tamara.co http://***********:5000 https://*.carwah.com https://*.oppwa.com https://*.techlab-cdn.com 'unsafe-inline';
              font-src 'self' https://fonts.gstatic.com data:;
              frame-src 'self' ${
                process.env.NEXT_PUBLIC_OPPWA_URL
              } https://*.tamara.co http://***********:5000 https://*.carwah.com https://*.oppwa.com;
              script-src 'self' ${
                process.env.NEXT_PUBLIC_OPPWA_URL
              } https://*.firebaseapp.com https://*.googleapis.com https://*.tamara.co http://***********:5000 https://*.carwah.com https://*.oppwa.com https://*.techlab-cdn.com 'nonce-${nonce}' 'unsafe-inline' 'unsafe-eval' ${
              process.env.NODE_ENV === "development" ? "'unsafe-eval'" : ""
            };
              connect-src 'self' ${
                process.env.NEXT_PUBLIC_OPPWA_URL
              } https://*.oppwa.com https://*.firebaseapp.com https://*.googleapis.com https://prebeta.carwah.co http://prebeta.carwah.co:* https://*.carwah.co https://*.carwah.com http://***********:5000 http://***********:5000/graphql https://*.tamara.co https://api-sandbox.tamara.co https://api.tamara.co https://carwah-cars.s3.eu-west-2.amazonaws.com https://*.techlab-cdn.com ${
              process.env.NODE_ENV === "development"
                ? "ws://localhost:* http://localhost:*"
                : ""
            };
              img-src 'self' ${
                process.env.NEXT_PUBLIC_OPPWA_URL
              } https://play-lh.googleusercontent.com https://*.carwah.co https://*.carwah.com https://*.googleapis.com https://*.ggpht.com https://*.gstatic.com https://*.s3.amazonaws.com https://carwah-cars.s3.eu-west-2.amazonaws.com https://*.tamara.co https://*.oppwa.com https://*.techlab-cdn.com data: blob:;
              form-action 'self' https://*.tamara.co ${
                process.env.NEXT_PUBLIC_OPPWA_URL
              } http://***********:5000 https://*.carwah.com https://*.oppwa.com;
              worker-src 'self' blob:;
              media-src 'self' https://carwah-cars.s3.eu-west-2.amazonaws.com blob:;
              object-src 'self' blob:;
            `}
          />
          {(this.props as any).styleTags}
        </Head>
        <body>
          <Main />
          <NextScript nonce={nonce} />
        </body>
      </Html>
    );
  }
}
