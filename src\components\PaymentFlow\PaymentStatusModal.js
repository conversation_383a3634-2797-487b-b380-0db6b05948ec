import React from "react";
import { FormattedMessage } from "react-intl";
import { Button } from "reactstrap";
import { CircularProgress } from "@material-ui/core";
import { CheckCircle, Error, HourglassEmpty, Payment, Refresh } from "@material-ui/icons";
import "./PaymentStatusModal.scss";

const PaymentStatusModal = ({
  status,
  error,
  onRetry,
  onClose,
  onManualCheck,
  loading = false,
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case "paid":
        return <CheckCircle className="status-icon success" />;
      case "failed":
        return <Error className="status-icon error" />;
      case "pending_transaction":
      case "pending_payment":
        return <HourglassEmpty className="status-icon pending" />;
      default:
        return <Payment className="status-icon default" />;
    }
  };

  const getStatusMessage = () => {
    if (loading) {
      return <FormattedMessage id="Processing payment..." />;
    }

    switch (status) {
      case "paid":
        return <FormattedMessage id="Payment completed successfully!" />;
      case "failed":
        return <FormattedMessage id="Payment failed" />;
      case "pending_transaction":
        return <FormattedMessage id="Payment is being processed..." />;
      case "pending_payment":
        return <FormattedMessage id="Waiting for payment confirmation..." />;
      default:
        return <FormattedMessage id="Processing payment..." />;
    }
  };

  const getStatusClass = () => {
    switch (status) {
      case "paid":
        return "success";
      case "failed":
        return "error";
      case "pending_transaction":
      case "pending_payment":
        return "pending";
      default:
        return "default";
    }
  };

  const showRetryButton = status === "failed" && !loading;
  const showCloseButton = status === "paid" || (status === "failed" && !loading);
  const showManualCheckButton =
    (status === "pending_transaction" || status === "pending_payment") && !loading && onManualCheck;

  return (
    <div className="payment-status-modal">
      <div className="status-content">
        <div className="status-header">
          {loading ? (
            <CircularProgress size={60} className="status-icon loading" />
          ) : (
            getStatusIcon()
          )}

          <h4 className={`status-title ${getStatusClass()}`}>{getStatusMessage()}</h4>
        </div>

        {error && (
          <div className="error-details">
            <div className="alert alert-danger">
              <strong>
                <FormattedMessage id="Error:" />
              </strong>{" "}
              {error}
            </div>
          </div>
        )}

        {status === "paid" && (
          <div className="success-details">
            <div className="alert alert-success">
              <FormattedMessage id="Your payment has been processed successfully. The booking status will be updated shortly." />
            </div>
          </div>
        )}

        {(status === "pending_transaction" || status === "pending_payment") && (
          <div className="pending-details">
            <div className="alert alert-info">
              <FormattedMessage id="Please wait while we process your payment. This may take a few moments." />
            </div>
          </div>
        )}

        <div className="status-actions">
          {showRetryButton && (
            <Button color="primary" onClick={onRetry} className="me-2">
              <Refresh className="me-1" />
              <FormattedMessage id="Try Again" />
            </Button>
          )}

          {showManualCheckButton && (
            <Button color="info" onClick={onManualCheck} className="me-2">
              <Refresh className="me-1" />
              <FormattedMessage id="Check Status" />
            </Button>
          )}

          {showCloseButton && (
            <Button color={status === "paid" ? "success" : "secondary"} onClick={onClose}>
              <FormattedMessage id={status === "paid" ? "Done" : "Close"} />
            </Button>
          )}

          {loading && (
            <Button color="secondary" onClick={onClose}>
              <FormattedMessage id="Cancel" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentStatusModal;
