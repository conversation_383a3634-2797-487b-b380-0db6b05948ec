/* eslint-disable @next/next/no-img-element */
import { useMutation } from "@apollo/client";
import { ButtonsWrapper, ImageCard } from "components/MyAccount/styled";
import RequestLoader from "components/shared/requestLoader";
import { UploadImage } from "gql/mutations/imageUpload";
import { UpdateProfileImagesMutation } from "gql/mutations/profile";
import useUserProfile from "hooks/useUserProfile";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toggleYakeenCompleteMissingModal } from "store/user/action";
import { TProfileStatus } from "types/profile";
import { useFileUpload } from "use-file-upload";
import { getBase64 } from "utilities/helpers";

function ImageComponent({
  t,
  register,
  setValue,
  errors,
  title,
  fieldName,
  placeHolderImgSrc,
  watch,
  required,
}) {
  const [image, setImage] = useFileUpload() as any;

  return (
    <div>
      <label className="color-19 text-align-localized w-100">
        {title}
        {required && <span className="color-9">*</span>}
      </label>
      {errors?.[fieldName] && (
        <p className="color-9 font-14px">
          {t("This field is required") as string}
        </p>
      )}
      <div>
        <ImageCard>
          <img
            className="add cursor-pointer"
            src="/assets/images/profile/add.svg"
            alt="add"
            onClick={(e) => {
              setImage(
                {
                  accept: "image/jpg, image/jpeg, image/png",
                },
                ({ source, name, size, file }) => {
                  setValue(fieldName, { source, file, name, size });
                }
              );
            }}
          />
          <input
            hidden
            {...register(fieldName, {
              required,
            })}
          />
          {image || watch(fieldName) ? (
            <img
              src={
                (!image && watch(fieldName)) ||
                (image?.file?.type === "image/jpg" ||
                image?.file?.type === "image/jpeg" ||
                image?.file?.type === "image/png"
                  ? image?.source
                  : placeHolderImgSrc)
              }
              alt={fieldName}
            />
          ) : (
            <img src={placeHolderImgSrc} alt="add" />
          )}
        </ImageCard>
      </div>
    </div>
  );
}

function FinalStep({ watch, register, errors, handleSubmit, setValue }) {
  const status = watch("status") as TProfileStatus;
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { refetch: refetchUserData } = useUserProfile();

  const [imageUpload] = useMutation(UploadImage, { errorPolicy: "all" });
  const [updateProfileImages] = useMutation(UpdateProfileImagesMutation, {
    errorPolicy: "all",
  });
  const [loading, setLoading] = useState(false);

  function onSubmit(data: any) {
    delete data.status;
    delete data.dob;
    const imagesList = Object.entries({
      licenseFrontImage: data?.licenseFrontImage,
      licenseSelfieImage: data?.licenseSelfieImage,
      passportFrontImage: data?.passportFrontImage,
    }).filter((entry) => entry?.[1]);
    let imagesUrlList = [];
    const uploadPromises = imagesList.map((image: any) => {
      setLoading(true);
      return getBase64(image[1].file).then(async (data) => {
        const res = await imageUpload({
          variables: {
            image: data,
            topic: image[1].name,
          },
        });
        imagesUrlList.push({
          name: image[0],
          url: res.data.imageUpload.imageUpload.imageUrl,
        });
      });
    });
    Promise.all(uploadPromises).then(() => {
      setLoading(false);
      updateProfileImages({
        variables: {
          input: {
            licenseFrontImage:
              imagesUrlList?.find((item) => item.name === "licenseFrontImage")
                ?.url || undefined,
            licenseSelfieImage:
              imagesUrlList?.find((item) => item.name === "licenseSelfieImage")
                ?.url || undefined,
            passportFrontImage:
              imagesUrlList?.find((item) => item.name === "passportFrontImage")
                ?.url || undefined,
          },
        },
      }).then((res) => {
        if (res.data?.updateProfileImages?.status === "success") {
          refetchUserData()
            .then(() => {
              dispatch(toggleYakeenCompleteMissingModal(false));
            })
            .finally(() => {
              document.getElementById("rent-action").click();
            });
        }
      });
    });
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="mt-3">
      <ImageComponent
        {...{
          title: t("Driver Licence"),
          fieldName: "licenseFrontImage",
          t,
          required: true,
          register,
          setValue,
          errors,
          watch,
          placeHolderImgSrc: "/assets/images/profile/driver.svg",
        }}
      />

      {status === "visitor" && (
        <ImageComponent
          {...{
            title: t("Passport Front Image"),
            fieldName: "passportFrontImage",
            t,
            required: true,
            register,
            setValue,
            errors,
            watch,
            placeHolderImgSrc: "/assets/images/profile/passport.svg",
          }}
        />
      )}
      {status === "gulf_citizen" && (
        <ImageComponent
          {...{
            title: t("ID"),
            fieldName: "nidImage",
            t,
            required: true,
            register,
            setValue,
            errors,
            watch,
            placeHolderImgSrc: "/assets/images/profile/passport.svg",
          }}
        />
      )}
      {status === "visitor" && (
        <ImageComponent
          {...{
            title: t("VISA"),
            fieldName: "visaImage",
            t,
            required: true,
            register,
            setValue,
            errors,
            watch,
            placeHolderImgSrc: "/assets/images/profile/passport.svg",
          }}
        />
      )}
      <ImageComponent
        {...{
          title: t("Driver Licence with Selfie"),
          fieldName: "licenseSelfieImage",
          t,
          register,
          required: false,
          setValue,
          errors,
          watch,
          placeHolderImgSrc: "/assets/images/profile/driverSelfie.svg",
        }}
      />
      <ButtonsWrapper className="d-flex gap-20px justify-content-end p-3 w-100">
        <button type="submit" style={{ width: "100%" }}>
          {t("Confirm") as string}
        </button>
      </ButtonsWrapper>
      <RequestLoader loading={loading} />
    </form>
  );
}
export default FinalStep;
