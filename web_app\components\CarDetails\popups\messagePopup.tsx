import Popup from "components/shared/popup";
import { ReactNode } from "react";

export default function MessagePopup({
  isOpen,
  setIsOpen,
  body,
  title,
  onClose,
}: {
  isOpen: boolean;
  setIsOpen: any;
  body: ReactNode;
  title?: string;
  onClose?: () => void;
}) {
  if (isOpen) {
    return (
      <Popup
        maxWidth="sm"
        fullWidth
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        title={title}
        onClose={onClose}
      >
        {body}
      </Popup>
    );
  }
  return null;
}
