/* eslint-disable @next/next/no-img-element */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @next/next/inline-script-id */
/* eslint-disable react-hooks/exhaustive-deps */
import RateAlly from "components/shared/RateAlly";
import { memo, useEffect, useState } from "react";
import RequestLoader from "components/shared/requestLoader";
import Script from "next/script";
import CancelReasons from "components/shared/CancelReasons";
import SuccessPopup from "../popups/sucess";
import ErrorPopup from "../popups/error";
import InvalidPickupPopup from "../popups/invalidPickup";
import InvalidDropoffPopup from "../popups/invalidDropoff";
import PaymentStatusPopup from "../popups/paymentStatus";
import PaymentPopup from "../popups/payment";
import Checkout from "../popups/checkout";
import TamaraFailPopup from "../popups/tmarahFail";
import CouponPopUp from "../popups/Coupon";
import InstallmentsBreakDown from "../popups/InstallmentBreakdown";
import TermsConditions from "../popups/termsConditions";
import { setShowInstallmmentBreakdownModal } from "store/installments/action";
import useLogic from "./useLogic";
import CarNotAvailable from "../popups/carNotAvailable";
import ActionButton from "./ActionButton";
import YakeenModal from "../popups/Yakeen";
import { RootStateOrAny, useSelector } from "react-redux";
import { InfoOutlined } from "@material-ui/icons";

const RentPayment = ({
  setCalendarTooltipOpen,
  refetchRentalDetails,
  extensionId,
  setExtensionId,
  isIntallmentDetailsModal,
  isInInstallmentBreakDown,
  valueToPay,
  refetchRentalAboutPrice,
}) => {
  const [isCouponInvalid, setIsCouponInvalid] = useState(false);
  const {
    submitRentHandler,
    getPaymentStatusModalLocalizedContent,
    showRentActionBtn,
    rentalIntegrationStatus,
    isIntegratedRental,
    showCancelReservation,
    loading,
    isSuccessOpen,
    setIsSuccessOpen,

    invaildPickupPopupOpen,
    setTermsConditionModalOpen,
    termsConditionModalOpen,
    isPaymentStatusPopupOpen,
    setIsPaymentPopupOpen,
    isTamaraFailPopupOpen,
    isCheckoutPopupOpen,
    isPaymentPopupOpen,
    ErrorMessage,
    ErrorOpen,
    setErrorOpen,
    invaildDropoffPopupOpen,
    AllyRate,
    setAllyRatePopup,
    cancelRent,
    setCancelRent,
    checkoutId,
    bookingId,
    paymentType,
    booking_id,
    t,
    rentalData,
    setInvaildPickupPopupOpen,
    setInvaildDropoffPopupOpen,
    setIsPaymentStatusPopupOpen,
    extension_id,
    setIsCheckoutPopupOpen,
    paymentMethod,
    setCheckoutId,
    getPaymentStatusHandler,
    setIsTamaraFailPopupOpen,
    dispatch,
    rentalHasInstallment,
    sucessInstallmentPaid,
    isDisabled,
    buttonText,
    clickHandler,
    pendingRentalHandler,
    carNotAvailablePopupOpen,
    setCarNotAvailablePopupOpen,
    changePymentMethodHandler,
    buttonIconUrl,
    showChangePaymentMethod,
    tamaraErrors,
    PaymentErrors,
    isPendingRentalPopupOpen,
    setIsPendingRentalPopupOpen,
    integrity,
  } = useLogic(
    extensionId,
    isInInstallmentBreakDown,
    isIntallmentDetailsModal,
    refetchRentalDetails,
    valueToPay,
    isCouponInvalid,
    setIsCouponInvalid,
    refetchRentalAboutPrice
  );

  const { about_rent_price } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const { rental_about_price, rentalBranch } =
    useSelector((state: RootStateOrAny) => state.booking) || {};
  const { couponErrorMessage } = !booking_id
    ? about_rent_price || {}
    : rental_about_price || {};

  const { is_paid_by_wallet } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};
  return (
    <>
      <RequestLoader loading={loading} />

      {showRentActionBtn ? (
        <ActionButton
          isDisabled={isDisabled()}
          buttonText={buttonText()}
          buttonIconUrl={buttonIconUrl()}
          actionHandler={clickHandler}
          changePymentMethodHandler={
            showChangePaymentMethod ? changePymentMethodHandler : null
          }
        />
      ) : null}

      {booking_id &&
      rentalData?.rentalDetails?.status.toLowerCase() == "invoiced" &&
      rentalData?.rentalDetails.subStatus != "rated" &&
      !rentalData?.rentalDetails.allyRentalRate ? (
        <button
          className="btn btn-link w-100"
          onClick={() => setAllyRatePopup(!AllyRate)}
        >
          {t("Rate Ally") as string}
        </button>
      ) : null}

      {showCancelReservation ? (
        <button
          className="btn btn-link w-100 mt-2"
          onClick={() => setCancelRent(!cancelRent)}
        >
          {t("Cancel Reservation") as string}
        </button>
      ) : null}

      <SuccessPopup
        isOpen={isSuccessOpen}
        setIsOpen={setIsSuccessOpen}
        isInstallment={booking_id && rentalHasInstallment}
      />

      <CouponPopUp
        isOpen={isCouponInvalid}
        setIsOpen={setIsCouponInvalid}
        couponErrorMessage={couponErrorMessage}
      />
      <ErrorPopup
        isOpen={ErrorOpen}
        setIsOpen={setErrorOpen}
        ErrorMessage={ErrorMessage}
        buttonText={undefined}
        buttonClickHandler={undefined}
      />

      <RateAlly
        isOpen={AllyRate}
        setIsOpen={setAllyRatePopup}
        title={t("Rate Ally")}
      />

      <CancelReasons
        isOpen={cancelRent}
        setIsOpen={setCancelRent}
        rentalId={booking_id}
        status={rentalData?.rentalDetails?.status}
        title={t("Cancel request")}
        isRentToOwn={undefined}
      />

      <InvalidPickupPopup
        isOpen={invaildPickupPopupOpen}
        setIsOpen={setInvaildPickupPopupOpen}
        setCalendarTooltipOpen={setCalendarTooltipOpen}
        actionFunction={() => {
          dispatch(setShowInstallmmentBreakdownModal(false));
        }}
      />

      <InvalidDropoffPopup
        isOpen={invaildDropoffPopupOpen}
        setIsOpen={setInvaildDropoffPopupOpen}
        setCalendarTooltipOpen={setCalendarTooltipOpen}
        actionFunction={() => {
          dispatch(setShowInstallmmentBreakdownModal(false));
        }}
      />
      {isPaymentStatusPopupOpen || is_paid_by_wallet === "Success" ? (
        <PaymentStatusPopup
          isOpen={isPaymentStatusPopupOpen || is_paid_by_wallet === "Success"}
          PaymentErrors={PaymentErrors}
          ErrorMessage={ErrorMessage}
          setIsOpen={setIsPaymentStatusPopupOpen}
          localizedContent={getPaymentStatusModalLocalizedContent(
            isIntegratedRental,
            rentalIntegrationStatus
          )}
          paid={Boolean(status === "paid" || status === "partially_refunded")}
          extensionId={extensionId || extension_id}
          bookingId={bookingId}
          setCheckoutId={setCheckoutId}
        />
      ) : null}

      <PaymentPopup
        isOpen={isPaymentPopupOpen}
        setIsOpen={setIsPaymentPopupOpen}
        hideCash={rentalBranch?.paymentMethod === "online"}
      />

      <Checkout
        isOpen={isCheckoutPopupOpen}
        setIsOpen={setIsCheckoutPopupOpen}
        paymentBrandId={paymentMethod}
        checkoutId={checkoutId}
        integrity={integrity}
        setCheckoutId={setCheckoutId}
        extensionId={extensionId || extension_id}
        bookingId={bookingId}
        isInstallment={rentalHasInstallment}
        setIsPaymentStatusPopupOpen={setIsPaymentStatusPopupOpen}
        getPaymentStatusHandler={getPaymentStatusHandler}
      />

      <TamaraFailPopup
        isOpen={isTamaraFailPopupOpen}
        setIsOpen={setIsTamaraFailPopupOpen}
        setIsPaymentPopupOpen={setIsPaymentPopupOpen}
        tamaraErrors={tamaraErrors}
      />

      {!isInInstallmentBreakDown && (
        <InstallmentsBreakDown
          title=""
          setTermsConditionModalOpen={setTermsConditionModalOpen}
          setExtensionId={setExtensionId}
          extensionId={extensionId}
          refetchRentalDetails={refetchRentalDetails}
          setCalendarTooltipOpen={setCalendarTooltipOpen}
        />
      )}

      {termsConditionModalOpen ? (
        <TermsConditions
          isOpen={termsConditionModalOpen}
          setIsOpen={setTermsConditionModalOpen}
        />
      ) : (
        ""
      )}
      {carNotAvailablePopupOpen ? (
        <CarNotAvailable
          isOpen={carNotAvailablePopupOpen}
          setIsOpen={setCarNotAvailablePopupOpen}
        />
      ) : null}
      <YakeenModal />
      <ErrorPopup
        {...{
          isOpen: isPendingRentalPopupOpen,
          setIsOpen: setIsPendingRentalPopupOpen,

          ErrorMessage: (
            <div>
              <InfoOutlined className="mb-2" />
              <h4 className="text-center mb-3">
                {t("Pending Rental") as string}
              </h4>
              <p className="text-center">
                {t("Pending rental alert") as string}
              </p>
            </div>
          ),
          buttonText: t("See Rental Details"),
          buttonClickHandler: () => {
            pendingRentalHandler();
            dispatch(setShowInstallmmentBreakdownModal(false));
          },
        }}
      />
    </>
  );
};
export default memo(RentPayment);
