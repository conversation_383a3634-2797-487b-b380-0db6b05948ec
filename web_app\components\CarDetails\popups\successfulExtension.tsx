/* eslint-disable react-hooks/exhaustive-deps */
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import MessagePopup from "./messagePopup";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import styled from "styled-components";
import { setIsExtensionRequestCreatedSuccessfully } from "store/extensions/action";

const DIV = styled.div`
  .button {
    color: var(--color-3);
    padding: 15px 20px 15px 20px;
    cursor: pointer;
    background: #f2f8fa;
  }
  .btn-back {
    color: #000;
    padding: 10px 20px 15px 20px;
    cursor: pointer;
  }
`;
export default function SuccessfulExtensionPopup({ setIsExtensionModalOpen }) {
  const router = useRouter();
  const { car, bookingId } = router.query || {};
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { is_extension_requset_created_successfully } =
    useSelector((state: RootStateOrAny) => state.extensions) || {};

  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      dispatch(setIsExtensionRequestCreatedSuccessfully(false));
    }
  }, [isOpen]);

  useEffect(() => {
    setIsOpen(is_extension_requset_created_successfully ? true : false);
  }, [is_extension_requset_created_successfully]);

  if (isOpen) {
    return (
      <MessagePopup
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        body={
          <>
            <p>
              {
                t(
                  "Dear customer.. your extension request is pending now. The ally has the right to confirm or reject the request. You can follow up on your request through Rental"
                ) as string
              }
            </p>
            <DIV className="d-flex justify-content-between mt-3">
              <div
                className="button"
                onClick={() => {
                  if (!bookingId) {
                    router.push("my-rentals?tab=current");
                  } else {
                    setIsOpen(false);
                    setIsExtensionModalOpen(false);
                    router.push(
                      `/car-details?car=${car}&bookingId=${bookingId}`
                    );
                  }
                }}
              >
                {!bookingId
                  ? (t("See Rental Details") as string)
                  : (t("Done") as string)}
              </div>
              <div className="btn-back" onClick={() => router.push("/")}>
                {t("Back to Home") as string}
              </div>
            </DIV>
          </>
        }
        title={t("Rental request has been sent")}
        onClose={() => {
          router.push(`/car-details?car=${car}&bookingId=${bookingId}`);
          setIsExtensionModalOpen(false);
        }}
      />
    );
  }
  return null;
}
