/* eslint-disable prettier/prettier */
import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import Select from "react-select";
export function PaymentBrandDropDown({
  loading,
  setSelectedPaymentBrand,
  SelectedPaymentBrand,
  error,
  valueAttribute,
  ...props
}) {

  const { locale, formatMessage } = useIntl();

 

  React.useEffect(() => {
    if (!SelectedPaymentBrand) {
      onClear();
    }
  }, [SelectedPaymentBrand]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef?.current?.select.clearValue();
  };
  

  return (
    // <Select
    //   className={`dropdown-select ${error ? "selection-error" : ""}`}
    //   // styles={{ menuPortal: (base) => ({ ...base, zIndex: 9999 }) }}
    //   menuPortalTarget={document.body}
    //   menuPosition="fixed"
    //   options={options}
    //   ref={selectInputRef}
    //   isMulti={inBooking}
    //   loadOptions={gettingMakes || loading}
    //   defaultValue={options.find((optn) => `${optn.value}` === `${selectedMake}`)}
    //   value={options.find((optn) => `${optn.value}` === `${selectedMake}`)}
    //   placeholder={formatMessage({ id: "car.make" })}
    //   onChange={(selection) => {
    //     if (inBooking && selection?.[selection?.length - 1]?.value == "all") {
    //       const makes = selection?.filter((onselectoion) => onselectoion.value == "all");

    //       setSelectedMake(makes);
    //     } else if (inBooking) {
    //       const makesWithoutAll = selection?.filter((onselectoion) => onselectoion.value != "all");

    //       setSelectedMake(makesWithoutAll);
    //     } else {
    //       setSelectedMake(selection?.value);
    //     }
    //     // if(inBooking){
    //     //   setSelectedMake(selection)
    //     // }else{
    //     // setSelectedMake(selection?.value);

    //     // }
    //   }}
    //   noOptionsMessage={() => {
    //     if (gettingMakes) {
    //       return <CircularProgress />;
    //     }
    //     if (!options?.length) return "no data found";
    //   }}
    //   {...props}
    // />
    <Select
      isMulti
      className={`dropdown-select`}
      options={[
        {
          value: "APPLEPAY",
          label: formatMessage({ id: "APPLEPAY" }),
        },
        {
          value: "MADA",
          label: formatMessage({ id: "MADA" }),
        },

        {
          value: "CREDIT_CARD",
          label: formatMessage({ id: "CREDIT_CARD" }),
        },
        {
          value: "TAMARA",
          label: formatMessage({ id: "Tamara" }),
        },
      ]}
      value={[
        {
          value: "APPLEPAY",
          label: formatMessage({ id: "APPLEPAY" }),
        },
        {
          value: "MADA",
          label: formatMessage({ id: "MADA" }),
        },

        {
          value: "CREDIT_CARD",
          label: formatMessage({ id: "CREDIT_CARD" }),
        },
        {
          value: "TAMARA",
          label: formatMessage({ id: "Tamara" }),
        },
      ].filter((optn) => SelectedPaymentBrand?.includes(optn.value))}
      placeholder={formatMessage({ id: "paymentbrand" })}
      onChange={(selection) => {
        console.log(selection);
        setSelectedPaymentBrand(selection?.map(item=>item.value));
        // setFieldValue("maxLimitValue", undefined);
      }}
    />
  );
}
PaymentBrandDropDown.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  SelectedPaymentBrand: PropTypes.string,
  setSelectedPaymentBrand: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
