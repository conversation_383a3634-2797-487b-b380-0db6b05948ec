/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import React, { useEffect } from "react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { DateTimeCombined } from "utilities/helpers";
import styled from "styled-components";
import CalendarTooltip from "components/shared/calendarTooltip";
import { setDateTimeAction } from "store/search/action";
import moment from "moment";
import { ClickAwayListener, Grid } from "@material-ui/core";
import { default_date_time } from "store/search/reducer";

//Styled Components

const InputWrap = styled.div`
  position: relative;
  border: none !important;
  input:focus ~ .floating-label,
  input:not(:focus):valid ~ .floating-label {
    top: -15px;
  }
  input {
    border-radius: var(--radius-3);
    padding: 20px !important;
  }
  .floating-label {
    position: absolute;
    pointer-events: none;
    top: ${(props) => (props.hasValue ? "-15px" : "25px")} !important;
    background-color: white;
    transition: 0.2s ease all;
    font-size: 14px;
    left: ${(props) => (props.lang === "en" ? "20px" : null)};
    right: ${(props) => (props.lang === "ar" ? "20px" : null)};
    color: #858585;
    @media (max-width: 768px) {
      top: ${(props) => (props.hasValue ? "-17px" : "14px")} !important;
      left: ${(props) => (props.lang === "en" ? "5px" : null)};
      right: ${(props) => (props.lang === "ar" ? "5px" : null)};
      z-index: 9;
    }
  }
`;

export default function DateTimeInputs() {
  //Store
  const dateTime = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );
  const selectionIndex = useSelector(
    (state: RootStateOrAny) => state.search_data.selection_index
  );

  //Hooks
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();

  //State
  const reservationDays = dateTime?.reservation_days;
  const pickUpDate = dateTime?.pickUpDate;
  const dropOffDate = dateTime?.dropOffDate;
  const [toolTipPickupOpen, settoolTipPickupOpen] = useState(false);
  const [toolTipReturnOpen, settoolTipReturnOpen] = useState(false);

  return (
    <Grid item container md={7} spacing={2}>
      <Grid
        item
        md={selectionIndex === 1 ? 12 : 6}
        xs={12}
        className="position-relative"
      >
        <Grid
          container
          justifyContent="space-between"
          alignItems="center"
          className="input"
        >
          <Grid item xs={9}>
            <InputWrap
              className="input"
              lang={i18n.language}
              hasValue={
                reservationDays
                  ? DateTimeCombined(
                      reservationDays[0],
                      dateTime?.pickup_time,
                      i18n.language
                    )
                  : ""
              }
            >
              <input
                type="text"
                value={
                  reservationDays
                    ? DateTimeCombined(
                        pickUpDate,
                        dateTime?.pickup_time,
                        i18n.language
                      )
                    : ""
                }
                readOnly
                onClick={() => {
                  setTimeout(() => {
                    settoolTipPickupOpen(true);
                  });
                }}
              />
              <span className="floating-label">
                {t("Pick-up Car Time") as string}
              </span>
            </InputWrap>
          </Grid>
          <Grid
            item
            xs={dateTime?.pickup_time ? 3 : "auto"}
            className="text-center d-flex justify-content-center gap-30px"
            onClick={() => {
              setTimeout(() => {
                settoolTipPickupOpen(true);
              });
            }}
          >
            <img src={`/assets/images/calendar.svg`} alt="icon" />
          </Grid>
        </Grid>
        <ClickAwayListener
          touchEvent={"onTouchStart"}
          mouseEvent={"onMouseDown"}
          onClickAway={() => {
            settoolTipPickupOpen(false);
          }}
        >
          <div>
            {toolTipPickupOpen ? (
              <CalendarTooltip
                isOpen={toolTipPickupOpen}
                setIsOpen={settoolTipPickupOpen}
              />
            ) : null}
          </div>
        </ClickAwayListener>
      </Grid>
      {selectionIndex !== 1 ? (
        <>
          <div title={"عدد الايام"} className="counter">
            <div>
              <img
                src={`/assets/images/counter.svg`}
                alt="exchange"
                className="h-auto "
                style={{ width: "25px" }}
              />
              <span className="bold">{dateTime.selected_days_count || 0}</span>
            </div>
          </div>
          <Grid item md={6} xs={12}>
            <Grid
              container
              justifyContent="space-between"
              alignItems="center"
              className="input"
            >
              <Grid item xs={9}>
                <InputWrap
                  className="input"
                  lang={i18n.language}
                  hasValue={
                    reservationDays
                      ? DateTimeCombined(
                          dropOffDate,
                          dateTime?.return_time,
                          i18n.language
                        )
                      : ""
                  }
                >
                  <input
                    type="text"
                    value={
                      reservationDays
                        ? DateTimeCombined(
                            dropOffDate,
                            dateTime?.return_time,
                            i18n.language
                          )
                        : ""
                    }
                    readOnly
                    onClick={() => {
                      setTimeout(() => {
                        settoolTipReturnOpen(true);
                      });
                    }}
                  />
                  <span className="floating-label">
                    {t("Return Car Time") as string}
                  </span>
                </InputWrap>
              </Grid>
              <Grid
                item
                xs={dateTime?.return_time ? 3 : "auto"}
                className="text-center d-flex justify-content-center gap-30px"
                onClick={() => {
                  setTimeout(() => {
                    settoolTipReturnOpen(true);
                  });
                }}
              >
                <img src={`/assets/images/calendar.svg`} alt="icon" />
              </Grid>
            </Grid>
            <ClickAwayListener
              touchEvent={"onTouchStart"}
              mouseEvent={"onMouseDown"}
              onClickAway={() => {
                settoolTipReturnOpen(false);
              }}
            >
              <div>
                {toolTipReturnOpen ? (
                  <CalendarTooltip
                    isOpen={toolTipReturnOpen}
                    setIsOpen={settoolTipReturnOpen}
                  />
                ) : null}
              </div>
            </ClickAwayListener>
          </Grid>
        </>
      ) : null}
    </Grid>
  );
}
