/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import Select from "react-select";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { useEffect, useState } from "react";
import "moment/locale/en-in";
import "moment/locale/ar";
import { useMutation, useQuery } from "@apollo/client";
import React, { useRef } from "react";

import AddIcon from "@material-ui/icons/Add";
import SelectComponent from "components/shared/select";

import {
  BussinessActivites,
  BussinessProfile,
} from "gql/queries/bussinessActivites";

import { CreateBussinesProfile } from "gql/mutations/createBussinessProfile";
import { UploadImage } from "gql/mutations/imageUpload";
import { useForm } from "react-hook-form";
import { useRouter } from "next/router";

import { IconButton, Snackbar, Grid } from "@material-ui/core";
import CloseIcon from "@material-ui/icons/Close";
import DeleteIcon from "@material-ui/icons/Delete";
import RequestLoader from "components/shared/requestLoader";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { RemoveList } from "store/boooking/action";
const emailPattern = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
const phonePattern = /^5\d/;
const Form = styled.form`
  > div {
    margin-top: 28px !important;
    text-align: ${(props) => (props.language === "ar" ? "right" : "left")};
    & > div {
      padding: 7px 15px;
      width: 100%;
      &,
      &:hover {
        border: solid 1px var(--color-7);
      }
    }
  }
  h4 {
    margin-top: 30px !important;
  }
  > input {
    margin-bottom: 28px !important;
    padding: 13px 25px;
    width: 100%;
    border-radius: var(--radius-1);
    border: solid 1px var(--color-7);
  }
  .required {
    &:after {
      content: "*";
      color: var(--color-9);
      position: absolute;
      top: 10px;
      right: ${(props) => (props.language === "ar" ? "75px" : null)};
      left: ${(props) => (props.language === "en" ? "136px" : null)};
    }
  }
  .value {
    &after {
      content: "";
    }
  }
`;

const CarNumbersWrapper = styled.div`
  position: relative;
  span {
    position: absolute;
    top: 12px;
    right: ${(props) => (props.language === "ar" ? "15px" : null)};
    left: ${(props) => (props.language === "en" ? "15px" : null)};
  }
  input {
    padding: 13px 25px;
    width: 100%;
    border-radius: var(--radius-1);
    border: solid 1px var(--color-7);
  }
  &.required {
    position: relative;
    &:after {
      content: "*";
      color: var(--color-9);
      position: absolute;
      top: 10px;
      right: ${(props) => (props.language === "ar" ? "114px" : null)};
      left: ${(props) => (props.language === "en" ? "135px" : null)};
    }
  }
  &.requiredemial {
    position: relative;
    &:after {
      content: "*";
      color: var(--color-9);
      position: absolute;
      top: 10px;
      right: ${(props) => (props.language === "ar" ? "177px" : null)};
      left: ${(props) => (props.language === "en" ? "135px" : null)};
    }
  }
  &.requiredCommercialNumber {
    position: relative;
    &:after {
      content: "*";
      color: var(--color-9);
      position: absolute;
      top: 10px;
      right: ${(props) => (props.language === "ar" ? "150px" : null)};
      left: ${(props) => (props.language === "en" ? "250px" : null)};
    }
  }
  &.hasValue {
    .MuiTextField-root {
      &:after {
        display: none;
      }
    }
  }
`;
const Phone = styled.div`
  div.phone {
    position: relative;
    padding: 0px !important ;
    border: none !important;
    display: grid;
    grid-template-columns: 1fr 102px;
    grid-column-gap: 7px;
  }
  span.validation {
    position: absolute;
    top: 12px;
    right: ${(props) => (props.language === "ar" ? "15px" : null)};
    left: ${(props) => (props.language === "en" ? "141px" : null)};
  }

  .MuiSelect-root {
    width: 100%;
    border-radius: var(--radius-1) !important;
    border: solid 1px var(--color-7) !important;
    padding: 13px 25px;
  }
  input {
    padding: 13px 25px;
    width: 100%;
    border-radius: var(--radius-1);
    border: solid 1px var(--color-7);
  }
  &.required {
    position: relative;
    &:after {
      content: "*";
      color: var(--color-9);
      position: absolute;
      top: 10px;
      right: ${(props) => (props.language === "ar" ? "135px" : null)};
      left: ${(props) => (props.language === "en" ? "135px" : null)};
    }
  }
  &.hasValue {
    .MuiTextField-root {
      &:after {
        display: none;
      }
    }
  }
`;

const Validation = styled.span`
  display: inline-block;
  color: var(--color-9);
  padding: 0 10px;
  transform: translateY(10px);
  width: 100%;
`;
const Steppeing = styled.div`
  min-width: 300px;
  .container {
    width: 100%;
    margin-top: 10px;
  }

  .progressbar {
    counter-reset: step;
  }
  .progressbar li {
    list-style: none;
    display: inline-block;
    width: 30.33%;
    position: relative;
    cursor: pointer;
    color: #aeaeae;
  }
  .progressbar li:before {
    content: "";
    counter-increment: step;
    width: 8px;
    height: 8px;
    border: 1px solid #ddd;
    border-radius: 100%;
    display: block;
    right: 0;
    background-color: #fff;
    position: relative;
    z-index: 9;
  }
  .progressbar li:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #eff1f5;
    top: 2px;
    left: ${(props) => (props.language == "en" ? "-100%" : "100%")};
  }
  .progressbar li:first-child:after {
    content: none;
  }
  .progressbar li.active {
    color: var(--color-2);
  }
  .progressbar li.active:before {
    border-color: var(--color-2);
  }

  .progressbar li.active:before {
    background-color: var(--color-2) !important;
  }
  .progressbar li span {
    transform: ${(props) =>
      props.language == "en" ? "translateX(-30px)" : "translateX(30px)"};
    display: inline-block;
    width: 100%;
    text-align: center;
  }
`;
const DIV = styled.div`
  .MuiGrid-root {
    border: none !important ;
  }
  button.order {
    width: 100%;
    padding: 10px 0px;
    border: none;
    box-shadow: none;
    border-radius: var(--radius-3);
    color: var(--color-4);
    font-weight: 500;
  }
  .title {
    color: var(--color-3);
  }
  .content {
    padding: 20px 0px;
  }
  .btn-link {
    color: #000;
  }
`;
const FileWrapper = styled.div`
  background: #f8f8f8;
  .title-extention {
    color: #bdbdbd;
    font-size: 11px;
  }
  .MuiGrid-root {
    border: 1 px solid #eee;
  }
  button {
    width: 34px;
    height: 34px;
    border-radius: var(--radius-3);
    font-weight: bold;

    color: var(--color-4);
    background: var(--color-1);
  }
`;
// const { InputContainer, ORDivider } = useStyle();

export default function Step1() {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const [phone, setPhone] = useState<string>();
  const [other, setother] = useState<string>();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    clearErrors,
    setValue,
    getValues,
  } = useForm({ mode: "onChange" });
  const inputFile = useRef(null);
  const watchFields = watch([
    "commercialRegistrationCertificate",
    "commercialRegistrationNo",
    "companyEmail",
    "companyPhone",
    "businessActivityId",
    "companyName",
  ]);

  const [snackBarClosed, setSnackBarClosed] = useState(true);
  const [snackBarMessage, setSnackBarMessage] = useState("");
  const dispatch = useDispatch();
  const steps = ["step1", "step2", "step3"];
  const BookingState = useSelector((state: RootStateOrAny) => state.booking);

  const [createprofile] = useMutation(CreateBussinesProfile);
  const [uploadfile, { loading }] = useMutation(UploadImage);
  const [uploadingImage, setUploadingImage] = useState(loading);
  const [filename, setFileName] = useState<string>();
  const { data: bussinessactivites } = useQuery(BussinessActivites);
  const { data: bussinessprofile, loading: profileload } =
    useQuery(BussinessProfile);

  const onSubmit = (data) => {
    data.companyPhone = "+966".concat(data.companyPhone);

    const mutate = async () => {
      createprofile({
        variables: {
          commercialRegistrationCertificate:
            data.commercialRegistrationCertificate,
          commercialRegistrationNo: data.commercialRegistrationNo,
          companyEmail: data.companyEmail,
          companyName: data.companyName,
          companyPhone: data.companyPhone,
          businessActivityId: data.businessActivityId,
          phone: data.phone,
          businessActivityString: data.businessActivityString,
          commercialRegistrationCertificateOriginalFilename: filename,
        },
      })
        .then((res) => {
          setSnackBarMessage(t("profile created success"));
          setSnackBarClosed(false);
          router.push("carwah-business#step2");
        })
        .catch((err) => {
          setSnackBarMessage(err.message);
          setSnackBarClosed(false);
        });
    };
    mutate();
  };

  function handleCloseSnackBar() {
    setSnackBarClosed(true);
  }
  useEffect(() => {
    if (
      bussinessprofile?.profile?.businessProfile
        ?.commercialRegistrationCertificate ||
      JSON.parse(sessionStorage.getItem("data"))?.[0]
    ) {
      setValue(
        "commercialRegistrationCertificate",
        bussinessprofile?.profile?.businessProfile
          ?.commercialRegistrationCertificate ||
          JSON.parse(sessionStorage.getItem("data"))?.[0]
      );
      setFileName(
        bussinessprofile?.profile?.businessProfile
          ?.commercialRegistrationCertificateOriginalFilename
      );
    }
    if (bussinessprofile?.profile?.businessProfile?.businessActivity.id) {
      setValue(
        "businessActivityId",
        bussinessprofile?.profile?.businessProfile?.businessActivity.id
      );
    }
    // if (!bussinessprofile?.profile.businessProfile) {
    setValue(
      "companyName",
      bussinessprofile?.profile?.businessProfile?.companyName ||
        JSON.parse(sessionStorage.getItem("data"))?.[5]
    );
    setValue(
      "businessActivityId",
      bussinessprofile?.profile?.businessProfile?.businessActivityId ||
        JSON.parse(sessionStorage.getItem("data"))?.[4]
    );
    setValue(
      "companyPhone",
      bussinessprofile?.profile?.businessProfile?.companyPhone?.replace(
        "+966",
        ""
      ) || JSON.parse(sessionStorage.getItem("data"))?.[3]
    );
    setValue(
      "companyEmail",
      bussinessprofile?.profile?.businessProfile?.companyEmail ||
        JSON.parse(sessionStorage.getItem("data"))?.[2]
    );
    setValue(
      "commercialRegistrationNo",
      bussinessprofile?.profile?.businessProfile?.commercialRegistrationNo ||
        JSON.parse(sessionStorage.getItem("data"))?.[1]
    );
    // }
  }, [bussinessprofile]);

  const action = (
    <>
      <IconButton
        size="small"
        aria-label="close"
        color="inherit"
        onClick={handleCloseSnackBar}
      >
        <CloseIcon fontSize="small" />
      </IconButton>
    </>
  );

  const Head = styled.div`
    @media (max-width: 768px) {
      flex-direction: column;
      ul,
      .container {
        padding: 0;
        margin: 0;
      }
    }
  `;
  return (
    <>
      <RequestLoader loading={uploadingImage} />
      <div>
        <Head className="d-flex justify-content-between">
          <div>
            <h2 className="font-27px bold mb-3">
              {t("Company Info") as string}
            </h2>
          </div>
          <Steppeing language={i18n.language}>
            <div className="container">
              <ul className="progressbar text-align-localized">
                <li
                  className="active"
                  onClick={() => router.push("carwah-business#step1")}
                >
                  <span>{t("step1") as string}</span>
                </li>
                <li
                  onClick={() => {
                    if (
                      BookingState?.list?.length &&
                      BookingState?.BookingList?.length
                    ) {
                      router.push("carwah-business#step2");
                    }
                  }}
                >
                  <span>{t("step2") as string}</span>
                </li>
                <li onClick={() => router.push("carwah-business#step3")}>
                  <span>{t("step3") as string}</span>
                </li>
              </ul>
            </div>
          </Steppeing>
        </Head>
        {!profileload && bussinessactivites && (
          <Form
            id="step2"
            language={i18n.language}
            onSubmit={handleSubmit(onSubmit)}
          >
            {getValues("companyName")}
            <CarNumbersWrapper
              className={
                bussinessprofile?.profile?.businessProfile?.companyName
                  ? `hasValue`
                  : `required`
              }
              language={i18n.language}
            >
              <input
                {...register("companyName", { required: true })}
                type="text"
                placeholder={t("CompanyName")}
                defaultValue={
                  bussinessprofile?.profile?.businessProfile?.companyName
                }
                onChange={(e) => {
                  setValue("companyName", e.target.value);
                  clearErrors("companyName");
                }}
                maxLength={150}
              />
            </CarNumbersWrapper>
            {errors.companyName && (
              <Validation className="text-align-localized">
                {t("This field is required")}
              </Validation>
            )}
            <Phone
              className={
                bussinessprofile?.profile?.businessProfile?.companyName ||
                watchFields[3] ||
                JSON.parse(sessionStorage.getItem("data"))?.[3]
                  ? `hasValue`
                  : `required`
              }
              language={i18n.language}
            >
              <div className="phone">
                <input
                  {...register("companyPhone", {
                    required: true,
                    pattern: phonePattern,
                  })}
                  type="text"
                  inputMode="numeric"
                  pattern="\d*"
                  placeholder={t("Phone Number")}
                  defaultValue={
                    bussinessprofile?.profile?.businessProfile?.companyPhone.substr(
                      4
                    ) || JSON.parse(sessionStorage.getItem("data"))?.[3]
                  }
                  onChange={(e) => {
                    e.target.value = e.target.value;
                    setValue("companyPhone", e.target.value);
                  }}
                  onInput={(e) => {
                    (e.target as HTMLInputElement).value = (
                      e.target as HTMLInputElement
                    ).value.replace(/[^0-9]/g, "");
                  }}
                  maxLength={9}
                />

                <SelectComponent
                  id="phone_number"
                  defaultValue="SA"
                  options={[
                    {
                      value: "SA",
                      text: `
                    <div className="d-flex align-items-center justify-content-between gap-5px" style="height:24px">
                      <img src="/assets/images/flags/saudi-flag.svg" alt="SA_flag"/>
                      <span>+966</span>
                    </div>
                  `,
                    },
                  ]}
                />
              </div>
            </Phone>
            {errors.companyPhone?.type == "pattern" && (
              <Validation className="text-align-localized">
                {t("Invalid phone number")}
              </Validation>
            )}
            <CarNumbersWrapper language={i18n.language}>
              <input
                {...register("phone")}
                type="text"
                inputMode="numeric"
                pattern="\d*"
                defaultValue={
                  bussinessprofile?.profile?.businessProfile?.phone ||
                  sessionStorage.getItem("phone")
                }
                placeholder={t("phone")}
                onChange={(e) => {
                  setValue("phone", e.target.value);
                  setPhone(e.target.value);
                }}
                onInput={(e) => {
                  (e.target as HTMLInputElement).value = (
                    e.target as HTMLInputElement
                  ).value.replace(/[^0-9 +]/g, "");
                }}
                // maxLength={4}
                maxLength={20}
              />
            </CarNumbersWrapper>
            <CarNumbersWrapper
              className={
                bussinessprofile?.profile?.businessProfile?.companyEmail ||
                watchFields[2] ||
                JSON.parse(sessionStorage.getItem("data"))?.[2]
                  ? `hasValue`
                  : `requiredemial`
              }
              language={i18n.language}
            >
              <input
                {...register("companyEmail", {
                  required: true,
                  pattern: emailPattern,
                })}
                type="text"
                placeholder={t("email")}
                defaultValue={
                  bussinessprofile?.profile?.businessProfile?.companyEmail ||
                  JSON.parse(sessionStorage.getItem("data"))?.[2]
                }
                onChange={(e) => {
                  setValue("companyEmail", e.target.value);
                }}
                // maxLength={4}
              />
            </CarNumbersWrapper>
            {errors.companyEmail && (
              <Validation className="text-align-localized">
                {errors.companyEmail?.type == "required"
                  ? t("This field is required")
                  : t("email pattern")}
              </Validation>
            )}
            <CarNumbersWrapper
              className={
                bussinessprofile?.profile?.businessProfile
                  ?.commercialRegistrationNo ||
                watchFields[1] ||
                JSON.parse(sessionStorage.getItem("data"))?.[1]
                  ? `hasValue`
                  : `requiredCommercialNumber`
              }
              language={i18n.language}
            >
              <input
                {...register("commercialRegistrationNo", { required: true })}
                type="text"
                inputMode="numeric"
                pattern="\d*"
                placeholder={t("Commercial registration number")}
                defaultValue={
                  bussinessprofile?.profile?.businessProfile
                    ?.commercialRegistrationNo ||
                  JSON.parse(sessionStorage.getItem("data"))?.[1]
                }
                onChange={(e) => {
                  e.target.value = e.target.value.replace(/[^\d]/, "");
                  setValue("commercialRegistrationNo", e.target.value);
                  clearErrors("commercialRegistrationNo");
                }}
                onInput={(e) => {
                  (e.target as HTMLInputElement).value = (
                    e.target as HTMLInputElement
                  ).value.replace(/[^0-9]/g, "");
                }}
                maxLength={20}
              />
            </CarNumbersWrapper>
            {errors.commercialRegistrationNo && (
              <Validation className="text-align-localized">
                {t("This field is required")}
              </Validation>
            )}

            <Select
              {...register("businessActivityId", { required: true })}
              placeholder={t("Business activity")}
              isSearchable
              isClearable
              className={
                bussinessactivites?.businessActivities?.length ||
                watchFields[4] ||
                JSON.parse(sessionStorage.getItem("data"))?.[4]
                  ? "value"
                  : "required"
              }
              options={
                bussinessactivites?.businessActivities?.map((item, index) => {
                  return {
                    label: i18n.language == "ar" ? item?.nameAr : item?.nameEn,
                    value: +item.id,
                  };
                }) || []
              }
              defaultValue={
                bussinessprofile?.profile?.businessProfile?.businessActivity.id
                  ? [
                      bussinessactivites?.businessActivities?.find(
                        (activity) =>
                          +activity.id ==
                          bussinessprofile?.profile?.businessProfile
                            ?.businessActivity.id
                      ),
                    ].map((item) => {
                      return {
                        label:
                          i18n.language == "ar" ? item?.nameAr : item?.nameEn,
                        value: +item?.id,
                      };
                    })
                  : bussinessactivites?.businessActivities?.filter(
                      (activity) =>
                        +activity.id ==
                        +JSON.parse(sessionStorage.getItem("data"))?.[4]
                    ).length > 0
                  ? [
                      bussinessactivites?.businessActivities?.find(
                        (activity) =>
                          +activity.id ==
                          +JSON.parse(sessionStorage.getItem("data"))?.[4]
                      ),
                    ].map((item) => {
                      return {
                        label:
                          i18n.language == "ar" ? item?.nameAr : item?.nameEn,
                        value: +item?.id,
                      };
                    })
                  : undefined
              }
              onChange={(e) => {
                setValue("businessActivityId", e?.value);
                clearErrors("businessActivityId");
              }}
            />

            {errors.businessActivityId && (
              <Validation className="text-align-localized">
                {t("This field is required")}
              </Validation>
            )}
            {watchFields[4] == 9 && (
              <CarNumbersWrapper language={i18n.language}>
                <input
                  {...register("businessActivityString")}
                  type="text"
                  defaultValue={
                    bussinessprofile?.profile?.businessProfile
                      ?.businessActivityString ||
                    sessionStorage.getItem("other")
                  }
                  placeholder={t("other")}
                  onChange={(e) => {
                    setValue("businessActivityString", e.target.value);
                    setother(e.target.value);
                  }}
                  // maxLength={4}
                  maxLength={100}
                />
              </CarNumbersWrapper>
            )}
            <FileWrapper>
              <Grid
                container
                direction="row"
                alignItems="center"
                style={{ borderRadius: "6px" }}
                xs={12}
                md={12}
              >
                <Grid item md={2} xs={2} sm={2}>
                  <img src="/assets/images/business/file.svg" alt="file" />
                </Grid>
                <Grid item md={8} xs={8} sm={8}>
                  <p>{t("Commercial registration certificate ") as string}</p>
                  <p className="title-extention">
                    {filename
                      ? t("File has been attached") + " " + filename
                      : t("Attach files have the following extensions") +
                        ": PDF / JPG" +
                        "/ PNG"}
                  </p>
                </Grid>
                <Grid item>
                  <div
                    onClick={() => {
                      if (watchFields[0] != undefined && watchFields[0] != "") {
                        setValue(
                          "commercialRegistrationCertificate",
                          undefined
                        );
                        setFileName(null);
                      } else {
                        inputFile.current.click();
                      }
                    }}
                  >
                    {watchFields[0] != undefined && watchFields[0] != "" ? (
                      <DeleteIcon
                        fontSize="medium"
                        className="cursor-pointer"
                      />
                    ) : (
                      <AddIcon fontSize="medium" className="cursor-pointer" />
                    )}
                  </div>
                  <input
                    type="file"
                    ref={inputFile}
                    style={{ display: "none" }}
                    onChange={(e) => {
                      const file = e.target.files[0];
                      const reader = new FileReader();
                      e.preventDefault();
                      let files: FileList;
                      if (e.target) {
                        files = e.target.files;
                      }

                      if (file?.size / 1024 > 5120) {
                        setSnackBarMessage(t("The max allowed size is 5 Mb"));
                        setSnackBarClosed(false);

                        return;
                      }
                      if (
                        ![
                          "image/jpeg",
                          "application/pdf",
                          "jpeg",
                          "png",
                          "image/png",
                          "gif",
                          "image/gif",
                        ].includes(files[0]?.type)
                      ) {
                        setSnackBarMessage(
                          t(
                            "file extensions PDF / JPG / JPEG / PNG only allowed"
                          )
                        );
                        setSnackBarClosed(false);

                        return;
                      }
                      reader.onload = () => {
                        if (reader.result) {
                          setUploadingImage(true);
                          uploadfile({
                            variables: {
                              image: reader.result,
                              topic: "commercialRegistrationCertificate",
                            },
                          }).then((res) => {
                            setValue(
                              "commercialRegistrationCertificate",
                              res.data.imageUpload.imageUpload.imageUrl
                            );
                            setFileName(files[0].name);
                            setSnackBarMessage(t("file uploaded successfully"));
                            setSnackBarClosed(false);
                            setUploadingImage(false);
                          });
                        }
                      };
                      reader.readAsDataURL(files[0]);
                    }}
                  />
                </Grid>
              </Grid>
            </FileWrapper>
            <DIV>
              <Grid
                container
                direction="row"
                justifyContent="center"
                alignItems="center"
                xs={12}
                md={12}
                className="form-wrapper"
              >
                <Grid item xs={8} md={8}>
                  <button
                    className="order"
                    type="submit"
                    style={{
                      backgroundColor: errors?.keys?.length
                        ? "#aec3ca"
                        : "var(--color-3)",
                    }}
                    disabled={errors?.keys?.length || !watchFields[0]}
                  >
                    {t("next") as string}
                  </button>
                </Grid>
                <Grid item xs={8} md={8}>
                  <button
                    className="btn btn-link w-100"
                    type="button"
                    onClick={() => {
                      sessionStorage.setItem(
                        "data",
                        JSON.stringify(watchFields)
                      );
                      sessionStorage.setItem("phone", phone ? phone : "");
                      sessionStorage.setItem("other", other ? other : "");
                      dispatch(RemoveList());
                      router.push("carwah-business#info");
                    }}
                  >
                    {t("close") as string}
                  </button>
                </Grid>
              </Grid>
            </DIV>
          </Form>
        )}
        <Snackbar
          open={!snackBarClosed}
          autoHideDuration={6000}
          onClose={handleCloseSnackBar}
          message={snackBarMessage}
          action={action}
        />
      </div>
    </>
  );
}
