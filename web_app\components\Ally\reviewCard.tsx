/* eslint-disable @next/next/no-img-element */
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import styled from "styled-components";
import Rating from "@material-ui/lab/Rating";
import { useState } from "react";

const Div = styled.div`
  text-align: center;
  background-color: var(--color-4);
  border-radius: var(--radius-2);
  padding: 50px 35px;
  -webkit-box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
  -moz-box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
  box-shadow: 9px 10px 15px -5px rgb(0 0 0 / 10%);
  direction: ${(props) => (props.language === "ar" ? "rtl" : null)};
  transform: ${(props) => (props.language === "ar" ? "rotateZ(180deg)" : null)};
  border: solid 1px #f6f6f6;
  display: flex;
  align-items: center;
  gap: 45px;
  @media (max-width: 960px) {
    flex-direction: column;
  }
  h4 {
    text-align: center !important;
  }
  img {
    border-radius: 50%;
  }
  > div {
    text-align: start;
    h6 {
      margin-bottom: 15px !important;
    }
    p {
      margin-top: 15px !important;
    }
  }
`;

export default function ReviewCard({ data }) {
  const { t } = useTranslation();
  const language = useSelector((state: RootStateOrAny) => state.language);
  const [value, setValue] = useState(4);

  return (
    <Div language={language}>
      <img src={data.img} alt="Carwah Car Rental - كروة لأيجار السيارات" />
      <div>
        <h6>{data.name}</h6>
        <Rating
          value={value}
          onChange={(event, newValue) => {
            setValue(newValue);
          }}
        />
        <p>{data.description}</p>
      </div>
    </Div>
  );
}
