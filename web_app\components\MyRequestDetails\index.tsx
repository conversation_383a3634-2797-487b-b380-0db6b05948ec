/* eslint-disable react-hooks/exhaustive-deps */
import { Head } from "./head";

import { useQuery } from "@apollo/client";
import { CircularProgress, Container } from "@material-ui/core";
import Layout from "components/shared/layout";
import { Bussiness_Rental_Details } from "gql/queries/rentalDetails";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Card from "./card";

const Div = styled.div`
  padding: 30px 0;
  background: #f5f5f5;
`;

const CardsContainer = styled.div`
  padding: 50px 0 10px 0;
`;

export default function RequestDetails({ id }) {
  const { t } = useTranslation();
  const {
    data: rentalRes,
    loading,
    refetch,
  } = useQuery(Bussiness_Rental_Details, {
    variables: { id: id, skip: !id },
  });

  useEffect(() => {
    let clean = false;
    if (!clean) refetch();
    return (clean = true);
  }, []);

  return (
    <Layout>
      <Div>
        <Head t={t} rentalRes={rentalRes} />
        <CardsContainer>
          <Container>
            <div className="d-flex flex-column gap-30px">
              {rentalRes?.businessRentalDetails && !loading ? (
                <Card {...rentalRes?.businessRentalDetails} refetch={refetch} />
              ) : (
                <p className="text-center mb-2">
                  {loading ? <CircularProgress /> : t("No.Recordes")}
                </p>
              )}
            </div>
          </Container>
        </CardsContainer>
      </Div>
    </Layout>
  );
}
