/* eslint-disable react-hooks/exhaustive-deps */
import { Provider } from "react-redux";
import "../styles/_styles.scss";
import "../localization";
import { ThemeProvider } from "@material-ui/styles";
import { createTheme } from "@material-ui/core/styles";
import Head from "next/head";
import { SnackbarProvider } from "notistack";
import "sweetalert2/src/sweetalert2.scss";
import BaseComponent from "components/BaseComponent";
import { PersistGate } from "redux-persist/integration/react";
import { useStore } from "store";
import Script from "next/script";
import SmartBanner from "react-smartbanner";
import "react-smartbanner/dist/main.css";

const theme = createTheme({
  palette: {
    primary: { main: "#7AB3C5" },
    secondary: { main: "#FA9C3F" },
  },
  overrides: {
    MuiSwitch: {
      switchBase: {
        color: "#ccc", // this is working
        "&$checked": {
          // this is not working
          color: "#f2ff00",
        },
      },
      track: {
        // this is working
        opacity: 0.2,
        backgroundColor: "#ccc",
        "$checked$checked + &": {
          opacity: 0.7,
          backgroundColor: "var(--color-2)",
        },
      },
    },
    MuiTypography: {
      body1: {
        fontSize: "1.1rem",
      },
    },
  },
});

function App({ Component, pageProps }) {
  const { store, persistor } = useStore();

  return (
    <>
      <Head>
        <title>Carwah Website - Car Rental | موقع كروة - ايجار السيارات</title>
      </Head>
      <Provider store={store}>
        <Script
          src={process.env.NEXT_PUBLIC_TAMARA_CDN}
          defer
          type="text/javascript"
        />
        <ThemeProvider theme={theme}>
          <SnackbarProvider
            anchorOrigin={{ horizontal: "center", vertical: "bottom" }}
            maxSnack={3}
          >
            <BaseComponent>
              <PersistGate loading={null} persistor={persistor}>
                <SmartBanner title={"Carwah"} appStoreLanguage="ar" />
                <Component {...pageProps} />
              </PersistGate>
            </BaseComponent>
          </SnackbarProvider>
        </ThemeProvider>
      </Provider>
    </>
  );
}

export default App;
