/* eslint-disable @next/next/no-img-element */
import React from "react";
import GoogleMapReact from "google-map-react";
import ToAppPopup from "components/shared/toAppPopup";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  position: relative;
  > div {
    display: none;
    background-color: white;
    width: fit-content;
    padding: 5px 10px;
    position: absolute;
    top: -25px;
    left: 0;
  }

  img {
    cursor: pointer;
  }
`;

function Map({ isShowCarousel, setIsShowCarousel }) {
  const { t } = useTranslation();
  const [isOpenToAppPopup, setIsOpenToAppPopup] = useState();

  const mockLocationsList = [
    { lat: 21.403357, lng: 39.8170105, price: 2200 },
    { lat: 21.413367, lng: 39.8270105, price: 2200 },
    { lat: 21.433397, lng: 39.8370105, price: 2200 },
    { lat: 21.443357, lng: 39.8470205, price: 2200 },
    { lat: 21.453391, lng: 39.8570105, price: 2200 },
    { lat: 21.463359, lng: 39.8670105, price: 2200 },
  ];
  const defaultProps = {
    center: {
      lat: 21.403357,
      lng: 39.8170105,
    },
    zoom: 11,
  };
  const BranchLocationComponent = ({
    price,
    isShowCarousel,
    setIsShowCarousel,
  }) => (
    <Div
      className="location-wrap"
      onClick={() => setIsShowCarousel(!isShowCarousel)}
    >
      <img src={`/assets/images/circle.svg`} alt="circle" />
      <div>
        <span>{price}</span>
        <span>{t("SR")}</span>
      </div>
    </Div>
  );

  return (
    <>
      <ToAppPopup
        isOpenToAppPopup={isOpenToAppPopup}
        setIsOpenToAppPopup={setIsOpenToAppPopup}
      />
      <div
        style={{
          height: "65vh",
          width: "100%",
          minHeight: "600px",
          maxHeight: "700px",
        }}
        onClick={() => setIsOpenToAppPopup(true)}
      >
        <GoogleMapReact
          bootstrapURLKeys={{
            key: process.env.NEXT_PUBLIC_MAP_API,
          }}
          defaultCenter={defaultProps.center}
          defaultZoom={defaultProps.zoom}
          options={{
            gestureHandling: "greedy",
          }}
        >
          {mockLocationsList.map((mockLocation, index) => {
            return (
              <BranchLocationComponent
                key={index}
                lat={mockLocation.lat}
                lng={mockLocation.lng}
                price={mockLocation.price}
                isShowCarousel={isShowCarousel}
                setIsShowCarousel={setIsShowCarousel}
                onClick={setIsShowCarousel}
              />
            );
          })}
        </GoogleMapReact>
      </div>
    </>
  );
}

export default Map;
