import React, { useEffect, useRef } from "react";
import { FormattedMessage, useIntl } from "react-intl";

const TamaraWidget = ({ 
  checkoutUrl, 
  onSuccess, 
  onFailure, 
  onCancel,
  totalAmount,
  locale = "ar" 
}) => {
  const { formatMessage } = useIntl();
  const widgetRef = useRef(null);
  const scriptRef = useRef(null);

  useEffect(() => {
    if (!checkoutUrl) return;

    // Load Tamara widget script
    const loadTamaraScript = () => {
      if (scriptRef.current) {
        document.head.removeChild(scriptRef.current);
      }

      const script = document.createElement("script");
      script.src = process.env.REACT_APP_TAMARA_CDN || "https://cdn-sandbox.tamara.co/widget-v2/tamara-widget.js";
      script.async = true;
      script.onload = initializeTamaraWidget;
      script.onerror = () => {
        console.error("Failed to load Tamara widget script");
        if (onFailure) {
          onFailure("Failed to load payment widget");
        }
      };
      
      scriptRef.current = script;
      document.head.appendChild(script);
    };

    const initializeTamaraWidget = () => {
      if (window.TamaraCheckout && widgetRef.current) {
        try {
          window.TamaraCheckout.init({
            lang: locale,
            country: "SA",
            publicKey: process.env.REACT_APP_TAMARA_PUBLIC_KEY || "0aa13b77-e140-4e1c-9139-663443e4ecd1",
            debug: process.env.NODE_ENV === "development",
            onPaymentSuccess: (data) => {
              console.log("Tamara payment success:", data);
              if (onSuccess) {
                onSuccess(data);
              }
            },
            onPaymentFailed: (error) => {
              console.error("Tamara payment failed:", error);
              if (onFailure) {
                onFailure(error.message || "Payment failed");
              }
            },
            onPaymentCancelled: () => {
              console.log("Tamara payment cancelled");
              if (onCancel) {
                onCancel();
              }
            }
          });

          // Create checkout
          window.TamaraCheckout.createCheckout(checkoutUrl);
        } catch (error) {
          console.error("Error initializing Tamara widget:", error);
          if (onFailure) {
            onFailure("Failed to initialize payment widget");
          }
        }
      }
    };

    loadTamaraScript();

    // Cleanup function
    return () => {
      if (scriptRef.current && document.head.contains(scriptRef.current)) {
        document.head.removeChild(scriptRef.current);
      }
      if (window.TamaraCheckout) {
        try {
          window.TamaraCheckout.destroy();
        } catch (error) {
          console.warn("Error destroying Tamara widget:", error);
        }
      }
    };
  }, [checkoutUrl, locale, onSuccess, onFailure, onCancel]);

  return (
    <div className="tamara-widget-container">
      <div className="tamara-info mb-3">
        <div className="d-flex align-items-center justify-content-center mb-2">
          <img 
            src="/assets/icons/tamara.svg" 
            alt="Tamara" 
            style={{ height: "32px", marginRight: "8px" }}
          />
          <span className="font-weight-bold">
            <FormattedMessage id="3 Payments interest free" />
          </span>
        </div>
        
        {totalAmount && (
          <div className="tamara-installment-preview text-center">
            <div 
              className="tamara-installment-plan-widget"
              data-lang={locale}
              data-price={totalAmount}
              data-currency="SAR"
              data-color-type="default"
              data-country-code="SA"
              data-number-of-installments="3"
            />
          </div>
        )}
      </div>

      <div 
        ref={widgetRef} 
        id="tamara-checkout-widget"
        className="tamara-checkout-container"
        style={{
          minHeight: "400px",
          border: "1px solid #e0e0e0",
          borderRadius: "8px",
          padding: "20px",
          backgroundColor: "#fafafa"
        }}
      >
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">
              <FormattedMessage id="Loading..." />
            </span>
          </div>
          <p className="mt-2">
            <FormattedMessage id="Loading payment form..." />
          </p>
        </div>
      </div>

      <div className="tamara-footer mt-3">
        <small className="text-muted text-center d-block">
          <FormattedMessage id="Powered by Tamara" />
        </small>
      </div>
    </div>
  );
};

export default TamaraWidget;
