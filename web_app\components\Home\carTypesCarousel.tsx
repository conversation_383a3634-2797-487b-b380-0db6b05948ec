/* eslint-disable @next/next/no-img-element */
/* eslint-disable react/jsx-key */
import React, { useEffect, useLayoutEffect, useRef } from "react";
import { setVehiclesType, setVehicleTypeAction } from "store/search/action";
import Swiper from "components/shared/carousel";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { useQuery } from "@apollo/client";
import { VehicleTypes } from "gql/queries/vehicleTypes";
import { useRouter } from "next/router";
import { carsListCurrentPageAction } from "store/cars/action";

const Div = styled.div`
  @media (max-width: 900px) {
    #car-type-carousel{
      .swiper-button-prev {
        left: ${(props) => (props.language === "ar" ? "-10px" : "")};
      }
      .swiper-button-next {
        right: ${(props) => (props.language === "ar" ? "-15px" : "")};
      }
    }
    .swiper-button-prev:before, .swiper-button-next:before{
      background: none !important;
    }
    .swiper-button-next:after{
      transform: translateX(0px) !important;
    }
    .swiper-button-prev:after{
      /* transform: translateX(-7px) !important; */
    }
    .card-service {
      min-width: 150px;
      min-height: 135px !important;
      margin: auto !important;
    }
    .swiper-slide img{
      width: 75%;
      /* object-fit: scale-down !important; */
      overflow: inherit !important;
      transform: ${(props) =>
        props.language === "en" ? "rotateY(180deg)" : ""};
    }
  }
  @media (min-width: 901px) {
    .veichle-types-mobile {
      display: none !important;
    }
    .swiper-slide img{
      width: 80%;
      object-fit: initial !important;
      transform: ${(props) =>
        props.language === "en" ? "rotateY(180deg)" : ""};
    }
  }
  .veichle-types-mobile {
    display: flex;
    flex-direction: row-reverse;
    gap: 10px;
    overflow-x: scroll;
    -webkit-overflow-x: scroll;
    -webkit-overflow-scrolling: touch
    overflow-y: hidden;
    > div {
      background: white;
      border-radius: var(--radius-2);
    }
  }
  padding-top: 30px;

  .swiper-wrapper {
    align-items: center;
    min-height: 170px;
    > div {
      min-height: 170px;
    }
  }
  h6 {
    text-align: center !important;
    font-size: 19px;
    line-height: 24px;
    margin-top: 20px !important;
    @media (max-width: 900px) {
      font-size: 18px;
      margin-top: 0 !important;
    }
  }
  h4 {
    line-height: 28px;
    @media (min-width: 901px) {
      font-size: 27px;
    }
    @media (max-width: 900px) {
      font-size: 24px;
    }
  }
  @media (min-width: 1025px) {
    .swiper-button-prev {
      left: -50px;
    }
    .swiper-button-next {
      right: -50px;
    }
  }

  .swiper-button-prev,
  .swiper-button-next {
    color: var(--color-2);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: var(--color-3);
    display: flex;
    justify-content: center;
    align-items: center;
    
    &:after {
      font-size: 18px;
      transform: none;
      margin: 0;
      font-weight: bold;
    }
    
    &:before {
      content: none;
    }
  }
  
  .card-service {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: white;
    padding: 15px;
    border-radius: 20px;
    margin: 0 10px;
    min-height: 170px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    img {
      height: 60px;
      margin-bottom: 15px;
      /* filter: invert(65%) sepia(54%) saturate(2651%) hue-rotate(346deg) brightness(101%) contrast(91%); */
    }
  }
`;
function VeichleTypes(data) {
  const dispatch = useDispatch();
  const router = useRouter();

  return data?.map((item: any) => {
    return (
      <div
        className="card-service cursor-pointer"
        style={{ width: "160px", height: "170px" }}
        onClick={() => {
          dispatch(carsListCurrentPageAction(1));
          dispatch(
            setVehiclesType({
              label: item.name,
              value: item.id,
              action: "vehicle",
            })
          );
          router.push("car-search#car-grid");
        }}
      >
        <img
          src={`/assets/images/${item.enName.toLowerCase()}.svg`}
          alt={item.name}
          className="vehicle-icon"
        />
        <h6 style={data == item.id ? { color: "var(--color-3)" } : null}>
          {item.name}
        </h6>
      </div>
    );
  });
}
function CarTypesCarousel() {
  //Hooks
  const { t, i18n } = useTranslation();

  //gQL
  const { data: vehicletypesRes } = useQuery(VehicleTypes, {
    fetchPolicy: "cache-first",
    variables: { orderBy: "display_order", sortBy: "asc" },
  });
  const { vehicleTypes } = vehicletypesRes || {};

  const SwiperRef = useRef(styled(Swiper)`
    .swiper-wrapper {
      .swiper-slide {
        > div {
          direction: ${i18n.language === "ar" ? "rtl" : "ltr"};
          width: fit-content;
          margin: 0 auto;
        }
      }
    }
  `);
  const SwiperTag = SwiperRef.current;
  useEffect(() => {
    // Moved next and prev arrows outside of swiper container for styling
    const next = document.querySelector(
      "#car-type-carousel .swiper-button-next"
    );
    const prev = document.querySelector(
      "#car-type-carousel .swiper-button-prev"
    );
    const wrap = document.querySelector("#car-type-carousel .swiper-wrap");
    wrap.appendChild(next);
    wrap.appendChild(prev);
  }, []);

  return (
    <Div language={i18n.language}>
      <div id="car-type-carousel">
        <SwiperTag
          spaceBetween={20}
          slidesPerView={2}
          breakpoints={{
            320: {
              slidesPerView: 2,
              spaceBetween: 20,
            },
            900: {
              slidesPerView: 3.25,
              spaceBetween: 30,
            },
            1024: {
              slidesPerView: 4.5,
              spaceBetween: 0,
            },
            1200: {
              slidesPerView: 4.5,
              spaceBetween: 0,
            },
          }}
          navigation
          onSwiper={(swiper) => null}
          onSlideChange={() => null}
          slides={VeichleTypes(vehicleTypes)}
          loop={false}
        />
      </div>
    </Div>
  );
}

export default React.memo(CarTypesCarousel);
