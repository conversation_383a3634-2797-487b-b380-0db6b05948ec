import { useRouter } from "next/router";
import { RootStateOrAny, useSelector } from "react-redux";

function useRentalType() {
  const router = useRouter();
  const rentalData = useSelector((state: RootStateOrAny) => state.booking);
  const { isRentToOwn: isRentToown_details } = rentalData?.rentalDetails || {};
  const isRentToOwn =
    Boolean(router.pathname.includes("rent-to-own")) ||
    Boolean(router.query.isRentToOwn) ||
    isRentToown_details;
  return { isRentToOwn };
}

export default useRentalType;
