/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import React, { memo, useEffect, useState, useCallback } from "react";
import Header from "components/shared/header";
import Footer from "components/shared/footer";
import styled from "styled-components";
import SigninPopup from "./signinPopup";
import { GetUnseenConfirmedExtendedRentalsQuery } from "gql/queries/GetUnseenConfirmedExtensions";
import { useLazyQuery, useMutation } from "@apollo/client";
import { UnseenExtensionAction } from "store/authentication/action";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import UnseenExtensionPopUp from "./unSeenExtensionPopup";
import { SeenExtensionMutation } from "gql/mutations/SeenExtension";
import { useSnackbar } from "notistack";
import { useQuery } from "@apollo/client";
import { USER_ACTIVITY_SUMMERY_QUERY } from "gql/queries/userActivitySummery";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";

const Div = styled.div`
  background-color: #f8f8f8;
  min-height: calc(100vh - 160px);
  @media (min-width: 961px) {
    > div:first-child {
      /* padding-bottom: 50px; */
    }
  }
  padding-top: 45px;
  @media (max-width: 960px) {
    padding-top: 52px !important;
  }
`;
function Layout({
  children,
  showTransparentBackground,
  className,
}: {
  children: React.ReactNode;
  showTransparentBackground?: boolean;
  className?: string;
}) {
  const { unseenExtension, login_data, rentalId } = useSelector(
    (state: RootStateOrAny) => state.authentication
  );
  const { push, pathname } = useRouter();
  const { t, i18n } = useTranslation();
  const [isSigninPopupOpen, setIsSigninPopupOpen] = useState(false);
  const [isSignupPopupOpen, setIsSignupPopupOpen] = useState(false);
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();
  const { data: userActivitySummary } = useQuery(USER_ACTIVITY_SUMMERY_QUERY, {
    skip: !login_data,
    fetchPolicy: "cache-first",
    nextFetchPolicy: "cache-only",
  });
  const { id: paymentOrderRentalId, isRentToOwn } =
    userActivitySummary?.paymentOrderRentalObject || {};

  const [, { refetch }] = useLazyQuery(GetUnseenConfirmedExtendedRentalsQuery, {
    fetchPolicy: "cache-first",
    nextFetchPolicy: "cache-only",
  });

  const [unseenExtensions, setUnseenExtensions] = useState<any>();
  const [isOpen, setIsOpen] = useState(false);
  const [SeenExtension] = useMutation(SeenExtensionMutation, {
    errorPolicy: "all",
  });
  const dispatch = useDispatch();

  useEffect(() => {
    if (
      login_data &&
      login_data?.token &&
      !unseenExtension &&
      !unseenExtensions &&
      unseenExtensions !== null
    ) {
      refetch().then((res) => {
        if (res?.data) {
          setUnseenExtensions(res.data.getUnseenConfirmedExtendedRentals);
        }
      });
    }
  }, []);

  useEffect(() => {
    if (
      unseenExtensions?.getUnseenConfirmedExtendedRentals &&
      !unseenExtension
    ) {
      dispatch(
        UnseenExtensionAction({
          rentalId: unseenExtensions?.getUnseenConfirmedExtendedRentals?.id,
          carId: unseenExtensions?.getUnseenConfirmedExtendedRentals?.carId,
        })
      );
      SeenExtension({
        variables: {
          rentalId: unseenExtensions?.getUnseenConfirmedExtendedRentals?.id,
        },
      });
      setIsOpen(true);
    }
  }, [unseenExtensions]);

  const [showScrollToTop, setShowScrollToTop] = useState(false);

  // Handle scroll event to show/hide scroll to top button
  useEffect(() => {
    const handleScroll = () => {
      // Show button when user scrolls down 200px from the top
      if (window.scrollY > 200) {
        setShowScrollToTop(true);
      } else {
        setShowScrollToTop(false);
      }
    };

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll);

    // Clean up event listener on component unmount
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);
  useEffect(() => {
    if (paymentOrderRentalId && pathname === "/") {
      enqueueSnackbar(
        `${t("You have a new payment to confirm your booking #")}`,
        {
          variant: "info",
          preventDuplicate: true,
          autoHideDuration: 5000,
          action: (key) => {
            return (
              <div
                style={{
                  textDecoration: "underline",
                  padding: "0 !important",
                  cursor: "pointer",
                  marginLeft: i18n.language === "en" ? "-15px" : "0",
                  marginRight: i18n.language === "ar" ? "-15px" : "0",
                }}
                onClick={() => {
                  push(`/car-details?bookingId=${paymentOrderRentalId}`);
                  closeSnackbar(key);
                }}
              >
                {paymentOrderRentalId}
              </div>
            );
          },
        }
      );
    }
  }, [paymentOrderRentalId, pathname]);
  return (
    <>
      <Header showHeader={true} />
      <Div className={`${!showTransparentBackground} ${className}`}>
        {children}
      </Div>
      <Footer />
      <SigninPopup
        setIsSigninPopupOpen={setIsSigninPopupOpen}
        setIsSignupPopupOpen={setIsSignupPopupOpen}
      />
      <UnseenExtensionPopUp
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        _carId={undefined}
        _rentalId={undefined}
        cb={undefined}
      />

      <img
        id="up-arrow"
        src="/assets/icons/up.svg"
        alt="up"
        style={{
          width: "50px",
          position: "fixed",
          bottom: "15px",
          right: "15px",
          cursor: "pointer",
          opacity: "0.5",
          zIndex: 9999,
        }}
        onMouseOver={(e: any) => (e.target.style.opacity = "1")}
        onMouseOut={(e: any) => (e.target.style.opacity = "0.5")}
        onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
      />

      {showScrollToTop && (
        <img
          id="up-arrow"
          src="/assets/icons/up.svg"
          alt="up"
          style={{
            width: "50px",
            position: "fixed",
            bottom: "15px",
            right: "15px",
            cursor: "pointer",
            opacity: "0.5",
            zIndex: 9999,
          }}
          onMouseOver={(e: any) => (e.target.style.opacity = "1")}
          onMouseOut={(e: any) => (e.target.style.opacity = "0.5")}
          onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
        />
      )}
    </>
  );
}

export default memo(Layout);
