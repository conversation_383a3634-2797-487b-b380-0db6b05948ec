/* eslint-disable @next/next/no-img-element */
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { FullCalendar } from "components/MyAccount/styled";
import { DatePicker, MuiPickersUtilsProvider } from "@material-ui/pickers";
import DateFnsUtils from "@date-io/date-fns";
import arLocale from "date-fns/locale/ar";
import enLocale from "date-fns/locale/en-GB";
import styled from "styled-components";

const Wrapper = styled.div`
  border: solid 1px rgba(128, 128, 128, 0.5);
  padding: 14px;
  border-radius: 12px;
  font-size: 16px;
  width: 100%;
`;

function PassportExpiry({ control, watch, errors }) {
  const { t, i18n } = useTranslation();

  return (
    <div className="mt-4">
      <label className="color-19 text-start w-100 mb-2">
        {t("Passport Expiray Date") as string}
        <span className="color-9">*</span>
      </label>
      <Controller
        name={"passportExpireAt"}
        control={control}
        rules={{
          required: true,
        }}
        render={({ field, fieldState }) => {
          return (
            <Wrapper>
              <FullCalendar
                className="d-flex gap-20px"
                language={i18n.language}
              >
                <MuiPickersUtilsProvider
                  utils={DateFnsUtils}
                  locale={i18n.language === "en" ? enLocale : arLocale}
                >
                  <DatePicker
                    // key={userProfileRes?.profile?.passportExpireAt}
                    value={watch("passportExpireAt")}
                    onChange={(e) => {
                      // setSelectedDateTime(e);
                      field.onChange(e);
                    }}
                    okLabel={t("Ok") as string}
                    cancelLabel={t("Cancel") as string}
                    disablePast
                    views={["year", "month", "date"]}
                    openTo="year"
                    format="dd-MM-yyyy"
                  />
                </MuiPickersUtilsProvider>
                <img
                  src="/assets/images/business/calendar.svg"
                  alt="calendar-icon"
                  style={{ top: "5px" }}
                />
              </FullCalendar>
            </Wrapper>
          );
        }}
      />
      {errors?.passportExpireAt?.type === "required" && (
        <p className="color-9">{t("This field is required") as string}</p>
      )}
    </div>
  );
}

export default PassportExpiry;
