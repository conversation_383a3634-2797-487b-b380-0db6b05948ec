/* eslint-disable @next/next/no-img-element */
import React from "react";
import Link from "next/link";
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/dist/client/router";
import { RootStateOrAny, useSelector } from "react-redux";

const Nav = styled.nav`
  display: flex;
  justify-content: space-around;
  a {
    color: var(--color-1);
    text-decoration: none;
    transition: all ease 0.3s;
    &:hover {
      color: var(--color-3);
    }
  }
  .current {
    position: relative;
  }

  li {
    list-style: none;
    @media (max-width: 960px) {
      margin: 35px 0;
    }

    &:hover:not(.active-item) {
      a {
        color: var(--color-2) !important;
        font-weight: bold;
      }
    }
    &.active-item {
      a {
        color: white !important;
        padding: 0px 15px 5px 15px;
        border-radius: 50px;
        border: solid 1px white;
        background-color: var(--color-2);
      }
    }
  }
  .new-deals {
    position: relative;
    span {
      position: relative;
      img {
        object-fit: cover;
        width: 4px;
        position: absolute;
        top: 0;
        right: ${(props) => (props.language === "en" ? "-5px" : null)};
        left: ${(props) => (props.language === "ar" ? "-5px" : null)};
      }
    }
  }
  @media (max-width: 1200px) {
    font-size: 1.1rem;
  }
`;
export default function Menu({
  header,
  footer,
  dimensions,
}: {
  header?: boolean;
  footer?: boolean;
  dimensions?: {
    width: number;
    height: number;
  };
}) {
  const { t, i18n } = useTranslation();
  const user =
    useSelector(
      (state: RootStateOrAny) => state.authentication.login_data?.user
    ) || {};
  const router = useRouter();
  return (
    <>
      {dimensions?.width < 960 ? (
        <div className="text-center">
          <img
            src="/assets/images/logo.svg"
            alt="logo"
            style={{
              padding: "0 30px",
              marginBottom: "10px",
              width: "100%",
              // transform: i18n.language === "ar" ? "scale(-1, 1)" : "",
            }}
          />
        </div>
      ) : null}
      <Nav
        language={i18n.language}
        className="text-align-localized w-100 gap-5px"
      >
        <li className={router.pathname === "/" ? "active-item" : null}>
          <Link href={`/`} prefetch={false}>
            {t("Home") as string}
          </Link>
        </li>
        <li
          className={router.pathname === "/about-carwah" ? "active-item" : null}
        >
          <Link href={`/about-carwah`} prefetch={false}>
            {t("About Carwah") as string}
          </Link>
        </li>
        {header ? (
          <>
            <li
              className={router.pathname === "/support" ? "active-item" : null}
            >
              <Link href={`/support`} prefetch={false}>
                {t("Support") as string}
              </Link>
            </li>
          </>
        ) : null}
        <li
          className={
            router.pathname === "/carwah-business" ? "active-item" : null
          }
        >
          <Link href={`/carwah-business`} prefetch={false}>
            {t("Carwah Business") as string}
          </Link>
        </li>
        <li className={router.pathname === "/ally" ? "active-item" : null}>
          <Link href={`/ally`} prefetch={false}>
            {t("Become an Ally") as string}
          </Link>
        </li>

        {footer ? (
          <>
            <li>
              <Link href={`/support`} locale={i18n.language} prefetch={false}>
                {t("Contacts") as string}
              </Link>
            </li>
            <li>
              <Link href={`/FAQs`} prefetch={false}>
                {t("FAQ") as string}
              </Link>
            </li>
            <li>
              <Link href={`/terms-conditions`} prefetch={false}>
                {t("Terms & Conditions") as string}
              </Link>
            </li>
            <li>
              <Link href={`/privacy-policy`} prefetch={false}>
                {t("Privacy_Policy") as string}
              </Link>
            </li>
          </>
        ) : null}
      </Nav>
    </>
  );
}
