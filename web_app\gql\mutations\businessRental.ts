import { gql } from "@apollo/client";

const CreateBusinessRental_Mutation = gql`
  mutation CreateBusinessRental(
    $additionalNotes: String
    $carModelId: ID
    $carVersionId: ID
    $insuranceId: ID!
    $makeId: ID
    $numberOfCars: Int!
    $numberOfMonths: Int!
    $otherCarName: String
    $pickUpCityId: ID
    $otherCityName: String
    $pickUpDatetime: String!
    $userId: ID!
  ) {
    createBusinessRental(
      additionalNotes: $additionalNotes
      carModelId: $carModelId
      carVersionId: $carVersionId
      insuranceId: $insuranceId
      makeId: $makeId
      numberOfCars: $numberOfCars
      numberOfMonths: $numberOfMonths
      otherCarName: $otherCarName
      pickUpCityId: $pickUpCityId
      otherCityName: $otherCityName
      pickUpDatetime: $pickUpDatetime
      userId: $userId
    ) {
      businessRental {
        additionalNotes
        arBusinessActivity
        arMakeName
        arModelName
        arPickUpCityName
        businessActivityId
        businessActivityName
        carImage
        carModelId
        carVersionId
        commercialRegistrationCertificate
        commercialRegistrationNo
        companyEmail
        companyName
        companyPhone
        enBusinessActivity
        enMakeName
        enModelName
        enPickUpCityName
        id
        insuranceId
        makeId
        makeName
        modelName
        numberOfCars
        numberOfMonths
        otherCarName
        phone
        pickUpCityId
        pickUpCityName
        pickUpDatetime
        userId
        year
      }
    }
  }
`;

export { CreateBusinessRental_Mutation };
