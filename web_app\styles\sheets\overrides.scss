.swiper-container,
.swiper-wrap {
  z-index: 0 !important;
}
.MuiSelect-select {
  padding-left: 10px !important;
}

.Mui-checked {
  color: #fa9c3f !important;
}
.MuiSwitch-track {
  background-color: red;
}
.MuiFilledInput-underline {
  &:before,
  &:after {
    display: none !important;
  }
}
.btn {
  cursor: auto;
}
.select {
  > div {
    // padding-right: 24px !important;
  }
}
.MuiPagination-ul {
  direction: ltr !important;
}
html[lang="ar"] {
  .MuiPickerDTToolbar-toolbar > div > div:last-child {
    display: flex;
    flex-direction: row-reverse;
  }
  .MuiPickersCalendarHeader-switchHeader {
    flex-direction: row-reverse;
  }
}
.MuiSnackbarContent-action {
  margin: 0 !important;
}
.MuiSnackbarContent-root {
  justify-content: space-between;
}

html[lang="ar"] .row-reverse-localized .MuiOutlinedInput-root {
  flex-direction: row-reverse;
}

input[type="time"]::-webkit-calendar-picker-indicator {
  filter: invert(1) sepia(1) saturate(5) hue-rotate(175deg);
  cursor: pointer;
}
.swiper-slide {
  text-align: center;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select:focus,
  textarea:focus,
  input:focus {
    font-size: 16px !important;
  }
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select,
  textarea,
  input {
    font-size: 16px;
  }
}
.smartbanner-show.smartbanner-margin-top {
  margin-top: 0 !important;
}

.smartbanner-title,
.smartbanner-description {
  text-align: center;
}
.smartbanner-wrapper,
.smartbanner-wrapper a {
  width: 90%;
  display: inline-block !important;
  text-align: center;
  border-radius: 2px !important;
}

a:hover {
  color: unset !important;
}
.react-tel-input {
  direction: ltr;
  input {
    text-align: left !important;
  }
  .form-control {
    padding: 20px 55px 23px 55px !important;
  }
}
// html[lang=ar]{
//   .react-tel-input{
//     display: flex;
//     flex-direction: row-reverse;
//     input:not(.search-box){
//       left: 0;
//       position: relative;
//       padding: 0 55px !important;
//     }
//   }
// }
@media (max-width: 768px) {
  .country-list {
    width: 200px !important;
    direction: ltr;
  }
}
// html[lang=ar] .country-list{
//   right: 0;
//   z-index: 999999999;
//   li{
//     display: flex;
//     flex-direction: row-reverse;
//     gap: 5px;
//     > div{
//       position: relative !important;
//       top: 0 !important;
//       left: 0 !important;
//     }
//     &.search{
//       justify-content: center;
//       input{
//         text-align: right !important;
//       }
//     }
//   }
// }
// .swiper-button-next:after, .swiper-container-rtl .swiper-button-prev:after{
//   transform: translateX(26px);
//   -webkit-transform: translateX(26px);
//   -moz-transform: translateX(26px);
//   -ms-transform: translateX(26px);
//   -o-transform: translateX(26px);
// }
.swiper-button-prev:after,
.swiper-button-next:after {
  color: #616060 !important;
  font-weight: bold;
  color: white !important;

}
.swiper-button-prev,
.swiper-button-next {
  z-index: 9999;
  background-color: var(--color-2) !important;

}
body {
  overflow-y: auto !important;
}

.tamara-product-widget {
  border: none !important;
}
@media (max-width: 768px) {
  .swiper-button-prev,
  .swiper-button-next {
    top: 50% !important;
  }
  .section-fancy-title {
    margin-bottom: 20px;
  }
}
.section-fancy-title {
  h2 {
    font-size: 1.7rem !important;
  }
}

.swiper-pagination-bullets {
  .swiper-pagination-bullet-active {
    background-color: #2a292f !important;
    width: 8px;
    height: 8px;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
  }
}

html[lang="ar"] {
  .MuiPagination-ul {
    transform: scaleX(-1);
    -webkit-transform: scaleX(-1);
    -moz-transform: scaleX(-1);
    -ms-transform: scaleX(-1);
    -o-transform: scaleX(-1);
    li:not(:nth-child(1)):not(:nth-child(2)):not(:nth-last-child(1)):not(:nth-last-child(2)) {
      transform: scaleX(-1);
      -webkit-transform: scaleX(-1);
      -moz-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
      -o-transform: scaleX(-1);
    }
  }
}

@view-transition {
  navigation: auto;
}

.wpwl-control {
  height: 40px !important;
  padding: 0px 20px !important;
  border-radius: 15px !important;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  background-color: #ffffff !important;
  box-sizing: border-box !important;
  width: 100% !important;
  input {
    font-family: "system-ui" !important;
  }
}

.wpwl-brand.wpwl-brand-MADA.wpwl-brand-card,
.ui-icon.ui-icon-help,
.wpwl-group-brand {
  display: none !important;
}

.wpwl-target {
  width: 130% !important;
  transform: translate(-10%, 0%) !important;
  -webkit-transform: translate(-10%, 0%) !important;
  -moz-transform: translate(-10%, 0%) !important;
  -ms-transform: translate(-10%, 0%) !important;
  -o-transform: translate(-10%, 0%) !important;
}

#frameDiv h5 {
  padding: 0 !important;
}

.tamara-summary-widget__container svg {
  font-size: 12px;
}
