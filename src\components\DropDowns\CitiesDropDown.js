/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
import React from "react";
import { AllAreas } from "gql/queries/Areas.queries.gql";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";

export function CitiesDropDown({
  loading,
  setSelectedCity,
  selectedCity,
  valueAttribute,
  multiple,
  error,
  required,
  setList,
  inBooking,
  ...props
}) {
  const { data: AreasRes, loading: gettingAreas } = useQuery(AllAreas);
  const { locale, formatMessage } = useIntl();
  const options =
    AreasRes?.areas?.map((x) => ({
      value: x?.[valueAttribute],
      label: x?.[`${locale}Name`],
      id: x.id,
      centerLat:x.centerLat, 
      centerLng: x.centerLng
    })) || [];

  React.useEffect(() => {
    if (!selectedCity) {
      onClear();
    }
  }, [selectedCity]);

  React.useEffect(() => {
    if (AreasRes && setList) {
      setList(AreasRes?.areas?.length);
    }
  }, [AreasRes]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  if (multiple || inBooking) {
    options.unshift({ value: "all", label: formatMessage({ id: "widgets.all" }) });
  }
  return (
    options && (
      <Select
        className={`dropdown-select ${multiple ? "multiple" : ""}  ${required ? "required" : ""} ${
          error ? "selection-error" : ""
        }`}
        options={[...options]}
        ref={selectInputRef}
        isMulti={multiple || inBooking}
        defaultValue={options.find((optn) => `${optn.value}` === `${selectedCity}`)}
        isClearable
        value={
          multiple || inBooking
            ? options?.filter(
                (optn, index) =>
                  selectedCity?.includes(optn.id) ||
                  selectedCity?.includes(optn.value) ||
                  selectedCity?.includes(+optn.id),
              )
            : options.find((optn) => `${optn.value}` === `${selectedCity}`)
        }
        loadOptions={gettingAreas || loading}
        placeholder={formatMessage({ id: "components.city" })}
        onChange={(selection) => {
          if ((!selection && multiple) || (!selection && inBooking)) {
            setSelectedCity([]);
            return;
          }
          if (multiple || inBooking) {
            if (selection[selection?.length - 1]?.value == "all") {
              if (selection[selection?.length - 1]?.value == "all") {
                const city = selection?.filter((onselectoion) => onselectoion.value == "all");
                setSelectedCity(city);
                return;
              }
              const city = options?.filter((onselectoion) => onselectoion.value != "all");
              setSelectedCity(city);
              return;
            }
          } else {
            setSelectedCity(selection);
            return;
          }
          setSelectedCity(selection.filter((sel) => sel.value != "all"));
        }}
        noOptionsMessage={() => {
          if (gettingAreas) {
            return <CircularProgress />;
          }
          if (!options?.length) return "no data found";
        }}
        {...props}
      />
    )
  );
}

CitiesDropDown.propTypes = {
  valueAttribute: PropTypes.string.isRequired,
  loading: PropTypes.bool,
  error: PropTypes.bool,
  selectedCity: PropTypes.any,
  setSelectedCity: PropTypes.func,
  multiple: PropTypes.bool,
};
