import { useMutation } from "@apollo/client";
import { ButtonsWrapper } from "components/MyAccount/styled";
import Popup from "components/shared/popup";
import RequestLoader from "components/shared/requestLoader";
import { YakeenVerifyProfileMutation } from "gql/mutations/profile";
import useUserProfile from "hooks/useUserProfile";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { setYakeenVerificationSuccess } from "store/user/action";
import { TProfileStatus } from "types/profile";

function ConfirmVerification({
  watch,
  isConfirmationPopupOpen,
  setIsConfirmationPopupOpen,
}) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [yakeenVerifyProfile, { loading }] = useMutation(
    YakeenVerifyProfileMutation
  );
  const { refetch: refetchUserData } = useUserProfile();

  async function verifyUserHandler() {
    const status = watch("status") as TProfileStatus;
    const input = {
      status: watch("status"),
      nid: status !== "visitor" ? watch("nid") : undefined,
      nationalityId:
        status === "visitor" ? +watch("nationalityId").id : undefined,
      passportExpireAt:
        status === "visitor"
          ? moment(watch("passportExpireAt")).format("YYYY-MM-DD")
          : undefined,
      passportNumber:
        status === "visitor" ? +watch("nationalityId") : undefined,
      dob:
        (watch("status") as TProfileStatus) === "gulf_citizen"
          ? moment(watch("dob")).format("YYYY-MM-DD")
          : undefined,
      driverLicense:
        (watch("status") as TProfileStatus) === "gulf_citizen" ||
        (watch("status") as TProfileStatus) === "visitor"
          ? moment(watch("driverLicense")).format("YYYY-MM-DD")
          : undefined,
      driverLicenseExpireAt: moment(watch("driverLicenseExpireAt")).format(
        "YYYY-MM-DD"
      ),
    };
    try {
      const res = await yakeenVerifyProfile({ variables: { input } });
      if (res?.data?.yakeenVerifyProfile?.success) {
        dispatch(setYakeenVerificationSuccess("success"));
      } else {
        dispatch(setYakeenVerificationSuccess("error"));
      }
    } catch (err) {
      dispatch(setYakeenVerificationSuccess("error"));
    }
    setIsConfirmationPopupOpen(false);
    refetchUserData();
  }

  return (
    <Popup
      maxWidth="xs"
      isOpen={isConfirmationPopupOpen}
      setIsOpen={() => setIsConfirmationPopupOpen(false)}
      title={""}
    >
      <RequestLoader loading={loading} />
      <h4 className="text-center bold mb-2">
        {t("Info Confirmation") as string}
      </h4>
      <p className="mb-2 text-center">
        {t("Please check the info below before checking") as string}
      </p>
      <div className="d-flex flex-column gap-10px mb-2">
        <div className="d-flex flex-column gap-10px">
          <div>
            <label className="color-19 text-start w-100 ">
              {t("Status") as string}
            </label>
            <p className="bold">{t(watch("status")) as string}</p>
          </div>
        </div>
        {(watch("status") as TProfileStatus) !== "visitor" && (
          <div className="d-flex flex-column gap-10px">
            <div>
              <label className="color-19 text-start w-100 ">
                {watch("status") === "resident"
                  ? (t("Iqama ID") as string)
                  : watch("status") === "citizen"
                  ? (t("National ID") as string) ||
                    watch("status") === "visitor"
                  : watch("status") === "visitor"
                  ? ""
                  : (t("Gulf National ID") as string)}
              </label>
              <p className="bold">{watch("nid")}</p>
            </div>
          </div>
        )}
        {(watch("status") as TProfileStatus) === "visitor" && (
          <div className="d-flex flex-column gap-10px">
            <div>
              <label className="color-19 text-start w-100 ">
                {t("Passport No.") as string}
              </label>
              <p className="bold">{watch("passportNumber")}</p>
            </div>
          </div>
        )}
        {(watch("status") as TProfileStatus) === "visitor" && (
          <div className="d-flex flex-column gap-10px">
            <div>
              <label className="color-19 text-start w-100 ">
                {t("Passport Expiray Date") as string}
              </label>
              <p className="bold">{watch("passportExpireAt")}</p>
            </div>
          </div>
        )}
        {((watch("status") as TProfileStatus) === "gulf_citizen" ||
          (watch("status") as TProfileStatus) === "visitor") && (
          <div className="d-flex flex-column gap-10px">
            <div>
              <label className="color-19 text-start w-100 ">
                {t("Date of Birth") as string}
              </label>
              <p className="bold">
                {moment(watch("dob")).format("DD/MM/YYYY")}
              </p>
            </div>
          </div>
        )}
        {((watch("status") as TProfileStatus) === "gulf_citizen" ||
          (watch("status") as TProfileStatus) === "visitor") && (
          <div className="d-flex flex-column gap-10px">
            <div>
              <label className="color-19 text-start w-100 ">
                {t("Driver license number") as string}
              </label>
              <p className="bold">{watch("driverLicense")}</p>
            </div>
          </div>
        )}
        <div className="d-flex flex-column gap-10px">
          <div>
            <label className="color-19 text-start w-100 ">
              {t("Driver Liscence Expiry Date") as string}
            </label>
            <p className="bold">
              {moment(watch("driverLicenseExpireAt")).format("DD/MM/YYYY")}
            </p>
          </div>
        </div>
      </div>
      <ButtonsWrapper className="d-flex gap-20px justify-content-end p-3 w-100">
        <button
          onClick={(e) => {
            e.preventDefault();
            setIsConfirmationPopupOpen(false);
          }}
          type="button"
          style={{ width: "50%" }}
        >
          {t("Edit Info") as string}
        </button>
        <button
          type="submit"
          style={{ width: "50%" }}
          onClick={() => {
            verifyUserHandler();
          }}
        >
          {t("Confirm") as string}
        </button>
      </ButtonsWrapper>
    </Popup>
  );
}

export default ConfirmVerification;
