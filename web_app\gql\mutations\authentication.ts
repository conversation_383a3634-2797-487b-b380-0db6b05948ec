import { gql } from "@apollo/client";
const RequestPasscode_Mutation = gql`
  mutation requestPasscode($input: RequestPasscodeInput!) {
    requestPasscode(input: $input) {
      clientMutationId
      errors
      expiredAt
      status
    }
  }
`;

const Login_Mutation = gql`
  mutation LoginMutation($input: LoginInput!) {
    login(input: $input) {
      basicProfileCompleted
      clientMutationId
      errors
      licenseProfileCompleted
      status
      token
      user {
        companyName
        createdAt
        dob
        driverLicense
        driverLicenseExpireAt
        driverLicenseStatus
        email
        emailConfirmed
        firstName
        gender
        id
        isActive
        isCustomerProfileCompleted
        lastName
        lat
        licenseFrontImage
        licenseSelfieImage
        lng
        middleName
        mobile
        name
        nid
        passportNumber
        profileImage
        status
        title
      }
    }
  }
`;

export { RequestPasscode_Mutation, Login_Mutation };
