/* eslint-disable @next/next/no-img-element */
import { Grid } from "@material-ui/core";
import moment from "moment";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Link from "next/link";
import MyRequestsModal from "./myRequestsModal";
import { useState } from "react";
import { useMutation, useQuery, useLazyQuery } from "@apollo/client";

import { CancelReasons } from "gql/queries/CancelReasons";

import "moment/locale/ar";
const Div = styled(Grid)`
  background-color: var(--color-4);
  border-radius: var(--radius-2);
  padding: 25px;
  margin-bottom: 60px !important;

  h6:first-child {
    font-size: 15px;
    color: var(--color-12);
    font-weight: lighter;
    margin-bottom: 5px;
  }
  h6:last-child {
    font-size: 15px;
    font-weight: bold;
  }
  .car-box img {
    width: 100%;
  }
  .booking-title {
    color: #000 !important;
  }
  .car-box {
    border-left: ${(props) =>
      props.language === "ar" ? "1px solid #eee" : ""};
    border-right: ${(props) =>
      props.language === "ar" ? "" : "1px solid #eee"};
    text-transform: uppercase;
    padding: 5px 10px;
    > div:first-child {
      margin-bottom: 5px;
      padding-bottom: 5px;
    }

    .booking-status {
      border-radius: var(--radius-1);
      padding: 3px 7px;
      &.confirmed {
        color: var(--color-3);
        background-color: var(--color-13);
      }
      span {
        font-size: 12px;
      }
    }
  }
  .booking-details {
    text-transform: capitalize;
    .booking-issue {
      position: relative;
      padding: 12px 20px;
      border-radius: var(--radius-2);
      p.error {
        color: var(--color-9);
        display: inline;
        &:after {
          content: "";
          background-color: var(--color-9);
          opacity: 0.07;
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          border-radius: var(--radius-2);
        }
      }
    }
    .grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-row-gap: 20px;
      grid-column-gap: 20px;
      @media (max-width: 900px) {
        margin-top: 20px;
      }
    }
  }
  > div:last-child {
    button.cancel {
      color: red;
    }
    button.order {
      color: #fff;
      background-color: var(--color-3);
      border: none;
      font-size: 18px;
      font-weight: 600;
      padding: 10px 25px;
      border-radius: var(--radius-2);
      &:focus {
        outline: none;
      }
      &.branch-details {
        background-color: var(--color-13);
      }
      &.dots {
        background: transparent;
        border: solid 1px var(--color-10);
      }
    }
    .price-box {
      padding: 15px 20px;
      border-radius: var(--radius-2);
      .total-price {
        color: var(--color-3);
        font-size: 24px;
      }
      .daily-price {
        color: var(--color-14);
        font-size: 18px;
      }
    }
  }
`;
const Btn = styled.div`
  background: var(--color-3);
  color: var(--color-4);
  border-radius: var(--radius-3);
  padding: 7px;
  margin-top: 10px;
  &:hover {
    a {
      text-decoration: none;
      color: var(--color-4);
      pointer-events: ${(props) => (props.offers == 0 ? "none" : "")};
    }
  }
`;

export default function CarCard(props) {
  const { t, i18n } = useTranslation();
  const [open, setOpen] = useState(false);
  const { data: cancelreasons } = useQuery(CancelReasons);
  return (
    <Div
      container
      className="car-card"
      justifyContent="space-between"
      language={i18n.language}
    >
      <Grid
        container
        item
        md={10}
        spacing={4}
        style={{ border: "1px solid #eee", borderRadius: "10px" }}
      >
        <Grid item md={3}>
          <div className="car-box">
            <Grid container justifyContent="space-between">
              <Grid item className="w-100">
                {props.otherCarName && !props?.[`${i18n.language}MakeName`] ? (
                  <h5 className="bold">{props.otherCarName}</h5>
                ) : null}
                <h5 className="bold">
                  {props?.[`${i18n.language}MakeName`] &&
                    props?.[`${i18n.language}MakeName`] +
                      " " +
                      props?.[`${i18n.language}ModelName`]}
                </h5>
                <h6 className="mb-2">{props.year}</h6>
                <img
                  src={props?.carImage || "/assets/images/Vector.png"}
                  alt="Car Image"
                />
              </Grid>
            </Grid>
          </div>
        </Grid>
        <Grid
          item
          container
          justifyContent="space-between"
          className="w-auto booking-details"
          md={7}
        >
          <Grid
            className="w-auto"
            item
            direction="column"
            justifyContent="space-between"
          >
            <div className="grid">
              <div>
                <h6>{t("PickupDate")}:</h6>
                <h6>
                  {i18n.language == "ar"
                    ? moment(new Date(props.pickUpDatetime))
                        .locale(i18n.language)
                        .format("YYYY/MM/DD")
                    : moment(new Date(props.pickUpDatetime))
                        .locale(i18n.language)
                        .format("DD/MM/YYYY")}
                </h6>
              </div>
              <div>
                <h6>{t("Duration")}</h6>
                <h6>
                  <span>{props?.duration}</span>
                  <span>
                    {props.numberOfMonths == 1
                      ? t("month")
                      : props.numberOfMonths == 2
                      ? t("2.month")
                      : props.numberOfMonths >= 3 && props.numberOfMonths <= 10
                      ? ` ${props.numberOfMonths} ` + t("months")
                      : props.numberOfMonths > 10
                      ? ` ${props.numberOfMonths} ` + t("month")
                      : null}
                  </span>
                </h6>
              </div>
              {/* <div>
                <h6>{t("Duration in months")}</h6>
                <h6>{props?.numberOfMonths}</h6>
              </div> */}

              <div>
                <h6>{t("Requested car numbers")}:</h6>
                <h6>{props.numberOfCars}</h6>
              </div>
              <div>
                <h6>{t("City")}:</h6>
                <h6>{props?.[`${i18n.language}PickUpCityName`]}</h6>
              </div>
              <div>
                <h6>{t("Insurance type")}:</h6>
                <h6>{t(props.insuranceName)}</h6>
              </div>
            </div>
          </Grid>
        </Grid>
        <Grid
          item
          container
          justifyContent="space-between"
          className="w-auto booking-details"
          md={2}
        >
          <Grid
            className="w-auto"
            item
            md={12}
            style={{
              borderRight: i18n.language == "ar" ? "1px solid #eee" : "",
              borderLeft: i18n.language == "en" ? "1px solid #eee" : "",
            }}
          >
            <div>
              <h6 className="text-center booking-title">{t("Booking No")}:</h6>
              <h6 className="text-center  booking-title">{props.bookingNo}</h6>
            </div>
            <div>
              <h6 className="text-center  booking-title">
                {t("booking.Status")}:
              </h6>
              <h4 className="text-center text-white ">
                <span className="badge bg-secondary">
                  {props.statusLocalized}
                </span>
              </h4>
            </div>
          </Grid>
        </Grid>
      </Grid>
      {props.status != "Car_received" && (
        <Grid
          className="w-auto"
          container
          md={2}
          direction="column"
          justifyContent="space-between"
          alignItems="center"
          style={{
            background:
              props.tabstatus == "current" ? "rgba(241, 241, 241, 0.3)" : "",
            border: "1px solid #fff",
            borderRadius: "10px",
          }}
        >
          <Grid
            className="w-auto"
            item
            container
            alignItems="flex-start"
            justifyContent="space-between"
          >
            <div className="text-center">
              {props.status == "pending" &&
              props.businessRentalOffers.length ? (
                <>
                  <h6 className="text-center">
                    {props.businessRentalOffers.length}
                  </h6>
                  <h6 className="text-center">{t("booking.Offers")}</h6>
                  <Btn offers={props.businessRentalOffers.length}>
                    <Link className="order mt-2" href={`/offers/${props.id}`}  prefetch={false}>
                      {t("View Offers")}
                    </Link>
                  </Btn>
                </>
              ) : props.status != "pending" && props.tabstatus != "history" ? (
                <Btn offers={props.businessRentalOffers.length}>
                  <Link
                    className="order mt-2"
                    href={`/request-details/${props.id}`}
                    passHref
                    prefetch={false}
                  >
                    {t("View Details")}
                  </Link>
                </Btn>
              ) : null}
              {props.status != "pending" &&
              props.status != "confirmed" ? null : (
                <button
                  className="btn btn-link mt-2 cancel "
                  onClick={() => setOpen(true)}
                >
                  {t("Cancel Request")}
                </button>
              )}
              <MyRequestsModal
                refetch={props.refetchData}
                open={open}
                setOpened={setOpen}
                requestId={props.id}
                cancelreasosnes={cancelreasons?.businessCancelledReasons}
                isWidth={true}
              />
            </div>
          </Grid>
        </Grid>
      )}
    </Div>
  );
}
