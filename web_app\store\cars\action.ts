export const CARS_LIST = "CARS_LIST";
export const CARS_LIST_CURRENT_PAGE = "CARS_LIST_CURRENT_PAGE";
export const CAR_SELECTED_BRANCH = "CAR_SELECTED_BRANCH";
export const CAR_DATA = "CAR_DATA";
export const ABOUT_RENT_PRICE = "ABOUT_RENT_PRICE";
export const CAR_SELECTED_INURANCE = "CAR_SELECTED_INURANCE";
export const PAYMENT_TYPE = "PAYMENT_TYPE";
export const PAYMENT_METHOD = "PAYMENT_METHOD";
export const AVAILABLE_PAYMENT_METHODS = "AVAILABLE_PAYMENT_METHODS";
export const DELIVERY_TYPE = "DELIVERY_TYPE";
export const HANDOVER_BRANCH = "HANDOVER_BRANCH";
export const DELIVERY = "DELIVERY";
export const DELIVERY_RETURN = "DELIVERY_RETURN";
export const SELECTED_CAR_EXTRA_SERVICES = "SELECTED_CAR_EXTRA_SERVICES";
export const CLEAR_CAR = "CLEAR_CAR";
export const INSURANCE_TYPE = "INSURANCE_TYPE";
export const SHIFTS_TODAY = "SHIFTS_TODAY";
export const WORKING_HOURS = "WORKING_HOURS";
export const IS_WORKING_HOURS_OPEN = "IS_WORKING_HOURS_OPEN";
export const TAMARA_AVAILABLE = "TAMARA_AVAILABLE";
export const TAMARA_USED = "TAMARA_USED";
export const CLEAR_DATA = "CLEAR_DATA";
export const RENT_TO_OWN_PLANS = "RENT_TO_OWN_PLANS";
import { TavialablePaymentMethods, TpaymentMethods } from "utilities/enums";

export function carsListAction(payload) {
  return {
    type: CARS_LIST,
    payload,
  };
}

export function carsListCurrentPageAction(payload) {
  return {
    type: CARS_LIST_CURRENT_PAGE,
    payload,
  };
}

export function carsSlectedBranchAction(payload) {
  return {
    type: CAR_SELECTED_BRANCH,
    payload,
  };
}

export function setCarData(payload) {
  return {
    type: CAR_DATA,
    payload,
  };
}
export function setAboutRentPrice(payload) {
  return {
    type: ABOUT_RENT_PRICE,
    payload,
  };
}
export function carInsurance(payload) {
  return {
    type: CAR_SELECTED_INURANCE,
    payload,
  };
}
export function carInsuranceType(payload) {
  return {
    type: INSURANCE_TYPE,
    payload,
  };
}
export function SelectPaymentType(payload) {
  return {
    type: PAYMENT_TYPE,
    payload,
  };
}

export function SelectPaymentMethod(payload?: TpaymentMethods) {
  return {
    type: PAYMENT_METHOD,
    payload,
  };
}
export function SetAvialablePaymentMethods(payload: TavialablePaymentMethods) {
  return {
    type: AVAILABLE_PAYMENT_METHODS,
    payload,
  };
}
export function setDeliveryType(payload) {
  return {
    type: DELIVERY_TYPE,
    payload,
  };
}

export function setHandoverInAnotherBranch(payload) {
  return {
    type: HANDOVER_BRANCH,
    payload,
  };
}

export function setDelivery(payload) {
  return {
    type: DELIVERY,
    payload,
  };
}

export function setDeliveryReturn(payload) {
  return {
    type: DELIVERY_RETURN,
    payload,
  };
}

export function setCarSelectedExtraServices(payload) {
  return {
    type: SELECTED_CAR_EXTRA_SERVICES,
    payload,
  };
}
export function setClearCar(payload) {
  return {
    type: CLEAR_CAR,
    payload,
  };
}
export function setShiftsToday(payload) {
  return {
    type: SHIFTS_TODAY,
    payload,
  };
}
export function setWorkingHours(payload) {
  return {
    type: WORKING_HOURS,
    payload,
  };
}
export function setWorkingHoursModalOpen(payload) {
  return {
    type: IS_WORKING_HOURS_OPEN,
    payload,
  };
}
export function setTamaraAvailablility(payload) {
  return {
    type: TAMARA_AVAILABLE,
    payload,
  };
}
export function setTamaraUsed(payload) {
  return {
    type: TAMARA_USED,
    payload,
  };
}
export function setRentToOwnPlan(payload) {
  return {
    type: RENT_TO_OWN_PLANS,
    payload,
  };
}
export function setClearCarData() {
  return {
    type: CLEAR_DATA,
  };
}
