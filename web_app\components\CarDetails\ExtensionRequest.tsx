/* eslint-disable react-hooks/exhaustive-deps */
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { memo, useMemo, useState } from "react";
import { useRouter } from "next/router";
import { KeyboardArrowDown, KeyboardArrowUp } from "@material-ui/icons";
import moment from "moment";
import i18n from "localization";
import { Button } from "@material-ui/core";
import { setCreatingOnlineExtensionRequset } from "store/extensions/action";
import { useMutation } from "@apollo/client";
import { CancelExtension_Mutation } from "gql/mutations/CancelExtension";
import Swal from "sweetalert2";
import ExtensionRequestModal from "./popups/ExtensionRequestModal";
import RequestLoader from "components/shared/requestLoader";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  padding: 20px;
  margin: 24px 0;

  .wallet-balance {
    color: var(--color-16);
  }
  .buttons-wrap {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
    grid-gap: 10px;
    margin: 10px 0;
  }
  .pay {
    border: none;
    background: var(--color-3);
    font-weight: bold;
    color: var(--color-4);
    padding: 12px 0;
    width: 100%;
    border: solid 2px transparent;
    border-radius: var(--radius-3);
    margin: 0 !important;
    &:hover {
      color: #fff !important;
      border: solid 2px var(--color-3);
      background: var(--color-3) !important;
    }
  }
  .edit {
    border: 2px solid var(--color-3);
    color: var(--color-3);
    font-weight: bold;
    padding: 12px 0;
    width: 100%;
    border-radius: var(--radius-3);
    margin: 0 !important;
    &:hover {
      color: #fff !important;
      border: solid 2px var(--color-3);
      background: var(--color-3) !important;
    }
  }
`;
const ExtensionRequest = ({
  extensionDetails,
  showMore,
  setIsViewAllExtensionModalOpen,
  setExtensionId,
}) => {
  //Hooks
  const { t } = useTranslation();
  const router = useRouter();
  const { car, bookingId } = router.query || {};
  const dispatch = useDispatch();
  const [cancelRentalExtensionRequest, { loading: cancellingExtension }] =
    useMutation(CancelExtension_Mutation, {
      errorPolicy: "all",
    });

  //State
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isExtensionRequestOpen, setIsExtensionRequestOpen] = useState(false);
  const [payNow, setPayNow] = useState(false);

  const isExtensionPayable = extensionDetails.payable;
  const isInstallmentExtension = extensionDetails?.withInstallment;

  //Functions
  function payRequestHandler() {
    setExtensionId();
    setExtensionId(extensionDetails.id);
  }
  const CancelExtension = (id, requestNo) => {
    cancelRentalExtensionRequest({
      variables: {
        rentalExtensionId: id,
      },
    })
      .then((res) => {
        // Check for GraphQL errors first
        if (res.errors && res.errors.length > 0) {
          // Handle GraphQL errors
          Swal.fire({
            titleText: t("Error canceling extension request"),
            text: res.errors[0].message,
            icon: "error",
            confirmButtonText: t("ok"),
          });
          return;
        }

        // Then check for errors in the response data
        if (res?.data?.cancelRentalExtensionRequest?.errors?.length) {
          // Handle errors from the mutation response
          Swal.fire({
            titleText: t("Error canceling extension request"),
            text: res.data.cancelRentalExtensionRequest.errors[0],
            icon: "error",
            confirmButtonText: t("ok"),
          });
          return;
        }

        // If no errors, show success message
        Swal.fire({
          titleText:
            i18n.language == "ar"
              ? `تم إلغاء طلب التمديد رقم ${requestNo} بنجاح`
              : `Extension request ${requestNo} is canceled successfully`,
          icon: "success",
          confirmButtonText: t("ok"),
        }).then(() => {
          router.push(`car-details?car=${car}&bookingId=${bookingId}`);
        });
        router.push(
          `${router.pathname}?car=${router.query.car}&bookingId=${router.query.bookingId}`
        );
      })
      .catch((error) => {
        // Catch any unexpected errors
        Swal.fire({
          titleText: t("Error canceling extension request"),
          text: error.message,
          icon: "error",
          confirmButtonText: t("ok"),
        });
      });
  };
  return (
    <>
      <RequestLoader loading={cancellingExtension} />
      <Div>
        <div className="d-flex gap-5px  mt-2 mb-3">
          <span className="bold">{t("Extension Request") as string}</span>
        </div>

        {!isCollapsed && (
          <>
            <div className="d-flex justify-content-between mt-2 mb-2">
              <div>{t("Request Status") as string}</div>
              <div
                style={{
                  color:
                    extensionDetails.status == "pending"
                      ? "#9EA6A9"
                      : extensionDetails.status == "confirmed"
                      ? "#7AB3C5"
                      : extensionDetails.status == "rejected "
                      ? "#9B51E0"
                      : "#F85959",
                }}
              >
                {extensionDetails.statusLocalized}
              </div>
            </div>
            <div className="d-flex justify-content-between mt-2 mb-2">
              <div>{t("Request No.") as string}</div>
              <div>{extensionDetails.requestNo}</div>
            </div>
            <div className="d-flex justify-content-between mt-2 mb-2">
              <div>{t("New Return Time") as string}</div>
              <div>
                {moment(
                  `${extensionDetails.dropOffDate}T${extensionDetails.dropOffTime}`
                )
                  .locale(i18n.language)
                  .format("DD /MM /yyyy h:mm:ss a")}
              </div>
            </div>
            <div className="d-flex justify-content-between mt-2 mb-2">
              <div>{t("Extension Duration") as string}</div>
              <div>{`${extensionDetails.extensionDays} ${t("days")}`}</div>
            </div>
            {!isInstallmentExtension ? (
              <div className="d-flex justify-content-between mt-2 mb-2">
                <div>{t("Payment Method") as string}</div>
                <div>
                  {extensionDetails.paymentMethod == "ONLINE"
                    ? (t("online") as string)
                    : (t("cash") as string)}
                </div>
              </div>
            ) : null}
            <div className="d-flex justify-content-between mt-2 mb-2">
              {extensionDetails.paymentMethod != "CASH" ? (
                <>
                  <div>{t("Payment Status") as string}</div>
                  <div>
                    {extensionDetails?.paymentStatus
                      ?.toLowerCase()
                      ?.includes("pending")
                      ? (t("Pending") as string)
                      : extensionDetails?.isPaid
                      ? (t("paid") as string)
                      : (t("notpaid") as string)}
                  </div>
                </>
              ) : null}
            </div>
            <div className="d-flex justify-content-between mt-2 mb-2">
              <div>
                <h5 className="color-3">{t("Grand Total") as string}</h5>
                <span className="color-16">{t("+VAT") as string}</span>
              </div>
              <div className="d-flex gap-5px">
                <RiyalSymbol
                  style={{ order: i18n.language === "ar" ? 1 : 0 }}
                />
                <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                  {extensionDetails.totalRemainingPrice}
                </span>
              </div>
            </div>
          </>
        )}
        {
          <div className="text-center cursor-pointer">
            {isCollapsed ? (
              <KeyboardArrowDown onClick={() => setIsCollapsed(false)} />
            ) : (
              <KeyboardArrowUp onClick={() => setIsCollapsed(true)} />
            )}
          </div>
        }
        <div className="buttons-wrap">
          {isExtensionPayable ? (
            <>
              <div className="flex flex-column">
                <div
                  className="text-center"
                  onClick={(e) => {
                    if (
                      extensionDetails.paymentMethod.toLowerCase() == "online"
                    ) {
                      dispatch(setCreatingOnlineExtensionRequset(true));
                      setIsExtensionRequestOpen(true);
                      payRequestHandler();
                    }
                  }}
                >
                  <Button className="pay mt-4 mb-2">
                    {t("Pay Now") as string}
                  </Button>
                </div>
              </div>
            </>
          ) : null}
          <div className="flex flex-column">
            <div
              className="text-center"
              onClick={() => {
                setIsExtensionRequestOpen(true);
              }}
            >
              {!extensionDetails.isPaid &&
              extensionDetails.paymentMethod.toLowerCase() == "online" &&
              (extensionDetails?.paymentStatus?.toLowerCase() === "pending" ||
                extensionDetails?.paymentStatus?.toLowerCase() == null) &&
              extensionDetails?.status === "pending" ? (
                <Button className="edit mt-4 mb-2">
                  {t("Edit") as string}
                </Button>
              ) : null}
            </div>
          </div>
        </div>
        <div className="text-center mt-2 mb-2">
          {extensionDetails.canCancel && (
            <button
              className="btn btn-link t"
              style={{ color: "red", fontWeight: "bold" }}
              onClick={() =>
                CancelExtension(extensionDetails.id, extensionDetails.requestNo)
              }
            >
              {t("Cancel Extension Request") as string}
            </button>
          )}
        </div>
        {showMore ? (
          <div
            className="px-4 cursor-pointer text-center mt-2"
            style={{ textDecoration: "underline" }}
            onClick={() => setIsViewAllExtensionModalOpen(true)}
          >
            {t("More") as string}
          </div>
        ) : null}
      </Div>

      <ExtensionRequestModal
        {...{
          setExtensionId: setExtensionId,
          isOpen: isExtensionRequestOpen,
          setIsOpen: setIsExtensionRequestOpen,
          extensionDetails,
          payNow,
          setPayNow,
          payRequestHandler,
          refetchRentalDetails: undefined,
          withInstallment: extensionDetails?.withInstallment,
        }}
      />
    </>
  );
};
export default memo(ExtensionRequest);
