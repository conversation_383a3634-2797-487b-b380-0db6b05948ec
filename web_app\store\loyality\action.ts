export const SELECT_FURSAN = "SELECT_FURSAN";
export const IS_FURSAN_MEMBRSHIPID_VERIFIED = "IS_FURSAN_MEMBRSHIPID_VERIFIED";
export const IS_FURSAN_MODAL_OPEN = "IS_FURSAN_MODAL_OPEN";
export const CLEAR_DATA = "CLEAR_DATA";
export const MEMBERSHIP_ID = "MEMBERSHIP_ID";

export function selectFursanAction(payload: boolean) {
  return {
    type: SELECT_FURSAN,
    payload,
  };
}
export function verifyFursanMembershipId(payload: boolean) {
  return {
    type: IS_FURSAN_MEMBRSHIPID_VERIFIED,
    payload,
  };
}
export function toggleFursanModal(payload: boolean) {
  return {
    type: IS_FURSAN_MODAL_OPEN,
    payload,
  };
}
export function saveMembershipId(payload: string | number) {
  return {
    type: MEMBERSHIP_ID,
    payload,
  };
}

export function clearLoyalityData() {
  return {
    type: CLEAR_DATA,
  };
}
