import Popup from "components/shared/popup";
import VerifyAccount from "./verifyAccount";
import Form from "./signInForm";
import styled from "styled-components";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { GoToLogin } from "store/authentication/action";

const SignInContainer = styled.div`
  /* margin: 40px 0 55px 0; */
`;

export default function SigninPopup({
  setIsSigninPopupOpen,
  setIsSignupPopupOpen,
}) {
  const { t, i18n } = useTranslation();
  const [mobileData, setMobileData] = useState(null);
  const [showForm, setShowForm] = useState(true);
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);
  const dispatch = useDispatch();
  const { Islogin, login_data } =
    useSelector((state: RootStateOrAny) => state.authentication) || {};

  return (
    <Popup
      isOpen={Islogin && !login_data?.token}
      setIsOpen={() => dispatch(GoToLogin(false))}
      maxWidth="sm"
    >
      <SignInContainer className="px-lg-2 pb-2">
        {showForm ? (
          <Form
            t={t}
            i18n={i18n}
            setShowForm={setShowForm}
            setIsSignupPopupOpen={setIsSignupPopupOpen}
            setIsSigninPopupOpen={setIsSigninPopupOpen}
            setMobileData={setMobileData}
          />
        ) : (
          <VerifyAccount
            verifyBy="phone"
            t={t}
            isPhoneVerified={isPhoneVerified}
            setIsPhoneVerified={setIsPhoneVerified}
            setIsSigninPopupOpen={setIsSigninPopupOpen}
            mobileData={mobileData}
            setMobileData={setMobileData}
          />
        )}
      </SignInContainer>
    </Popup>
  );
}
