/* eslint-disable @next/next/no-img-element */
import Close from "@material-ui/icons/Close";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { getMobileOperatingSystem } from "utilities/helpers";

const Div = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px 0 16px !important;
  svg {
    cursor: pointer;
  }
  @media all and (orientation: landscape) {
    display: none;
  }
  display: ${(props) => (props.isNotSafari ? "" : "none")}; ;
`;
export default function GetCarwahApp() {
  const { t } = useTranslation();
  return (
    <Div
      isNotSafari={
        getMobileOperatingSystem() === "Android" &&
        getMobileOperatingSystem() !== "Windows Phone"
      }
    >
      <img src="/assets/images/miniLogo.svg" alt="logo" />
      <a
        href={
          getMobileOperatingSystem() === "Android" &&
          getMobileOperatingSystem() !== "Windows Phone"
            ? "https://play.google.com/store/apps/details?id=com.carwah.customer&hl=en_US&gl=US"
            : "https://apps.apple.com/sa/app/carwah-%D9%83%D8%B1%D9%88%D8%A9/id1387161215"
        }
        target="_blank"
        rel="noreferrer"
        className="color-1 text-decoration-underline"
      >
        {t("Get the Carwah App")}
      </a>
      <Close />
    </Div>
  );
}
