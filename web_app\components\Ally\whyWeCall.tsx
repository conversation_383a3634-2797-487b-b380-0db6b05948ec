/* eslint-disable @next/next/no-img-element */
import { Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import BecomeAnAllyBox from "./becomeAnAllyBox";

const Div = styled.div`
  & {
    padding: 30px 0;
    .hands {
      margin-top: 50px;
    }
    p {
      font-size: 24px;
    }
    > div {
      > div:not(:first-child) {
        border-radius: var(--radius-2);
      }
    }
  }
`;

export default function WhyWeCall() {
  const { t } = useTranslation();

  return (
    <Div>
      <Grid container justifyContent="space-between" alignItems="center">
        <Grid item xs={12} md={3}>
          <div className="section-fancy-title">
            <h2>
              <span>{t("Why Did We Call")}</span> {t("Them An Ally")}
            </h2>
          </div>
        </Grid>
        <Grid item xs={12} md={8}>
          <p>
            {t(
              "Ally in Arabic the one who is with you and considered as the best friend who supported you in all of your situations. <PERSON><PERSON><PERSON>'s allies are our friends from car rental companies who seek to provide the best service together for their customers."
            )}
          </p>
        </Grid>
      </Grid>
      <Grid container justifyContent="space-between" alignItems="center">
        <Grid item xs={12} md={2} container justifyContent="center">
          <img
            className="hands"
            src="/assets/images/ally/hands.svg"
            alt="logo"
          />
        </Grid>
        <Grid item xs={12} md={8}>
          <BecomeAnAllyBox />
        </Grid>
      </Grid>
    </Div>
  );
}
