/* eslint-disable @next/next/no-img-element */
import { Container, Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  min-height: 470px;
  margin-bottom: -200px;
  position: relative;
  background-color: var(--color-6);
  padding: 60px 0;
  &:after {
    content: "";
    /* background: url("/assets/images/home/<USER>"); */
    position: absolute;
    top: 10px;
    right: 0;
    width: 590px;
    height: 100%;
    background-repeat: no-repeat;
    background-size: contain;
  }
  h1 {
    font-size: 38px;
    span {
      font-size: 48px;
    }
  }
  @media (max-width: 786px) {
    margin-bottom: -130px !important;
  }
`;

export default function Hero({ cityData }) {
  const { t, i18n } = useTranslation();

  return (
    <Div>
      <Container>
        <Grid container alignItems="baseline" spacing={4}>
          <Grid item>
            <img src={cityData?.icon} alt="icon" />
          </Grid>
          <Grid item>
            <h1>
              {t("Rent a car in")}
              <br />
              <span className="bold">
                {i18n.language === "en" ? cityData?.nameEn : cityData?.nameAr}
              </span>
            </h1>
          </Grid>
        </Grid>
      </Container>
    </Div>
  );
}
