/* eslint-disable @next/next/no-img-element */
import { memo, FC, ReactNode } from "react";
import { CouponWrapper, Div, Modal } from "./styled";
import useLogic from "./useLogic";
import RentPayment from "../RentPayment";
import Close from "@material-ui/icons/Close";
import {
  ArrowBackIosOutlined,
  ArrowForwardIosOutlined,
  CheckCircle,
} from "@material-ui/icons";
import Popup from "components/shared/popup";
import { RootStateOrAny, useSelector } from "react-redux";
import RiyalSymbol from "components/shared/RiyalSymbol";

// Types
interface InvoiceProps {
  setCalendarTooltipOpen: (value: boolean) => void;
  refetchRentalDetails: () => void;
  extensionId?: string;
  setExtensionId?: (id: string) => void;
  isIntallmentDetailsModal?: boolean;
  isInInstallmentBreakDown?: boolean;
  hideAboutPrice?: boolean;
  refetchRentalAboutPrice?: () => void;
}

interface ExtraService {
  extraService?: {
    title: string;
  };
  allyExtraService?: {
    extraService?: {
      title: string;
    };
    subtitle?: string;
  };
  title?: string;
  subtitle?: string;
  serviceValue?: number;
  totalServiceValue?: number;
  hide?: boolean;
}

interface WalletProps {
  style: React.CSSProperties | undefined;
}

interface InstallmentsData {
  paidInstallmentsCount: number;
  notPaidInstallmentsCount: number;
  totalInstallmentsCount: number;
  totalPaidAmount: number;
  nextInstallmentToPay?: number;
  nextInstallmentAmountToPay?: number;
  upcomingInstallmentToPay?: number;
  upcomingInstallmentAmountToPay?: number;
  totalnextInstallmentToPay: number;
}

const Invoice: FC<InvoiceProps> = ({
  setCalendarTooltipOpen,
  refetchRentalDetails,
  extensionId,
  setExtensionId,
  isIntallmentDetailsModal,
  isInInstallmentBreakDown,
  hideAboutPrice,
  refetchRentalAboutPrice,
}) => {
  const {
    rentalExtraServices,
    paymentStatusCode,
    paymentStatusMessage,
    getInstallmentsInvoiceData,
    hasInstallment,
    dailyPrice,
    numberOfDays,
    priceBeforeDiscount,
    discountValue,
    discountType,
    discountPercentage,
    priceBeforeInsurance,
    insuranceValue,
    deliveryPrice,
    handoverPrice,
    addsPrice,
    allyExtraServices,
    branchExtraServices,
    isUnlimited,
    isUnlimitedFree,
    unlimitedFeePerDay,
    totalUnlimitedFee,
    priceBeforeTax,
    valueAddedTaxPercentage,
    taxValue,
    totalPrice,
    pricePerDay,
    couponCode,
    couponDiscount,
    isCouponApplied,
    couponErrorMessage,
    totalAmountDue,
    handover_in_another_branch,
    deliveryType,
    insuranceType,
    delivery,
    coupon_data,
    setIsPopupOpen,
    isPopupOpen,
    i18n,
    t,
    getExtraServicesPrice,
    bookingId,
    rentalData,
    installments,
    walletAmount,
    walletAmountDetails,
    dueAmoutOutside,
    totalWalletPaidFromRentalDetails,
    isRentToOwn,
    rentToOwnInstallmentBreakdown,
    totalInstallmentsPaidAmount,
    totalInstallmentsAmount,
    upcomingInstallmentToPay,
    nextInstallmentToPay,
    sucessInstallmentPaid,
    ownCarDetails,
    totalExtensionsDays,
    totalExtensionsPrice,
    priceBeforeTax_rentalAbouPrice,
    priceBeforeInsurance_rentalAboutPrice,
    totalWalletPaidAmount_rentalAboutPrice,
    nextInstallmentAmountToPay,
    upcomingInstallmentAmountToPay,
    showDueAmount,
    payable,
  } = useLogic(isIntallmentDetailsModal);

  const { pay_with_wallet } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};

  // Cache installments data to avoid repeated calls
  const installmentsData: InstallmentsData = getInstallmentsInvoiceData(
    installments
  ) || {
    paidInstallmentsCount: 0,
    notPaidInstallmentsCount: 0,
    totalInstallmentsCount: 0,
    totalPaidAmount: 0,
    nextInstallmentToPay: undefined,
    nextInstallmentAmountToPay: undefined,
    upcomingInstallmentToPay: undefined,
    upcomingInstallmentAmountToPay: undefined,
    totalnextInstallmentToPay: 0,
  };

  // --- Helper rendering functions ---
  const renderTranslation = (key: string): string => {
    return t(key) as string;
  };

  // --- Subcomponents ---

  const RentalStatus = () => {
    if (!paymentStatusMessage) return null;

    return (
      <p
        style={{
          color: paymentStatusCode?.includes("pending")
            ? "#FA9C3F"
            : paymentStatusCode === "refund_with_discount"
            ? "var(--color-9)"
            : "var(--color-22)",
        }}
        className="d-flex align-items-center justify-content-center text-center mt-2"
      >
        {paymentStatusCode?.includes("pending") ? (
          <img src="/assets/icons/pending.svg" alt="icon" />
        ) : paymentStatusCode === "refund_with_discount" ||
          paymentStatusCode === "upcoming_installment" ? (
          <Close />
        ) : (
          <CheckCircle />
        )}
        {paymentStatusMessage}
      </p>
    );
  };

  // wallet display if the booking is not id or the rental details is payable and the wallet amount details and pay with wallet is true or the rental details is not payable and the total wallet paid from rental details or the total wallet paid amount rental about price is true
  const Wallet: FC<WalletProps> = ({ style }) => {
    const walletDisplay = !bookingId
      ? walletAmount
      : (rentalData?.rentalDetails?.payable &&
          walletAmountDetails &&
          pay_with_wallet) ||
        (!rentalData?.rentalDetails?.payable &&
          (totalWalletPaidFromRentalDetails ||
            totalWalletPaidAmount_rentalAboutPrice));

    if (!walletDisplay) return null;

    return (
      <div style={style}>
        <h6 style={{ fontSize: "15px", padding: "0 5px", color: "green" }}>
          {renderTranslation("Wallet")}
        </h6>
        <div
          style={{ color: "green" }}
          className="price d-flex gap-5px align-items-baseline"
        >
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {` - ${
              !bookingId
                ? walletAmount
                : totalWalletPaidAmount_rentalAboutPrice &&
                  pay_with_wallet &&
                  payable
                ? totalWalletPaidAmount_rentalAboutPrice
                : totalWalletPaidFromRentalDetails
            }`}
          </span>
        </div>
      </div>
    );
  };

  const CompletedPayments = () => {
    if (!hasInstallment) return null;

    return (
      <div>
        <h6>
          {renderTranslation("Completed Payments")}(
          {installmentsData.paidInstallmentsCount}/
          {installmentsData.totalInstallmentsCount})
        </h6>
        <div className="price d-flex gap-5px align-items-baseline">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {`${installmentsData.totalPaidAmount}`}
          </span>
        </div>
      </div>
    );
  };

  const RemainingDue = () => {
    const remainingDue = bookingId
      ? totalInstallmentsAmount - (installmentsData.totalPaidAmount || 0)
      : totalAmountDue;

    return (
      <div className="mt-3">
        <div>
          <h6 className="color-3">{renderTranslation("Remaining Due")}</h6>
        </div>
        <div className="color-3 price d-flex gap-5px align-items-baseline">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {remainingDue?.toFixed(2)}
          </span>
        </div>
      </div>
    );
  };

  const GrandTotal = () => {
    if (
      !pay_with_wallet &&
      !(bookingId && rentalData?.rentalDetails?.totalWalletPaidAmount)
    ) {
      return <></>;
    }
    const grandTotal = isRentToOwn
      ? isInInstallmentBreakDown
        ? totalInstallmentsAmount
        : bookingId
        ? rentalData?.rentalDetails?.totalBookingPrice
        : totalPrice
      : !bookingId
      ? totalPrice
      : hasInstallment
      ? totalInstallmentsAmount
      : rentalData?.rentalDetails?.totalBookingPrice;

    return (
      <div className="mt-3">
        <div>
          <h6 className="color-3">{renderTranslation("Grand Total")}</h6>
          <span className="color-16">{renderTranslation("+VAT")}</span>
        </div>
        <div
          className="color-3 price d-flex gap-5px align-items-baseline"
          style={{ fontSize: "16px", fontWeight: 600 }}
        >
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {`${grandTotal}`}
          </span>
        </div>
      </div>
    );
  };

  const DueAmount = () => {
    return (
      <div className="mt-3">
        <div>
          <h6 className="color-3">
            {!showDueAmount()
              ? renderTranslation("Paid Amount")
              : renderTranslation("Due Amount")}
          </h6>
        </div>
        <div
          className="color-3 price d-flex gap-5px align-items-baseline bold"
          style={{ fontSize: "16px", fontWeight: 600 }}
        >
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {" "}
            {`${dueAmoutOutside?.toFixed(2)}`}
          </span>
        </div>
      </div>
    );
  };

  const Vat = () => {
    const vatPercentage =
      bookingId && rentalData?.rentalDetails
        ? rentalData.rentalDetails.valueAddedTaxPercentage
        : valueAddedTaxPercentage;
    const vatValue =
      bookingId && rentalData?.rentalDetails
        ? rentalData.rentalDetails.taxValue
        : taxValue;

    return (
      <div>
        <h6>{`${renderTranslation("VAT")} ${vatPercentage}%`}</h6>
        <div className="price d-flex gap-5px align-items-baseline">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {`${vatValue}`}
          </span>
        </div>
      </div>
    );
  };

  const Total = () => {
    return (
      <div>
        <h6>{renderTranslation("Total")}</h6>
        <div
          className="price d-flex gap-5px align-items-baseline"
          style={{ width: "max-content" }}
        >
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {`${bookingId ? priceBeforeTax_rentalAbouPrice : priceBeforeTax}`}
          </span>
        </div>
      </div>
    );
  };

  const ExtraServices = () => {
    const extraServices = getExtraServicesPrice({
      unlimitedFeePerDay,
      isUnlimitedFree,
      isUnlimited,
      totalUnlimitedFee,
      branchExtraServices: bookingId
        ? rentalExtraServices?.filter(
            (i) => i.extraServiceType === "ally_company"
          )
        : branchExtraServices || [],
      allyExtraServices: bookingId
        ? rentalExtraServices?.filter(
            (i) => i.extraServiceType !== "ally_company"
          )
        : allyExtraServices
        ? [...allyExtraServices]
        : [],
    });

    return (
      <section>
        <h5>{renderTranslation("extraservice")}</h5>
        {(insuranceType?.toLowerCase() === "full" ||
          insuranceType === "شامل") && (
          <div>
            <h6>{`${renderTranslation("Insurance")} (${renderTranslation(
              "Full"
            )})`}</h6>
            <div className="price d-flex gap-5px align-items-baseline">
              <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                <RiyalSymbol />
              </span>
              <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                {`${
                  bookingId && rentalData?.rentalDetails
                    ? rentalData.rentalDetails.totalInsurancePrice
                    : insuranceValue
                }`}
              </span>
            </div>
          </div>
        )}
        {(delivery?.checked ||
          Boolean(rentalData?.rentalDetails?.deliveryPrice)) && (
          <div>
            <div className="text-align-localized">
              <h6>{renderTranslation("Car Delivery Fee")}</h6>
              <span className="color-16 font-12px">
                {renderTranslation(deliveryType)}
              </span>
            </div>
            <div className="price d-flex gap-5px align-items-baseline">
              <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                <RiyalSymbol />
              </span>
              <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                {`${
                  bookingId && rentalData?.rentalDetails
                    ? handover_in_another_branch?.checked
                      ? rentalData.rentalDetails.deliveryPrice
                      : rentalData.rentalDetails.deliveryPrice +
                        rentalData.rentalDetails.handoverPrice
                    : handover_in_another_branch?.checked
                    ? deliveryPrice
                    : deliveryPrice + handoverPrice
                }`}
              </span>
            </div>
          </div>
        )}
        {(handover_in_another_branch?.checked ||
          Boolean(
            rentalData?.rentalDetails?.handoverPrice &&
              rentalData?.rentalDetails?.deliverType !== "two_ways"
          )) && (
          <div>
            <div className="text-align-localized">
              <h6>{renderTranslation("Car return in another branch")}</h6>
              <span className="color-16 font-12px">
                {handover_in_another_branch?.option?.label}
              </span>
            </div>
            <div className="price d-flex gap-5px align-items-baseline">
              <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                <RiyalSymbol />
              </span>
              <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                {`${
                  bookingId && rentalData?.rentalDetails
                    ? rentalData.rentalDetails.handoverPrice
                    : handoverPrice
                }`}
              </span>
            </div>
          </div>
        )}
        {extraServices?.map((item: ExtraService, index) =>
          !item?.hide ? (
            <div key={index} className="my-2">
              <div className="text-align-localized">
                <h6>
                  {item?.extraService?.title ||
                    item?.allyExtraService?.extraService?.title ||
                    item?.title}
                </h6>
                <span className="color-16 font-12px riyal-symbol">
                  {(item?.subtitle || item?.allyExtraService?.subtitle)
                    ?.replace("SAR", "﷼")
                    ?.replace("ريال", "﷼")}
                </span>
              </div>
              <div
                style={{
                  minWidth: "100px",
                  textAlign: "end",
                }}
              >
                {item?.serviceValue === 0 ? (
                  <span style={{ color: "#6FCFA1" }}>
                    {renderTranslation("Free")}
                  </span>
                ) : (
                  <span className="text-uppercase price d-flex gap-5px align-items-baseline justify-content-end">
                    <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                      <RiyalSymbol />
                    </span>
                    <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                      {`${item?.totalServiceValue}`}
                    </span>
                  </span>
                )}
              </div>
            </div>
          ) : null
        )}
        <div className="break" title="addsPrice">
          <div className="separator d-bolck" />
          <div style={{ textAlign: "start" }}>
            <div className="price d-flex gap-5px align-items-baseline">
              <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                <RiyalSymbol />
              </span>
              <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                {bookingId && rentalData?.rentalDetails
                  ? rentalData.rentalDetails.addsPrice
                  : addsPrice}
              </span>
            </div>
          </div>
        </div>
      </section>
    );
  };

  const Coupons = () => {
    if (
      !coupon_data?.id &&
      !(rentalData?.rentalDetails?.couponCode && bookingId)
    ) {
      return null;
    }

    return (
      <CouponWrapper language={i18n.language}>
        <div style={{ position: "relative" }}>
          <h6
            style={{
              color: !isCouponApplied && !bookingId ? "red" : "",
              marginBottom: "18px",
            }}
            className="coupon-text"
          >
            {`${renderTranslation("Coupon discount")} `}
            <span style={{ fontFamily: "var(--font-en) !important" }}>
              (
              {couponCode && !bookingId
                ? couponCode
                : rentalData?.rentalDetails?.couponCode && bookingId
                ? rentalData?.rentalDetails?.couponCode
                : null}
              )
            </span>
          </h6>
          {couponErrorMessage ? (
            <span className="tooltiptext"> {couponErrorMessage}</span>
          ) : null}
        </div>
        <div className="color-9 price d-flex gap-5px align-items-baseline">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>{`- ${
            couponDiscount &&
            !rentalData?.rentalDetails?.couponCode &&
            !bookingId
              ? couponDiscount
              : rentalData?.rentalDetails?.couponCode && bookingId
              ? rentalData?.rentalDetails?.couponDiscount
              : 0
          }`}</span>
        </div>
      </CouponWrapper>
    );
  };

  const Discount = () => {
    if (bookingId && rentalData?.rentalDetails?.discountValue) {
      return (
        <div>
          <h6>{`${renderTranslation("Discount")} (${
            rentalData.rentalDetails.discountType
          }) ${rentalData.rentalDetails.discountPercentage} %`}</h6>
          <div className="color-9 price d-flex gap-5px align-items-baseline">
            <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
              <RiyalSymbol />
            </span>
            <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
              {`- ${rentalData.rentalDetails.discountValue}`}
            </span>
          </div>
        </div>
      );
    }

    if (discountValue) {
      return (
        <div>
          <h6>{`${renderTranslation(
            "Discount"
          )} (${discountType}) ${discountPercentage} %`}</h6>
          <div className="color-9 price d-flex gap-5px align-items-baseline">
            <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
              <RiyalSymbol />
            </span>
            <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
              {`- ${discountValue}`}
            </span>
          </div>
        </div>
      );
    }

    return null;
  };

  const PriceBeforeInsurance = () => {
    return (
      <div className="break" title="priceBeforeInsurance">
        <div className="separator d-bolck" />
        <div className="price d-flex gap-5px align-items-baseline">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>{`${
            bookingId
              ? priceBeforeInsurance_rentalAboutPrice
              : priceBeforeInsurance
          }`}</span>
        </div>
      </div>
    );
  };

  const PricePerDay = () => {
    return (
      <div>
        <h6>{renderTranslation("Price per day")}</h6>
        <div className="price d-flex gap-5px align-items-baseline">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {bookingId && rentalData?.rentalDetails
              ? `${rentalData.rentalDetails.dailyPrice}`
              : `${dailyPrice}`}
          </span>
        </div>
      </div>
    );
  };

  const TotalDays = () => {
    return (
      <div>
        <h6>
          {`${renderTranslation("Total days")} `}{" "}
          <span style={{ fontFamily: "var(--font-en) !important" }}>
            (
            {bookingId && rentalData?.rentalDetails
              ? rentalData.rentalDetails.numberOfDays
              : numberOfDays}
            )
          </span>
        </h6>
        <div className="price d-flex gap-5px align-items-baseline">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {bookingId && rentalData?.rentalDetails
              ? rentalData.rentalDetails.priceBeforeDiscount
              : priceBeforeDiscount}
          </span>
        </div>
      </div>
    );
  };

  const OutsideSection = () => {
    // Calculate amount to display based on various conditions
    const amountToDisplay = (() => {
      if (!showDueAmount() && hasInstallment)
        return totalInstallmentsPaidAmount;
      if (
        !isInInstallmentBreakDown &&
        (dueAmoutOutside ||
          (!dueAmoutOutside &&
            (pay_with_wallet ||
              (bookingId && rentalData?.rentalDetails?.totalWalletPaidAmount))))
      ) {
        return dueAmoutOutside;
      }
      if (Boolean(nextInstallmentToPay) || !sucessInstallmentPaid) {
        return nextInstallmentToPay;
      }
      if (Boolean(upcomingInstallmentToPay && sucessInstallmentPaid)) {
        return upcomingInstallmentToPay;
      }
      return 0;
    })();

    // Determine the appropriate label for the amount
    const amountLabel = (() => {
      if (!showDueAmount()) {
        return renderTranslation("Paid Amount");
      }
      const noUpcomingInstallment =
        (dueAmoutOutside || dueAmoutOutside === 0) && !upcomingInstallmentToPay;
      const noBookingOrUnpaidInstallment =
        !bookingId ||
        (bookingId &&
          hasInstallment &&
          (!sucessInstallmentPaid || !isIntallmentDetailsModal));
      if (noUpcomingInstallment || noBookingOrUnpaidInstallment) {
        return renderTranslation("Due Amount");
      }
      if (Boolean(upcomingInstallmentToPay)) {
        return renderTranslation("Upcoming Amount");
      }
      return "";
    })();

    return (
      <>
        {pay_with_wallet &&
          (isInInstallmentBreakDown ||
            isIntallmentDetailsModal ||
            !bookingId ||
            (bookingId && !rentalData?.rentalDetails?.isPaid)) && (
            <>
              <div className="mt-3 d-flex justify-content-between p-1">
                <div className="d-flex align-items-baseline gap-5px">
                  <h6 className="font-20px bold">
                    {renderTranslation("Total")}
                  </h6>
                  <span className="color-16 font-12px">
                    {renderTranslation("+VAT")}
                  </span>
                </div>
                <div
                  className="price"
                  style={{
                    fontSize: "20px",
                    fontWeight: 600,
                    display: "flex",
                    gap: "5px",
                  }}
                >
                  <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                    <RiyalSymbol />
                  </span>
                  <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                    {` ${
                      !hasInstallment
                        ? bookingId && rentalData?.rentalDetails
                          ? rentalData.rentalDetails.totalBookingPrice
                          : totalPrice
                        : !bookingId
                        ? isIntallmentDetailsModal ||
                          isInInstallmentBreakDown ||
                          !sucessInstallmentPaid
                          ? nextInstallmentAmountToPay
                          : totalPrice
                        : isIntallmentDetailsModal || isInInstallmentBreakDown
                        ? nextInstallmentAmountToPay ||
                          upcomingInstallmentAmountToPay
                        : totalInstallmentsAmount
                    } `}
                  </span>
                </div>
              </div>
              <div className="d-flex justify-content-between">
                <Wallet
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    width: "100%",
                    fontSize: "14px",
                    marginTop: "5px",
                    padding: "0 5px",
                  }}
                />
              </div>
            </>
          )}
        <div className="d-flex justify-content-between align-items-start px-1 py-1 gap-40px mt-2">
          <div className="d-flex flex-column">
            <div className="d-flex align-items-baseline gap-5px">
              <h6 className="bold font-20px d-inline-block">{amountLabel}</h6>
              {showDueAmount() && (
                <span className="color-16 font-12px mx-1">
                  {renderTranslation("+VAT")}
                </span>
              )}
            </div>
            {!hideAboutPrice && (
              <div
                className="font-14px d-flex gap-5px cursor-pointer mt-1"
                onClick={() => setIsPopupOpen(true)}
              >
                <span>{renderTranslation("About Price")}</span>
                {i18n.language === "en" ? (
                  <ArrowForwardIosOutlined style={{ width: "14px" }} />
                ) : (
                  <ArrowBackIosOutlined style={{ width: "14px" }} />
                )}
              </div>
            )}
          </div>
          <div className="color-3 font-20px text-right">
            <span className="bold price d-flex gap-5px align-items-baseline">
              <div style={{ direction: "rtl" }}>
                <div className="bold price d-flex gap-5px align-items-baseline align-items-baseline">
                  <span style={{ order: 1 }}>
                    <RiyalSymbol />
                  </span>
                  <span style={{ order: 0 }}>
                    {amountToDisplay?.toFixed(2)}
                  </span>
                </div>
              </div>
            </span>
            <div
              className="color-16 font-14px align-items-baseline"
              style={{
                display: isRentToOwn && bookingId ? "none" : "flex",
                justifyContent: "flex-end",
              }}
            >
              <div style={{ direction: "rtl" }}>
                <div className="price d-flex gap-5px align-items-baseline align-items-baseline">
                  <span style={{ order: 1 }}>
                    <RiyalSymbol />
                  </span>
                  <span style={{ order: 0 }}>
                    {bookingId && rentalData?.rentalDetails
                      ? isRentToOwn
                        ? ownCarDetails?.rentalOwnCarPlan?.monthlyInstallment
                        : rentalData.rentalDetails.pricePerDay
                      : isRentToOwn
                      ? rentToOwnInstallmentBreakdown?.monthlyInstallment
                      : pricePerDay}
                  </span>
                </div>
              </div>
              {isRentToOwn ? String(t("/month")) : String(t("/day"))}
            </div>
          </div>
        </div>
      </>
    );
  };

  const RentToOwnMonthlyPayment = () => {
    return (
      <div>
        <h6
          style={{
            display: "flex",
            gap: "5px",
            fontWeight: "bold",
            alignItems: "baseline",
          }}
        >
          {renderTranslation("Monthly installment")}
          <span
            style={{
              fontWeight: "lighter",
              color: "#9E9E9E",
              fontSize: "13px",
            }}
          >
            {renderTranslation("per month")}
          </span>
        </h6>
        <div className="price d-flex gap-5px align-items-baseline">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {bookingId && rentalData?.rentalDetails
              ? ownCarDetails?.rentalOwnCarPlan?.monthlyInstallment
              : rentToOwnInstallmentBreakdown?.monthlyInstallment}
          </span>
        </div>
      </div>
    );
  };

  const RentToOwnFirstPayment = () => {
    return (
      <div>
        <h6
          style={{
            display: "flex",
            gap: "5px",
            fontWeight: "bold",
            alignItems: "baseline",
          }}
        >
          {renderTranslation("invoice.1st payment")}
          <span
            style={{
              fontWeight: "lighter",
              color: "#9E9E9E",
              fontSize: "13px",
            }}
          >
            {renderTranslation("include 1st installment")}
          </span>
        </h6>
        <div className="price d-flex gap-5px align-items-baseline">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
            {bookingId && rentalData?.rentalDetails
              ? ownCarDetails?.rentalOwnCarPlan?.firstPayment
              : rentToOwnInstallmentBreakdown?.firstPayment}
          </span>
        </div>
      </div>
    );
  };

  const InvoiceNote = () => {
    return (
      <div
        className="d-flex align-content-center gap-15px radius-2 mt-4"
        style={{
          border: "solid 3px var(--color-3)",
          padding: "15px 10%",
        }}
      >
        <img src="/assets/icons/invoice.svg" alt="invoice" />
        <p className="bold">
          {renderTranslation(
            "This is not a tax invoice, the tax invoice will be issued upon receipt of the vehicle"
          )}
        </p>
      </div>
    );
  };

  // --- Main JSX ---
  return (
    <>
      <Div
        className="p-1 px-3 pb-4"
        isRentToOwn={isRentToOwn && isIntallmentDetailsModal}
      >
        <OutsideSection />
        <RentPayment
          {...{
            setCalendarTooltipOpen,
            refetchRentalDetails,
            extensionId,
            setExtensionId,
            isIntallmentDetailsModal,
            isInInstallmentBreakDown,
            refetchRentalAboutPrice,
            valueToPay:
              dueAmoutOutside || (!dueAmoutOutside && pay_with_wallet)
                ? dueAmoutOutside
                : upcomingInstallmentToPay,
          }}
        />
      </Div>
      <Popup maxWidth="md" isOpen={isPopupOpen} setIsOpen={setIsPopupOpen}>
        <Modal>
          <div>
            <h4 className="bold mb-4">{renderTranslation("About Price")}</h4>
            <section>
              <h5>{renderTranslation("Basic")}</h5>
              {isRentToOwn ? (
                <>
                  <RentToOwnMonthlyPayment />
                  <RentToOwnFirstPayment />
                </>
              ) : (
                <>
                  <PricePerDay />
                  <TotalDays />
                </>
              )}
              <Coupons />
              <Discount />
              <PriceBeforeInsurance />
            </section>
            <ExtraServices />
            <div className="total" title="Total Prices">
              <Total />
              <Vat />
              {!isRentToOwn && (
                <>
                  <GrandTotal />
                  <CompletedPayments />
                </>
              )}
              <Wallet style={undefined} />
              {hasInstallment && !isRentToOwn ? (
                <RemainingDue />
              ) : (
                <DueAmount />
              )}
            </div>
            <RentalStatus />
            {isRentToOwn && <InvoiceNote />}
          </div>
        </Modal>
      </Popup>
    </>
  );
};

export default memo(Invoice);
