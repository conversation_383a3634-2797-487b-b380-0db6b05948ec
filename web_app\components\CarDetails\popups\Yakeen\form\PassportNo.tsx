/* eslint-disable @next/next/no-img-element */
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { Input } from ".";

function PassportNo({ control, watch, errors }) {
  const { t } = useTranslation();

  return (
    <div className="mt-4">
      <label className="color-19 text-start w-100 mb-2">
        {t("Passport No.") as string}
        <span className="color-9">*</span>
      </label>
      <Controller
        name={"passportNumber"}
        control={control}
        rules={{
          required: true,
        }}
        render={({ field, fieldState }) => {
          return (
            <Input
              className="hide-arrows"
              value={watch("passportNumber")}
              onChange={(e) => {
                field.onChange(e.target.value);
              }}
            />
          );
        }}
      />
      {errors?.passportNumber?.type === "required" && (
        <p className="color-9">{t("This field is required") as string}</p>
      )}
    </div>
  );
}

export default PassportNo;
