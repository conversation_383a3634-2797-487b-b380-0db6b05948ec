/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { Switch } from "@material-ui/core";
import useExtraServicesGenerator from "components/shared/extraServicesGenerator";
import Popup from "components/shared/popup";
import { useRouter } from "next/router";
import { memo, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { setCarSelectedExtraServices } from "store/cars/action";
import styled from "styled-components";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  margin: 24px 0;
  position: relative;
`;

function ExtraServices() {
  //Hooks
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const { getCarExtraServices } = useExtraServicesGenerator();
  const router = useRouter();

  const bookingId = router?.query.bookingId;
  const rentalData = useSelector((state: RootStateOrAny) => state.booking);

  //Store
  const { extra_services } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const { car_data } = useSelector((state: RootStateOrAny) => state.cars) || {};

  //State
  const [isOpen, setIsOpen] = useState(false);
  const [description, setDescription] = useState("");

  //Variables
  const extraServices = getCarExtraServices(car_data);
  const availableExtraServices = extraServices?.filter((i) => !i.hide);

  //Function
  function switchHandler(e, item) {
    const checked = e?.target?.checked;
    const value = item;
    const filteredExServices = extra_services?.filter((i) => i?.id != value.id);

    if (checked) {
      dispatch(setCarSelectedExtraServices([...extra_services, value]));
    } else {
      dispatch(setCarSelectedExtraServices(filteredExServices));
    }
  }

  //LifeCycle
  useEffect(() => {
    if (availableExtraServices && !extra_services?.length) {
      const required = availableExtraServices?.filter((i) => i.isRequired);
      if (!extra_services?.find((i) => i.isRequired)) {
        dispatch(setCarSelectedExtraServices([...extra_services, ...required]));
      }
    }
  }, []);

  //Checking not hidden extraservices
  const retalExtraServiceIds =
    rentalData?.rentalDetails?.rentalExtraServices.map(
      (extraservice: any) => extraservice.extraServiceId
    );

  if (car_data && extraServices?.length && availableExtraServices?.length) {
    return (
      <>
        <Div>
          <div className="d-flex gap-10px align-items-center px-4 pt-3 pb-3">
            <h6 className="bold">{t("Extra services") as string}</h6>
          </div>
          <div className="separator" />
          <div className="p-4">
            {extraServices?.map((item) => {
              if (item && !item?.hide) {
                return (
                  <div
                    key={Math.random()}
                    className="mb-3 d-flex justify-content-between gap-30px"
                  >
                    <div className="d-flex align-items-start gap-10px">
                      <div className="d-flex flex-column">
                        <span className="bold text-align-localized">
                          {item?.extraService?.title ||
                            item?.allyExtraService?.extraService?.title}
                        </span>
                        <span className="color-12 font-15px text-align-localized">
                          {item?.serviceValue == 0 ? (
                            <span style={{ color: "#6FCFA1" }}>
                              {t("Free") as string}
                            </span>
                          ) : (
                            <div className="text-uppercase d-flex gap-2px align-items-baseline">
                              {typeof item?.subtitle === "string"
                                ? item.subtitle
                                    .replace("SAR", "")
                                    .replace("SR", "")
                                    .replace("ريال", "")
                                    .split("/")
                                    .map((part, index, arr) => (
                                      <div
                                        className="d-flex gap-2px"
                                        key={`${item?.id}-${index}`}
                                      >
                                        {arr.length > 1 && index === 1 ? (
                                          <>
                                            <span>/</span>
                                            <span>{part}</span>
                                          </>
                                        ) : (
                                          <div className="d-flex gap-2px align-items-baseline">
                                            <span
                                              style={{
                                                order:
                                                  i18n.language === "en"
                                                    ? 0
                                                    : 1,
                                              }}
                                            >
                                              <RiyalSymbol />
                                            </span>
                                            <span
                                              style={{
                                                order:
                                                  i18n.language === "en"
                                                    ? 1
                                                    : 0,
                                              }}
                                            >
                                              {part?.replace("﷼", "").trim()}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                    ))
                                : item?.subtitle}
                            </div>
                          )}
                        </span>
                      </div>
                      <img
                        src="/assets/images/info.svg"
                        alt="icon"
                        className="mt-1 cursor-pointer"
                        onClick={() => {
                          setDescription(item?.extraService?.description);
                          setTimeout(() => {
                            setIsOpen(true);
                          });
                        }}
                      />
                    </div>
                    <Switch
                      value={item}
                      checked={
                        bookingId && retalExtraServiceIds
                          ? (item.id === "isUnlimited" &&
                              !item.hide &&
                              rentalData?.rentalDetails?.isUnlimited) ||
                            retalExtraServiceIds.includes(item.id)
                            ? true
                            : false
                          : item?.isRequired ||
                            extra_services?.find((i) => i?.id == item.id)
                          ? true
                          : false
                      }
                      disabled={item.isRequired}
                      onChange={(e) => switchHandler(e, item)}
                      inputProps={{ "aria-label": "controlled" }}
                    />
                  </div>
                );
              }
            })}
          </div>
        </Div>
        {description && isOpen ? (
          <Popup maxWidth="md" isOpen={isOpen} setIsOpen={setIsOpen}>
            {description}
          </Popup>
        ) : null}
      </>
    );
  } else {
    return null;
  }
}

export default memo(ExtraServices);
