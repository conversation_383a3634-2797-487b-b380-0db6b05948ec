/* eslint-disable react-hooks/exhaustive-deps */
import { useQuery } from "@apollo/client";
import { Wallet_Balance_Query } from "gql/queries/profile";
import { useRouter } from "next/router";
import { useEffect } from "react";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { payWithWalletAction, setWalletBalance } from "store/wallet/action";

function useWallet() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { bookingId } = router.query || {};
  const { balance } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};
  const { login_data } =
    useSelector((state: RootStateOrAny) => state?.authentication) || {};
  const { token } = login_data || {};
  const { refetch } = useQuery(Wallet_Balance_Query, {
    fetchPolicy: "cache-first",
    nextFetchPolicy: "cache-only",
    errorPolicy: "all",
    skip: true,
  });
  const { paymentType, paymentMethod } =
    useSelector((state: RootStateOrAny) => state.cars) || {};

  const payWithWalletAbility: boolean =
    balance &&
    (!bookingId
      ? paymentType?.toLowerCase() === "online"
      : paymentMethod?.toLowerCase() !== "CASH" ||
        paymentType?.toLowerCase() === "online")
      ? true
      : false;

  useEffect(() => {
    //sets wallet false when payment is cash
    if (paymentMethod === "CASH") {
      dispatch(payWithWalletAction(false));
    }
  }, [paymentMethod]);

  useEffect(() => {
    if (token) {
      refetch().then((res: any) => {
        const walletData = res?.data?.viewWallet as {
          balance: number;
          id: string;
          userId: number;
        };
        const { balance: _balance } = walletData || {};
        dispatch(setWalletBalance(_balance));
      });
    }
  }, [router.asPath]);

  return { balance: balance, payWithWalletAbility };
}

export default useWallet;
