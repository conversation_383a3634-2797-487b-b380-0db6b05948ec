import { gql } from "@apollo/client";

export const User_Profile_Query = gql`
  query Profile {
    profile {
      companyName
      dob
      driverLicense
      driverLicenseExpireAt
      driverLicenseStatus
      email
      firstName
      gender
      id
      isActive
      isCustomerProfileCompleted
      lastName
      lat
      licenseFrontImage
      licenseSelfieImage
      lng
      middleName
      mobile
      name
      nid
      passportNumber
      borderNumber
      nidImage
      visaImage
      profileImage
      status
      title
      customerProfile {
        passportExpireAt
        nationalIdVersion
        nationalIdExpireAt
        passportFrontImage
        statusLocalized
        nationality {
          id
          name
        }
        city {
          id
          name
        }
      }
    }
  }
`;

export const Customer_Statuses_Query = gql`
  query CustomerStatuses {
    customerStatuses {
      key
      value
    }
  }
`;
export const Wallet_Balance_Query = gql`
  query WalletBalance {
    viewWallet {
      id
      userId
      balance
    }
  }
`;

export const Wallet_History_Query = gql`
  query WalletHistory($page: Int, $limit: Int) {
    viewWallet {
      walletTransactions(page: $page, limit: $limit) {
        metadata {
          totalCount
          currentPage
          limitValue
          totalPages
        }
        collection {
          id
          amount
          bookingNo
          transactionNo
          transactionDate
          transactionType
          isIncoming
          walletSource {
            id
            name
            img
          }
          currentBalance
        }
      }
    }
  }
`;

export const Get_Profile = gql`
  query Profile {
    profile {
      isCustomerProfileCompleted
      customerProfile {
        status
        isYakeenVerified
        yakeenResponse
        yakeenTriesLeft
        alfursanMembership {
          membershipId
        }
      }
    }
  }
`;
