/* eslint-disable @next/next/no-img-element */
/* eslint-disable react/jsx-key */
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { Grid } from "@material-ui/core";
import { useRouter } from "next/router";
import "moment/locale/en-in";
import "moment/locale/ar";

const DIV = styled.div`
text-align:center;
.MuiCard-root {
  border:1px solid #1b1b1b;
  border-radius:12px;
  line-height:1.3;
}
h4{
    color: var(--color-3);
    font-weight:bold;
    margin-bottom:20px !important ;
}
h6{
    color:var(--color-11)
}
.verified{
    margin: 50px 0px ;
}
.privacy-link{
  color:#7AB3C5;
}
.actions{
  alig-self:center !important;
}
.delete-btn{
  color:#F85959;
  border:none;
  background:none;
  FA9C3F
}
.edit-btn{
  color:#FA9C3F;
  border:none;
  background:none;
  
}
.add-btn{
  border:1px solid #000;
  border-radius:8px;
  background:none;
  padding:5px;
}
.request-title{
  font-weight:bold;
}
  button.order {
    width: 100%;
    padding: 10px 0px;
    border: none;
    box-shadow: none;
    border-radius: var(--radius-3);
    background-color: var(--color-3);
    color: var(--color-4);
    font-weight: 500;
  }
  .title {
    color: var(--color-3);
  }
  .content {
    padding: 20px 0px;
  }
  .btn-link {
    color: #000;
  }
`;

export default function Success() {
  const { t } = useTranslation();
  const router = useRouter();

  return (
    <div>
      <DIV>
        <Grid
          container
          direction="row"
          justifyContent="center"
          alignItems="center"
          xs={12}
          md={12}
          className="form-wrapper verified"
        >
          <Grid item xs={12} md={12}>
            <img src="/assets/images/verified.svg" alt="verified" />
          </Grid>
          <h4>{t("Request has been received successfully")}</h4>
          <h6> {t("Your request will be answered as soon as possible")} </h6>
        </Grid>
        <Grid
          container
          direction="row"
          justifyContent="center"
          alignItems="center"
          xs={12}
          md={12}
          className="form-wrapper"
        >
          <Grid item xs={9} md={9}>
            <button
              className="order"
              onClick={() => {
                router.push("my-requests");
              }}
            >
              {t("Request Follow-up")}
            </button>
          </Grid>
          <Grid item xs={8} md={8}>
            <button
              className="btn btn-link w-100"
              onClick={() => router.push("carwah-business")}
            >
              {t("close.success")}
            </button>
          </Grid>
        </Grid>
      </DIV>
    </div>
  );
}
