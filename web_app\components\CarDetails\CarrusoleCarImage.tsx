/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @next/next/no-img-element */
import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";

import SwiperCore, {
  Navigation,
  Pagination,
  Scrollbar,
  A11y,
  Autoplay,
} from "swiper";
import styled from "styled-components";
import "swiper/swiper-bundle.css";

SwiperCore.use([Navigation, Pagination, Scrollbar, A11y]);
const Div = styled.div`
  .swiper-button-next,
  .swiper-button-prev {
    visibility: hidden;
  }
`;

export default function CarusoleCarImage(props) {
  return (
    <Div className="swiper-wrap car-swiper" style={{ position: "relative" }}>
      <Swiper
        {...props}
        style={{ direction: "ltr" }}
        rewind="true"
        simulateTouch={true}
        modules={[Pagination, Autoplay]}
        grabCursor={true}
        pagination={{ clickable: true }}
        loop={true}
        autoplay={{
          delay: 5000,
          disableOnInteraction: true,
        }}
      >
        {[...props?.slides, ...props?.slides]?.map((slide: any) => {
          return (
            <SwiperSlide key={Math.random()}>
              {
                <img
                  style={{
                    maxHeight: "190px",
                    width: "100%",
                    objectFit: "contain",
                  }}
                  src={slide}
                />
              }
            </SwiperSlide>
          );
        })}
      </Swiper>
      {/* Arrows styles are applied to the wrapper that holds the swiper */}
    </Div>
  );
}
