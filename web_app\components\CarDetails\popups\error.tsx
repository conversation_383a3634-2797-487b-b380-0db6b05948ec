import { useTranslation } from "react-i18next";
import MessagePopup from "./messagePopup";

import styled from "styled-components";
export const DIV = styled.div`
  .button {
    background: var(--color-3);
    padding: 15px 20px 15px 20px;
    cursor: pointer;
    color: #ffffff;
    border-radius: var(--radius-3);
    font-weight: bold;
  }
  .btn-back {
    color: #000;
    padding: 10px 20px 15px 20px;
    cursor: pointer;
  }
`;
export default function ErrorPopup({
  isOpen,
  setIsOpen,
  ErrorMessage,
  buttonText,
  buttonClickHandler,
}) {
  const { t } = useTranslation();

  if (isOpen) {
    return (
      <MessagePopup
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        body={
          <div className="text-center">
            <div>{ErrorMessage}</div>
            <DIV className="d-flex justify-content-center mt-3">
              <div
                className="button w-100 text-center"
                onClick={() => {
                  setIsOpen(false);
                  buttonClickHandler && buttonClickHandler();
                }}
              >
                {buttonText || (t("ok") as string)}
              </div>
            </DIV>
          </div>
        }
        title={""}
      />
    );
  }
  return null;
}
