/* eslint-disable @next/next/no-img-element */
export default function Counter({ counter, setCounter }) {
  return (
    <div className="d-flex align-align-items-center justify-content-center gap-10px counter">
      <img
        className="cursor-pointer"
        src="/assets/images/minus-dark.svg"
        alt="icon"
        onClick={() => setCounter(counter - 1)}
      />
      <span className="m-0">{counter}</span>
      <img
        className="cursor-pointer"
        src="/assets/images/plus-dark.svg"
        alt="icon"
        onClick={() => setCounter(counter + 1)}
      />
    </div>
  );
}
