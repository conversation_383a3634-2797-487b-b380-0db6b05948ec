import { Grid } from "@material-ui/core";
import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Card from "./card";

const Div = styled.div`
  img {
    width: 120px;
  }
  > div > div {
    margin-bottom: 20px;
  }
  .white-card {
    height: 100%;
    margin: 0 10px;
    > div {
      padding: 35px 40px;
    }
    h6 {
      font-weight: 600;
      font-size: 1.3rem;
    }
    h6,
    p {
      text-align: center;
    }
    p {
      margin-top: 10px;
      font-size: 1.05rem;
    }
  }
`;

export default function Cards() {
  const { t } = useTranslation();
  const cardsList = [
    {
      img: `/assets/images/whyChoose1.svg`,
      title: t("Competitive Prices"),
      description: t(
        "Carwah provides the most competitive prices with our network of allies"
      ),
    },
    {
      img: `/assets/images/whyChoose2.svg`,
      title: t("More Locations"),
      description: t(
        "Our ally's location is spread across the Kingdom to serve you better"
      ),
    },
    {
      img: `/assets/images/whyChoose3.svg`,
      title: t("Variety of Options"),
      description: t(
        "Carwah provides many options and services on one easy-to-use platform"
      ),
    },
    {
      img: `/assets/images/whyChoose4.svg`,
      title: t("Quality Service"),
      description: t(
        "We offer dedicated 24/7 customer service to ensure top-notch assistance and support for all your needs"
      ),
    },
  ];

  return (
    <Div>
      <Grid container wrap="wrap">
        {cardsList.map((cardData, index) => {
          return (
            <Grid item xs={12} md={6} lg={3} key={index}>
              <Card
                img={cardData.img}
                title={cardData.title}
                description={cardData.description}
              />
            </Grid>
          );
        })}
      </Grid>
    </Div>
  );
}
