/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { useMutation } from "@apollo/client";
import Popup from "components/shared/popup";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";

import { defaultTime } from "utilities/enums";
import { calendarDaysConvert } from "utilities/helpers";
import {
  setCreatingOnlineExtensionRequset,
  setIsExtensionRequestCreatedSuccessfully,
  setExtensionTotalRemainingPrice,
} from "store/extensions/action";
import {
  Create_Extension_Request,
  Update_Extension_Request,
  Extension_Pay_By_Wallet,
} from "gql/mutations/extensions";
import RequestLoader from "components/shared/requestLoader";
import { useRouter } from "next/router";
import PaymentPopup from "../payment";
import ErrorPopup from "../error";
import InstallmentExtensionRequestModal from "./InstallmentExtensionModal";
import RegularExtension from "./RegularExtension";
import { useSnackbar } from "notistack";
import UnseenExtensionPopUp from "components/shared/unSeenExtensionPopup";

export default function ExtensionRequestModal({
  isOpen,
  setIsOpen,
  setExtensionId,
  extensionDetails,
  refetchRentalDetails,
  withInstallment,
}) {
  //Hooks
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const router = useRouter();
  const { bookingId, car } = router.query || {};
  const [createRentalDateExtensionRequest, { loading: creatingRequest }] =
    useMutation(Create_Extension_Request, { errorPolicy: "all" });
  const [updateRentalDateExtensionRequest, { loading: updatingRequest }] =
    useMutation(Update_Extension_Request, { errorPolicy: "all" });
  const [extensionPayByWallet, { loading: PayWallet }] = useMutation(
    Extension_Pay_By_Wallet,
    { errorPolicy: "all" }
  );

  //State
  const [isToolTipOpen, setIsTooltipOpen] = useState(false);
  const [days, setDays] = useState<any>();
  const [date, setDate] = useState<any>();
  const [time, setTime] = useState(defaultTime);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
  const [totalRemainingPrice, setTotalRemainingPrice] = useState(0);
  const [walletAmount, setWalletAmount] = useState(0);
  const [isWalletCoversPayment, setIsWalletCoversPayment] = useState(false);
  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false);

  //Store
  const { paymentMethod } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const rentalData = useSelector((state: RootStateOrAny) => state.booking);
  const { dropOffDate, lastConfirmedExtensionRequest, installments } =
    rentalData.rentalDetails || {};

  const { pay_with_wallet } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};
  const { balance } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};

  //Varaibles
  const showExtendNowBtn = totalRemainingPrice == 0 && !walletAmount;

  function redirectionHandler() {
    return router.push(`car-details?car=${car}&bookingId=${bookingId}`);
  }

  function requestHandler(data: any) {
    async function createMutation() {
      try {
        const res = await createRentalDateExtensionRequest({
          variables: data,
        });
        const { errors, data: _data } = res || {};
        if (errors?.length) {
          const isNotExtendable = errors.find((err: any) => {
            return err?.extensions?.code === "not_extendable_rental";
          });
          if (isNotExtendable) {
            setIsErrorModalOpen(true);
            redirectionHandler();
          }
          errors.map((e) => {
            enqueueSnackbar(e.message, { variant: "error" });
          });
          return;
        }
        const {
          createRentalDateExtensionRequest: {
            status,
            rentalDateExtensionRequest: { id },
          },
        } = _data || {};
        if (status === "success" && withInstallment) {
          return dispatch(setIsExtensionRequestCreatedSuccessfully(true));
        }
        dispatch(setCreatingOnlineExtensionRequset(true));
        dispatch(setExtensionTotalRemainingPrice(totalRemainingPrice));

        if (status === "success") {
          if (showExtendNowBtn) {
            setConfirmationModalOpen(true);
            return;
          }
          if (paymentMethod === "CASH") {
            dispatch(setIsExtensionRequestCreatedSuccessfully(true));
          } else if (paymentMethod != "CASH") {
            if (
              (balance < totalRemainingPrice && pay_with_wallet) ||
              !pay_with_wallet
            ) {
              setExtensionId && setExtensionId(id);
              dispatch(setCreatingOnlineExtensionRequset(true));
            } else {
              if (!walletAmount && pay_with_wallet) {
                dispatch(setIsExtensionRequestCreatedSuccessfully(true));
              }
            }
            refetchRentalDetails();
          }
          dispatch(setCreatingOnlineExtensionRequset(false));
          dispatch(setExtensionTotalRemainingPrice(totalRemainingPrice));
          setIsOpen(false);
          redirectionHandler();
        }
      } catch (e) {
        enqueueSnackbar(e.message, { variant: "error" });
      }
    }
    async function updateMutation() {
      try {
        const res = await updateRentalDateExtensionRequest({
          variables: { ...data, id: extensionDetails.id },
        });
        if (
          res.data &&
          res.data.updateRentalDateExtensionRequest.status === "success"
        ) {
          if (isWalletCoversPayment && pay_with_wallet) {
            const res = await extensionPayByWallet({
              variables: { extensionRequestId: extensionDetails.id },
            });
            dispatch(setIsExtensionRequestCreatedSuccessfully(true));
          } else {
            if (!withInstallment) {
              setExtensionId(extensionDetails.id);
              dispatch(setCreatingOnlineExtensionRequset(true));
            } else {
              dispatch(setIsExtensionRequestCreatedSuccessfully(true));
            }
          }
        } else {
          if (res?.errors?.length) {
            res?.errors.map((e) => {
              enqueueSnackbar(e.message, { variant: "error" });
            });
          }
        }
      } catch (e) {
        enqueueSnackbar(e.message, { variant: "error" });
      }
    }
    if (data) {
      !extensionDetails && createMutation();
      extensionDetails && updateMutation();
    }
  }

  return (
    <>
      <RequestLoader loading={creatingRequest || updatingRequest} />
      <Popup
        maxWidth={"sm"}
        fullWidth
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        title={t("Extension Request")}
        onClose={() => {
          router.push(`car-details?car=${car}&bookingId=${bookingId}`);
        }}
      >
        {!withInstallment ? (
          <RegularExtension
            {...{
              date,
              setDate,
              setIsTooltipOpen,
              calendarDaysConvert,
              t,
              isToolTipOpen,
              days,
              pay_with_wallet,
              paymentMethod,
              dropOffDate,
              lastConfirmedExtensionRequest,
              setTime,
              time,
              setDays,
              requestHandler,
              balance,
              bookingId,
              setIsPaymentModalOpen,
              setTotalRemainingPrice,
              extensionDetails,
              setWalletAmount,
              setIsWalletCoversPayment,
            }}
          />
        ) : (
          <InstallmentExtensionRequestModal
            {...{ requestHandler, bookingId, paymentMethod, extensionDetails }}
          />
        )}
      </Popup>
      {!withInstallment ? (
        <PaymentPopup
          isOpen={isPaymentModalOpen}
          setIsOpen={setIsPaymentModalOpen}
          onClose={() => setIsPaymentModalOpen(false)}
          hideCash={Boolean(
            extensionDetails?.paymentMethod?.toLowerCase() === "online"
          )}
        />
      ) : null}
      <ErrorPopup
        {...{
          isOpen: isErrorModalOpen,
          setIsOpen: setIsErrorModalOpen,
          ErrorMessage: (
            <>
              <img
                src="/assets/images/pendingExtension.svg"
                alt="pending extesnion"
              />
              <h4 className="text-center mb-3">{t("request") as string}</h4>
              <p className="text-center">{t("creating") as string}</p>
            </>
          ),
          buttonText: t("View extension request"),
          buttonClickHandler: () => {
            setIsOpen(false);
          },
        }}
      />
      <UnseenExtensionPopUp
        {...{
          isOpen: confirmationModalOpen,
          setIsOpen: setConfirmationModalOpen,
          _rentalId: bookingId,
          _carId: car,
          cb: () => {
            setIsOpen(false);
          },
        }}
      />
    </>
  );
}
