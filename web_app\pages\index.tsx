/* eslint-disable react-hooks/exhaustive-deps */
import Head from "next/head";
import { useRouter } from "next/router";
import Home from "components/Home";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import {
  setUserPickupAddressAction,
  setUserReturnAddressAction,
} from "store/search/action";
import { useTranslation } from "react-i18next";
function HomePage({ locale }) {
  const router = useRouter();
  const { i18n } = useTranslation();
  const { user_address, areas } =
    useSelector(
      (state: RootStateOrAny) =>
        (
          state as {
            search_data: {
              user_address: { pick_up: string; return: string };
              areas: { list: { enName: string }[] };
            };
          }
        ).search_data
    ) || {};

  const dispatch = useDispatch();

  useEffect(() => {
    //Fetch Riyadh data and set it as default pickup and return
    const Riyadh = areas?.list?.find((i) => i.enName === "Riyadh");
    if (!user_address?.pick_up && Riyadh) {
      dispatch(setUserPickupAddressAction(Riyadh));
    } else if (!user_address?.return && Riyadh) {
      dispatch(setUserReturnAddressAction(Riyadh));
    }
  }, [areas]);

  useEffect(() => {
    if (router?.query?.bannerId) {
      router.push(`/car-search?bannerId=${router.query.bannerId}#car-grid`);
    }
  }, [router?.query?.bannerId]);

  // Check for payment token and redirect to pay-by-link page
  useEffect(() => {
    if (router.isReady && router.query.token) {
      router.push(`/pay-by-link?token=${router.query.token}`);
    }
  }, [router.isReady, router.query.token]);

  return (
    <>
      <Home />
    </>
  );
}

// Replace getStaticProps with getServerSideProps for server-side handling of token parameter
export async function getServerSideProps(context) {
  const { locale } = context;

  return {
    props: {
      locale,
    },
  };
}

export default HomePage;
