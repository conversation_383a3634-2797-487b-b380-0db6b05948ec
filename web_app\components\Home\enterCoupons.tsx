/* eslint-disable react-hooks/exhaustive-deps */
import { useQuery } from "@apollo/client";
import { Container, Grid } from "@material-ui/core";
import { Cancel, CheckCircle } from "@material-ui/icons";
import Popup from "components/shared/popup";
import RequestLoader from "components/shared/requestLoader";
import { COUPON_AVAILABILITY_QUERY } from "gql/queries/coupons";
import i18n from "localization";
import moment from "moment";
import PageLoader from "next/dist/client/page-loader";
import Link from "next/link";
import { useSnackbar } from "notistack";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { setCouponData } from "store/coupons/action";
import styled from "styled-components";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  padding-top: 70px;
  @media (max-width: 960px) {
    padding-top: 30px !important;
  }
  .coupon-wrap {
    box-shadow: 10px 10px 15px 2px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
    background-color: var(--color-4);
    padding: 20px;
    border-radius: var(--radius-3);
    input {
      border: none;
      background: #f9f9f9b8;
      padding: 14px;
      color: var(--color-17);
      border-radius: var(--radius-3);
    }
    button {
      border: none;
      background: none;
      font-weight: bold;
      &:focus {
        outline: 0;
      }
    }
  }
  .actions {
    button {
      background: var(--color-3);
      color: var(--color-4);
      border-radius: var(--radius-3);
      padding: 10px;
    }
    span {
      text-decoration: underline;
      color: var(--color-3);
      cursor: pointer;
    }
    @media (max-width: 960px) {
      flex-direction: column-reverse;
      justify-content: space-between;
      &,
      button {
        width: 100%;
      }
    }
  }
  @media (max-width: 960px) {
    .remove {
      position: absolute;
      top: 0;
      right: ${(props: any) => (props.lang === "en" ? 0 : "")};
      left: ${(props: any) => (props.lang === "ar" ? 0 : "")};
      width: fit-content;
    }
    form {
      flex-direction: column;
      flex-wrap: wrap;
      > div {
        width: 100%;
      }
    }
  }

  .section-title {
    text-align: right;
    margin-bottom: 30px;
    z-index: 1;

    h4 {
      font-size: 22px;
      font-weight: bold;
      line-height: 1.2;
      margin: 0;
      color: #333;
      font-weight: 400;
    }

    .highlight {
      /* color: var(--color-2); */
      display: block;
      font-weight: bold;
      margin-top: 5px;
      position: relative;

      &:after {
        content: "";
        position: absolute;
        bottom: -15px;
        right: ${(props) => (props.lang === "ar" ? "0" : "auto")};
        left: ${(props) => (props.lang === "ar" ? "auto" : "0")};
        width: 20px;
        border-radius: 10px;
        height: 4px;
        background-color: var(--color-2);
      }
    }
  }
  padding-bottom: 70px;
  .entered-coupon-actions {
    display: flex;
    gap: 30px;
    button {
      background: var(--color-2);
      color: var(--color-4);
      border-radius: 30px;
      box-shadow: unset;
      border: none;
      font-weight: bold !important;
      padding: ${(props: any) =>
        props.lang === "ar" ? "10px 40px 15px 40px" : "15px 40px"};
    }
    span {
      color: black;
      font-weight: 600;
      font-size: 1.1rem;
    }
  }
`;
export default function EnterCoupons() {
  //Hooks
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  //Ref
  const inputRef = useRef<HTMLInputElement>();

  //Store
  const { coupon_data } =
    useSelector((state: RootStateOrAny) => state?.coupons) || {};
  const {
    id,
    code,
    localizedPaymentMethod,
    expireAt,
    generalTerms,
    areas,
    maxLimitValue,
    minRentPrice,
    numOfUsagesPerUser,
    discountTypeKey,
    numOfDays,
    discountValue,
  } = coupon_data || {};

  //State
  const [disabled, setDisabled] = useState(true);
  const [couponCode, setCouponCode] = useState<string>();
  const [showModal, setShowModal] = useState(false);

  //gql
  const { data, error, loading } = useQuery(COUPON_AVAILABILITY_QUERY, {
    variables: { couponCode: couponCode || code },
    skip: !couponCode && !code,
    fetchPolicy: "no-cache",
    nextFetchPolicy: "no-cache",
    errorPolicy: "all",
  });

  useEffect(() => {
    const { message, graphQLErrors } = error || {};
    if (graphQLErrors?.[0]?.extensions?.code === "INVALID_COUPON") {
      dispatch(setCouponData("invalid"));
    } else if (message && message !== "Invalid coupon") {
      enqueueSnackbar(message, { variant: "error" });
    }
    if (data) {
      dispatch(setCouponData(data?.couponAvailability));
    }
  }, [loading]);

  return (
    <>
      <Container className="my-3 ">
        <Div lang={i18n.language}>
          <Grid container alignItems="center" justifyContent="space-around">
            <Grid item xs={12} md={2}>
              <div className="section-title">
                <h4>
                  {t("Have a coupon?").toString()}
                  <span className="highlight">{t("discount").toString()}</span>
                </h4>
              </div>
            </Grid>
            <Grid item xs={12} md={10} container justifyContent="center">
              <Grid item xs={12} md={9}>
                <div className="w-100 coupon-wrap">
                  <form
                    onSubmit={(e) => e.preventDefault()}
                    className="w-100 d-flex gap-20px align-items-center justify-content-between position-relative"
                  >
                    {!coupon_data ? (
                      <input
                        name="code"
                        placeholder={t("Add coupon here")}
                        className="w-100 text-uppercase"
                        ref={inputRef}
                        onChange={(e) => {
                          if (e.target.value) {
                            setDisabled(false);
                          } else {
                            setDisabled(true);
                          }
                        }}
                      />
                    ) : coupon_data === "invalid" ? (
                      <div className="bold d-flex gap-5px align-items-center">
                        <Cancel className="color-9" />
                        <span className="color-9">
                          {t("Invalid discount coupon") as string}
                        </span>
                      </div>
                    ) : (
                      <div className=" d-flex gap-5px align-items-center">
                        <CheckCircle style={{ color: "#7edb26" }} />
                        <span style={{ color: "#7edb26" }}>
                          {t("Coupon") as string}{" "}
                          <span className="bold">{code}</span>
                        </span>
                      </div>
                    )}
                    <div
                      className="d-flex gap-20px justify-content-between "
                      style={{ flexWrap: "wrap" }}
                    >
                      {coupon_data ? (
                        <button
                          className="color-9 remove"
                          onClick={() => {
                            dispatch(setCouponData());
                            setCouponCode("");
                            setDisabled(true);
                          }}
                        >
                          {t("Remove") as string}
                        </button>
                      ) : (
                        <button
                          className="color-2"
                          // disabled={disabled}
                          style={{
                            pointerEvents: disabled ? "none" : "auto",
                          }}
                          onClick={() => {
                            setCouponCode(
                              inputRef?.current && inputRef.current.value
                            );
                          }}
                        >
                          {t("Apply") as string}
                        </button>
                      )}
                    </div>
                  </form>
                </div>
                {coupon_data && coupon_data !== "invalid" ? (
                  <div className="entered-coupon-actions d-flex gap-10px justify-content-start mt-4 align-items-center actions w-100">
                    <Link href="/car-search" passHref prefetch={false}>
                      <button>{t("View car list") as string}</button>
                    </Link>
                    <span onClick={() => setShowModal(true)}>
                      {t("Coupon usage Terms") as string}
                    </span>
                  </div>
                ) : null}
              </Grid>
            </Grid>
          </Grid>
        </Div>
        <RequestLoader loading={loading} />
      </Container>
      {showModal ? (
        <Popup
          isOpen={showModal}
          setIsOpen={setShowModal}
          title={t("Coupon usage Terms")}
        >
          {[
            {
              id: 1,
              title: t("Allowed payment method"),
              value: localizedPaymentMethod || "",
            },
            {
              id: 2,
              title: t("Coupon Expiry Date"),
              value: moment(expireAt).format("DD/MM/YYYY") || "",
              hide: !expireAt,
            },
            {
              id: 3,
              title: t("Allowed cities"),
              value: areas?.length
                ? areas.map((i, index) => {
                    return (
                      <span key={i.id}>
                        {`${index != 0 ? "/" : ""} ${i.name} `}{" "}
                      </span>
                    );
                  })
                : t("All Cities"),
            },
            {
              id: 4,
              title: t("No. of coupon usage"),
              value: `${
                numOfUsagesPerUser == 1
                  ? t("Only one time")
                  : numOfUsagesPerUser == 2
                  ? t("Two times")
                  : numOfUsagesPerUser > 2 && numOfUsagesPerUser < 11
                  ? `${numOfUsagesPerUser} ${t("times")}`
                  : `${numOfUsagesPerUser} ${t("time")}`
              }`,
            },
            {
              id: 5,
              title: t("Max. limit value"),
              value: (
                <div className="d-flex gap-5px">
                  <span style={{ order: 1 }}>
                    <RiyalSymbol />
                  </span>
                  <span style={{ order: 0 }}>
                    {maxLimitValue ||
                      (discountTypeKey === "fixed_value" ? discountValue : 0)}
                  </span>
                </div>
              ),
              hide: !maxLimitValue && discountTypeKey !== "fixed_value",
            },
            {
              id: 6,
              title: t("Min. rental value"),
              value: (
                <div className="d-flex gap-5px">
                  <span style={{ order: 1 }}>
                    <RiyalSymbol />
                  </span>
                  <span style={{ order: 0 }}>{minRentPrice}</span>
                </div>
              ),
            },
            {
              id: 7,
              title: t("Service"),
              hide:
                discountTypeKey === "percentage" ||
                discountTypeKey === "fixed_value"
                  ? true
                  : false,
              value: `${t(discountTypeKey)}
                ${
                  discountTypeKey === "free_days"
                    ? numOfDays === 1
                      ? `(${t("One day")})`
                      : numOfDays === 2
                      ? `(${t("Two days")})`
                      : numOfDays > 2 && numOfDays < 11
                      ? `(${numOfDays} ${t("days")})`
                      : `(${numOfDays} ${t("day")})`
                    : ""
                }
              `,
            },
            {
              id: "generalTerms",
              title: t("General terms"),
              value: generalTerms
                ?.split("- ")
                ?.slice(1, generalTerms.length)
                ?.map((item, index) => {
                  return item ? <div key={index}>- {item}</div> : "";
                }),
            },
          ].map((item) => {
            if (item?.hide) return;
            return (
              <div
                key={item.id}
                className={`${
                  item.id !== "generalTerms"
                    ? "d-flex justify-content-between align-items-center my-1 gap-10px"
                    : "d-flex flex-column justify-content-start mt-3 mb-5"
                }`}
              >
                <h6 className="bold">{item.title}</h6>
                <div
                  className={`text-align-localized`}
                  style={{ direction: "rtl" }}
                >
                  <div
                    style={
                      item.id == 3
                        ? {
                            maxHeight: "65px",
                            overflowY: "auto",
                            lineHeight: "normal",
                          }
                        : {}
                    }
                  >
                    {item.value}
                  </div>
                </div>
              </div>
            );
          })}
          <div
            className="radius-3 color-4 pt-3 pb-3 cursor-pointer w-25 text-center m-auto"
            style={{
              background: "var(--color-3)",
            }}
            onClick={() => setShowModal(false)}
          >
            {t("Done.well") as string}
          </div>
        </Popup>
      ) : null}
    </>
  );
}
