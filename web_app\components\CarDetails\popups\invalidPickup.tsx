import moment from "moment";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import MessagePopup from "./messagePopup";
import styled from "styled-components";
export const DIV = styled.div`
  .button {
    color: var(--color-3);
    padding: 15px 20px 15px 20px;
    cursor: pointer;
    background: #f2f8fa;
  }
  .btn-back {
    color: #000;
    padding: 10px 20px 15px 20px;
    cursor: pointer;
  }
`;

export default function InvalidPickupPopup({
  isOpen,
  setIsOpen,
  actionFunction,
  setCalendarTooltipOpen,
}) {
  const { t } = useTranslation();
  const { working_hours } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const { date_time } = useSelector(
    (state: RootStateOrAny) => state.search_data
  );

  const pickUpDayIndex = moment(date_time?.pickUpDate, "DD/MMYYYY").day();
  const day = working_hours?.find((i) => i?.[0].weekDay == pickUpDayIndex);

  if (isOpen) {
    return (
      <MessagePopup
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        body={
          <>
            {day?.length ? (
              <>
                <p>{`${t("Available time for pickup is")} ${
                  date_time?.pickUpDate
                } `}</p>
                {day?.map((singleDayObj) => {
                  return (
                    <div key={singleDayObj.id} className="d-flex gap-15px">
                      <div>
                        <span className="color-2 mx-1">
                          {t("From") as string}
                        </span>
                        {singleDayObj.startTime}
                      </div>
                      <div>
                        <span className="color-2 mx-1">
                          {t("To") as string}
                        </span>
                        {singleDayObj.endTime}
                      </div>
                    </div>
                  );
                })}
              </>
            ) : (
              <p>{`${date_time?.pickUpDate} ${t(
                "the branch closed, please pick-up another time"
              )}`}</p>
            )}
            <DIV className="d-flex justify-content-center mt-3 w-100 ">
              <div
                className="button radius-1"
                onClick={(e) => {
                  e.stopPropagation();
                  actionFunction && actionFunction();
                  setIsOpen(false);
                  setTimeout(() => {
                    window.scrollTo(0, 0);
                  });
                  setCalendarTooltipOpen(true);
                }}
              >
                {t("View") as string}
              </div>
            </DIV>
          </>
        }
        title=""
      />
    );
  }
  return null;
}
