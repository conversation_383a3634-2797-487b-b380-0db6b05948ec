import { Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Item from "./serviceItem";

const Div = styled.div`
  .section-fancy-title {
    margin-bottom: 30px;
  }
`;

export default function Services() {
  const { t } = useTranslation();

  const itemsMockData = [
    {
      img: "/assets/images/about/item-1.svg",
      number: 1,
      title: t("Bidding"),
      description: t(
        "Get ready for an amazing deal! Our latest product at Carwah is both exclusive and budget-friendly, and it's designed to meet our customers' urgent needs with lightning speed. Stay tuned for updates on its release!"
      ),
    },
    {
      img: "/assets/images/about/item-2.svg",
      number: 2,
      title: t("Karam Carwah"),
      description: t(
        "Loyalty program for our dear customers who can take advantage of their bookings by collecting points and transferring them to a balance in their wallets."
      ),
    },
    {
      img: "/assets/images/about/item-3.svg",
      number: 3,
      title: t("Carwah Lease"),
      description: t(
        "Is a service designed to meet the long-term needs of individuals wishing to rent carsoffering special offers and additional services."
      ),
    },
    {
      img: "/assets/images/about/item-4.svg",
      number: 4,
      title: t("Carwah Business"),
      description: t(
        "Is a program that targets sectors of all kinds government, private and charitable to meet their needs and facilitate their leasing operations."
      ),
    },
  ];

  return (
    <Div>
      <div className="section-fancy-title">
        <h4>
          <span className="bold">{t("Services") as string}</span>{" "}
          {t("And Products") as string}
        </h4>
      </div>
      <Grid container spacing={4}>
        {itemsMockData.map((item, index) => {
          return (
            <Grid key={index} item xs={12} sm={6} md={6}>
              <Item
                data={{
                  img: item.img,
                  number: item.number,
                  title: item.title,
                  description: item.description,
                }}
              />
            </Grid>
          );
        })}
      </Grid>
    </Div>
  );
}
