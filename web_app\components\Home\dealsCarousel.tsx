/* eslint-disable react/jsx-key */
import React, { useEffect, useLayoutEffect } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Swiper from "components/shared/carousel";
import CarCard from "components/shared/carCard";

const Div = styled.div`
  .swiper-button-next {
    @media (max-width: 1024px) {
      right: -15px;
    }
    @media (min-width: 1025px) {
      right: -50px;
    }
  }
  .swiper-button-prev {
    @media (max-width: 1024px) {
      left: -10px;
    }
    @media (min-width: 1025px) {
      left: -50px;
    }
  }
  .swiper-button-prev,
  .swiper-button-next {
    color: var(--color-4);
    width: 35px;
    &:after {
      font-size: 18px;
      transform: ${(props) =>
        props.language === "en"
          ? "translateX(-17px)"
          : (props) => (props.language === "ar" ? "translateX(17px)" : null)};
    }
    &:before {
      content: "";
      width: 100%;
      height: calc(100% * 5);
      opacity: 1;
      background-color: var(--color-3);
      border-radius: var(--radius-1);
    }
  }
  @media (max-width: 900px) {
    margin-top: 100px;
  }

  .swiper-wrap {
    .swiper-container {
      padding: 15px 0px;
    }
  }
`;

export default function DealsCarousel() {
  const { t, i18n } = useTranslation();
  useEffect(() => {
    let clean = false;
    if (!clean) {
      // Moved next and prev arrows outside of swiper container for styling
      const next = document.querySelector(
        "#deals-carousel .swiper-button-next"
      );
      const prev = document.querySelector(
        "#deals-carousel .swiper-button-prev"
      );
      const wrap = document.querySelector("#deals-carousel .swiper-wrap");
      wrap.appendChild(next);
      wrap.appendChild(prev);
    }
    return (clean = true);
  }, []);

  return (
    <Div id="deals-carousel" language={i18n.language}>
      <Swiper
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 25,
          },
          900: {
            slidesPerView: 1,
            spaceBetween: 25,
          },
          1024: {
            slidesPerView: 3,
            spaceBetween: 25,
          },
        }}
        navigation
        // pagination={{ clickable: true }}
        // scrollbar={{ draggable: true }}
        onSwiper={(swiper) => null}
        onSlideChange={() => null}
        // loop
        slides={[
          <CarCard
            deal={{
              text: t("Price Discount"),
              icon: `/assets/images/discount.svg`,
              description: t("20% Rental Price Discount"),
            }}
            car={{
              img: `/assets/images/accent.png`,
              model: t("Hyundai Accent"),
              year: 2021,
              // similarType: t("Sedan"),
              location: { city: t("Riyadh"), district: t("Alyasamin") },
              price: { before: 142, after: 119 },
            }}
          />,
          <CarCard
            deal={{
              text: t("Coupon Code"),
              icon: `/assets/images/coupon.svg`,
              description: t("Use Coupon ( Carwah ) for 20% off up to 50 SR"),
            }}
            car={{
              img: `/assets/images/camry.png`,
              model: t("Hyundai Camry"),
              year: 2020,
              // similarType: t("Sedan"),
              location: { city: t("Riyadh"), district: t("Alyasamin") },
              price: { before: 310, after: 245 },
            }}
          />,
          <CarCard
            deal={{
              text: t("Coupon Code"),
              icon: `/assets/images/coupon.svg`,
              description: t("Use Coupon ( Carwah ) for 20% off up to 50 SR"),
            }}
            car={{
              img: `/assets/images/kona.png`,
              model: t("Hyundai Kona"),
              year: 2021,
              // similarType: t("Sedan"),
              location: { city: t("Riyadh"), district: t("Alyasamin") },
              price: { before: 205, after: 175 },
            }}
          />,
        ]}
      />
    </Div>
  );
}
