import { useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootStateOrAny } from "react-redux";
import { useTranslation } from "react-i18next";
import { DateObject } from "react-multi-date-picker";
import moment from "moment";
import { setDateTimeAction } from "store/search/action";

const useCustomHook = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();

  const { pickUpDate, pickup_time, return_time } = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );

  const { availableAfterDays } =
    useSelector(
      (state: RootStateOrAny) => state.cars?.car_data?.ownCarDetail
    ) || {};

  const [isOpen, setIsOpen] = useState<boolean>();

  function generateAvailabilityDates() {
    const minDate = new DateObject().add(availableAfterDays, "days");
    const maxDate = new DateObject().add(availableAfterDays + 9, "days");
    const dateDiff = moment(pickUpDate, "DD/MM/YYYY").diff(
      moment(minDate.format("DD/MM/YYYY"), "DD/MM/YYYY"),
      "days"
    );
    if (dateDiff < 0) {
      dispatch(
        setDateTimeAction({
          reservation_days: [minDate, maxDate],
          pickUpDate: minDate.format("DD/MM/YYYY"),
          dropOffDate: maxDate.format("DD/MM/YYYY"),
          selected_days_count: 10,
          pickup_time,
          return_time,
        })
      );
    }

    return {
      minDate,
      maxDate,
    };
  }

  return {
    t,
    i18n,
    pickUpDate,
    pickup_time,
    isOpen,
    setIsOpen,
    generateAvailabilityDates,
  };
};

export default useCustomHook;
