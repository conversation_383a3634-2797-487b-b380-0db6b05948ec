/* eslint-disable @next/next/no-img-element */
import Popup from "components/shared/popup";
import { memo, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import TermsConditions from "./TermsConditions";
import { Button } from "@material-ui/core";
import { useDispatch, useSelector } from "react-redux";
import {
  saveMembershipId,
  verifyFursanMembershipId,
} from "store/loyality/action";
import { modValidator } from "utilities/helpers";
import { useMutation } from "@apollo/client";
import { Alfursan_Verify_Profile } from "gql/mutations/loyality";
import Verified from "./Verified";
import RequestLoader from "components/shared/requestLoader";
import { TUserReducer } from "store/user/reducer";
import { useRouter } from "next/router";
import { useSnackbar } from "notistack";

const Div = styled.div`
  .card {
    background-color: #fff;
    border-radius: var(--radius-2);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 30px;
    margin-top: 40px;
    border-color: transparent;
    form {
      width: 100%;
    }
    h4 {
      margin-bottom: 7px;
    }
    h6 {
      color: #bbbbbb;
    }
    .memebership-input {
      border: solid 1px #e1e1e1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      margin-top: 20px;
      border-radius: var(--radius-2);

      > div {
        color: rgba(42, 41, 47, 0.5);
        background: #f5f5f5;
        font-weight: bold;
        padding: 15px 0;
        text-align: center;
        border-radius: var(--radius-2);
      }
      input {
        border: none;
        border-radius: var(--radius-2);
        padding: 0 10px;
        font-weight: bold;
      }
    }
    .terms-conditions {
      width: 100%;
      text-align: center;
      margin-top: 15px;
      font-size: 15px;
      span {
        margin: 0 2px;
      }
      div:last-child span {
        color: var(--color-3);
        cursor: pointer;
      }
    }
  }
  .buttons {
    margin-top: 20px;
    width: 100%;
    button {
      width: 100%;
      padding: 10px 0;
      border: var(--radius-5);
      font-weight: bold;
      &:first-child {
        background-color: var(--color-3);
        color: white;
      }
    }
  }
`;

function VerifyMembershipModal({
  logo,
  isOpen,
  setIsOpen,
  title,
  subTitle,
  inputLabel,
  getSettingValue,
}) {
  const { t } = useTranslation();
  const router = useRouter();
  const { enqueueSnackbar } = useSnackbar();
  const [value, setValue] = useState("");
  const [isTermsConditionsOpen, setIsTermsConditionsOpen] = useState(false);
  const dispatch = useDispatch();
  const [errorMessage, setErrorMessage] = useState("");
  const [verifyAlfrsan, { loading, error }] = useMutation(
    Alfursan_Verify_Profile,
    {
      errorPolicy: "all",
    }
  );
  const { profile } =
    useSelector((state: { user: TUserReducer }) => state.user) || {};

  const { alfursanMembership } = profile?.customerProfile || {};

  useEffect(() => {
    if (alfursanMembership?.membershipId)
      setValue(alfursanMembership?.membershipId);
  }, [alfursanMembership?.membershipId]);

  function handleSubmit(e, values: { membership: string }) {
    e.preventDefault();
    setErrorMessage("");
    const { membership } = values || {};
    if (
      Boolean(
        membership.startsWith("0") ||
          (membership.length !== 8 && membership.length !== 10)
      )
    ) {
      return setErrorMessage(
        t("ID mustn't start with 0, and must consist of 8 or 10 digits")
      );
    }

    if (membership.length === 8 && !Boolean(modValidator(membership))) {
      return setErrorMessage(t("Invalid ID"));
    }
    verifyAlfrsan({
      variables: {
        input: {
          membership,
        },
      },
    }).then((res) => {
      const { data, errors } = res || {};
      if (errors?.length) {
        const isProfileIncomplete = errors.find(
          (err) => err.extensions.code === "basic_profile_incomplete"
        );
        isProfileIncomplete
          ? (() => {
              router.push(`/my-account?route=${router.asPath}`);
              enqueueSnackbar(`Please complete your profile first`, {
                variant: "warning",
              });
            })()
          : null;
        return;
      }
      if (data?.alfursanVerifyProfile?.success) {
        dispatch(saveMembershipId(membership));
        dispatch(verifyFursanMembershipId(true));
      } else {
        setErrorMessage(t("Membership ID is inactive in Al Fursan"));
      }
    });
  }

  return (
    <>
      <RequestLoader {...{ loading }} />
      <Popup
        {...{
          isOpen,
          setIsOpen: () => {
            setErrorMessage("");
            setIsOpen(false);
          },
          backgroundColor: "#F4F5F7",
        }}
      >
        <Div>
          <div className="text-center">
            <img src={logo} alt="logo" width={180} />
          </div>
          <div className="card">
            <form onSubmit={(e) => handleSubmit(e, { membership: value })}>
              <h4 className="bold">{title}</h4>
              <h6>{subTitle}</h6>
              <div className="memebership-input">
                <div>{inputLabel}</div>
                <input
                  value={value}
                  onChange={(e) => {
                    setErrorMessage("");
                    setValue(e.target.value);
                  }}
                  className="hide-arrows"
                  type="number"
                />
              </div>
              {errorMessage ? (
                <p className="color-9 bold font-14px mt-2 text-center">
                  {errorMessage}
                </p>
              ) : null}
              <div className="terms-conditions">
                <div>
                  <span>
                    {
                      t(
                        "By continuing you indicate your acceptance of the"
                      ) as string
                    }
                  </span>
                </div>
                <div>
                  <span
                    onClick={() => {
                      setIsTermsConditionsOpen(!isTermsConditionsOpen);
                    }}
                  >
                    {t("Terms & Conditions") as string}
                  </span>
                </div>
              </div>
              <div className="buttons">
                <Button
                  // onClick={}
                  disabled={Boolean(!value || errorMessage)}
                  type="submit"
                >
                  {t("Confirm") as string}
                </Button>
              </div>
            </form>
          </div>
        </Div>
      </Popup>

      <TermsConditions
        {...{
          logo,
          isOpen: isTermsConditionsOpen,
          setIsOpen: setIsTermsConditionsOpen,
          getSettingValue,
        }}
      />
      <Verified />
    </>
  );
}

export default memo(VerifyMembershipModal);
