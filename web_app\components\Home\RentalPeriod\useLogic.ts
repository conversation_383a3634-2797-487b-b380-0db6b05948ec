import { useTranslation } from "react-i18next";
import { DateObject } from "react-multi-date-picker";
import { RootStateOrAny, useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { setPayWithInstallments } from "store/installments/action";
import { setDateTimeAction, setRentalMonthsAction } from "store/search/action";
import { defaultDaysCount } from "utilities/enums";
import { calendarDaysConvert } from "utilities/helpers";

function useLogic() {
  //Hooks
  const { t } = useTranslation();
  //Store
  const { rental_months = 12, date_time } =
    useSelector((state: RootStateOrAny) => state.search_data) || {};
  const dispatch = useDispatch();
  //Functions
  function monthlyDateHandler(monthsNumbers: number) {
    const newReservationDays = new DateObject(
      date_time?.reservation_days?.[0]
    ).add(30 * Number(monthsNumbers), "days");
    dispatch(setRentalMonthsAction(monthsNumbers));
    dispatch(
      setDateTimeAction({
        ...date_time,
        reservation_days: monthsNumbers
          ? [date_time?.reservation_days?.[0], newReservationDays]
          : [new DateObject(), new DateObject().add(3, "days")],
        dropOffDate: monthsNumbers
          ? calendarDaysConvert(newReservationDays)
          : calendarDaysConvert(new DateObject().add(3, "days")),
        selected_days_count: monthsNumbers * 30 || defaultDaysCount,
      })
    );
    if (monthsNumbers >= 2) {
      dispatch(setPayWithInstallments(true));
    } else {
      dispatch(setPayWithInstallments(false));
    }
  }
  const handleSliderChange = (event, newValue) => {
    monthlyDateHandler(newValue);
  };

  function monthLocale(count) {
    if (count === 1) {
      return t("Month");
    } else if (count === 2) {
      return t("2Months");
    } else if (count > 10) {
      return t("Month/s");
    } else {
      return t("Months");
    }
  }
  return {
    t,
    rental_months,
    handleSliderChange,
    monthlyDateHandler,
    monthLocale,
  };
}

export default useLogic;
