/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import styled from "styled-components";
import { Icon } from "@material-ui/core";
import AddIcon from "@material-ui/icons/Add";
import RemoveIcon from "@material-ui/icons/Remove";
import moment from "moment";
import { useRouter } from "next/router";
import { convertToEnglishNumbers } from "utilities/helpers";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { setWorkingHoursModalOpen } from "store/cars/action";
import i18n from "localization";

const Div = styled.div`
  display: flex;
  justify-content: center;
  icon {
    cursor: pointer;
    font-size: 24px;
    transform: translateY(-2px);
  }
  > div {
    display: flex;
    align-items: center;
    > span {
      font-size: 16px;
    }
  }
  .material-icons {
    width: auto;
    height: auto;
    cursor: pointer;
  }
`;

const Select = styled.select`
  padding: 5px 10px;
  border: none;
  border-radius: var(--radius-1);
  border-bottom: solid 1px;
  margin-top: 15px;
  cursor: pointer;
`;

export default function TimePicker({
  time,
  setTime,
  setReturnTime,
  textColor,
  fontSize,
  pickUpShifts,
  dropOffShifts,
  reservationDays,
  isPickUp,
  roundedTime,
  setDisabledApply,
}) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const hours = Number(time.substr(0, 2));
  const minutes = Number(time.substr(3));
  const today = moment().format("YYYY/MM/DD");
  const { pathname } = useRouter();
  const [size, setSize] = useState(1); // Default size is 1
  const handleFocus = () => setSize(5); // Set size to 5 on focus to expand the dropdown
  const handleChange = () => {
    setSize(1); // Collapse the dropdown after selection
    // Additional logic for handling the change can be added here
  };
  const handleBlur = () => setSize(1); // Collapse the dropdown when it loses focus

  const pick_up = reservationDays?.[0];

  function timeHandler(action) {
    const setMinutesHours = () => {
      if (action === "increase") {
        if (minutes === 0) {
          return `${hours < 10 ? `0${hours}` : hours}:30`;
        }
        if (minutes === 30)
          return `${hours + 1 < 10 ? `0${hours + 1}` : hours + 1}:00`;
      }
      if (action === "decrease") {
        if (minutes === 0) {
          return `${hours - 1 < 10 ? `0${hours - 1}` : hours - 1}:30`;
        }
        if (minutes === 30) return `${hours < 10 ? `0${hours}` : hours}:00`;
      }
    };
    setTime(setMinutesHours());
    if (setReturnTime) {
      setReturnTime(setMinutesHours());
    }
  }

  const pickUpShiftsFiltered =
    today ==
    moment(convertToEnglishNumbers(pick_up?.format()), "YYYY/MM/DD")?.format(
      "YYYY/MM/DD"
    )
      ? pickUpShifts?.includes(roundedTime)
        ? pickUpShifts?.slice(pickUpShifts?.indexOf(roundedTime))
        : pickUpShifts?.filter(
            (i) => +i.substr(0, 2) > +roundedTime?.substr(0, 2)
          )
      : pickUpShifts;

  useEffect(() => {
    if (setDisabledApply) {
      if (pickUpShiftsFiltered && !pickUpShiftsFiltered?.length) {
        setDisabledApply(true);
      } else if (pickUpShiftsFiltered?.length) {
        setDisabledApply(false);
      }
    }

    if (pickUpShiftsFiltered?.length && !pickUpShiftsFiltered?.includes(time)) {
      setTime(pickUpShiftsFiltered[0]);
    }
  }, [pickUpShiftsFiltered]);

  if (pickUpShiftsFiltered?.length || dropOffShifts?.length) {
    return (
      <div>
        <Select
          key={time}
          defaultValue={time}
          className="w-100"
          size={size}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={handleChange}
          onClick={(e) => {
            setTime(e.target.value);
            if (setReturnTime) {
              setReturnTime(e.target.value);
            }
          }}
        >
          {(pickUpShiftsFiltered || dropOffShifts).map((item, index) => {
            return (
              <option key={index} value={item}>
                {convertToEnglishNumbers(
                  moment(item, "HH:mm").locale(i18n.language).format("hh:mm A")
                )}
              </option>
            );
          })}
        </Select>
      </div>
    );
  } else if (pathname === "/car-details") {
    return (
      <p
        className=" w-100 mt-2 color-9 font-16px"
        style={{ maxWidth: "112px", width: "112px" }}
      >
        {t("Time is not available, please select another time.") as string}
        <span
          className="text-white text-decoration-underline bold cursor-pointer mx-1 font-13px"
          onClick={() => {
            dispatch(setWorkingHoursModalOpen(true));
          }}
        >
          {t("Working Hours") as string}
        </span>
      </p>
    );
  }

  return (
    <Div id="time-picker" style={{ color: textColor, fontSize }}>
      <Icon
        onClick={() => {
          if (today != pick_up?.format() || !isPickUp) {
            if (hours > 0) {
              timeHandler("decrease");
            }
          } else if (
            hours > 0 &&
            moment(time, "HH:mm").diff(
              moment(roundedTime, "HH:mm"),
              "minutes"
            ) >= 30
          ) {
            timeHandler("decrease");
          }
        }}
      >
        <RemoveIcon />
      </Icon>
      <div>
        <input
          value={
            i18n.language === "ar"
              ? convertToEnglishNumbers(
                  moment(time, "HH:mm").locale("ar").format("hh:mm A")
                )
              : moment(time, "HH:mm").format("hh:mm A")
          }
          type="text"
        />
      </div>
      <Icon
        onClick={() => {
          if (time != "24:00") {
            timeHandler("increase");
          }
        }}
      >
        <AddIcon />
      </Icon>
    </Div>
  );
}
