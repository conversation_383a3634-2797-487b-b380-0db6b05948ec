/* eslint-disable @next/next/no-img-element */
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { Input } from ".";

function DriverLiscenceNo({ control, watch, errors }) {
  const { t } = useTranslation();

  return (
    <div className="mt-4">
      <label className="color-19 text-start w-100 mb-2">
        {t("Driver license number") as string}
        <span className="color-9">*</span>
      </label>
      <Controller
        name={"driverLicense"}
        control={control}
        rules={{
          required: true,
          pattern: /^\d{1,20}$/,
        }}
        render={({ field, fieldState }) => {
          return (
            <Input
              className="hide-arrows"
              type="number"
              value={watch("driverLicense")}
              onChange={(e) => {
                field.onChange(e.target.value);
              }}
            />
          );
        }}
      />
      {errors?.driverLicense?.type === "required" && (
        <p className="color-9">{t("This field is required") as string}</p>
      )}
      {errors?.driverLicense?.type === "pattern" && (
        <p className="color-9">{t("Max. 20 numbers") as string}</p>
      )}
    </div>
  );
}

export default DriverLiscenceNo;
