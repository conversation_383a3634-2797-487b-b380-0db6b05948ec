import { useQuery } from "@apollo/client";
import { ClickAwayListener } from "@material-ui/core";
import ActionButton from "components/CarDetails/RentPayment/ActionButton";
import Tamara from "components/CarDetails/tamara";
import CalendarTooltip from "components/shared/calendarTooltip";
import {
  Rental_Extension_Request_Price,
  Created_Rental_Extension_Request_Price,
} from "gql/queries/extensions";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import { DateObject } from "react-multi-date-picker";
import styled from "styled-components";
import { useSnackbar } from "notistack";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { setCreatingOnlineExtensionRequset } from "store/extensions/action";
import { setPriceTemp } from "store/boooking/action";
import i18n from "localization";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  margin-top: "20px";
  /* padding: 0 40px; */
  #calendar-extension {
    border-radius: var(--radius-3);
    display: flex;
    padding: 5px;
    * {
      cursor: pointer;
    }
    input {
      border: none;
      width: 100%;
      &::placeholder {
        color: var(--color-9);
      }
    }
  }
  .pay {
    border: none;
    background: var(--color-3);
    font-weight: bold;
    color: var(--color-4);
    padding: 12px 0;
    &:hover {
      color: var(--color-3) !important;
      border: solid 1px var(--color-3);
    }
  }
`;

function Wallet({ t, walletAmount }) {
  return (
    <>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          width: "100%",
          fontSize: "12px",
          marginTop: "10px",
          padding: "0 20px",
        }}
      >
        <h6>{`${t("Wallet")}`}</h6>
        <div style={{ color: "green" }} className="price">
          {`- ${walletAmount?.toFixed(2)} `}
          <RiyalSymbol />
        </div>
      </div>
    </>
  );
}

function RegularExtension({
  date,
  setDate,
  requestHandler,
  setIsTooltipOpen,
  calendarDaysConvert,
  isToolTipOpen,
  days,
  pay_with_wallet,
  paymentMethod,
  dropOffDate,
  lastConfirmedExtensionRequest,
  setTime,
  time,
  setDays,
  balance,
  bookingId,
  setIsPaymentModalOpen,
  setTotalRemainingPrice,
  extensionDetails,
  setWalletAmount,
  setIsWalletCoversPayment,
}) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const [variables, setVariables] = useState<any>({});
  const [changed, setChanged] = useState(false);

  const isTamara = useMemo(() => {
    return paymentMethod === "TAMARA" ? true : false;
  }, [paymentMethod]);

  //gql
  const {
    data: rentalExtensionRequestPriceData,
    loading: gettingExtensionPrices,
  } = useQuery(Rental_Extension_Request_Price, {
    variables: {
      ...variables,
      paymentMethod: paymentMethod === "CASH" ? paymentMethod : "ONLINE",
      withWallet: paymentMethod !== "CASH" && pay_with_wallet ? true : false,
    },
    skip: !Object.keys(variables).length || (extensionDetails && !changed),
    errorPolicy: "all",
    onError(error) {
      enqueueSnackbar(error.message, { variant: "error" });
    },
    fetchPolicy: "no-cache",
  });
  const { data: createdRentalExtensionRequestPriceData } = useQuery(
    Created_Rental_Extension_Request_Price,
    {
      variables: {
        id: extensionDetails?.id,
      },
      skip: !extensionDetails,
      errorPolicy: "all",
      onError(error) {
        enqueueSnackbar(error.message, { variant: "error" });
      },
      fetchPolicy: "no-cache",
    }
  );

  const { totalRemainingPrice, extensionDays, walletAmount } =
    (extensionDetails && !changed
      ? createdRentalExtensionRequestPriceData?.rentalDateExtensionRequest
      : rentalExtensionRequestPriceData?.rentalExtensionRequestPrice) || {};

  const calculatedTotalPrice = useMemo(() => {
    return (
      (totalRemainingPrice || 0) - (pay_with_wallet ? walletAmount : 0)
    ).toFixed(2);
  }, [totalRemainingPrice, walletAmount]);

  const isWalletCoversPayment: boolean =
    pay_with_wallet &&
    Math.round(Math.max(0, totalRemainingPrice - walletAmount)) == 0;

  const zeroDueToPay = totalRemainingPrice <= 0 && !pay_with_wallet;

  //Functions
  function extensionDatePickerActionHandler() {
    setVariables({
      rentalId: bookingId,
      dropOffDate: calendarDaysConvert(
        days,
        days?.locale?.name?.includes("arabic")
      ),
      dropOffTime: `${time}:00`,
    });
    setIsTooltipOpen(false);
    setDate(days);
    setChanged(true);
  }

  function buttonIconUrl() {
    return paymentMethod && !isWalletCoversPayment && !zeroDueToPay
      ? `/assets/icons/${
          paymentMethod === "MADA"
            ? "mada"
            : paymentMethod === "CREDIT_CARD"
            ? "visamaster"
            : isTamara
            ? "tamara"
            : "cash"
        }.svg`
      : "";
  }

  function changePymentMethodHandler() {
    setIsPaymentModalOpen(true);
  }

  useEffect(() => {
    dispatch(setPriceTemp(calculatedTotalPrice));
  }, [calculatedTotalPrice]);

  useEffect(() => {
    return () => {
      if (!window.location.hash) {
        dispatch(setCreatingOnlineExtensionRequset(false));
      }
    };
  }, []);

  useEffect(() => {
    if (extensionDetails) {
      const { dropOffDate, dropOffTime } = extensionDetails || {};
      const trimmedTime = dropOffTime.split(":");
      trimmedTime.pop();
      const formattedTime = trimmedTime.join(":");

      setDays(new DateObject(dropOffDate));
      setDate(new DateObject(dropOffDate));
      setTime(formattedTime);
      setVariables({
        rentalId: bookingId,
        dropOffDate: calendarDaysConvert(
          dropOffDate,
          dropOffDate?.locale?.name?.includes("arabic")
        ),
        dropOffTime: `${formattedTime}:00`,
      });
    }
  }, [extensionDetails]);

  useEffect(() => {
    setDays(
      new DateObject(
        moment(lastConfirmedExtensionRequest?.dropOffDate || dropOffDate)
          .add(1, "days")
          .format("YYYY-MM-DD")
      )
    );
  }, [lastConfirmedExtensionRequest?.dropOffDate, dropOffDate]);

  useEffect(() => {
    setTotalRemainingPrice(totalRemainingPrice);
  }, [totalRemainingPrice]);

  useEffect(() => {
    setWalletAmount(walletAmount);
  }, [walletAmount]);

  useEffect(() => {
    setIsWalletCoversPayment(isWalletCoversPayment);
  }, [isWalletCoversPayment]);

  useEffect(() => {
    setChanged(true);
  }, [pay_with_wallet]);

  useEffect(() => {
    return () => {
      setChanged(false);
    };
  }, []);

  return (
    <>
      <Wrapper>
        <div>
          <div
            id="calendar-extension"
            className="position-relative shadow p-3 mb-4"
            onClick={() => {
              setTimeout(() => setIsTooltipOpen(true));
            }}
          >
            <input
              placeholder={t("Choose Extension Time")}
              value={
                date
                  ? calendarDaysConvert(
                      date,
                      date?.locale.name.includes("arabic")
                    )
                  : null
              }
              readOnly
            />
            <img src={`/assets/images/calendar.svg`} alt="icon" />
          </div>
          <div className="d-flex justify-content-between align-items-center mt-4 bold shadow p-3 radius-3 border-2">
            <p>{t("Extension Duration") as string}</p>
            <span>{extensionDays || 0}</span>
          </div>
        </div>
        <ClickAwayListener
          touchEvent={"onTouchStart"}
          mouseEvent={"onMouseDown"}
          onClickAway={() => {
            setIsTooltipOpen(false);
          }}
        >
          <div>
            <CalendarTooltip
              isSingleSelection
              isOpen={isToolTipOpen}
              setIsOpen={setIsTooltipOpen}
              days={days}
              setDays={setDays}
              time={time}
              setTime={setTime}
              handleButtonClick={extensionDatePickerActionHandler}
              minDate={
                moment(
                  lastConfirmedExtensionRequest?.dropOffDate || dropOffDate
                )
                  .add(1, "day")
                  .format("YYYY/MM/DD") as any
              }
            />
          </div>
        </ClickAwayListener>
        <div className="mt-4">
          {paymentMethod !== "CASH" ? (
            <Tamara calculatedPrice={Number(calculatedTotalPrice || 0)} />
          ) : null}
        </div>
        <div style={{ marginTop: "50px", borderBottom: "solid 20px white" }}>
          <div className="d-flex justify-content-between align-items-center shadow p-3 radius-3">
            <div>
              <h6 className="bold font-20px d-inline-block">
                {t("Due Amount") as string}
              </h6>
              <span className="color-16 font-12px">{t("+VAT") as string}</span>
            </div>

            <div className="color-3 font-20px text-right d-flex gap-5px">
              <RiyalSymbol style={{ order: i18n.language === "ar" ? 1 : 0 }} />
              <span
                className="bold"
                style={{ order: i18n.language === "ar" ? 0 : 1 }}
              >
                {calculatedTotalPrice}
              </span>
            </div>
          </div>
          {walletAmount && pay_with_wallet ? (
            <div className="d-flex justify-content-between">
              <Wallet {...{ t, walletAmount }} />
            </div>
          ) : null}
          <div
            style={{
              visibility: isToolTipOpen ? "hidden" : "visible",
              top: 0,
            }}
          >
            <ActionButton
              isDisabled={!date}
              buttonText={
                zeroDueToPay
                  ? t("Extend Now")
                  : paymentMethod == "CASH" ||
                    (paymentMethod !== "CASH" &&
                      (isWalletCoversPayment || zeroDueToPay))
                  ? (t("Confirm") as string)
                  : (t("Pay Now") as string)
              }
              buttonIconUrl={buttonIconUrl()}
              actionHandler={() =>
                requestHandler({
                  rentalId: bookingId,
                  dropOffDate: calendarDaysConvert(
                    days,
                    days?.locale.name.includes("arabic")
                  ),
                  dropOffTime: `${time}:00`,
                  paymentMethod: paymentMethod === "CASH" ? "CASH" : "ONLINE",
                  withWallet: Boolean(pay_with_wallet && balance),
                })
              }
              changePymentMethodHandler={
                !zeroDueToPay ? changePymentMethodHandler : undefined
              }
            />
          </div>
        </div>
      </Wrapper>
    </>
  );
}

export default RegularExtension;
