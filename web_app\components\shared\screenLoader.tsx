/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
// import { useRouter } from "next/dist/client/router";
// import { useTranslation } from "react-i18next";
// import { STORED_LANGUAGE } from "utilities/enums";
import { useEffect, useLayoutEffect } from "react";

function ScreenLoader({ loading, setLoading }) {
  useEffect(() => {
    let clean = false;
    if (!clean && process.browser) window.onload = () => setLoading(false); // make sure all loaded
    return (clean = true);
  }, []);

  return loading ? (
    <div
      style={{
        position: "fixed",
        width: "100vw",
        height: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "var(--color-4)",
        zIndex: "calc(9 * 10000)",
      }}
    >
      <img
        style={{ width: "250px" }}
        src="/assets/images/loader.gif"
        alt="loader"
      />
    </div>
  ) : null;
}

export default ScreenLoader;
