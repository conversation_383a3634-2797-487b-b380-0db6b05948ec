html[lang="ar"] {
  .delivery-popup-footer {
    .save-location {
      text-align: right;
    }
  }
}
.delivery-popup-title {
  text-transform: uppercase;
  font-weight: 700;
}
.delivery-popup-footer {
  padding: 25px 40px 20px 39px;
  h6 {
    span {
      color: var(--color-1);
      opacity: 0.4;
      display: block;
      margin-bottom: -5px;
    }
  }
  .separator {
    height: 100px;
    width: 1px;
    background-color: var(--color-1);
    opacity: 0.2;
    display: block;
    // position: absolute;
    margin: 0 auto;
    margin-top: -5px;
  }
  button {
    padding: 10px 60px;
    border: none;
    background-color: var(--color-3);
    border-radius: var(--radius-3);
    color: var(--color-4);
    font-weight: 500;
    &:focus {
      outline: none;
    }
  }
  .save-location {
    margin-top: 10px;
    label {
      color: var(--color-3);
      text-transform: uppercase;
      font-weight: 500;
      padding: 0 5px;
    }
    span,
    span * {
      padding: 0;
    }
    > span {
      transform: translate(-2px, -2px);
    }
  }
}

@media (max-width: 960px) {
  .delivery-popup-header {
    padding: 25px !important;
  }
  .switch {
    width: 100% !important;
  }
  .delivery-popup-title {
    font-size: 18px;
    margin-bottom: 10px !important;
  }
  .delivery-popup-footer {
    padding: 0 20px;
    margin: 20px 0;
    > div {
      margin-bottom: 15px;
      min-width: unset !important;
    }
  }
  .separator {
    // display: none;
  }
}

.tooltip-wrap {
  position: absolute;
  z-index: 999999 !important;
  @media (max-width: 768px) {
    transform: translateX(0) !important;
    > div {
      position: relative !important;
      width: auto !important;
    }
  }
  > div {
    > div {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      width: initial !important;
      height: 50vh;
    }
    .head {
      color: var(--color-3);
      cursor: pointer;
      border-bottom: solid 1px var(--color-10);
      margin-bottom: 5px;
    }
  }
  &.calendar {
    > div {
      > div {
        &:last-child {
          @media (max-width: 768px) {
            margin-top: 60px;
          }
        }
      }
    }
  }
}
html[lang="en"] .tooltip-wrap.calendar {
  left: 0 !important;
}
html[lang="ar"] .tooltip-wrap.calendar {
  right: 0 !important;
}
