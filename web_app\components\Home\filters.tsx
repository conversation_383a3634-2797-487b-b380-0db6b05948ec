/* eslint-disable react-hooks/exhaustive-deps */
import {
  setMake,
  setModal,
  setTransmission,
  setVehiclesType,
  setInsuranceType,
  setExtraServices,
  setYear,
  setDailyPrice,
  clearFilter,
  setIsUnlimitedKM,
  setCarReturnInAnotherBranch,
  setIsBarq,
  setIsInstantConfirmation,
  setfiltersUsedInSearch,
  setFullInsuranceExtraService,
} from "store/search/action";
import { Grid } from "@material-ui/core";
import Select, { createFilter } from "react-select";
import { Collapse } from "@material-ui/core";
import { useLazyQuery, useQuery } from "@apollo/client";
import { VehicleTypes } from "gql/queries/vehicleTypes";
import {
  ExtraService_Query,
  Insurances_Query,
  Makes_Query,
  ModelsOfMake_Query,
} from "gql/queries/car";
import styled from "styled-components";
import { RootStateOrAny, useSelector } from "react-redux";
import { memo, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { carsListCurrentPageAction } from "store/cars/action";

//Styled Components
const CollapseCMP = styled(Collapse)`
  border-top: solid 2px #ebedf1;
  padding-top: 20px;
  @media (max-width: 900px) {
    &.collapse-wrapper {
      padding: 24px !important;
    }
    .order {
      margin-top: 20px !important;
    }
  }
`;
const Wrapper = styled.div`
  flex-wrap: wrap;
  > div {
    border: none !important;
    text-align: center;
    margin-top: 0 !important;
    background-color: var(--color-6);
    color: var(--color-8);
    padding: 10px !important;
    cursor: pointer;
    font-size: 0.9rem;
    &.active {
      color: var(--color-3);
      background: #f2f8fa;
      position: relative;
      &:after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: calc(50% - 15px);
        width: 30px;
        height: 5px;
        background: var(--color-3);
      }
    }
  }
`;

//JSX
function Filters({
  isCollapsed,
  setIsCollapsed,
  home,
  fetchCars,
  setFetchCars,
  getCarList,
}) {
  //Hooks
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();

  //Store
  const fullInsurance = useSelector(
    (state: RootStateOrAny) => state.search_data.fullInsurance
  );
  const stored_fitlers = useSelector(
    (state: RootStateOrAny) => state.search_data.filters
  );
  const userAddress = useSelector(
    (state: RootStateOrAny) => state.search_data.user_address
  );
  const dateTime = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );
  const selectionIndex = useSelector(
    // refers to active tab
    (state: RootStateOrAny) => state.search_data.selection_index
  );

  //gql
  const { data: makes_res, loading: loadingMakes } = useQuery(Makes_Query, {
    variables: { limit: 500, status: true },
  });
  const { data: vehicletypes } = useQuery(VehicleTypes);
  const { data: Insurances_res } = useQuery(Insurances_Query);
  const { data: extraservice, loading: loadingExtraService } = useQuery(
    ExtraService_Query,
    {
      variables: { limit: 500, page: 1, isActive: true },
    }
  );
  const [fetchModelsofMake, { data: modelsRes, loading: loadingModels }] =
    useLazyQuery(ModelsOfMake_Query, {
      variables: { limit: 500 },
    });

  //Helpers

  function getMakesOptions() {
    return !loadingMakes && makes_res
      ? [
          ...makes_res?.makes?.collection,
          {
            id: 999,
            name: t("Other"),
            enName: "other",
            arName: "أخري",
          },
        ].map((item) => {
          return {
            label: item.name,
            value: item.id,
            enName: item.enName,
            arName: item.arName,
          };
        })
      : [];
  }

  function getSelectedMakeOption() {
    if (stored_fitlers?.make) {
      return getMakesOptions()?.find(
        (i) => i.value == stored_fitlers?.make?.value
      );
    }
    return null;
  }

  function getModelsOptions() {
    return !loadingModels
      ? modelsRes?.make?.carModels.map((item) => {
          return {
            label: item.name,
            value: item.id,
            enName: item.enName,
            arName: item.arName,
          };
        })
      : [];
  }

  function getSelectedModelOption() {
    if (stored_fitlers?.model) {
      return getModelsOptions()?.find(
        (i) => i.value == stored_fitlers?.model?.value
      );
    }
    return null;
  }

  const carYears = useMemo(() => {
    const currentYear = new Date().getFullYear() + 1;
    const fiveYearsAgo = new Date().getFullYear() - 4;
    let years = [];
    for (let i = fiveYearsAgo; i <= currentYear; i++) {
      years.push(i);
    }
    return years;
  }, []);

  function getExtraServiceOptions() {
    return !loadingExtraService && extraservice
      ? extraservice?.extraServices?.collection.map((item) => {
          return {
            label: item?.[`${i18n.language}Title`],
            value: item.id,
            action: "extraService",
          };
        })
      : [];
  }

  function getInsurancesOptions() {
    if (Insurances_res) {
      return [
        {
          arName: "الكل",
          enName: "All",
          id: "-1",
        },
        ...Insurances_res?.insurances,
      ];
    }
    return [];
  }

  function getVehiclesTypesOptions() {
    if (vehicletypes) {
      return [
        {
          arName: "الكل",
          enName: "All",
          id: "-1",
        },
        ...vehicletypes?.vehicleTypes,
      ];
    }
    return null;
  }

  function getDailyPricesOptions() {
    return [
      {
        dailyPriceFrom: null,
        dailyPriceTo: null,
        name: t("All"),
      },
      {
        dailyPriceFrom: 1,
        dailyPriceTo: 89,
        name: t("less than 89"),
      },
      { dailyPriceFrom: 90, dailyPriceTo: 299, name: "90-299" },
      {
        dailyPriceFrom: 300,
        dailyPriceTo: "+300",
        name: t("300 and more"),
      },
    ];
  }

  function getTransmissionOptions() {
    return ["all", "auto", "manual"];
  }

  function findCarHandler() {
    if (setFetchCars) {
      setFetchCars(!fetchCars);
    } else {
      getCarList({ isHome: home });
    }
  }

  //LifeCycle

  useEffect(() => {
    let clear = false;
    if (!clear && stored_fitlers?.make) {
      fetchModelsofMake({ variables: { id: stored_fitlers?.make?.value } });
    }
    return () => {
      clear = true;
    };
  }, [stored_fitlers?.make?.value]);

  return (
    <>
      <CollapseCMP
        in={isCollapsed}
        timeout="auto"
        className="w-100 collapse-wrapper px-5 bg-white"
        // style={{ marginTop: "-40px" }}
      >
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <h5 className="mb-4 bold">{t("car.Filters") as string}</h5>
          </Grid>

          <Grid item>
            <div
              className="p-0 cursor-pointer"
              onClick={() => {
                dispatch(clearFilter());
                dispatch(setIsUnlimitedKM(undefined));
                dispatch(setCarReturnInAnotherBranch(undefined));
                dispatch(setIsBarq(undefined));
                dispatch(setIsInstantConfirmation(undefined));
              }}
            >
              <span className="color-9">{t("clear.all") as string}</span>
            </div>
          </Grid>
        </Grid>
        <Grid container spacing={3} alignItems="baseline">
          <Grid item xs={12} md={4}>
            <h5 className="mb-4 bold">{t("car.make") as string}</h5>

            <Select
              placeholder={t("car.make")}
              isSearchable
              isClearable
              className="select required"
              filterOption={createFilter({
                matchFrom: "any",
                stringify: (option) => `${option.label}`,
              })}
              options={getMakesOptions()}
              isLoading={loadingMakes}
              value={getSelectedMakeOption()}
              onChange={(e) => {
                if (e?.value) {
                  dispatch(
                    setMake({
                      label: e.label,
                      value: e.value,
                      action: "make",
                    })
                  );
                  dispatch(setModal(undefined));
                } else {
                  dispatch(setMake(undefined));
                  dispatch(setModal(undefined));
                }
              }}
            />
          </Grid>
          {stored_fitlers?.make && makes_res ? (
            <Grid item xs={12} md={4}>
              <h5 className="mb-4 bold">{t("car.model") as string}</h5>

              <Select
                id="carModelId"
                placeholder={t("Model")}
                isDisabled={!makes_res}
                isSearchable
                isClearable
                className="select required"
                isLoading={loadingModels}
                filterOption={createFilter({
                  matchFrom: "any",
                  stringify: (option) => `${option.label}`,
                })}
                value={getSelectedModelOption()}
                options={getModelsOptions()}
                onChange={(e) => {
                  if (e?.value) {
                    dispatch(
                      setModal({
                        label: e.label,
                        value: e.value,
                        action: "model",
                      })
                    );
                  } else {
                    dispatch(setModal(undefined));
                  }
                }}
              />
            </Grid>
          ) : null}

          <Grid item md={4} xs={12}>
            <h5 className="mb-4 bold">{t("car.year") as string}</h5>
            <Wrapper className="d-flex gap-15px">
              {carYears?.map((item: any) => {
                return (
                  <div
                    key={item}
                    className={`${
                      item == stored_fitlers?.yearFrom?.value ||
                      (item == "All" && !stored_fitlers?.yearFrom?.value)
                        ? "active"
                        : ""
                    } bold`}
                    onClick={() => {
                      dispatch(
                        setYear(
                          item !== "All"
                            ? { label: item, value: item, action: "year" }
                            : undefined
                        )
                      );
                    }}
                  >
                    {t(item as string) as string}
                  </div>
                );
              })}
            </Wrapper>
          </Grid>
          <Grid item xs={12} md={4}>
            <h5 className="mb-4 bold">{t("extraservice") as string}</h5>
            <Select
              key={stored_fitlers?.extraServices}
              placeholder={t("extraservice")}
              isSearchable
              isClearable
              isMulti
              className="select"
              isLoading={loadingExtraService}
              filterOption={createFilter({
                matchFrom: "any",
                stringify: (option) => `${option.label}`,
              })}
              options={getExtraServiceOptions()}
              value={stored_fitlers?.extraServices}
              onChange={(e) => {
                if (e?.length) {
                  dispatch(setExtraServices(e));
                } else {
                  dispatch(setExtraServices([]));
                }
              }}
            />
          </Grid>
          <Grid item md={4} xs={12}>
            <h5 className="mb-4 bold">{t("insurance") as string}</h5>
            <Wrapper className="d-flex gap-15px">
              {getInsurancesOptions()?.map((item) => {
                return (
                  <div
                    key={item?.id}
                    className={`${
                      (item?.id == 2 && fullInsurance) ||
                      (!fullInsurance &&
                        (item?.id == stored_fitlers?.insuranceType?.value ||
                          (item?.id == -1 &&
                            !stored_fitlers?.insuranceType?.value)))
                        ? "active"
                        : ""
                    } bold`}
                    onClick={() => {
                      dispatch(
                        setInsuranceType({
                          label: `${
                            item?.[`${i18n.language}Name`]
                              ? item[`${i18n.language}Name`]
                              : item
                          }`,
                          value: +item.id,
                          id: item.id,
                          action: "insurance",
                        })
                      );
                      if (item.id === 1) {
                        dispatch(setFullInsuranceExtraService(false));
                      }
                    }}
                  >
                    {item?.[`${i18n.language}Name`]
                      ? item[`${i18n.language}Name`]
                      : item}
                  </div>
                );
              })}
            </Wrapper>
          </Grid>
          <Grid item md={4} xs={12}>
            <h5 className="mb-4 bold">{t("Daily Price") as string}</h5>
            <Wrapper className="d-flex gap-15px">
              {getDailyPricesOptions().map((item, index) => {
                return (
                  <div
                    key={item as any}
                    className={`${
                      (item.dailyPriceFrom ==
                        stored_fitlers?.dailyPriceFrom?.value &&
                        item.dailyPriceTo ==
                          stored_fitlers?.dailyPriceTo?.value) ||
                      (!item.dailyPriceTo &&
                        !stored_fitlers?.dailyPriceTo?.value)
                        ? "active"
                        : ""
                    } bold`}
                    onClick={() => {
                      if (item.dailyPriceFrom) {
                        dispatch(
                          setDailyPrice({
                            dailyPriceFrom: {
                              label: item.dailyPriceFrom,
                              value: item.dailyPriceFrom,
                              action: "dailyPrice",
                            },
                            dailyPriceTo: {
                              label: item.dailyPriceTo,
                              value: item.dailyPriceTo,
                              action: "dailyPrice",
                            },
                          })
                        );
                      } else {
                        dispatch(setDailyPrice(undefined));
                      }
                    }}
                  >
                    {item.name}
                  </div>
                );
              })}
            </Wrapper>
          </Grid>
          <Grid item md={4} xs={12}>
            <h5 className="mb-4 bold">{t("Vehicle.Type") as string}</h5>
            <Wrapper className="d-flex gap-15px">
              {getVehiclesTypesOptions()?.map((item) => {
                return (
                  <div
                    key={item?.id}
                    className={`${
                      item?.id == stored_fitlers?.vehicleType?.value ||
                      (!stored_fitlers?.vehicleType?.value && item?.id == -1)
                        ? "active"
                        : ""
                    } bold`}
                    onClick={() => {
                      dispatch(
                        setVehiclesType({
                          label: `${
                            item?.[`${i18n.language}Name`]
                              ? item[`${i18n.language}Name`]
                              : item
                          }`,
                          value: item.id,
                          id: item.id,
                          action: "vehicle",
                        })
                      );
                    }}
                  >
                    {item?.[`${i18n.language}Name`]
                      ? item[`${i18n.language}Name`]
                      : item}
                  </div>
                );
              })}
            </Wrapper>
          </Grid>
          <Grid item md={4} xs={12}>
            <h5 className="mb-4 bold">{t("transmission") as string}</h5>
            <Wrapper className="d-flex gap-15px">
              {getTransmissionOptions().map((item) => {
                return (
                  <div
                    key={item}
                    className={`${
                      item == stored_fitlers?.transmission?.value ||
                      (item == "all" && !stored_fitlers?.transmission?.value)
                        ? "active"
                        : ""
                    } bold`}
                    onClick={() => {
                      dispatch(
                        setTransmission({
                          label: t(item),
                          value: item,
                          id: item == "all" ? -1 : undefined,
                          action: "transmission",
                        })
                      );
                    }}
                  >
                    {t(item) as string}
                  </div>
                );
              })}
            </Wrapper>
          </Grid>
          <Grid
            container
            justifyContent="flex-end"
            md={12}
            xs={12}
            className="mb-5"
          >
            <Grid item md={2}>
              <div
                className={`order p-2 text-center bold text-white rounded cursor-pointer ${
                  !userAddress?.pick_up ||
                  !userAddress?.return ||
                  !dateTime?.pickup_time ||
                  !dateTime?.return_time ||
                  selectionIndex === "2"
                    ? "dimmed"
                    : ""
                }`}
                style={{ background: "var(--color-2)", marginTop: "-50px" }}
                onClick={() => {
                  dispatch(carsListCurrentPageAction(1));
                  findCarHandler();
                  setIsCollapsed(false);
                  dispatch(setfiltersUsedInSearch(stored_fitlers));
                }}
              >
                {t("Show results") as string}
              </div>
            </Grid>
          </Grid>
        </Grid>
      </CollapseCMP>
    </>
  );
}

export default memo(Filters);
