

  /* eslint-disable prettier/prettier */
import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { persist } from "constants/constants";
import { GetMakesQueryForSelect } from "gql/queries/Cars.queries.gql";
import { Airports } from "gql/queries/GetAirports.gql";

export function AirportsDropDown({
  loading,
  setSelectedAirPort,
  selectedAirport,
  error,
  valueAttribute,
  inBooking,
  areas,
  ...props
}) {
  const { data: makesRes, loading: gettingMakes } = useQuery(GetMakesQueryForSelect, {
    variables: { limit: persist.unlimitedLimit },
  });
  const { data: AirportsRes, loading: gettingAirports } = useQuery(Airports,{
    variables:{
      areaIds:areas?.map((area)=>+area)
    }
  });
  const { locale, formatMessage } = useIntl();

  const options =
  AirportsRes?.airports?.map((x) => ({
      value: x["id"],
      label: x[`${locale}Name`],
    })) || [];

  React.useEffect(() => {
    if (!selectedAirport) {
      onClear();
    }
  }, [selectedAirport]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  if(inBooking){
    options.unshift({value:"all",label:formatMessage({ id: "all" })})

  }

  
  return (
    <Select
      className={`dropdown-select ${error ? "selection-error" : ""}`}
      isClearable
      options={options}
      ref={selectInputRef}
      isMulti={inBooking}
      loadOptions={gettingMakes || loading}
      defaultValue={options.find((optn) => `${optn.value}` === `${selectedAirport}`)}
      value={options.find((optn) => `${optn.value}` === `${selectedAirport}`)}
      placeholder={formatMessage({ id: "Airports" })}
      onChange={(selection) => {
        if(inBooking &&  selection?.[selection?.length - 1]?.value == "all"){
          const makes = selection?.filter((onselectoion) => onselectoion.value == "all");
  
          setSelectedAirPort(makes)
            return
          }else{
            if(inBooking){
              const makesWithoutAll= selection?.filter((onselectoion) => onselectoion.value != "all");
  
              setSelectedAirPort(makesWithoutAll)
            }else{
              setSelectedAirPort(selection?.value)
            }
         
            return
          }
        // if(inBooking){
        //   setSelectedAirPort(selection)
        // }else{
        // setSelectedAirPort(selection?.value);

        // }
      }}
      noOptionsMessage={() => {
        if (gettingMakes) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
      {...props}
    />
  );
}
AirportsDropDown.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectedAirport: PropTypes.string,
  setSelectedAirPort: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
