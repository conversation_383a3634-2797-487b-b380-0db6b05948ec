/* eslint-disable react-hooks/exhaustive-deps */
import { useLazyQuery } from "@apollo/client";
import { CarProfile_Query } from "gql/queries/car";
import { useRouter } from "next/router";
import { useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { setCarData } from "store/cars/action";

export const useCarProfile = (id, carData) => {
  const dispatch = useDispatch();

  const [getCarProfile, { data: carProfileData, loading: carProfileLoader }] =
    useLazyQuery(CarProfile_Query, {
      fetchPolicy: "cache-first",
      nextFetchPolicy: "cache-only",
    });

  const carDataRes = carProfileData?.carProfile;

  useEffect(() => {
    if (id) {
      getCarProfile({ variables: { id: id } }).then((res) => {
        if (res?.data?.carProfile) dispatch(setCarData(res.data.carProfile));
      });
    }
  }, [id]);

  const {
    carImages,
    carMakeName,
    carModelName,
    year,
    distanceByMonth,
    distanceBetweenCarUser,
    additionalDistanceCost,
    ownCarDetail,
    ownCarMedia,
    ownCarPlans,
    ownCarFeature,
  } =
    useMemo(() => {
      return {
        ...carProfileData?.carProfile,
        ownCarMedia: carProfileData?.carProfile?.ownCarDetail?.ownCarMedia,
        ownCarPlans: carProfileData?.carProfile?.ownCarDetail?.ownCarPlans,
        ownCarFeature: carProfileData?.carProfile?.ownCarDetail?.ownCarFeature,
      };
    }, [carProfileData]) || {};

  return {
    carProfileLoader,
    carDataRes,
    carImages,
    carMakeName,
    carModelName,
    year,
    distanceByMonth,
    distanceBetweenCarUser,
    additionalDistanceCost,
    ownCarDetail,
    ownCarMedia,
    ownCarPlans,
    ownCarFeature,
  };
};
