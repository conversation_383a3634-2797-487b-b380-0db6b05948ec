/* eslint-disable @next/next/no-img-element */
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
} from "@material-ui/core";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import Popup from "components/shared/popup";
import { Dispatch, SetStateAction } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const AccordionStyled = styled(Accordion)`
  box-shadow: none !important;
  border-radius: var(--radius-2) !important;
  :before {
    display: none !important;
  }
  margin: 10px 0;
  background-color: white !important;
`;
const AccordionSummaryStyled = styled(AccordionSummary)`
  color: var(--coolor-2);
  > div:first-child {
    color: var(--color-2);
    font-size: 20px;
    font-weight: bold;
    margin: 10px 0 15px 0;
  }
`;

const AccordionDetailsStyled = styled(AccordionDetails)`
  text-align: start;
  font-size: 18px;
  flex-direction: ${(props) => (props.faq ? "column" : "row")};
`;

const WhyToRentStyled = styled.div`
  overflow-y: auto;
  > div {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    column-gap: 20px;
    margin: 10px 0;
    font-size: 18px;

    div {
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 100px;
      div {
        /* margin: 0 auto; */
      }
    }
  }
  .header {
    > div {
      background-color: #f6f6f6;
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius-2);
      /* min-width: 90px; */
    }
  }
`;

const ProductFeaturesStyled = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 20px;
  margin: 0 auto;
  row-gap: 20px;
  > div {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    img {
      width: 60px;
    }
    h4 {
      width: ${(props) => (props.lang === "en" ? "80%" : "60%")};
      text-align: center;
      font-size: 18px;
    }
  }
`;
const FAQStyled = styled.div`
  h6 {
    font-weight: bold;
  }
  margin: 10px 0;
`;

function RentToOwnInfoModal({
  rentToOwnInfoModalOpen,
  setRentToOwnInfoModalOpen,
}: {
  rentToOwnInfoModalOpen: boolean;
  setRentToOwnInfoModalOpen: Dispatch<SetStateAction<boolean>>;
}) {
  const { t, i18n } = useTranslation();

  return (
    <Popup
      maxWidth="sm"
      isOpen={rentToOwnInfoModalOpen}
      setIsOpen={setRentToOwnInfoModalOpen}
      backgroundColor={"#f6f6f6"}
      style={{ zIndex: 9999 }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          margin: "30px 0",
        }}
      >
        <img src="/assets/images/rentToOwnDark.svg" alt="rent to own" />
      </div>
      <AccordionStyled>
        <AccordionSummaryStyled
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          {t("Brief") as string}
        </AccordionSummaryStyled>
        <AccordionDetailsStyled>
          {t("Brief.Description") as string}
        </AccordionDetailsStyled>
      </AccordionStyled>
      <AccordionStyled>
        <AccordionSummaryStyled
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel2-content"
          id="panel2-header"
        >
          {t("Why... Rent to Own") as string}
        </AccordionSummaryStyled>
        <AccordionDetailsStyled>
          <WhyToRentStyled>
            <div className="header">
              <div>{t("Featured") as string}</div>
              <div style={{ flexDirection: "column" }}>
                <div>
                  <img src="/assets/icons/miniRentToOwn.svg" alt="" />
                </div>
                {t("Rent to Own") as string}
              </div>
              <div>{t("Cash Purchase") as string}</div>
              <div>{t("Lease with Ownership") as string}</div>
            </div>
            <div>
              <div>{t("Ease of Procedures") as string}</div>
              <div>
                <img src="/assets/icons/checked.svg" alt="" />
              </div>
              <div>
                <img src="/assets/icons/checked.svg" alt="" />
              </div>
              <div>
                <img src="/assets/icons/notChecked.svg" alt="" />
              </div>
            </div>
            <div>
              <div>{t("Free Maintenance") as string}</div>
              <div>
                <img src="/assets/icons/checked.svg" alt="" />
              </div>
              <div>
                <img src="/assets/icons/notChecked.svg" alt="" />
              </div>
              <div>
                <img src="/assets/icons/notChecked.svg" alt="" />
              </div>
            </div>
            <div>
              <div>{t("Free Cancellation") as string}</div>
              <div>
                <img src="/assets/icons/checked.svg" alt="" />
              </div>
              <div>
                <img src="/assets/icons/notChecked.svg" alt="" />
              </div>
              <div>
                <img src="/assets/icons/notChecked.svg" alt="" />
              </div>
            </div>
            <div>
              <div>{t("Car Replacement") as string}</div>
              <div>
                <img src="/assets/icons/checked.svg" alt="" />
              </div>
              <div>
                <img src="/assets/icons/notChecked.svg" alt="" />
              </div>
              <div>
                <img src="/assets/icons/notChecked.svg" alt="" />
              </div>
            </div>
          </WhyToRentStyled>
        </AccordionDetailsStyled>
      </AccordionStyled>
      <AccordionStyled>
        <AccordionSummaryStyled
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel3-content"
          id="panel3-header"
        >
          {t("Product Features") as string}
        </AccordionSummaryStyled>
        <AccordionDetailsStyled>
          <ProductFeaturesStyled lang={i18n.language}>
            <div>
              <img
                src="/assets/icons/rentToOwn/featured1.svg"
                alt="Product Features"
              />
              <h4>{t("Guaranteed cars from authorized allies") as string}</h4>
              <p></p>
            </div>
            <div>
              <img
                src="/assets/icons/rentToOwn/featured2.svg"
                alt="Product Features"
              />
              <h4>
                {t("Free cancellation with no financial commitments") as string}
              </h4>
              <p></p>
            </div>
            <div>
              <img
                src="/assets/icons/rentToOwn/featured3.svg"
                alt="Product Features"
              />
              <h4>
                {
                  t(
                    "More convenience, only a driver's license required"
                  ) as string
                }
              </h4>
              <p></p>
            </div>
            <div>
              <img
                src="/assets/icons/rentToOwn/featured4.svg"
                alt="Product Features"
              />
              <h4>
                {t("Routine maintenance, just fill up with gas") as string}
              </h4>
              <p></p>
            </div>
          </ProductFeaturesStyled>
        </AccordionDetailsStyled>
      </AccordionStyled>
      <AccordionStyled>
        <AccordionSummaryStyled
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel4-content"
          id="panel4-header"
        >
          {t("rentToOwnFAQ") as string}
        </AccordionSummaryStyled>
        <AccordionDetailsStyled faq>
          <FAQStyled>
            <h6>{t("FAQ1") as string}</h6>
            <p>{t("FAQA1") as string}</p>
          </FAQStyled>
          <FAQStyled>
            <h6>{t("FAQ2") as string}</h6>
            <p>{t("FAQA2") as string}</p>
          </FAQStyled>
          <FAQStyled>
            <h6>{t("FAQ3") as string}</h6>
            <p>{t("FAQA3") as string}</p>
          </FAQStyled>
        </AccordionDetailsStyled>
      </AccordionStyled>
      <Button
        onClick={() => {
          setRentToOwnInfoModalOpen(false);
        }}
        style={{
          backgroundColor: "var(--color-2)",
          padding: "12px 0 20px 0",
          fontWeight: "bold",
          borderRadius: "40px",
          color: "var(--color-4)",
          marginTop: "30px",
          fontSize: "20px",
        }}
      >
        {t("Done.well") as string}
      </Button>
    </Popup>
  );
}

export default RentToOwnInfoModal;
