import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import ItemCity from "./itemCity";
import ItemAirport from "./itemAirport";
import Tabs from "./tabs";

const Div = styled.div`
  min-width: 330px;
  padding-bottom: 25px;
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  z-index: 9;
  @media (min-width: 960px) {
    position: absolute;
    top: 50%;
    left: 50px;
    transform: translate(-10px, -50%);
  }
  .head {
    border-bottom: solid 1px var(--color-10);
    margin-bottom: 24px;
    .toggle-select {
      display: flex;
      justify-content: space-between;
      padding: 25px 0;
      margin: 5px 35px -1px 35px;
      padding-bottom: 15px;
      span {
        &.is-selected {
          color: var(--color-3);
          &:after {
            background: var(--color-3);
            height: 2px;
            bottom: -17px;
          }
        }
        color: var(--color-1);
      }
    }
  }
  #items-wrap {
    padding: 0 20px;
    > div:not(:last-child) {
      margin-bottom: 20px;
    }
  }
`;

export default function Box() {
  const { t } = useTranslation();

  const [tabIndex, setTabIndex] = useState(0);
  const [selectedCityIndex, setSelectedCityIndex] = useState(0);
  const [selectedAirportIndex, setSelectedAirportIndex] = useState(0);

  const mockCitiesList = [
    {
      img: `/assets/images/cities-01.jpg`,
      name: t("Jeddah"),
      count: 207,
    },
    {
      img: `/assets/images/cities-02.jpg`,
      name: t("Riyadh"),
      count: 320,
    },
    {
      img: `/assets/images/cities-03.jpg`,
      name: t("Dammam"),
      count: 115,
    },
    {
      img: `/assets/images/cities-04.jpg`,
      name: t("Qasim"),
      count: 55,
    },
  ];
  const mockAirportsList = [
    {
      name: t("King Abdulaziz"),
      city: t("Jeddah"),
    },
    {
      name: t("King Khaled"),
      city: t("Riyadh"),
    },
    {
      name: t("King Fahad"),
      city: t("Dammam"),
    },
    {
      name: t("Abha"),
      city: t("Qasim"),
    },
  ];

  return (
    <Div>
      <div className="head">
        <Tabs
          items={[
            { index: 0, text: t("Cities") },
            { index: 1, text: t("Airports") },
          ]}
          index={tabIndex}
          onClickHandler={(index) => setTabIndex(index)}
        />
      </div>
      <div id="items-wrap">
        {tabIndex === 0
          ? mockCitiesList.map((mockCity, index) => {
              return (
                <ItemCity
                  key={index}
                  img={mockCity.img}
                  name={mockCity.name}
                  count={mockCity.count}
                  isActive={selectedCityIndex === index}
                  itemIndex={index}
                  setSelectedItemIndex={setSelectedCityIndex}
                />
              );
            })
          : mockAirportsList.map((mockAirport, index) => {
              return (
                <ItemAirport
                  key={index}
                  name={mockAirport.name}
                  city={mockAirport.city}
                  isActive={selectedAirportIndex === index}
                  itemIndex={index}
                  setSelectedItemIndex={setSelectedAirportIndex}
                />
              );
            })}
      </div>
    </Div>
  );
}
