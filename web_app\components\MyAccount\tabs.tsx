import Link from "next/link";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import useLogic from "./useLogic";

const Div = styled.div`
  span.active {
    color: var(--color-3);
    display: inline-block;
    position: relative;
    &:after {
      content: "";
      width: 100%;
      height: 2px;
      background-color: var(--color-3);
      position: absolute;
      bottom: -17px;
      left: 0;
    }
  }
`;

export default function Tabs() {
  const { t } = useTranslation();
  const { tab } = useLogic();

  return (
    <Div className="d-flex justify-content-center gap-30px">
      <Link href="/my-account" prefetch={false}>
        <span
          className={`medium text-capitalize cursor-pointer ${
            !tab ? "active" : ""
          }`}
        >
          {t("Profile") as string}
        </span>
      </Link>
      <Link href="/my-account?tab=wallet" prefetch={false}>
        <span
          className={`medium text-capitalize cursor-pointer ${
            tab === "wallet" ? "active" : ""
          }`}
        >
          {t("Wallet") as string}
        </span>
      </Link>
    </Div>
  );
}
