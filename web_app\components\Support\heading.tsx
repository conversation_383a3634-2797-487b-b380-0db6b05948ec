import { Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  padding: 40px 0 0 0;
  h1 {
    color: var(--color-11);
    font-weight: 500;
    text-transform: uppercase;
    font-size: 16px;
  }
  p {
    font-size: 38px;
    line-height: 55px;
    color: var(--color-1);
  }
  @media (max-width: 570px) {
    padding: 50px 0 50px 0;
    p {
      font-size: 28px !important;
      line-height: 35px !important;
    }
  }
`;

export default function Heading() {
  const { t } = useTranslation();

  return (
    <Div>
      <Grid container>
        <Grid item xs={12} md={6}>
          <h1>{t("Contact Us") as string}</h1>
          <p>
            {
              t(
                "Feel free to reach to us about any questions you might have."
              ) as string
            }
          </p>
        </Grid>
      </Grid>
    </Div>
  );
}
