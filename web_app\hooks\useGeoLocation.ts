/* eslint-disable react-hooks/exhaustive-deps */
import { useLazyQuery } from "@apollo/client";
import { GET_AREA_QUERY } from "gql/queries/areas";
import { useSnackbar } from "notistack";
import { useEffect } from "react";
import Geocode from "react-geocode";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import {
  setcurrentLocationAction,
  setDeliveryLocationAction,
  setUserPickupAddressAction,
  setUserReturnAddressAction,
} from "store/search/action";

// Default Riyadh coordinates
const RIYADH_LOCATION = {
  lat: 24.7136,
  lng: 46.6753,
  name: "Riyadh",
  id: "1",
};

export default function useGeoLocaiton() {
  //GQL
  const [area, { data, loading }] = useLazyQuery(GET_AREA_QUERY);
  //Hooks
  const dispatch = useDispatch();
  const { i18n } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();

  //Variables
  const { language } = i18n;
  const APIKey = process?.env?.NEXT_PUBLIC_MAP_API;

  useEffect(() => {
    Geocode.setApiKey(APIKey);
    Geocode.setLanguage(language || "en");
  }, []);

  useEffect(() => {
    const hasRequestedLocation = sessionStorage.getItem("hasRequestedLocation");

    if (!hasRequestedLocation && navigator.geolocation) {
      sessionStorage.setItem("hasRequestedLocation", "true");
      navigatorGeoCode(null, () => {}, null);
    }
  }, []);

  function gecodeLatLng(lat, lng) {
    return Geocode.fromLatLng(lat, lng).then(
      (response) => {
        const address = response.results?.find((i) =>
          i.types.find((i) => i === "route")
        )?.formatted_address;
        return address;
      },
      (error) => {
        console.error(error);
      }
    );
  }

  // Handle invalid location
  const handleInvalidLocation = () => {
    dispatch(setUserPickupAddressAction(RIYADH_LOCATION));
    dispatch(setUserReturnAddressAction(RIYADH_LOCATION));
    dispatch(setDeliveryLocationAction(RIYADH_LOCATION));
    dispatch(setcurrentLocationAction(RIYADH_LOCATION));
  };

  function navigatorGeoCode(action, setIsOpen, geoLocationCallback) {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async function (position) {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;

          const areaRes = await area({
            variables: { lat, lng },
          });

          if (areaRes?.data?.area) {
            const locationData = {
              ...areaRes.data.area,
              lat,
              lng,
            };

            dispatch(setcurrentLocationAction(locationData));

            if (action) {
              dispatch(action(locationData));
              setIsOpen(false);
              return area;
            }

            geoLocationCallback && geoLocationCallback(locationData);
          } else {
            // Location is invalid (outside Saudi Arabia or other error)
            handleInvalidLocation();
            enqueueSnackbar(
              areaRes?.error?.message ||
                "Location not available in service area",
              {
                variant: "error",
              }
            );
          }
          setIsOpen(false);
        },
        function (err) {
          if (err.code === 1) {
            // Permission denied - fallback to Riyadh
            handleInvalidLocation();
          }
        }
      );
    }
  }
  return { navigatorGeoCode, gecodeLatLng };
}
