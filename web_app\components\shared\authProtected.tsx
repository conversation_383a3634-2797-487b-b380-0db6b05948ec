/* eslint-disable react-hooks/exhaustive-deps */
import { InfoOutlined } from "@material-ui/icons";
import { useRouter } from "next/router";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { GoToLogin } from "store/authentication/action";
import Layout from "./layout";

export default function AuthProtected({ children }) {
  //Hooks
  const { t } = useTranslation();
  const dispatch = useDispatch();

  //Store
  const authentication = useSelector(
    (state: RootStateOrAny) => state.authentication
  );
  const { token } = authentication?.login_data || {};

  if (token) {
    return children;
  }
  return (
    <Layout>
      <div
        className="d-flex justify-content-center align-items-center w-100 bold text-capitalize cursor-pointer text-decoration-underline"
        style={{ position: "absolute", top: "calc( 50% - 30px)" }}
      >
        <InfoOutlined />
        <p
          onClick={() => {
            dispatch(GoToLogin(true));
          }}
        >
          {t("Login to grant access to this page!") as string}
        </p>
      </div>{" "}
    </Layout>
  );
}
