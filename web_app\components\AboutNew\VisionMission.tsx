/* eslint-disable @next/next/no-img-element */
import { Box, Grid, Typography } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import { AboutContainer, Content } from "./styles";

function VisionMission() {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.language === "ar";

  return (
    <Box style={{ backgroundColor: "#F8F8F8" }}>
      <AboutContainer background="#F8F8F8">
        <Grid
          container
          spacing={2}
          justifyContent="space-between"
          className="our-story"
        >
          <Grid item xs={12} md="auto">
            <Box className="text-align-localized mt-1">
              <img
                src="/assets/images/about/icons/eye.svg"
                alt="Story Icon"
                loading="lazy"
              />
              <Typography
                variant="h3"
                className="bold"
                style={{ fontSize: "1.7rem", fontWeight: 700 }}
                dangerouslySetInnerHTML={{
                  __html: t("aboutus.visionmission.title") as string,
                }}
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={10} className="pt-3">
            <Grid
              container
              direction="row-reverse"
              justifyContent="space-between"
              spacing={2}
            >
              <Grid item xs={12} md={5}>
                <Typography
                  variant="h6"
                  className="text-align-localized bold mb-2"
                  style={{ direction: isRtl ? "rtl" : "ltr" }}
                >
                  {t("aboutus.visionmission.mission.title") as string}
                </Typography>
                <Typography
                  variant="body1"
                  className="text-align-localized"
                  style={{ direction: isRtl ? "rtl" : "ltr" }}
                >
                  {t("aboutus.visionmission.mission.content") as string}
                </Typography>
              </Grid>
              <Grid item xs={12} md={5}>
                <Typography
                  variant="h6"
                  className="text-align-localized bold mb-2"
                  style={{ direction: isRtl ? "rtl" : "ltr" }}
                >
                  {t("aboutus.visionmission.vision.title") as string}
                </Typography>
                <Typography
                  variant="body1"
                  className="text-align-localized"
                  style={{ direction: isRtl ? "rtl" : "ltr" }}
                >
                  {t("aboutus.visionmission.vision.content") as string}
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </AboutContainer>
    </Box>
  );
}

export default VisionMission;
