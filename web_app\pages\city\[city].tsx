import Home from "components/Home";

export default function CityPage({ cityData, cities }) {
  return <Home cityData={cityData} cities={cities} />;
}

export async function getServerSideProps(context) {
  const { city } = context.query;
  const res = await fetch(`http://${context.req.headers.host}/data.json`);
  const data = await res.json();
  const cityData = data.cities.find((item) => item.nameEn === city);

  return { props: { cityData: cityData, cities: data } };
}
