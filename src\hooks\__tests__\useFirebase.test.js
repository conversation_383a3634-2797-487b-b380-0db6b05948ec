import { renderHook } from '@testing-library/react';
import useFirebase from '../useFirebase';

// Mock Firebase
jest.mock('firebase/app', () => ({
  __esModule: true,
  default: {
    apps: [],
    initializeApp: jest.fn(() => ({
      remoteConfig: jest.fn(() => ({
        settings: {},
        fetchAndActivate: jest.fn(() => Promise.resolve(true)),
        getAll: jest.fn(() => ({
          skip_integrity: { _value: 'false' },
        })),
      })),
    })),
  },
}));

jest.mock('firebase/remote-config', () => ({}));

// Mock environment variable
const originalEnv = process.env;

describe('useFirebase Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env = {
      ...originalEnv,
      REACT_APP_FIREBASE_CONFIG: JSON.stringify({
        apiKey: 'test-api-key',
        authDomain: 'test.firebaseapp.com',
        projectId: 'test-project',
      }),
    };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  test('returns default values when Firebase is not configured', () => {
    process.env.REACT_APP_FIREBASE_CONFIG = '';
    
    const { result } = renderHook(() => useFirebase());
    
    expect(result.current.remoteConfigValues).toEqual({
      skip_integrity: { _value: 'false' },
    });
    expect(result.current.isLoaded).toBe(true);
  });

  test('initializes Firebase and fetches remote config', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useFirebase());
    
    // Wait for async operations to complete
    await waitForNextUpdate();
    
    expect(result.current.remoteConfigValues).toHaveProperty('skip_integrity');
    expect(result.current.isLoaded).toBe(true);
  });

  test('handles Firebase initialization errors gracefully', () => {
    // Mock Firebase to throw an error
    const firebase = require('firebase/app').default;
    firebase.initializeApp.mockImplementation(() => {
      throw new Error('Firebase initialization failed');
    });
    
    const { result } = renderHook(() => useFirebase());
    
    // Should still return default values
    expect(result.current.remoteConfigValues).toEqual({
      skip_integrity: { _value: 'false' },
    });
    expect(result.current.isLoaded).toBe(true);
  });

  test('handles remote config fetch errors gracefully', async () => {
    // Mock remote config to fail
    const firebase = require('firebase/app').default;
    firebase.initializeApp.mockReturnValue({
      remoteConfig: () => ({
        settings: {},
        fetchAndActivate: () => Promise.reject(new Error('Fetch failed')),
        getAll: () => ({}),
      }),
    });
    
    const { result, waitForNextUpdate } = renderHook(() => useFirebase());
    
    await waitForNextUpdate();
    
    // Should return default values on error
    expect(result.current.remoteConfigValues).toEqual({
      skip_integrity: { _value: 'false' },
    });
    expect(result.current.isLoaded).toBe(true);
  });

  test('parses skip_integrity value correctly', async () => {
    // Mock remote config with skip_integrity enabled
    const firebase = require('firebase/app').default;
    firebase.initializeApp.mockReturnValue({
      remoteConfig: () => ({
        settings: {},
        fetchAndActivate: () => Promise.resolve(true),
        getAll: () => ({
          skip_integrity: { _value: 'true' },
        }),
      }),
    });
    
    const { result, waitForNextUpdate } = renderHook(() => useFirebase());
    
    await waitForNextUpdate();
    
    expect(result.current.remoteConfigValues.skip_integrity._value).toBe('true');
  });

  test('uses NEXT_PUBLIC_FIREBASE_CONFIG as fallback', () => {
    delete process.env.REACT_APP_FIREBASE_CONFIG;
    process.env.NEXT_PUBLIC_FIREBASE_CONFIG = JSON.stringify({
      apiKey: 'next-api-key',
      authDomain: 'next.firebaseapp.com',
      projectId: 'next-project',
    });
    
    const { result } = renderHook(() => useFirebase());
    
    // Should initialize with Next.js config
    expect(result.current.isLoaded).toBe(true);
  });
});

// Integration test for PaymentFlow with Firebase
describe('Firebase Integration with PaymentFlow', () => {
  test('skip_integrity affects payment widget loading', () => {
    // This would be a more comprehensive test that verifies
    // the skip_integrity flag properly affects script loading
    // in the PaymentFlow component
    
    const mockRemoteConfig = {
      skip_integrity: { _value: 'true' },
    };
    
    // Mock the useFirebase hook
    jest.doMock('../useFirebase', () => ({
      __esModule: true,
      default: () => ({
        remoteConfigValues: mockRemoteConfig,
        isLoaded: true,
      }),
    }));
    
    // Test would verify that PaymentFlow respects the skip_integrity flag
    expect(mockRemoteConfig.skip_integrity._value).toBe('true');
  });
});
