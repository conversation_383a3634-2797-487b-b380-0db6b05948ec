import { Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Item from "./featureitem";

const Div = styled.div`
  @media (max-width: 960px) {
    .section-fancy-title {
      margin-bottom: 30px;
    }
  }
`;

export default function Features() {
  const { t } = useTranslation();

  const featuresMockData = [
    { icon: "/assets/images/about/features-1.svg", title: t("Pre-booking") },
    {
      icon: "/assets/images/about/features-2.svg",
      title: t("Insurance options"),
    },
    {
      icon: "/assets/images/about/features-3.svg",
      title: t("delivery and pick-up anywhere"),
    },
    {
      icon: "/assets/images/about/features-4.svg",
      title: t("Competitive prices"),
    },
    {
      icon: "/assets/images/about/features-5.svg",
      title: t("wide spread locations"),
    },
    {
      icon: "/assets/images/about/features-6.svg",
      title: t("diverse of cars fleet"),
    },
    {
      icon: "/assets/images/about/features-7.svg",
      title: t("24-hour service"),
    },
  ];

  return (
    <Div>
      <Grid container>
        <Grid item xs={12} md={3}>
          <div className="section-fancy-title">
            <h4>
              <span className="bold">{t("Features") as string}</span>{" "}
              {t("For Customer") as string}
            </h4>
          </div>
        </Grid>
        {featuresMockData.map((item, index) => {
          return (
            <Grid item key={index} xs={6} md={3}>
              <Item icon={item.icon} title={item.title} />
            </Grid>
          );
        })}
      </Grid>
    </Div>
  );
}
