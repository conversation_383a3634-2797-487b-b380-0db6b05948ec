/* eslint-disable @next/next/no-img-element */
/* eslint-disable react/jsx-key */
import React, { useState } from "react";
import Swiper from "components/shared/carousel";
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { Pagination } from "swiper";
import { RootStateOrAny, useSelector } from "react-redux";
import FullScreenModal from "../popups/showFullScreenFile";

const Div = styled.div`
  margin-bottom: 24px;
  .swiper-container {
    border-radius: var(--radius-3);
    img {
      width: 100%;
      height: 225px;
      object-fit: cover;
      object-position: top;
    }
  }
`;

const SwiperTag = styled(Swiper)``;

function CarMedia() {
  const { i18n } = useTranslation();
  const [file, setFile] = useState();
  const [open, setOpen] = useState(false);
  const { car_data } =
    useSelector((state: RootStateOrAny) => state?.cars) || {};
  const {
    ownCarMedia,
  }: {
    ownCarMedia: {
      id: number;
      mediaType: "image" | "video";
      mediaUrl: string;
      displayOrder: number;
    }[];
  } = car_data?.ownCarDetail || {};

  if (ownCarMedia?.length) {
    return (
      <Div>
        <SwiperTag
          simulateTouch={true}
          grabCursor={true}
          i18n={i18n}
          spaceBetween={50}
          slidesPerView="auto"
          onSwiper={(swiper) => null}
          onSlideChange={() => null}
          pagination={{ clickable: true }}
          autoPlay={true}
          modules={[Pagination]}
          slides={ownCarMedia?.map(
            ({ id, displayOrder, mediaType, mediaUrl }) => {
              if (mediaType === "image") {
                return (
                  <img
                    key={id}
                    src={mediaUrl}
                    alt="img"
                    style={{ cursor: "pointer" }}
                    onClick={() => {
                      setFile({ mediaUrl: mediaUrl, mediaType: mediaType });
                      setOpen(true);
                    }}
                  />
                );
              } else {
                return (
                  <video
                    controls
                    width="100%"
                    height="100%"
                    style={{ cursor: "pointer", maxHeight: "250px" }}
                  >
                    <source
                      src={mediaUrl}
                      // type={`video/${mediaUrl?.split(".")?.[1]}`}
                      type={`video/${mediaUrl?.substring(
                        mediaUrl?.lastIndexOf(".") + 1
                      )}`}
                    />
                  </video>
                );
              }
            }
          )}
        />
        <FullScreenModal setIsOpen={setOpen} isOpen={open} file={file} />
      </Div>
    );
  } else if (car_data?.carVersion?.images?.length) {
    return (
      <Div>
        <SwiperTag
          i18n={i18n}
          spaceBetween={50}
          slidesPerView="auto"
          onSwiper={(swiper) => null}
          onSlideChange={() => null}
          pagination={true}
          autoPlay={true}
          modules={[Pagination]}
          slides={car_data?.carVersion?.images?.map((img, index) => {
            return (
              <img
                key={index}
                src={img}
                alt="img"
                style={{ cursor: "pointer" }}
                onClick={() => {
                  setFile({ mediaUrl: img, mediaType: "image" });
                  setOpen(true);
                }}
                s
              />
            );
          })}
        />
        <FullScreenModal setIsOpen={setOpen} isOpen={open} file={file} />
      </Div>
    );
  }
  return <></>;
}

export default CarMedia;
