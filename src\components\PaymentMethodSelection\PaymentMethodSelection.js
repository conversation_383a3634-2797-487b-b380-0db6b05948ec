import React, { useState, useEffect } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "reactstrap";
import { CircularProgress } from "@material-ui/core";
import { CreditCard, AccountBalanceWallet } from "@material-ui/icons";
import "./PaymentMethodSelection.scss";

const PaymentMethodSelection = ({
  isOpen,
  onClose,
  onPaymentMethodSelect,
  loading = false,
  walletBalance = 0,
  totalAmount = 0,
  isAgencyDeactivated = false,
}) => {
  const { formatMessage } = useIntl();
  const [selectedMethod, setSelectedMethod] = useState("MADA");
  const [payWithWallet, setPayWithWallet] = useState(false);

  // Available payment methods for agency (excluding CASH)
  const paymentMethods = [
    {
      id: "MADA",
      name: formatMessage({ id: "<PERSON>a" }),
      icon: "/assets/icons/mada.svg",
      description: formatMessage({ id: "Pay with Mada card" }),
    },
    {
      id: "CREDIT_CARD", 
      name: formatMessage({ id: "Credit Card" }),
      icon: "/assets/icons/visa-master.svg",
      description: formatMessage({ id: "Pay with Visa or Mastercard" }),
    },
    {
      id: "TAMARA",
      name: formatMessage({ id: "Tamara" }),
      icon: "/assets/icons/tamara.svg", 
      description: formatMessage({ id: "3 Payments interest free" }),
    },
  ];

  useEffect(() => {
    // Reset selection when modal opens
    if (isOpen) {
      setSelectedMethod("MADA");
      setPayWithWallet(false);
    }
  }, [isOpen]);

  const handleMethodSelect = (methodId) => {
    setSelectedMethod(methodId);
  };

  const handleWalletToggle = () => {
    if (walletBalance >= totalAmount) {
      setPayWithWallet(!payWithWallet);
    }
  };

  const handleProceed = () => {
    if (isAgencyDeactivated) {
      return;
    }

    onPaymentMethodSelect({
      paymentMethod: selectedMethod,
      useWallet: payWithWallet,
      walletAmount: payWithWallet ? Math.min(walletBalance, totalAmount) : 0,
    });
  };

  const canUseWallet = walletBalance > 0;
  const isWalletSufficient = walletBalance >= totalAmount;

  return (
    <Modal isOpen={isOpen} toggle={onClose} size="md" centered>
      <ModalHeader toggle={onClose}>
        <div className="d-flex align-items-center">
          <CreditCard className="me-2" />
          <FormattedMessage id="Choose a payment method" />
        </div>
      </ModalHeader>
      
      <ModalBody>
        {isAgencyDeactivated && (
          <div className="alert alert-danger mb-3">
            <FormattedMessage id="Agency is deactivated. Payment is not allowed." />
          </div>
        )}

        <div className="payment-methods-container">
          {/* Payment Methods */}
          <div className="payment-methods-grid">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className={`payment-method-card ${
                  selectedMethod === method.id ? "selected" : ""
                } ${isAgencyDeactivated ? "disabled" : ""}`}
                onClick={() => !isAgencyDeactivated && handleMethodSelect(method.id)}
              >
                <div className="payment-method-content">
                  <div className="payment-method-header">
                    <div className="radio-indicator">
                      {selectedMethod === method.id ? (
                        <div className="radio-selected" />
                      ) : (
                        <div className="radio-unselected" />
                      )}
                    </div>
                    <img
                      src={method.icon}
                      alt={method.name}
                      className="payment-method-icon"
                    />
                  </div>
                  <div className="payment-method-info">
                    <h6 className="payment-method-name">{method.name}</h6>
                    <p className="payment-method-description">{method.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Wallet Option */}
          {canUseWallet && (
            <div className="wallet-section mt-4">
              <div className="wallet-header">
                <AccountBalanceWallet className="wallet-icon" />
                <h6><FormattedMessage id="Wallet Balance" /></h6>
              </div>
              
              <div className="wallet-info">
                <div className="wallet-balance">
                  <span className="balance-amount">
                    {walletBalance.toFixed(2)} <FormattedMessage id="SAR" />
                  </span>
                  <span className="balance-label">
                    <FormattedMessage id="Available Balance" />
                  </span>
                </div>
                
                {!isAgencyDeactivated && (
                  <div className="wallet-toggle">
                    <label className="wallet-switch">
                      <input
                        type="checkbox"
                        checked={payWithWallet}
                        onChange={handleWalletToggle}
                        disabled={!canUseWallet}
                      />
                      <span className="slider"></span>
                    </label>
                    <span className="wallet-toggle-label">
                      {isWalletSufficient ? (
                        <FormattedMessage id="Pay with wallet" />
                      ) : (
                        <FormattedMessage id="Use available balance" />
                      )}
                    </span>
                  </div>
                )}
              </div>
              
              {payWithWallet && (
                <div className="wallet-usage-info">
                  <small className="text-muted">
                    {isWalletSufficient ? (
                      <FormattedMessage 
                        id="Full amount will be paid from wallet"
                        values={{ amount: totalAmount.toFixed(2) }}
                      />
                    ) : (
                      <FormattedMessage 
                        id="Wallet will cover {amount} SAR, remaining {remaining} SAR will be charged"
                        values={{ 
                          amount: walletBalance.toFixed(2),
                          remaining: (totalAmount - walletBalance).toFixed(2)
                        }}
                      />
                    )}
                  </small>
                </div>
              )}
            </div>
          )}
        </div>
      </ModalBody>
      
      <ModalFooter>
        <Button color="secondary" onClick={onClose} disabled={loading}>
          <FormattedMessage id="button.cancel" />
        </Button>
        <Button 
          color="primary" 
          onClick={handleProceed}
          disabled={loading || isAgencyDeactivated || !selectedMethod}
        >
          {loading ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            <FormattedMessage id="Proceed to Payment" />
          )}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default PaymentMethodSelection;
