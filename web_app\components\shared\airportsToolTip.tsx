/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { LinearProgress, ClickAwayListener } from "@material-ui/core";
import Tooltip from "components/shared/tooltip";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Areas_Query } from "gql/queries/areas";
import { useQuery } from "@apollo/client";
import { setfiltersUsedInSearch } from "store/search/action";
import { useRouter } from "next/router";

const AirportsTooltip = ({ action, isOpen, setIsOpen, anchor, style }) => {
  //Hooks
  const { i18n } = useTranslation();
  const router = useRouter();

  //Store
  const dispatch = useDispatch();
  const { search_data } = useSelector((state: RootStateOrAny) => state) || {};
  const { filters, user_address } = search_data || {};

  //Queries
  const { data: areasListRes } = useQuery(Areas_Query, {
    fetchPolicy: "cache-first",
  });

  return (
    <ClickAwayListener
      mouseEvent="onMouseDown"
      touchEvent="onTouchStart"
      onClickAway={() => {
        setTimeout(() => {
          setIsOpen(false);
        }, 100);
      }}
    >
      <div className="tooltip-wrap" style={style}>
        <Tooltip
          isOpen={isOpen}
          anchor={anchor}
          // position={i18n.language === "en" ? "left" : "right"}
        >
          <div
            className="d-flex flex-column"
            style={{ width: "max-content", maxWidth: "80vw" }}
          >
            {areasListRes?.areas?.length ? (
              areasListRes?.areas.map((i) => {
                return (
                  <div key={i.id}>
                    {/* Rendering Airports */}
                    {i?.airports?.map((airport) => {
                      return (
                        <div
                          key={airport.id}
                          className={`d-flex flex-column justify-content-start ${
                            i18n.language === "en" ? "ml-2" : "mr-2"
                          }`}
                          onClick={() => {
                            dispatch(
                              dispatch(
                                action({
                                  ...i,
                                  airport,
                                })
                              )
                            );
                            setIsOpen(false);
                            dispatch(
                              setfiltersUsedInSearch({
                                ...filters,
                                airport: {
                                  label: user_address?.pick_up?.airport?.name,
                                  value: user_address?.pick_up?.airport?.id,
                                  action: "airport",
                                },
                              })
                            );
                            router.push("/car-search");
                          }}
                        >
                          <div
                            className="cursor-pointer text-align-localized"
                            style={{ display: "flex", alignItems: "center" }}
                          >
                            <img
                              src={`/assets/icons/airport.svg`}
                              alt="direction"
                            />
                            <span className="color-4 d-inline-block mx-2">
                              {airport.name}
                            </span>
                          </div>

                          <div
                            className={`${
                              !i.airports ? "mb-2" : ""
                            } d-flex justify-content-start`}
                          >
                            <img
                              src={`/assets/images/cityIcon.svg`}
                              alt="direction"
                            />
                            <span
                              className="color-4 d-inline-block"
                              style={{
                                transform: "translateY(2px)",
                              }}
                            >
                              {i.name}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                );
              })
            ) : (
              <LinearProgress />
            )}
          </div>
        </Tooltip>
      </div>
    </ClickAwayListener>
  );
};

export default AirportsTooltip;
