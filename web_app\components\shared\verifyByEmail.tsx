/* eslint-disable @next/next/no-img-element */
export default function VerifyByEmail({ Button, t, setIsSigninPopupOpen }) {
  return (
    <div id="verify-email">
      <div className="d-flex flex-column justify-content-center align-items-center mt-5">
        <img src="/assets/images/sent.svg" alt="verified" />
        <h4 className="color-1 bold mt-4 mb-5">{t("Check your email !!")}</h4>
        <p className="d-inline-block color-19 mb-5 font-17px">
          {t("Plase confirm email address we sent you")}
        </p>
      </div>
      <Button
        onClick={() => setIsSigninPopupOpen(false)}
        className="font-17px color-4 border-0 w-100 mt-4"
      >
        {t("Ok, I will verify my email")}
      </Button>
    </div>
  );
}
