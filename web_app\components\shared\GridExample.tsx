import React from "react";
import { Grid, Paper, Typography, makeStyles } from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    padding: theme.spacing(2),
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: "center",
    color: theme.palette.text.secondary,
    height: "100%",
  },
  sidebar: {
    backgroundColor: theme.palette.primary.light,
  },
  content: {
    backgroundColor: theme.palette.background.default,
  },
}));

const GridExample: React.FC = () => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        <Grid item xs={12} sm={2}>
          {/* Sidebar - 2 columns on sm and larger screens */}
          <Paper className={`${classes.paper} ${classes.sidebar}`}>
            <Typography variant="h6">Sidebar</Typography>
            <Typography variant="body2">Takes 2 columns</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={10}>
          {/* Main Content - 10 columns on sm and larger screens */}
          <Paper className={`${classes.paper} ${classes.content}`}>
            <Typography variant="h6">Main Content</Typography>
            <Typography variant="body2">Takes 10 columns</Typography>
          </Paper>
        </Grid>
      </Grid>
    </div>
  );
};

export default GridExample;
