import {
  CLEAR_DATA,
  IS_FURSAN_MEMBRSHIPID_VERIFIED,
  IS_FURSAN_MODAL_OPEN,
  MEMBERSHIP_ID,
  SELECT_FURSAN,
} from "./action";

export default function LoyalityReducer(
  state = {
    is_fursan_selected: false,
    is_fursan_membershipId_verified: false,
    is_fursan_modal_open: false,
    membership_id: "",
  },
  action
) {
  switch (action.type) {
    case SELECT_FURSAN:
      return { ...state, is_fursan_selected: action.payload };
    case IS_FURSAN_MEMBRSHIPID_VERIFIED:
      return { ...state, is_fursan_membershipId_verified: action.payload };
    case IS_FURSAN_MODAL_OPEN:
      return { ...state, is_fursan_modal_open: action.payload };
    case MEMBERSHIP_ID:
      return { ...state, membership_id: action.payload };
    case CLEAR_DATA:
      return {
        membership_id: state.membership_id,
        is_fursan_selected: false,
        is_fursan_membershipId_verified: false,
        is_fursan_modal_open: false,
      };
    default:
      return state;
  }
}
