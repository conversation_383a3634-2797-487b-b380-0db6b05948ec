/* eslint-disable @next/next/no-img-element */
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import styled from "styled-components";

const Div = styled.div`
  @media (min-width: 961px) {
    text-align: center !important;
    transform: ${(props) =>
      props.language === "en" ? "translateX(-45px)" : "translateX(45px)"};
    margin-bottom: 50px;
  }
  @media (max-width: 960px) {
    margin: 10px 25px 20px 25px;
    text-align: ${(props) => (props.language === "en" ? "left" : "right")};
  }
  &,
  * {
    font-weight: 600;
  }

  span {
    display: block;
    height: 2px;
    width: 10px;
    margin-top: 15px;
    margin-bottom: 10px;
    border-top: solid 3px;
    @media (min-width: 961px) {
      margin: 0 auto;
      margin-top: 15px !important;
      margin-bottom: 10px !important;
    }
  }
  img {
    max-height: 22px;
    max-width: 24px;
    object-fit: cover;
  }
  h5 {
    @media (min-width: 961px) {
      margin: 0 auto !important;
      text-align: center !important;
    }
  }
`;

export default function Item({ icon, title }) {
  const { i18n } = useTranslation();

  return (
    <Div language={i18n.language}>
      <img src={icon} alt="icon" />
      <span />
      <h5>{title}</h5>
    </Div>
  );
}
