/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import React, { useState } from "react";
import { RootStateOrAny, useSelector } from "react-redux";
import styled from "styled-components";
import Popup from "./deliveryPopup";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";

const DeliveryInputsWrapper = styled.div`
  margin-top: 15px !important;
  width: 100%;
  .delivery-input {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 25px;
    border-radius: var(--radius-3);
    border: solid 2px var(--color-7);
    div {
      width: 100%;
    }
    img {
      position: absolute;
      left: ${(props) => (props.language === "en" ? "20px" : null)};
      right: ${(props) => (props.language === "ar" ? "20px" : null)};
      top: 50%;
      transform: translate(0, -50%) !important;
    }
    input {
      padding: 25px 20px !important;
      border: none;
      width: 100%;
      /* min-width: 300px; */
      @media (max-width: 570px) {
        padding: 20px !important;
      }
      &::placeholder {
        color: var(--color-9) !important;
      }
    }
    span {
      color: var(--color-3);
      cursor: pointer;
    }
  }
  @media (max-width: 570px) {
    flex-direction: column;
  }
`;

export default function DeliveryInputs({ rentalDetailsLocation }) {
  //Hooks
  const { t, i18n } = useTranslation();
  const { query } = useRouter() || {};
  const { bookingId } = query || {};

  //Store
  const confirmedDeliveryLocation = useSelector(
    (state: RootStateOrAny) => state.search_data.confirmed_delivery_location
  );
  const currentLocation = useSelector(
    (state: RootStateOrAny) => state.search_data.current_location
  );
  const userAddress = useSelector(
    (state: RootStateOrAny) => state.search_data.user_address
  );

  //State
  const [isOpen, setIsOpen] = useState(false);
  const [switchChecked, setSwitchChecked] = useState(false);

  //Life Cycle
  useEffect(() => {
    if (!confirmedDeliveryLocation && !bookingId && !rentalDetailsLocation) {
      setIsOpen(true);
    }
  }, [userAddress, confirmedDeliveryLocation]);

  return (
    <DeliveryInputsWrapper item container md={12} language={i18n.language}>
      <div
        className="delivery-input gap-10px font-15px"
        onClick={() => setIsOpen(true)}
      >
        <img src="/assets/images/deliverypickup.svg" alt="icon" />
        <div>
          <p
            key={`${confirmedDeliveryLocation} ${currentLocation} ${rentalDetailsLocation}`}
            style={{
              margin: 0,
              // padding: "25px 20px",
              color:
                confirmedDeliveryLocation?.airport?.name ||
                confirmedDeliveryLocation?.address ||
                confirmedDeliveryLocation?.name ||
                currentLocation?.address ||
                rentalDetailsLocation
                  ? "inherit"
                  : "var(--color-9)",
            }}
          >
            {confirmedDeliveryLocation?.airport?.name ||
              confirmedDeliveryLocation?.address ||
              confirmedDeliveryLocation?.name ||
              currentLocation?.address ||
              rentalDetailsLocation ||
              t("Choose Delivery Location")}
          </p>
        </div>
        {!bookingId ? (
          <span className="mx-2 d-inline-block">{t("Edit")}</span>
        ) : null}
      </div>
      <div>
        {isOpen && !rentalDetailsLocation ? (
          <Popup
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            switchChecked={switchChecked}
            setSwitchChecked={setSwitchChecked}
          />
        ) : null}
      </div>
    </DeliveryInputsWrapper>
  );
}
