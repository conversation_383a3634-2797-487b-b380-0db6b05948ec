@import "./overrides.scss";

*:focus {
  outline: none;
}

.section-fancy-title {
  h4:nth-child(1) {
    font-weight: 400;
  }
  h4:nth-child(2) {
    margin-top: 10px;
    font-weight: 600;
  }
  @media (min-width: 901px) {
    h4 {
      font-size: 27px;
      line-height: 28px;
    }
  }
}
html[lang="ar"] {
  .select,
  .text-align-localized {
    text-align: right !important;
  }
}
html[lang="en"] {
  .select,
  .text-align-localized {
    text-align: left !important;
  }
}
.cursor-pointer {
  cursor: pointer;
}

.title {
  font-size: 27px;
  line-height: 38px;
}

.bold {
  font-weight: bold !important;
}

.medium {
  font-weight: 600;
}

.text-decoration-underline {
  text-decoration: underline;
}

.smooth {
  transition: all ease 0.3s;
}

.white-background {
  background-color: var(--color-4);
}

.grey-background {
  background-color: var(--color-6);
}

.gap-2px {
  gap: 2px;
}
.gap-5px {
  gap: 5px;
}
.gap-10px {
  gap: 10px;
}
.gap-15px {
  gap: 15px;
}
.gap-20px {
  gap: 20px;
}
.gap-25px {
  gap: 25px;
}
.gap-30px {
  gap: 30px;
}
.gap-35px {
  gap: 35px;
}
.gap-40px {
  gap: 40px;
}
.gap-45px {
  gap: 45px;
}
.gap-50px {
  gap: 50px;
}

.color-1 {
  color: var(--color-1);
}
.color-2 {
  color: var(--color-2);
}
.color-3 {
  color: var(--color-3);
}
.color-4 {
  color: var(--color-4);
}
.color-5 {
  color: var(--color-5);
}
.color-6 {
  color: var(--color-6);
}
.color-7 {
  color: var(--color-7);
}
.color-8 {
  color: var(--color-8);
}
.color-9 {
  color: var(--color-9);
}
.color-10 {
  color: var(--color-10);
}
.color-11 {
  color: var(--color-11);
}
.color-12 {
  color: var(--color-12);
}
.color-13 {
  color: var(--color-13);
}
.color-14 {
  color: var(--color-14);
}
.color-15 {
  color: var(--color-15);
}
.color-16 {
  color: var(--color-16);
}
.color-17 {
  color: var(--color-17);
}
.color-18 {
  color: var(--color-18);
}
.color-19 {
  color: var(--color-19);
}
.color-20 {
  color: var(--color-20);
}
.color-green {
  color: var(--color-22);
}

.font-12px {
  font-size: 12px;
}
.font-13px {
  font-size: 13px;
}
.font-14px {
  font-size: 14px;
}
.font-15px {
  font-size: 15px;
}
.font-16px {
  font-size: 16px;
}
.font-17px {
  font-size: 17px;
}
.font-18px {
  font-size: 18px;
}
.font-20px {
  font-size: 20px;
}
.font-27px {
  font-size: 27px;
}
.radius-1 {
  border-radius: var(--radius-1);
}
.radius-2 {
  border-radius: var(--radius-2);
}
.radius-3 {
  border-radius: var(--radius-3);
}
.radius-4 {
  border-radius: var(--radius-4);
}

.fill {
  * {
    transition: all 0.5s ease;
    fill: var(--color-9);
  }
}

.error {
  border: solid 1px var(--color-9) !important;
}

.bg-status {
  background-color: #4f4f4f;
}

.primary-btn {
  background-color: var(--color-2);
  color: var(--color-4);
  font-size: 15px;
  line-height: 22px;
  border: none;
  border-radius: var(--radius-1);
  @media (max-width: 570px) {
    font-size: 11px;
    font-weight: 700;
  }
  outline: none;
}

.inputRounded{
  
  .MuiOutlinedInput-root{
    border-radius: 50px;
    padding: 0;
  }
  @media (max-width: 768px){
    width: 100%;
  } 
}


.separator {
  width: 100%;
  border-bottom: solid 1px var(--color-15);
}
.dimmed {
  filter: grayscale(1);
  transition: all ease 0.3s;
  pointer-events: none;
}

button[disabled]{
  filter: grayscale(1);
}
.cursor-pointer-none{
  pointer-events: none;
}
.swiper-slide-prev{
  img{
    display: none;
  }
}

.flex-wrap-md{
  @media (max-width: 768px) {
    flex-wrap: wrap;
  }
}


