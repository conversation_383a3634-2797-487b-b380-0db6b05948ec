/* eslint-disable @next/next/no-img-element */
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import FeaturesModal from "./Modal";
import { useState } from "react";
import { RootStateOrAny, useSelector } from "react-redux";

const FeaturesCard = styled.div`
  margin-bottom: 24px;
  background-color: #fff;
  border-radius: var(--radius-2);
  padding: 20px 0;
  > div {
    padding: 0px 24px;
  }
  hr {
    margin: 15px 0;
  }
  .make-model {
    font-size: 24px;
    font-weight: 700;
    span {
      color: #aeaeae;
      font-weight: 400;
      display: block;
    }
  }
  .wrapper {
    display: flex;
    gap: 30px;
    align-items: center;
  }
`;

const Feature = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  text-transform: capitalize;
  min-width: 12%;
  &:not(:last-child) {
    &:after {
      content: " ";
      height: 100%;
      width: 1px;
      background: black;
      opacity: 0.25;
      position: absolute;
      right: ${(props) => (props.lang === "en" ? "-20%" : "")};
      left: ${(props) => (props.lang === "ar" ? "-20%" : "")};
    }
  }
  img {
    width: 25px;
    height: 35px;
    object-fit: fill;
  }
  div:nth-child(2) {
    color: #aeaeae;
    font-size: 13px;
  }
  div:last-child {
    font-size: 16px;
    font-weight: bold;
    margin-top: -5px;
  }
`;

const More = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0px;
  background-color: #f6f6f6;
  border-radius: var(--radius-3);
  padding: 10px;
  align-items: center;
  width: 70px;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
`;
const SingleFeatureComponent = ({ src, title, value }) => {
  return (
    <div className="d-flex align-items-center" style={{ gap: "8px" }}>
      <div>
        <img className="icon" src={src} alt="icon" style={{ width: "21px" }} />
      </div>
      <div className="text-align-localized">
        <span className="color-12 font-12px">{title}</span>
        <span className="text-capitalize" style={{ marginTop: "-5px" }}>
          <br />
          {value}
        </span>
      </div>
    </div>
  );
};

export type TFeature = {
  id: number;
  icon: string;
  name: string;
  isActive: string;
};

function CarFeatures() {
  const { t, i18n } = useTranslation();
  const { car_data } =
    useSelector((state: RootStateOrAny) => state?.cars) || {};

  const {
    carMakeName,
    carModelName,
    year,
    distanceByMonth,
    distanceBetweenCarUser,
    additionalDistanceCost,
    ownCarDetail,
    isUnlimited,
    isUnlimitedFree,
  } = car_data || {};

  const [isOpen, setIsOpen] = useState(false);

  const { km, colorName, transmissionName, features } =
    ownCarDetail?.ownCarFeature || {};
  return (
    <FeaturesCard>
      <div>
        <h2 className="make-model">
          {carMakeName} {carModelName}
          <span className="year d-block mt-2">{year}</span>
        </h2>{" "}
      </div>
      <div
        className="d-flex justify-content-between flex-column-mobile p-0"
        id="car-info"
      >
        <div
          className="footer d-flex px-3 mb-0"
          style={{ gap: "30px", flexWrap: "wrap" }}
        >
          <SingleFeatureComponent
            src="/assets/icons/city.svg"
            title={t("km.away")}
            value={distanceBetweenCarUser}
          />
          {isUnlimited && isUnlimitedFree ? (
            <>
              <SingleFeatureComponent
                src="/assets/icons/allowedkm.svg"
                title={t("Unlimited kilometers")}
                value={t(`is unlimited`)}
              />
              <SingleFeatureComponent
                src="/assets/icons/additionalkm.svg"
                title={t("extra.km")}
                value={t("Free")}
              />
            </>
          ) : (
            <>
              <SingleFeatureComponent
                src="/assets/icons/allowedkm.svg"
                title={t("Additional KM")}
                value={`${distanceByMonth} ${t("KM/month")}`}
              />
              <SingleFeatureComponent
                src="/assets/icons/additionalkm.svg"
                title={t("km.per.day")}
                value={additionalDistanceCost}
              />
            </>
          )}
        </div>
      </div>
      <hr />
      <div className="wrapper">
        <Feature lang={i18n.language}>
          <img src={"/assets/icons/km.svg"} alt={"KM"} />
          <div className="title">{t("KM") as string}</div>
          <div className="value">{km}</div>
        </Feature>
        <Feature lang={i18n.language}>
          <img src={"/assets/icons/color.svg"} alt={"KM"} />
          <div className="title">{t("Color") as string}</div>
          <div className="value">{colorName}</div>
        </Feature>
        <Feature lang={i18n.language}>
          <img src={"/assets/icons/transmission.svg"} alt={"KM"} />
          <div className="title">{t("transmission") as string}</div>
          <div className="value">{transmissionName}</div>
        </Feature>

        {Boolean(features?.length) && (
          <More onClick={() => setIsOpen(true)}>
            <img src="/assets/icons/more.svg" alt="more" />
            <span className="text-center">{t("More") as string}</span>
          </More>
        )}
      </div>
      {features?.length ? (
        <FeaturesModal
          {...{
            isOpen,
            setIsOpen,
            features,
          }}
        />
      ) : null}
    </FeaturesCard>
  );
}

export default CarFeatures;
