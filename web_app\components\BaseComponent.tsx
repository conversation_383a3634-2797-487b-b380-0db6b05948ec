/* eslint-disable react-hooks/exhaustive-deps */
import { ApolloProvider } from "@apollo/client";
import useApolloClient from "gql/apollo-client";
import ConnectionStatus from "./ConnectionStatus";
import useAppLogic from "./shared/useAppLogic";

export default function BaseComponent({ children }) {
  useAppLogic();
  const { client } = useApolloClient();

  return (
    <>
      <ApolloProvider client={client}>{children}</ApolloProvider>
      <ConnectionStatus />
    </>
  );
}
