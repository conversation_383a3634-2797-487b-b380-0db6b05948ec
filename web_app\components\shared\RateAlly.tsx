import Popup from "./popup";
import Rating from "@material-ui/lab/Rating";
import { useRef, useState } from "react";
import styled from "styled-components";
import { RateAllyMutation } from "gql/mutations/RateAlly";
import TextareaAutosize from "@material-ui/core/TextareaAutosize";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery } from "@apollo/client";
import { useRouter } from "next/router";
import { useSnackbar } from "notistack";

const DIV = styled.div`
  text-align: center;
  .allyBtn {
    text-align: center;
    background-color: var(--color-3);
    border-radius: var(--radius-2);
    color: var(--color-4);
    padding: 15px 20px 15px 20px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 20px;
  }
`;
const RateAlly = ({ isOpen, title, setIsOpen, rentalId = undefined }) => {
  const [allyRate, setAllyRate] = useState();
  const { t, i18n } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();

  const router = useRouter();
  const bookingId = router?.query.bookingId;

  const textareaVal = useRef(null);
  const [allyRateMutation, { data: allyRateData, loading: RateAllyLoading }] =
    useMutation(RateAllyMutation);

  const handelAllyRate = () => {
    allyRateMutation({
      variables: {
        allyComment: textareaVal.current.value,
        allyRate,
        rentalId: rentalId ? rentalId : bookingId,
      },
    }).then((res) => {
      if (res.data.allyRate.errors.length) {
        res.data.allyRate.errors.map((err) => {
          enqueueSnackbar(err.message, { variant: "error" });
        });
      } else {
        enqueueSnackbar(t("Ally Rated Sucessfully"), { variant: "success" });
        refetch();
        setIsOpen(false);
      }
    });
  };
  return (
    <Popup
      maxWidth="md"
      fullWidth
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      title={title}
    >
      <DIV>
        <p className="text-center">
          {t(
            "Rate Ally by the quality of communication and the level of service"
          )}
        </p>
        <div className="text-center" style={{ direction: "ltr" }}>
          <Rating
            value={allyRate}
            onChange={(event, newValue) => {
              setAllyRate(newValue);
            }}
          />
        </div>
        <div className="w-100">
          <TextareaAutosize
            className="w-100"
            ref={textareaVal}
            aria-label="minimum height"
            minRows={3}
            placeholder={t("comment")}
          />
        </div>
        <div className="w-100">
          <button
            className="allyBtn w-50 border-0"
            disabled={!allyRate}
            onClick={() => {
              handelAllyRate();
            }}
          >
            {t("done")}
          </button>
        </div>
      </DIV>
    </Popup>
  );
};
export default RateAlly;
