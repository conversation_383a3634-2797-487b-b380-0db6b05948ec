import Head from "next/head";
import { useRouter } from "next/router";
import MyRequestsView from "components/MyRequests";

function MyRequestsPage({ locale }) {
  const router = useRouter();

  return (
    <div>
      <Head>
        <link
          rel="canonical"
          href={`https://carwah.com.sa/${locale}${router?.pathname}`}
        />
        <link
          rel="alternate"
          hrefLang={locale === "en" ? "ar" : "en"}
          href={`https://carwah.com.sa/${locale === "en" ? "ar" : "en"}${
            router?.pathname
          }`}
        />
      </Head>
      <MyRequestsView />
    </div>
  );
}

export async function getStaticProps(context = {}) {
  const { locale } = context;

  return {
    props: {
      locale,
    },
  };
}

export default MyRequestsPage;
