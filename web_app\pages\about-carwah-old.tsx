import Head from "next/head";
import { useRouter } from "next/router";
import About from "components/AboutCarwah";

function AboutPage({ locale }) {
  const router = useRouter();

  return (
    <div>
      <Head>
        <link
          rel="canonical"
          href={`https://carwah.com.sa/${locale}${router?.pathname}`}
        />
        <link
          rel="alternate"
          hrefLang={locale === "en" ? "ar" : "en"}
          href={`https://carwah.com.sa/${locale === "en" ? "ar" : "en"}${
            router?.pathname
          }`}
        />
      </Head>
      <About />
    </div>
  );
}

export async function getStaticProps(context = {}) {
  const { locale } = context;

  return {
    props: {
      locale,
    },
  };
}

export default AboutPage;

// export async function getStaticPaths() {
//   // this will be generated, hardcoded this pages for testing
//   return {
//     paths: [
//       {
//         params: {
//           slug: ['produkte']
//         },
//         locale: 'de-DE'
//       },
//       {
//         params: {
//           slug: []
//         },
//         locale: 'de-DE'
//       },
//       {
//         params: {
//           slug: ['products']
//         },
//         locale: 'en-US'
//       },
//       {
//         params: {
//           slug: []
//         },
//         locale: 'en-US'
//       }
//     ],
//     fallback: true
//   };
// }
