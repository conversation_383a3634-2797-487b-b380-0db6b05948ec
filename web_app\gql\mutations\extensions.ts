const { gql } = require("@apollo/client");

const Create_Extension_Request = gql`
  mutation CreateRentalDateExtensionRequest(
    $rentalId: ID!
    $dropOffDate: String!
    $dropOffTime: String!
    $paymentMethod: PaymentMethod!
    $totalPrice: Float
    $isPaid: Boolean
    $withWallet: Boolean
    $withInstallment: Boolean
  ) {
    createRentalDateExtensionRequest(
      rentalId: $rentalId
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      paymentMethod: $paymentMethod
      isPaid: $isPaid
      totalPrice: $totalPrice
      withWallet: $withWallet
      withInstallment: $withInstallment
    ) {
      status
      errors
      rentalDateExtensionRequest {
        id
      }
    }
  }
`;

const Update_Extension_Request = gql`
  mutation UpdateRentalDateExtensionRequest(
    $id: ID!
    $dropOffDate: String!
    $dropOffTime: String!
    $withInstallment: Boolean
  ) {
    updateRentalDateExtensionRequest(
      id: $id
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      withInstallment: $withInstallment
    ) {
      status
      errors
    }
  }
`;

const Extension_Pay_By_Wallet = gql`
  mutation ExtensionPayByWallet($extensionRequestId: ID!) {
    extensionPayByWallet(extensionRequestId: $extensionRequestId) {
      rentalDateExtensionRequest {
        additionalUnlimitedKilometer
        id
        refundedAt
        refundedBy
        status
        withWallet
      }
    }
  }
`;
export {
  Create_Extension_Request,
  Update_Extension_Request,
  Extension_Pay_By_Wallet,
};
