/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Popup from "components/shared/popup";
import { useDispatch, useSelector } from "react-redux";
import {
  setYakeenVerificationSuccess,
  toggleYakeenCompleteMissingModal,
  toggleYakeenModal,
} from "store/user/action";
import { TProfileStatus } from "types/profile";
import { useForm } from "react-hook-form";
import YakeenForm from "./form";
import ConfirmVerification from "./ConfirmVerification";
import { useEffect, useState } from "react";
import { TUserReducer } from "store/user/reducer";
import VerificationStatus from "./VerificationStatus";
import FinalStep from "./FinalStep";

const Div = styled.div`
  .status-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 18px;
    margin-top: 30px;
    /* margin-bottom: 20px; */
    > div {
      padding: 17px;
      text-align: center;
      border-radius: 12px;
      font-size: 16px;
      cursor: pointer;
    }
  }
`;

export default function YakeenModal() {
  const { t } = useTranslation();
  const [isConfirmationPopupOpen, setIsConfirmationPopupOpen] = useState(false);

  const dispatch = useDispatch();
  const {
    handleSubmit,
    control,
    formState: { errors, isDirty, isValid },
    register,
    watch,
    setValue,
  } = useForm({
    mode: "all",
    reValidateMode: "onChange",
    defaultValues: {
      status: "citizen",
      driverLicenseExpireAt: null,
      dob: null,
      passportExpireAt: null,
    } as {
      status: TProfileStatus;
      nid: string;
      nationalityId: string;
      driverLicenseExpireAt: string | null;
      passportExpireAt?: string | null;
      dob?: string | null;
      driverLicense?: string | null;
    },
  });

  const {
    is_yakeen_modal_open,
    is_yakeen_verification_success,
    is_yakeen_missing_modal_open,
    profile,
  } = useSelector((state: { user: TUserReducer }) => state.user) || {};

  const { status: _status } = profile?.customerProfile || {};

  useEffect(() => {
    if (_status) {
      setValue("status", _status);
    }
  }, [_status]);

  useEffect(() => {
    return () => {
      dispatch(setYakeenVerificationSuccess(null));
    };
  }, []);

  if (is_yakeen_missing_modal_open) {
    return (
      <Popup
        maxWidth="xs"
        isOpen={is_yakeen_missing_modal_open}
        setIsOpen={() =>
          dispatch(
            toggleYakeenCompleteMissingModal(!is_yakeen_missing_modal_open)
          )
        }
        title={""}
        onClose={() => {
          dispatch(toggleYakeenCompleteMissingModal(false));
        }}
      >
        <Div>
          <h5 className="text-center mb-4 bold">{t("Final Step") as string}</h5>
          <p className="text-center" style={{ marginTop: "20px" }}>
            {
              t(
                "Please add the attachments below to complete verification"
              ) as string
            }
          </p>
          <FinalStep {...{ watch, register, errors, handleSubmit, setValue }} />
        </Div>
      </Popup>
    );
  }
  if (is_yakeen_modal_open) {
    return (
      <Popup
        maxWidth="xs"
        isOpen={is_yakeen_modal_open}
        setIsOpen={() => dispatch(toggleYakeenModal(!is_yakeen_modal_open))}
        title={""}
        onClose={() => {
          if (is_yakeen_verification_success) {
            dispatch(setYakeenVerificationSuccess(null));
          }
        }}
      >
        <Div>
          <h5 className="text-center mb-4">
            {t("Account Verification") as string}
          </h5>

          {is_yakeen_verification_success ? (
            <VerificationStatus {...{ is_yakeen_verification_success }} />
          ) : (
            <>
              <div className="text-center">
                <img src="/assets/images/yakeen.svg" alt="elm" />
              </div>
              <p style={{ marginTop: "20px" }}>{t("YakeenText") as string}</p>
              <YakeenForm
                {...{
                  handleSubmit,
                  onSubmit: () => setIsConfirmationPopupOpen(true),
                  control,
                  watch,
                  errors,
                  register,
                  setIsConfirmationPopupOpen,
                  isDirty,
                  isValid,
                }}
              />
            </>
          )}
          <ConfirmVerification
            {...{ watch, isConfirmationPopupOpen, setIsConfirmationPopupOpen }}
          />
        </Div>
      </Popup>
    );
  }
  return null;
}
