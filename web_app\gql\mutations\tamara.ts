import { gql } from "@apollo/client";

export const Tamara_Create_Checkout = gql`
  mutation tamaraCreateCheckoutSession(
    $locale: String
    $rentalId: ID!
    $requestPlatform: String!
    $withWallet: Boolean
  ) {
    tamaraCreateCheckoutSession(
      locale: $locale
      rentalId: $rentalId
      requestPlatform: $requestPlatform
      withWallet: $withWallet
    ) {
      checkoutUrl
      #   orderId
      #   checkoutId
        errors
      #   status
    }
  }
`;
export const Tamara_Create_Installment_Checkout = gql`
  mutation tamaraCreateInstallmentCheckoutSession(
    $locale: String
    $rentalId: ID!
    $requestPlatform: String!
    $withWallet: Boolean
  ) {
    tamaraCreateInstallmentCheckoutSession(
      locale: $locale
      rentalId: $rentalId
      requestPlatform: $requestPlatform
      withWallet: $withWallet
    ) {
      checkoutUrl
      #   orderId
      #   checkoutId
      #   errors
      #   status
    }
  }
`;
export const Tamara_Create_Extension_Checkout = gql`
  mutation tamaraCreateExtensionCheckoutSession(
    $locale: String
    $rentalExtensionId: ID!
    $requestPlatform: String!
    $withWallet: Boolean
  ) {
    tamaraCreateExtensionCheckoutSession(
      locale: $locale
      rentalExtensionId: $rentalExtensionId
      requestPlatform: $requestPlatform
      withWallet: $withWallet
    ) {
      checkoutUrl
      #   orderId
      #   checkoutId
      #   errors
      #   status
    }
  }
`;
