import { PAY_WITH_WALLET, WALLET_BALANCE, FULLY_PAID_WALLET } from "./action";

export default function walletReducer(
  state = {
    pay_with_wallet: false,
    balance: null,
    is_paid_by_wallet: "Error",
  },
  action: { type: string; payload: any }
) {
  switch (action.type) {
    case PAY_WITH_WALLET:
      return { ...state, pay_with_wallet: action.payload };
    case WALLET_BALANCE:
      return { ...state, balance: action.payload };
    case FULLY_PAID_WALLET:
      return { ...state, is_paid_by_wallet: action.payload };
    default:
      return state;
  }
}
