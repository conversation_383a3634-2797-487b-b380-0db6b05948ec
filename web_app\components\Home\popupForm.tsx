import Popup from "components/shared/popup";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import ToAppPopup from "components/shared/toAppPopup";

const Div = styled.div`
  @media (min-width: 961px) {
    padding: 40px 70px;
    /* width: 570px; */
  }
  @media (max-width: 960px) {
    padding: 20px 35px;
  }
  h3 {
    font-weight: 700;
  }
  form {
    display: grid;
    grid-row-gap: 20px;
    margin-top: 50px;
    input,
    textarea {
      border: solid 1px var(--color-7);
      border-radius: var(--radius-2);
      padding: 15px 25px;
      width: 100%;
    }
    .required {
      position: relative;
      &::after {
        content: "*";
        position: absolute;
        top: 10px;
        left: ${(props) => (props.language === "en" ? "15px" : null)};
        right: ${(props) => (props.language === "ar" ? "15px" : null)};
        color: #f00;
      }
    }
    button {
      color: var(--color-4);
      background-color: var(--color-3);
      border: none;
      border-radius: var(--radius-3);
      padding: 12px 0;
      margin-bottom: 10px;
      &:focus {
        outline: none;
      }
    }
  }
`;

export default function PopupForm({ isOpen, setIsOpen }) {
  const { t, i18n } = useTranslation();
  const [isOpenToAppPopup, setIsOpenToAppPopup] = useState<boolean>();

  return (
    <>
      <ToAppPopup
        isOpenToAppPopup={isOpenToAppPopup}
        setIsOpenToAppPopup={setIsOpenToAppPopup}
      />
      <Popup isOpen={isOpen} setIsOpen={setIsOpen}>
        <Div language={i18n.language}>
          <h3>{t("Contact Us") as string}</h3>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              setIsOpenToAppPopup(true);
            }}
          >
            <div className="required">
              <input required type="text" placeholder={t("Name")} />
            </div>
            <div className="required">
              <input required type="tel" placeholder={t("Phone Number")} />
            </div>
            <div className="required">
              <input required type="email" placeholder={t("Email")} />
            </div>
            <div className="required">
              <textarea required placeholder={t("Message")} />
            </div>
            <button type="submit" disabled className="disabled">
              {t("Save") as string}
            </button>
          </form>
        </Div>
      </Popup>
    </>
  );
}
