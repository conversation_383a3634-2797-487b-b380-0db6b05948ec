/* eslint-disable @next/next/no-img-element */
import FavoriteIcon from "components/shared/favoriteIcon";
import WhiteCard from "components/shared/whiteCard";
import styled from "styled-components";
import { useRouter } from "next/dist/client/router";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { setClearCar } from "store/cars/action";
import { useSnackbar } from "notistack";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  cursor: pointer;
  height: 100%;
  > div {
    /* padding: 20px; */
    > div {
      width: 100%;
    }
  }
  .price-label {
    color: var(--color-4);
    background-color: var(--color-2);
    padding: ${(props) =>
      props.lang === "ar" ? "7px 15px 15px 15px" : "10px 15px 10px 15px"};
    border-radius: ${(props) =>
      props.lang === "ar" ? "0 var(--radius-2)" : "var(--radius-2) 0"};
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    align-items: center;
    gap: 5px;
    min-width: 150px;

    span {
      display: block;
    }
    > div {
      align-items: center;
      div:last-child {
        font-size: 24px;
        font-weight: bold;
      }
      div:first-child {
        font-size: 24px;
        width: max-content;
        text-align: start;
        /* transform: translateY(-4px); */
      }
    }
  }
  .car-image {
    height: 150px;
    object-fit: contain;
    @media (max-width: 900px) {
      height: 120px !important;
    }
  }
`;

const RegularCarCardHeader = styled.div``;

const RentToOwnCardHeader = styled.div`
  display: flex;
  justify-content: end;

  border-radius: 40px;
  align-items: center;

  div {
    font-weight: bold;
    font-size: 18px;
    &:first-child {
      background-color: #f3f3f3;
      padding: ${(props) =>
        props.lang === "en" ? "10px 40px 10px 15px" : "2px 15px 10px 40px"};
      border-radius: 30px;
      margin-right: ${(props) => (props.lang === "en" ? "-35px" : "")};
      margin-left: ${(props) => (props.lang === "ar" ? "-35px" : "")};
    }
  }
  .allowed-cars-count {
    background-color: #2a292f;
    padding: ${(props) =>
      props.lang === "en" ? "6px 8px 6px 8px" : "4px 7px 7px 7px"};
    border-radius: 50%;
    margin: 0 8px;
    border: 7px solid white;
    color: white;
    min-width: 51px;
    min-height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
`;

export default function Card(props) {
  //Hooks
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();
  const { enqueueSnackbar } = useSnackbar();

  //Store
  const { selection_index, user_address } =
    useSelector((state: RootStateOrAny) => state.search_data) || {};

  //Props
  const { isRentToOwn } = props || {};
  return (
    <>
      <Div
        lang={i18n.language}
        isRentToOwn={isRentToOwn}
        onClick={() => {
          dispatch(setClearCar(undefined));
          if (!user_address?.pick_up || !user_address?.return) {
            router.push("/car-search#search-box");
            enqueueSnackbar(t("Car Pickup location is required") as string, {
              variant: "error",
            });
          } else if (!isRentToOwn) {
            router.push(`car-branches/${props?.carId}`);
          } else if (isRentToOwn) {
            router.push(`rent-to-own-cars/${props?.id}`);
          }
        }}
      >
        <WhiteCard>
          <div className="pt-3 px-4">
            {!isRentToOwn ? (
              <RegularCarCardHeader className="d-flex justify-content-between align-items-center">
                <p>
                  <span>{t("Available at") as string}</span>{" "}
                  <span className="bold">{props?.branchesCount}</span>{" "}
                  <span className="bold">{t("branch") as string}</span>
                </p>
              </RegularCarCardHeader>
            ) : (
              <RentToOwnCardHeader lang={i18n.language}>
                <div>{t("Allowed") as string}</div>
                <div className="allowed-cars-count">
                  {props?.availableCarsCount}
                </div>
              </RentToOwnCardHeader>
            )}
            <img
              src={
                props?.logo ||
                props?.carThumb ||
                props?.images?.[1] ||
                props?.images?.[0] ||
                "/assets/images/car.png"
              }
              alt="car"
              className="w-100 my-2 car-image"
            />
            <div className="mb-2">
              <div className="d-flex justify-content-between align-items-end">
                <h2 className="medium font-18px text-capitalize">
                  {props?.makeName || props?.make?.name}{" "}
                  {props?.modelName || props?.carModel?.name}{" "}
                  {props?.versionName || props?.version?.name}
                  {props?.year}
                </h2>
              </div>
              <div className="d-flex justify-content-between">
                {!isRentToOwn ? (
                  <h3 className="font-15px my-2 color-16">{`${props.t(
                    "or similar"
                  )}`}</h3>
                ) : null}
              </div>

              {/* {!isRentToOwn ? (
                <>
                  <div
                    className="footer d-flex  justify-content-end"
                    style={{ gap: "10px", flexDirection: "row-reverse" }}
                  >
                    <div className="separator my-3" />
                  </div>
                </>
              ) : null} */}
            </div>
          </div>
          {/* <div
            className="footer d-flex  justify-content-end"
            style={{ gap: "10px", flexDirection: "row-reverse" }}
          >
            <div className="separator mt-0 mb-3" />
          </div> */}
          {!isRentToOwn ? (
            <div
              className="d-flex align-items-center px-3 "
              style={{ gap: "7px" }}
            >
              <img src="/assets/images/features/feature1.svg" alt="icon" />
              <span>
                {i18n.language === "ar"
                  ? props?.transmission === "auto"
                    ? "أوتوماتيك"
                    : "مانيوال"
                  : props?.transmission}
              </span>
            </div>
          ) : null}
          <div className="d-flex justify-content-between">
            <div
              className="d-flex mt-2 justify-content-end w-100 align-items-center"
              style={{ gap: "10px" }}
            >
              <span className="color-2 font-14px px-4">
                {props?.branchesCount > 1 || isRentToOwn
                  ? (t("start from") as string)
                  : ""}
              </span>
              <div>
                <div className="font-16px price-label">
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      gap: "2px",
                      alignItems: "baseline",
                    }}
                  >
                    <div style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                      <RiyalSymbol />
                    </div>
                    <div style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                      {!isRentToOwn
                        ? selection_index !== 1
                          ? props?.minimumCarPrice
                          : props?.minimumCarMonthsPrice
                        : props?.minimumMonthlyInstallment}
                    </div>
                  </div>
                  <div>
                    {selection_index !== 1 && !isRentToOwn
                      ? ` ${props.t("/day")}`
                      : ` ${props.t("/month")}`}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </WhiteCard>
      </Div>
    </>
  );
}
