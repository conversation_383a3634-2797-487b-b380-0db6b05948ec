import { gql } from "@apollo/client";

export const SimpleCars_Query = gql`
  query simpleCars(
    $allyFeatures: [ID!]
    $branchId: ID
    $canDelivery: Boolean
    $canHandover: Boolean
    $canHandoverInAntherCity: Boolean
    $canHandoverInBranch: Boolean
    $carModelName: String
    $carVersionName: String
    $couponId: ID
    $dailyPriceFrom: Int
    $dailyPriceTo: Int
    $distance: Float
    $dropOffDeliverLat: Float
    $dropOffDeliverLng: Float
    $dropOffLocationId: ID
    $extraServices: [ID!]
    $filter: String
    $insuranceId: ID
    $insuranceType: Int
    $isActive: Boolean
    $isApiIntegrated: Boolean
    $isBarq: Boolean
    $isInstantConfirmation: Boolean
    $isUnlimited: Boolean
    $kmFrom: Float
    $kmTo: Float
    $lat: Float
    $limit: Int
    $lng: Float
    $make: ID
    $makeName: String
    $model: ID
    $page: Int
    $pickEndDate: String
    $pickEndTime: String
    $pickStartDate: String
    $pickStartTime: String
    $pickUpDeliverLat: Float
    $pickUpDeliverLng: Float
    $pickUpLocationId: ID
    $sortBy: String
    $transmission: String
    $uniqueCars: Boolean
    $userLat: Float
    $userLng: Float
    $vehicleType: ID
    $version: ID
    $yearFrom: Int
    $yearTo: Int
    $airportId: ID
    $bannerId: ID
  ) {
    simpleCars(
      allyFeatures: $allyFeatures
      branchId: $branchId
      canDelivery: $canDelivery
      canHandover: $canHandover
      canHandoverInAntherCity: $canHandoverInAntherCity
      canHandoverInBranch: $canHandoverInBranch
      carModelName: $carModelName
      carVersionName: $carVersionName
      couponId: $couponId
      dailyPriceFrom: $dailyPriceFrom
      dailyPriceTo: $dailyPriceTo
      distance: $distance
      dropOffDeliverLat: $dropOffDeliverLat
      dropOffDeliverLng: $dropOffDeliverLng
      dropOffLocationId: $dropOffLocationId
      extraServices: $extraServices
      filter: $filter
      insuranceId: $insuranceId
      insuranceType: $insuranceType
      isActive: $isActive
      isApiIntegrated: $isApiIntegrated
      isBarq: $isBarq
      isInstantConfirmation: $isInstantConfirmation
      isUnlimited: $isUnlimited
      kmFrom: $kmFrom
      kmTo: $kmTo
      lat: $lat
      limit: $limit
      lng: $lng
      make: $make
      makeName: $makeName
      model: $model
      page: $page
      pickEndDate: $pickEndDate
      pickEndTime: $pickEndTime
      pickStartDate: $pickStartDate
      pickStartTime: $pickStartTime
      pickUpDeliverLat: $pickUpDeliverLat
      pickUpDeliverLng: $pickUpDeliverLng
      pickUpLocationId: $pickUpLocationId
      sortBy: $sortBy
      transmission: $transmission
      uniqueCars: $uniqueCars
      userLat: $userLat
      userLng: $userLng
      vehicleType: $vehicleType
      version: $version
      yearFrom: $yearFrom
      yearTo: $yearTo
      airportId: $airportId
      bannerId: $bannerId
    ) {
      collection {
        allyClass
        arMakeName
        arModelName
        arVehicleName
        arVersionName
        branchesCount
        canDelivery
        carId
        carModelId
        carVersionId
        count
        deliverToAirport
        enMakeName
        enModelName
        enVehicleName
        enVersionName
        images
        isSameAlly
        logo
        makeId
        makeName
        maxDistance
        minMonthlyPrice
        minPrice
        minimumCarPrice
        minimumCarMonthsPrice
        modelName
        transmission
        vehicleImage
        vehicleName
        vehicleTypeId
        versionName
        year
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;
