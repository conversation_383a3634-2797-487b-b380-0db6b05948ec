/* eslint-disable @next/next/no-img-element */
import { useMutation } from "@apollo/client";
import { CircularProgress } from "@material-ui/core";
import {
  Accept_Business_Offers,
  Reject_Business_Offers,
} from "gql/mutations/offers";
import i18n from "localization";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Swal from "sweetalert2";
import React from "react";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  padding-top: 65px !important;
  position: relative;
  .border-localized {
    border-right: ${(props) =>
      props.language === "en" ? "solid 1px var(--color-11)" : null};
    border-left: ${(props) =>
      props.language === "ar" ? "solid 1px var(--color-15)" : null};
  }
  @media (max-width: 786px) {
    display: flex;
    flex-direction: column;
    gap: 20px;
    > div:not(:first-child),
    > div:not(:first-child) > div,
    > div:not(:first-child) > div > div {
      max-width: 100% !important;
      padding: 0 !important;
      border: none !important;
      margin: 0 !important;
    }
  }
`;

const Head = styled.div`
  position: absolute;
  top: 0;
  right: ${(props) => (props.language === "ar" ? 0 : null)};
  left: ${(props) => (props.language === "en" ? 0 : null)};
  width: 224px;
  display: grid;
  grid-template-columns: 60px 1fr;
  align-items: center;
  border-bottom: solid 1px #e1e3e7;
  img {
    width: 20px;
  }
  .class {
    color: var(--color-3);
    padding: 5px 10px;
    border-radius: var(--radius-1);
    background: var(--color-13);
  }
  .count {
    border-radius: ${(props) =>
      props.language === "ar" ? "0 15px 0 15px" : "15px 0 15px 0"};
    display: block;
    background: var(--color-2);
    color: var(--color-4);
    padding: 15px 20px;
  }
`;
export default function Card(props) {
  const { t } = useTranslation();
  const [acceptBusinessRentalOffer, { loading: loadingAccept }] = useMutation(
    Accept_Business_Offers
  );
  const [rejectBusinessRentalOffer, { loading: loadingReject }] = useMutation(
    Reject_Business_Offers
  );

  return (
    <Div
      className={`white-background m-0 p-0 row radius-2 p-4 align-items-center ${
        props?.businessRental?.status !== "pending"
          ? "justify-content-between"
          : null
      }`}
      language={i18n.language}
    >
      <Head language={i18n.language}>
        <div className="bold count d-flex">
          <span>#</span>
          <span>{Number(props.index) + 1}</span>
        </div>
        <div className="bold d-flex gap-15px align-items-center">
          <div className="d-flex gap-10px border-localized px-3 align-items-center">
            <span className="class">{props?.allyClass}</span>
            <span className="rate">{props?.allyRate}</span>
          </div>
          <div className="d-flex gap-10px">
            <span>{props?.rate}/5</span>
            <img src="/assets/images/star.svg" alt="star" />
          </div>
        </div>
      </Head>
      <div className="col-4 row border-localized align-items-center">
        <div className="col-5 font-18px text-align-localized car-details">
          {props?.businessRental?.makeId ? (
            <>
              <span className="bold">{`${props.businessRental.makeName} ${props.businessRental.modelName}`}</span>
              <br />
              <span>{props.businessRental.year}</span>
            </>
          ) : (
            <span>{props.businessRental.otherCarName}</span>
          )}
        </div>
        <div
          className="col-7 d-flex flex-column align-items-center color-3 py-4 radius-3"
          style={{ background: "var(--color-18)" }}
        >
          <div className="bold font-27px" style={{ marginTop: "-10px" }}>
            <p className="price">
              <div className="d-flex gap-5px">
                <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                  <RiyalSymbol />
                </span>
                <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                  {props?.offerPrice}
                </span>
              </div>
            </p>
          </div>
          <div className="font-14px color-11">
            {t("Includes vat") as string}
          </div>
          <div className="font-14px">{t("SR/month") as string}</div>
        </div>
      </div>
      <div className="col-3 border-localized">
        <div className="px-3">
          <div>
            <h6 className="color-11 mb-1">{t("kilometer/month")}:</h6>
            <p className="bold">{props?.kilometerPerMonth}</p>
          </div>
          <div className="mt-2">
            <h6 className="color-11 mb-1">{t("Additional distance Cost")}:</h6>
            <p className="bold">{props?.additionalKilometer}</p>
          </div>
          <div className="mt-2">
            <h6 className="color-11 mb-1">{t("Insurance type")}:</h6>
            <div className="d-flex gap-10px">
              {props?.carInsuranceStandard ? (
                <p className="bold font-14px">{`${t("Standard")} (${
                  props?.carInsuranceStandard
                } ${t("﷼ Monthly")})`}</p>
              ) : null}
              {props?.carInsuranceStandard && props?.carInsuranceFull
                ? "/"
                : null}
              {props?.carInsuranceFull ? (
                <p className="bold font-14px">{`${t("Full")} (${
                  props?.carInsuranceFull
                } ${t("﷼ Monthly")})`}</p>
              ) : null}
            </div>
          </div>
        </div>
      </div>
      <div className="col-4">
        <div>
          <div>
            <h6 className="color-11 mb-1">{t("AllyPolicies")}:</h6>
            <ul className="p-0 mt-3 text-align-localized">
              <li className="bold m-0 d-inline-block">
                <span> - </span>
                {props?.policyAndConditions[0]}.
              </li>
              <li className="bold m-0 d-inline-block">
                <span> - </span>
                {props?.policyAndConditions[1]}.
              </li>
            </ul>
          </div>
        </div>
      </div>
      {props?.status === "pending" ? (
        <div className="col-1">
          <div className="d-flex justify-content-between align-items-center gap-10px">
            <div
              className="cursor-pointer"
              onClick={() => {
                acceptBusinessRentalOffer({
                  variables: {
                    businessRentalId: props.businessRental.id,
                    businessRentalOfferId: props.id,
                  },
                }).then((res) => {
                  if (
                    res?.data?.acceptBusinessRentalOffer?.status === "success"
                  ) {
                    Swal.fire({
                      icon: "success",
                      title: t("Offer has been accepted"),
                      showConfirmButton: true,
                      timer: 10000,
                      showCloseButton: true,
                      confirmButtonText: `<span>${t("close.success")}</span>`,
                    });
                    setTimeout(() => {
                      props.refetch();
                    }, 1000);
                  }
                });
              }}
            >
              <img src="/assets/images/accept.svg" alt="img" />
              <p className="text-center color-11 mt-3">{t("Accept")}</p>
            </div>
            <div
              className="cursor-pointer"
              onClick={() => {
                rejectBusinessRentalOffer({
                  variables: {
                    businessRentalId: props.businessRental.id,
                    businessRentalOfferId: props.id,
                  },
                }).then((res) => {
                  if (
                    res?.data?.rejectBusinessRentalOffer?.status === "success"
                  ) {
                    Swal.fire({
                      icon: "success",
                      title: t("Offer has been rejected"),
                      showConfirmButton: true,
                      timer: 10000,
                      showCloseButton: true,
                      confirmButtonText: `<span>${t("close.success")}</span>`,
                    });
                    setTimeout(() => {
                      props.refetch();
                    }, 1000);
                  }
                });
              }}
            >
              <img src="/assets/images/reject.svg" alt="img" />
              <p className="text-center color-11 mt-3">{t("Reject")}</p>
            </div>
          </div>
        </div>
      ) : (
        <div className="col-1">
          <p
            className={`text-center ${
              props?.status === "accepted" ? "text-success" : null
            } ${props?.status === "rejected" ? "color-9" : null} medium`}
          >
            {props?.statusLocalized}
          </p>
        </div>
      )}
    </Div>
  );
}
