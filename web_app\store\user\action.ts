import { TProfileStatus } from "types/profile";

export const YA<PERSON><PERSON>_MODAL = "YAKEEN_MODAL";
export const YAKEEN_COMPLETE_MISSING_MODAL = "YAKEEN_COMPLETE_MISSING_MODAL";
export const PROFILE_DATA = "PROFILE_DATA";
export const PROFILE_STATUS = "PROFILE_STATUS";
export const YAKEEN_VERIFICATION_SUCCESS = "YAKEEN_VERIFICATION_SUCCESS";
export const CLEAR_USER_DATA = "CLEAR_USER_DATA";

export function toggleYakeenModal(payload) {
  return {
    type: YAKEEN_MODAL,
    payload,
  };
}

export function toggleYakeenCompleteMissingModal(payload) {
  return {
    type: YAKEEN_COMPLETE_MISSING_MODAL,
    payload,
  };
}
export function setYakeenVerificationSuccess(payload) {
  return {
    type: YAKEEN_VERIFICATION_SUCCESS,
    payload,
  };
}

// export function profileDataAction(payload) {
//   return {
//     type: PROFILE_DATA,
//     payload,
//   };
// }
export function profileDataAction(payload: any) {
  return {
    type: PROFILE_DATA,
    payload,
  };
}

export function claerUserDataAction() {
  return {
    type: CLEAR_USER_DATA,
  };
}
