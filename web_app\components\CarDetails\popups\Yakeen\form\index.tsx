/* eslint-disable @next/next/no-img-element */
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Statuses from "./Statuses";
import DriverLiscenceExpiry from "./DriverLiscenceExpiry";
import DateOfBirth from "./Dob";
import { TProfileStatus } from "types/profile";
import DriverLiscenceNo from "./DriverLiscenceNo";
import PassportNo from "./PassportNo";
import PassportExpiry from "./PassportExpiry";
import NationalId from "./NationalId";
import NationalityDDL from "./NationalityDDL";
import { useState } from "react";
import TermsConditions from "../../termsConditions";

export const Input = styled.input`
  border: solid 1px rgba(128, 128, 128, 0.5);
  padding: 14px;
  border-radius: 12px;
  font-size: 16px;
  width: 100%;
`;
export const ButtonsWrapper = styled.div`
  button {
    border-radius: var(--radius-3);
    border: none;
    margin-top: 10px;
    padding: 12px 0 16px 0;
    width: 100%;
    & {
      color: var(--color-4);
      background-color: var(--color-3);
    }
  }
`;

function YakeenForm({ handleSubmit, onSubmit, control, watch, errors }) {
  const status = watch("status") as TProfileStatus;
  const { t } = useTranslation();
  const [termsConditionModalOpen, setTermsConditionModalOpen] = useState(false);

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      style={{ width: "100%" }}
      noValidate
    >
      <Statuses {...{ control, watch }} />
      {status === "visitor" && (
        <NationalityDDL {...{ control, watch, errors }} />
      )}
      {status !== "visitor" && <NationalId {...{ control, watch, errors }} />}

      {status === "visitor" && <PassportNo {...{ control, watch, errors }} />}

      {status === "visitor" && (
        <PassportExpiry {...{ control, watch, errors }} />
      )}

      {status === "gulf_citizen" && (
        <DateOfBirth {...{ control, watch, errors }} />
      )}

      {(status === "gulf_citizen" || status === "visitor") && (
        <DriverLiscenceNo {...{ control, watch, errors }} />
      )}

      <DriverLiscenceExpiry {...{ control, watch, errors }} />
      <>
        <div
          className="d-flex gap-5px mt-4 justify-content-center align-items-center text-align-localized"
          style={{ flexWrap: "wrap" }}
        >
          <span>
            {t("By continuing you indicate your acceptance of the") as string}
          </span>
          <span
            className="color-3 d-inline-block text-decoration-underline cursor-pointer "
            onClick={() => setTermsConditionModalOpen(true)}
          >
            {t("Terms_and_Conditions") as string}
          </span>
        </div>
        <TermsConditions
          isOpen={termsConditionModalOpen}
          setIsOpen={setTermsConditionModalOpen}
        />
        <ButtonsWrapper className="d-flex gap-20px justify-content-end p-3 w-100">
          <button
            type="submit"
            // disabled={Boolean(Object.keys(errors)?.length)}
          >
            {t("Verify") as string}
          </button>
        </ButtonsWrapper>
      </>
    </form>
  );
}

export default YakeenForm;
