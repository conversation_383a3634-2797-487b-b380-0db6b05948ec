import { gql } from "@apollo/client";
export const RentalDetails = gql`
  query RentalDetails($id: ID!) {
    rentalDetails(id: $id) {
      installments {
        amount
        amountDue
        dueDate
        id
        installmentNumber
        status
        totalAmountPaidAt
        totalPaidAmount
        payable
      }
      branchId
      installmentsFullyPaid
      paidInstallmentsCount
      addsPrice
      allyRentalRate
      arAllyName
      isRentToOwn
      ownCarDetails {
        rentalOwnCarPlan {
          id
        }
      }
      bookingNo
      closingReason
      cancelledReason
      couponCode
      couponDiscount
      couponId
      carId
      couponType
      dailyPrice
      deliverType
      deliveryPrice
      discountPercentage
      discountType
      discountValue
      dropOffBranchId
      dropOffCityId
      dropOffCityName
      dropOffDate
      dropOffTime
      handoverAddress
      insuranceName
      handoverAntherCity
      handoverDistance
      handoverPrice
      handoverLat
      handoverLng
      id
      insuranceId
      isExtendable
      isPaid
      isUnlimited
      isUnlimitedFree
      totalWalletPaidAmount
      numberOfDays
      payable
      paymentMethod
      paymentStatusCode
      paymentStatusMessage
      pickUpCityId
      pickUpDate
      pickUpTime
      priceBeforeDiscount
      priceBeforeInsurance
      priceBeforeTax
      pricePerDay
      rentalDateExtensionRequests {
        id
        isPaid
        numberOfDays
        requestNo
        status
        statusLocalized
        dropOffDate
        canCancel
        dropOffTime
        totalBookingPrice
        totalRemainingPrice
        paymentMethod
        extensionDays
        payable
        paymentStatus
        withInstallment
      }
      lastRentalDateExtensionRequest {
        id
      }
      rentalExtraServices {
        arDescription
        arSubtitle
        arTitle
        description
        enDescription
        enSubtitle
        enTitle
        extraServiceId
        extraServiceType
        iconUrl
        id
        isRequired
        payType
        rentalId
        serviceValue
        subtitle
        title
        totalServiceValue
      }
      walletTransactions {
        amount
      }
      rentalIntegrationErrorMessage
      rentalIntegrationResponse
      rentalIntegrationStatus

      status
      statusLocalized
      subStatus
      subStatusLocalized
      suggestedPrice
      taxNote
      taxValue
      totalBookingPrice
      totalAmountDue
      totalExtraServicesPrice
      totalInsurancePrice
      totalUnlimitedFee
      unlimitedFeePerDay
      userId
      valueAddedTaxPercentage
      versionName
      year
      hasPendingExtensionRequests
      lastConfirmedExtensionRequest {
        dropOffDate
      }
      carImage
      pendingPaymentOrder {
        createdAt
        id
        oldTotalBookingPrice
        orderType
        priceBeforeTax
        status
        taxValue
        totalBookingPrice
        totalPaidAmount
        totalRemainingAmount
        totalRemainingDue
        updatedAt
      }
    }
  }
`;

export const Rental_About_Price = gql`
  query RentalAboutPrice(
    $id: ID!
    $withInstallment: Boolean
    $withWallet: Boolean
    $paymentBrand: RentalPaymentBrand
  ) {
    rentalAboutPrice(
      id: $id
      withInstallment: $withInstallment
      withWallet: $withWallet
      paymentBrand: $paymentBrand
    ) {
      totalExtensionsPrice
      totalExtensionsDays
      deliveryPrice
      installments {
        amount
        amountDue
        dueDate
        id
        installmentNumber
        status
        totalAmountPaidAt
        totalPaidAmount
        payable
      }
      totalAmountDue
      totalPrice
      totalInstallmentsAmount
      remainingDueInstallmentsAmount
      remainingInstallmentsAmount
      walletAmount
      totalWalletPaidAmount
      totalBeforeWallet
      priceBeforeTax
      priceBeforeInsurance
      couponErrorMessage
      ownCarDetails {
        rentalOwnCarPlan {
          firstInstallment
          monthlyInstallment
          firstPayment
        }
      }
    }
  }
`;
