/* eslint-disable no-restricted-globals */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
import React, { memo, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { GetCarProfile, GetAllAvailableCars } from "gql/queries/Cars.queries.gql";

function CarsDropDownForBooking({
  loading,
  setSelectedBranch,
  selectedBranch,
  setSelectedCar,
  selectedCar,
  error,
  valueAttribute,
  allyId,
  multiple,
  required,
  isAlly,
  manageradd,
  areaIds,
  noSkip,
  isDisabled,
  coupon,
  BranchesDropDown,
  setList,
  banner,
  isRentToOwn,
  canDelivery,
  requestCarsvariables,
  ...props
}) {
  const { data: CarsRes,loading: gettingCars  } = useQuery(GetAllAvailableCars, {
    skip:!selectedBranch?.value,
    fetchPolicy: "no-cache",
    variables: {
    ...requestCarsvariables,
    branchId: selectedBranch?.value,
    canDelivery: canDelivery,
    isRentToOwn:isRentToOwn,
    carId: selectedCar?.id,
    },
  });

 useEffect(()=>{
  console.log(CarsRes?.listCars,"CarsRes?.listCars")
  setOptions( CarsRes?.listCars?.map((car) => ({
    label: carName(car),
    value: car.id,
    isUnlimited: car.isUnlimited,
    unlimitedFeePerDay: car.unlimitedFeePerDay,
    carInsurances:car.carInsurances,
    branch:car.branch,
    distanceBetweenCarUser:car.distanceBetweenCarUser
  })))
 },[CarsRes])
 

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  const [options, setOptions] = useState([]);
  const { locale, formatMessage, messages } = useIntl();
  function carName(c) {
    return `${c.transmissionName} / ${c.make?.[`${locale}Name`]} - ${
      c?.carModel?.[`${locale}Name`]
    } - ${c?.carVersion?.[`${locale}Name`] ? c?.carVersion?.[`${locale}Name`] : ""} - ${
      c.year
    } | [${formatMessage({ id: "branchName" })}: ${c.branch[`${locale}Name`]}]
    [${formatMessage({ id: "daily" })}: ${c?.dailyPrice} ${formatMessage({
      id: "rental.weeklyPrice",
    })}: ${c?.weeklyPrice ? c?.weeklyPrice : ""} ${formatMessage({
      id: "rental.monthlyPrice",
    })}: ${c?.monthlyPrice}]`;
  }
  console.log(selectedCar,"selectedCar")

  return (
    // <Select
    //   ref={selectInputRef}
    //   isMulti={multiple}
    //   isDisabled={isDisabled}
    //   isClearable
    //   className={`dropdown-select ${multiple ? "multiple" : ""}  ${required ? "required" : ""} ${
    //     error ? "selection-error" : ""
    //   }`}
    //   options={options}
    //   loadOptions={gettingModels || loading}
    //   value={
    //     multiple
    //       ?  options?.filter((optn) => selectedBranch?.includes(+optn.value))
    //       : options?.find((optn) => `${optn.value}` === `${selectedBranch}`)
    //   }
    //   placeholder={
    //     coupon || banner
    //       ? formatMessage({ id: "ally.branches" })
    //       : formatMessage({ id: "branches" })
    //   }
    //   onChange={(selection) => {
    //     if (multiple) {
    //       const branchids = [];
    //       if (selection == null && multiple) {
    //         setSelectedBranch();
    //         return;
    //       }
    //       if (selection[0].value == "all" || selection[selection.length - 1].value == "all") {
    //         options.map(
    //           (onselectoion) => onselectoion.value != "all" && branchids.push(+onselectoion?.value),
    //         );
    //       }
    //       selection?.map((onselectoion) => branchids.push(+onselectoion?.value));
    //       if (branchids.length) {
    //         const versions = branchids.filter((id) => !isNaN(id));
    //         setSelectedBranch([...versions]);
    //       } else {
    //         setSelectedBranch([]);
    //       }
    //     } else {
    //       if (selection?.value == "all") {
    //         setSelectedBranch("null");
    //         return;
    //       }
    //       setSelectedBranch(+selection?.value);
    //     }
    //   }}
    //   noOptionsMessage={() => {
    //     if (gettingModels) {
    //       return <CircularProgress />;
    //     }
    //     if (!allbranches?.length) return <FormattedMessage id="No data found" />;
    //   }}
    //   {...props}
    // />
    <Select
    options={options}
    value={ selectedCar ? options?.find((val) => +val.value === +selectedCar?.id) : null}
    placeholder="select.car"
    isLoading={!!gettingCars}
    onChange={(sel)=>{
      setSelectedCar(sel)
    }}
    // onChange={(sel) => {
    //   if (sel.value == rentalDetails?.rentalDetails?.carId) {
    //     setCarChanged(false);
    //   } else {
    //     setCarChanged(true);
    //   }
    //   const selectedCar = availableCarsCollection?.find(
    //     (car) => +car.id === +sel?.value,
    //   );
    //   setCopounId(null);
    //   setCouponCode(null);
    //   setPlan(selectedCar?.ownCarDetail?.ownCarPlans[0]);
    //   setCarPlans(
    //     selectedCar?.ownCarDetail?.ownCarPlans?.filter(
    //       (plan) => plan?.isActive,
    //     ),
    //   );
    //   setDistanceCarUser(+selectedCar.distanceBetweenCarUser);
    //   setunLimited(false);
    //   setSelectedCar(selectedCar);
    //   setSelectedDropoffBranch(null);
    //   setChanged(true);
    //   setInsuranceId(null);
    //   setTimeout(() => {
    //     setIsDelivery(false);
    //   }, []);
    // }}
  />
  );
}

CarsDropDownForBooking.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  setSelectedBranch: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};

export default memo(CarsDropDownForBooking);
