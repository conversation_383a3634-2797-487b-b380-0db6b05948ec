/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { CircularProgress, Container } from "@material-ui/core";
import Heading from "components/shared/heading";
import Layout from "components/shared/layout";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import CarCard from "./carCard";
import { Customer_Rentals } from "gql/queries/customerRentals";
import { useQuery } from "@apollo/client";
import { useRouter } from "next/router";
import { Pagination } from "@material-ui/lab";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { MyRentalsCurrentPageAction } from "store/boooking/action";
import PerPage from "components/shared/PerPage";
import ToggleTabs from "components/shared/ToggleTabs";

const Div = styled.div``;
const NoCar = styled.div`
  text-align: center;
  .button {
    text-align: center;
    background-color: var(--color-3);
    border-radius: var(--radius-2);
    color: var(--color-4);
    padding: 10px 20px 15px 20px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 20px;
  }
`;

export default function MyRentalsView() {
  //Hooks
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch();

  const { tab } = router.query || {};

  //State
  const [limit, setLimit] = useState(10);

  //Store
  const myRentalsListCurrentPage = useSelector(
    (state: RootStateOrAny) => state.booking.MyRentalsCurrentPage
  );

  //GQL
  const { data: myrentals, loading: loadingrentals } = useQuery(
    Customer_Rentals,
    {
      variables: {
        limit: limit,
        page: myRentalsListCurrentPage ? myRentalsListCurrentPage : 1,
        status: tab,
      },
    }
  );

  // Lifecycle;

  //Functions
  const handlePageChange = (event, value) => {
    dispatch(MyRentalsCurrentPageAction(Number(value)));
  };
  return (
    <Layout>
      <Div className="grey-background pt-5" language={i18n.language}>
        <Container>
          <Heading />
          <ToggleTabs
            tabs={[
              {
                title: t("Current") as string,
                clickAction: () => {
                  router.replace(`/my-rentals?tab=current`);
                  dispatch(MyRentalsCurrentPageAction(1));
                },
                routerQuery: "current",
              },
              {
                title: t("History") as string,
                clickAction: () => {
                  router.replace(`/my-rentals?tab=history`);
                  dispatch(MyRentalsCurrentPageAction(1));
                },
                routerQuery: "history",
              },
            ]}
            activeTab={tab}
          />
          <div className="d-flex flex-column mt-5">
            {myrentals?.customerRentals.collection.length &&
            myrentals?.customerRentals.collection ? (
              myrentals?.customerRentals.collection.map((i) => {
                return <CarCard key={i.id} {...i} />;
              })
            ) : loadingrentals ? (
              <div
                className="d-flex justify-content-center align-items-center"
                style={{
                  position: "fixed",
                  top: 0,
                  left: 0,
                  width: "100vw",
                  height: "100vh",
                  zIndex: 999,
                }}
              >
                <CircularProgress />
              </div>
            ) : (
              <NoCar className="d-flex justify-content-center">
                <div>
                  <img src="/assets/images/Vector.png" alt="car not found" />
                  <h3 className="text-center mt-1">
                    {t("No rentals found") as string}
                  </h3>
                  <p className="text-center mt-1">
                    {
                      t(
                        "You have not submitted a car rental request yet. To find a car go to the search section"
                      ) as string
                    }
                  </p>
                  <button
                    className="button w-100 border-0"
                    onClick={() =>
                      router.push(
                        `car-search${
                          router?.query?.bannerId
                            ? `?bannerId=${router.query.bannerId}`
                            : ""
                        }#car-grid`
                      )
                    }
                  >
                    {t("Find a car") as string}
                  </button>
                </div>
              </NoCar>
            )}
            {myrentals?.customerRentals.collection.length ? (
              <div className="d-flex justify-content-around mb-3 mt-3">
                <Pagination
                  page={myRentalsListCurrentPage}
                  onChange={handlePageChange}
                  count={myrentals?.customerRentals.metadata.totalPages}
                  showFirstButton
                  showLastButton
                  className="d-flex justify-content-center"
                />
                <PerPage
                  specialPagination={[10, 25, 50, 100]}
                  handlePerPageChange={(value) => {
                    dispatch(MyRentalsCurrentPageAction(1));
                    setLimit(value);
                  }}
                  perPage={limit}
                  setPage={(index) =>
                    dispatch(MyRentalsCurrentPageAction(index))
                  }
                />
              </div>
            ) : null}
          </div>
        </Container>
      </Div>
    </Layout>
  );
}
