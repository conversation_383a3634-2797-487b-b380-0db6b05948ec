import {
  ApolloClient,
  InMemory<PERSON>ache,
  HttpLink,
  ApolloLink,
  concat,
} from "@apollo/client";
import { onError } from "@apollo/client/link/error";
import { useSnackbar } from "notistack";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import { signatureGenerator } from "utilities/helpers";
import { v4 as uuidv4 } from "uuid";

export default function useApolloClient() {
  const { i18n } = useTranslation();
  const { login_data } =
    useSelector((state: RootStateOrAny) => state?.authentication) || {};
  const { token } = login_data || {};
  const { enqueueSnackbar } = useSnackbar();

  const middleware = new ApolloLink((operation, forward) => {
    const date = new Date();
    const timestamp = date.toISOString();
    const nonce = uuidv4();

    const signature = signatureGenerator({
      queryName: operation.operationName,
      variables: operation.variables,
      nonce,
      timestamp,
    });

    operation.setContext(({ headers = {} }) => ({
      headers: {
        ...headers,
        Authorization: token ? `Bearer ${token}` : "",
        "accept-language": i18n?.language || "ar",
        "x-platform": "web",
        "x-timestamp": timestamp,
        "x-nonce": nonce,
        "x-signature": signature,
        "x-operation-name": operation.operationName,
      },
    }));

    return forward(operation);
  });

  // Error handling middleware
  const errorLink = onError(({ graphQLErrors, networkError }) => {
    if (graphQLErrors) {
      graphQLErrors.forEach(({ message, path, extensions }) => {
        enqueueSnackbar(`[GraphQL error]: Message: ${message}, Path: ${path}`, {
          variant: "error",
          preventDuplicate: true,
          autoHideDuration: 5000,
        });
        // if (extensions?.code === "DOWNSTREAM_SERVICE_ERROR") {
        //   // Custom handling for downstream errors
        //   enqueueSnackbar(
        //     `Service: ${extensions.serviceName}, Status: ${extensions?.exception?.status}`,
        //     { variant: "error" }
        //   );
        // }
      });
    }

    if (networkError) {
      enqueueSnackbar(`[Network error]: ${networkError}`, { variant: "error" });
    }
  });

  const client = new ApolloClient({
    link: ApolloLink.from([
      // errorLink,
      middleware,
      new HttpLink({
        uri: process.env.NEXT_PUBLIC_BASE_URL || process.env.BASE_URL,
      }),
    ]),
    cache: new InMemoryCache(),
    defaultOptions: {
      query: {
        fetchPolicy: "cache-first",
        errorPolicy: "all", // Continue fetching data even if partial errors occur
      },
    },
    queryDeduplication: false,
  });

  return { client };
}
