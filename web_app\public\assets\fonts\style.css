@font-face {
  font-family: "Helvetica";
  src: url("HelveticaNeueLTArabic-Roman.eot");
  src: url("HelveticaNeueLTArabic-Roman.eot?#iefix") format("embedded-opentype"),
    url("HelveticaNeueLTArabic-Roman.woff2") format("woff2"),
    url("HelveticaNeueLTArabic-Roman.woff") format("woff"),
    url("HelveticaNeueLTArabic-Roman.ttf") format("truetype"),
    url("HelveticaNeueLTArabic-Roman.svg#HelveticaNeueLTArabic-Roman")
      format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Helvetica-Riyal";
  src: url("HelveticaNeueLTArabic-Roman-Riyal.eot");
  src: url("HelveticaNeueLTArabic-Roman-Riyal.eot?#iefix") format("embedded-opentype"),
    url("HelveticaNeueLTArabic-Roman-Riyal.woff2") format("woff2"),
    url("HelveticaNeueLTArabic-Roman-Riyal.woff") format("woff"),
    url("HelveticaNeueLTArabic-Roman-Riyal.ttf") format("truetype"),
    url("HelveticaNeueLTArabic-Roman-Riyal.svg#HelveticaNeueLTArabic-Roman-Riyal")
      format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Jost";
  src: url("Jost-Regular.eot");
  src: url("Jost-Regular.eot?#iefix") format("embedded-opentype"),
    url("Jost-Regular.woff2") format("woff2"),
    url("Jost-Regular.woff") format("woff"),
    url("Jost-Regular.ttf") format("truetype"),
    url("Jost-Regular.svg#Jost-Regular")
      format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

