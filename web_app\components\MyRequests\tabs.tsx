import React from "react";
import styled from "styled-components";

const Div = styled.div`
  text-transform: uppercase;
  .active {
    color: var(--color-3);
    transition: all ease 0.3s;
    border-bottom: solid 2px;
    position: relative;
  }
  span {
    border-bottom: solid 2px transparent;
    font-size: 16px;
    line-height: 18px;
    &:first-child {
     
    }
  }
`;

export default function Tabs({ t, language, activeItem, setActiveItem,setStatus,refetchData }) {
  return (
    <>
    <Div
      className="d-flex justify-content-center medium"
      style={{ gap: "25px" }}
      language={language}
    >
      <span
        className={`${activeItem === 0 && "active"} cursor-pointer`}
        onClick={() => {
          setActiveItem(0)
          setStatus("current")
          refetchData()
          
        }}
      >
        {t("Current.request")}
      </span>
      <span
        className={`${activeItem === 1 && "active"} cursor-pointer`}
        onClick={() => {
          setActiveItem(1)
          setStatus("history")
          refetchData()
         

        }}
      >
        {t("History.request")}
      </span>
    </Div>
    
    </>
    
  );
}
