import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Parteners from "./Parteners";
import { useQuery } from "@apollo/client";
import { GET_CUSTOMER_LOYALITY_SETTINGS } from "gql/queries/loyality";
import { RootStateOrAny, useSelector } from "react-redux";
import { TpaymentMethods } from "utilities/enums";
import useRentalType from "../heplers/rentalType";
import { memo, useMemo } from "react";

const Div = styled.div`
  margin-top: 30px;
  margin-bottom: 30px;
  > div:first-child {
    h6 {
      font-weight: bold;
    }
    p {
      color: #a4a4a4;
      margin-bottom: -10px;
    }
    padding: 0 5px;
  }
`;

function Loyality() {
  const { t } = useTranslation();
  const { data } = useQuery(GET_CUSTOMER_LOYALITY_SETTINGS);
  const { paymentMethod: _paymentMethod } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const paymentMethod = _paymentMethod as TpaymentMethods;
  const { customerLoyaltySettings } = data || {};
  const isCash = paymentMethod?.toLocaleLowerCase() === "cash";
  const { isRentToOwn } = useRentalType();

  function getSettingValue(key: string) {
    return customerLoyaltySettings?.find((i) => i.key === key)?.value;
  }

  const hideSection = useMemo(() => {
    if (
      isCash ||
      Boolean(
        getSettingValue("is_fursan_rental_active") == "false" && !isRentToOwn
      ) ||
      Boolean(
        getSettingValue("is_fursan_rental_to_own_active") == "false" &&
          isRentToOwn
      )
    ) {
      return true;
    }

    return false;
  }, [
    isCash,
    getSettingValue("is_fursan_rental_active"),
    getSettingValue("is_fursan_rental_to_own_active"),
  ]);

  if (hideSection) return <></>;

  return (
    <Div>
      <div>
        <h6>{t("Earn Gifts from our Loyalty Partners") as string}</h6>
        <p>
          {
            t(
              "Convert your successful booking amounts after returning the car into points with our partners"
            ) as string
          }
        </p>
      </div>
      <Parteners {...{ getSettingValue }} />
    </Div>
  );
}

export default memo(Loyality);
