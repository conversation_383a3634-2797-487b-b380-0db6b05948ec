import React, { useState } from 'react';
import { Button } from 'reactstrap';
import { FormattedMessage } from 'react-intl';
import PaymentFlow from '../PaymentFlow';
import PaymentButton from '../PaymentButton';
import { NotificationManager } from 'react-notifications';

/**
 * Example implementations of the Payment Flow system
 * These examples demonstrate various use cases and integration patterns
 */

// Example 1: Basic Rental Payment
export const BasicRentalPayment = ({ rentalId, totalAmount }) => {
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);

  const handlePaymentSuccess = (paymentMethod) => {
    NotificationManager.success(`Payment successful with ${paymentMethod}`);
    setPaymentModalOpen(false);
    // Redirect or update UI as needed
  };

  const handlePaymentError = (error) => {
    NotificationManager.error(`Payment failed: ${error}`);
    setPaymentModalOpen(false);
  };

  return (
    <div className="rental-payment-example">
      <h4>Basic Rental Payment</h4>
      <p>Total Amount: {totalAmount} SAR</p>
      
      <Button 
        color="primary" 
        onClick={() => setPaymentModalOpen(true)}
      >
        <FormattedMessage id="Pay Now" />
      </Button>

      <PaymentFlow
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        rentalId={rentalId}
        totalAmount={totalAmount}
        walletBalance={0}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
      />
    </div>
  );
};

// Example 2: Extension Payment
export const ExtensionPayment = ({ rentalId, extensionId, extensionAmount }) => {
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);

  return (
    <div className="extension-payment-example">
      <h4>Extension Payment</h4>
      <p>Extension Amount: {extensionAmount} SAR</p>
      
      <Button 
        color="warning" 
        onClick={() => setPaymentModalOpen(true)}
      >
        <FormattedMessage id="Pay Extension" />
      </Button>

      <PaymentFlow
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        rentalId={rentalId}
        extensionId={extensionId}
        totalAmount={extensionAmount}
        onPaymentSuccess={(paymentMethod) => {
          NotificationManager.success('Extension payment successful');
          setPaymentModalOpen(false);
          // Refresh extension data
        }}
        onPaymentError={(error) => {
          NotificationManager.error(`Extension payment failed: ${error}`);
        }}
      />
    </div>
  );
};

// Example 3: Installment Payment
export const InstallmentPayment = ({ rentalId, installmentAmount }) => {
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);

  return (
    <div className="installment-payment-example">
      <h4>Installment Payment</h4>
      <p>Installment Amount: {installmentAmount} SAR</p>
      
      <Button 
        color="info" 
        onClick={() => setPaymentModalOpen(true)}
      >
        <FormattedMessage id="Pay Installment" />
      </Button>

      <PaymentFlow
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        rentalId={rentalId}
        isInstallment={true}
        totalAmount={installmentAmount}
        onPaymentSuccess={(paymentMethod) => {
          NotificationManager.success('Installment payment successful');
          setPaymentModalOpen(false);
          // Update installment status
        }}
        onPaymentError={(error) => {
          NotificationManager.error(`Installment payment failed: ${error}`);
        }}
      />
    </div>
  );
};

// Example 4: Payment with Wallet Balance
export const PaymentWithWallet = ({ rentalId, totalAmount, walletBalance }) => {
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);

  return (
    <div className="wallet-payment-example">
      <h4>Payment with Wallet</h4>
      <p>Total Amount: {totalAmount} SAR</p>
      <p>Wallet Balance: {walletBalance} SAR</p>
      <p>Remaining: {Math.max(0, totalAmount - walletBalance)} SAR</p>
      
      <Button 
        color="success" 
        onClick={() => setPaymentModalOpen(true)}
      >
        <FormattedMessage id="Pay with Wallet" />
      </Button>

      <PaymentFlow
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        rentalId={rentalId}
        totalAmount={totalAmount}
        walletBalance={walletBalance}
        onPaymentSuccess={(paymentMethod) => {
          NotificationManager.success('Payment successful');
          setPaymentModalOpen(false);
        }}
        onPaymentError={(error) => {
          NotificationManager.error(`Payment failed: ${error}`);
        }}
      />
    </div>
  );
};

// Example 5: Using PaymentButton Component
export const PaymentButtonExample = ({ rentalId, totalAmount }) => {
  return (
    <div className="payment-button-example">
      <h4>Payment Button Component</h4>
      <p>Simplified payment integration using PaymentButton</p>
      
      <PaymentButton
        rentalId={rentalId}
        totalAmount={totalAmount}
        onPaymentSuccess={(paymentMethod) => {
          console.log('Payment successful with:', paymentMethod);
          // Handle success
        }}
        onPaymentError={(error) => {
          console.error('Payment error:', error);
          // Handle error
        }}
        color="primary"
        size="lg"
      >
        <FormattedMessage id="Quick Pay" />
      </PaymentButton>
    </div>
  );
};

// Example 6: Multiple Payment Buttons for Different Scenarios
export const MultiplePaymentScenarios = ({ 
  rentalId, 
  extensionId, 
  totalAmount, 
  extensionAmount,
  installmentAmount 
}) => {
  return (
    <div className="multiple-payments-example">
      <h4>Multiple Payment Scenarios</h4>
      
      <div className="payment-options">
        {/* Regular Rental Payment */}
        <div className="payment-option">
          <h6>Rental Payment</h6>
          <PaymentButton
            rentalId={rentalId}
            totalAmount={totalAmount}
            onPaymentSuccess={() => console.log('Rental paid')}
            color="primary"
          />
        </div>

        {/* Extension Payment */}
        {extensionId && (
          <div className="payment-option">
            <h6>Extension Payment</h6>
            <PaymentButton
              rentalId={rentalId}
              extensionId={extensionId}
              totalAmount={extensionAmount}
              onPaymentSuccess={() => console.log('Extension paid')}
              color="warning"
            />
          </div>
        )}

        {/* Installment Payment */}
        <div className="payment-option">
          <h6>Installment Payment</h6>
          <PaymentButton
            rentalId={rentalId}
            installmentId="inst-123"
            totalAmount={installmentAmount}
            onPaymentSuccess={() => console.log('Installment paid')}
            color="info"
          />
        </div>
      </div>
    </div>
  );
};

// Example 7: Payment with Custom Error Handling
export const PaymentWithErrorHandling = ({ rentalId, totalAmount }) => {
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [paymentError, setPaymentError] = useState(null);

  const handlePaymentError = (error) => {
    setPaymentError(error);
    setPaymentModalOpen(false);
    
    // Custom error handling based on error type
    if (error.includes('deactivated')) {
      NotificationManager.error('Agency is deactivated. Please contact support.');
    } else if (error.includes('insufficient')) {
      NotificationManager.error('Insufficient funds. Please add money to your wallet.');
    } else {
      NotificationManager.error(`Payment failed: ${error}`);
    }
  };

  return (
    <div className="error-handling-example">
      <h4>Payment with Error Handling</h4>
      
      {paymentError && (
        <div className="alert alert-danger">
          <strong>Payment Error:</strong> {paymentError}
          <Button 
            color="link" 
            onClick={() => setPaymentError(null)}
            className="float-right"
          >
            ×
          </Button>
        </div>
      )}
      
      <Button 
        color="primary" 
        onClick={() => setPaymentModalOpen(true)}
      >
        <FormattedMessage id="Pay with Error Handling" />
      </Button>

      <PaymentFlow
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        rentalId={rentalId}
        totalAmount={totalAmount}
        onPaymentSuccess={(paymentMethod) => {
          setPaymentError(null);
          NotificationManager.success('Payment successful');
          setPaymentModalOpen(false);
        }}
        onPaymentError={handlePaymentError}
      />
    </div>
  );
};

// Example 8: Complete Integration Example
export const CompletePaymentIntegration = () => {
  const [selectedScenario, setSelectedScenario] = useState('rental');
  
  const mockData = {
    rentalId: 'rental-123',
    extensionId: 'ext-456',
    totalAmount: 500,
    extensionAmount: 150,
    installmentAmount: 200,
    walletBalance: 100,
  };

  const renderPaymentComponent = () => {
    switch (selectedScenario) {
      case 'rental':
        return (
          <BasicRentalPayment 
            rentalId={mockData.rentalId}
            totalAmount={mockData.totalAmount}
          />
        );
      case 'extension':
        return (
          <ExtensionPayment
            rentalId={mockData.rentalId}
            extensionId={mockData.extensionId}
            extensionAmount={mockData.extensionAmount}
          />
        );
      case 'installment':
        return (
          <InstallmentPayment
            rentalId={mockData.rentalId}
            installmentAmount={mockData.installmentAmount}
          />
        );
      case 'wallet':
        return (
          <PaymentWithWallet
            rentalId={mockData.rentalId}
            totalAmount={mockData.totalAmount}
            walletBalance={mockData.walletBalance}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="complete-integration-example">
      <h3>Payment Flow Examples</h3>
      
      <div className="scenario-selector mb-4">
        <h5>Select Payment Scenario:</h5>
        <div className="btn-group" role="group">
          <Button 
            color={selectedScenario === 'rental' ? 'primary' : 'outline-primary'}
            onClick={() => setSelectedScenario('rental')}
          >
            Rental Payment
          </Button>
          <Button 
            color={selectedScenario === 'extension' ? 'primary' : 'outline-primary'}
            onClick={() => setSelectedScenario('extension')}
          >
            Extension Payment
          </Button>
          <Button 
            color={selectedScenario === 'installment' ? 'primary' : 'outline-primary'}
            onClick={() => setSelectedScenario('installment')}
          >
            Installment Payment
          </Button>
          <Button 
            color={selectedScenario === 'wallet' ? 'primary' : 'outline-primary'}
            onClick={() => setSelectedScenario('wallet')}
          >
            Wallet Payment
          </Button>
        </div>
      </div>

      <div className="payment-demo">
        {renderPaymentComponent()}
      </div>
    </div>
  );
};

export default CompletePaymentIntegration;
