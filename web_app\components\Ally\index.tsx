import { Container } from "@material-ui/core";
import Layout from "components/shared/layout";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import DownloadApp from "components/shared/downloadApp";
import Advantages from "./advantages";
import Heading from "./heading";
import HowItWorks from "./howIWorks";
import WhyWeCall from "./whyWeCall";

const Div = styled.div`
  padding-bottom: 50px;
  section:nth-child(odd) {
    background-color: var(--color-6);
  }
  section#our-allies {
    background-color: transparent;
    @media (min-width: 961px) {
      position: relative;
      &::after {
        content: "";
        position: absolute;
        top: 0;
        right: ${(props) => (props.language === "ar" ? 0 : null)};
        left: ${(props) => (props.language === "en" ? 0 : null)};
        width: 40%;
        background-color: var(--color-6);
        height: 100%;
        z-index: -1;
      }
      &::before {
        content: "";
        position: absolute;
        bottom: 0;
        right: ${(props) => (props.language === "ar" ? "15%" : null)};
        left: ${(props) => (props.language === "en" ? "15%" : null)};
        width: 295px;
        background-repeat: no-repeat;
        background: url("/assets/images/ally/lines.svg");
        height: 470px;
        z-index: 0;
        transform: ${(props) =>
          props.language === "ar" ? "rotateY(180deg)" : null};
      }
      > div {
        max-width: initial;
        padding: 0;
      }
    }
  }
`;

export default function Ally() {
  const { i18n } = useTranslation();

  return (
    <Layout>
      <Div language={i18n.language}>
        <section>
          <Container>
            <Heading />
            <HowItWorks />
          </Container>
        </section>
        <section>
          <Container>
            <WhyWeCall />
          </Container>
        </section>
        <section>
          <Container>
            <Advantages />
          </Container>
        </section>
        <section>
          <Container>
            <DownloadApp />
          </Container>
        </section>
      </Div>
    </Layout>
  );
}
