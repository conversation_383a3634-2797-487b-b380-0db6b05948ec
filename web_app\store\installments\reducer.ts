import {
  PAY_WITH_INSTALLMENTS,
  SELECTED_PACKAGE,
  INSTALLMENT_BREAKDOWN_MODAL,
  CLEAR_DATA,
} from "./action";

export default function InstallmentsReducer(
  state = {
    pay_with_installments: false,
    selected_package_data: undefined,
    show_installment_breakdown_modal: undefined,
  },
  action
) {
  switch (action.type) {
    case SELECTED_PACKAGE:
      return { ...state, selected_package_data: action.payload };
    case PAY_WITH_INSTALLMENTS:
      return {
        ...state,
        pay_with_installments: action.payload,
      };
    case INSTALLMENT_BREAKDOWN_MODAL:
      return {
        ...state,
        show_installment_breakdown_modal: action.payload,
      };
    case CLEAR_DATA:
      return {
        pay_with_installments: false,
        selected_package_data: undefined,
        show_installment_breakdown_modal: undefined,
      };
    default:
      return state;
  }
}
