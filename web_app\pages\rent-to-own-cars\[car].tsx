import RentToOwnCarGrouping from "components/RentToOwnCarGrouping";
import Head from "next/head";
import { useRouter } from "next/router";

export default function RentToOwnCarsPage({ locale, carId }) {
  const router = useRouter();

  return (
    <div>
      <Head>
        <link
          rel="canonical"
          href={`https://carwah.com.sa/${router.locale}${router?.pathname}`}
        />
        <link
          rel="alternate"
          hrefLang={locale === "en" ? "ar" : "en"}
          href={`https://carwah.com.sa/${router.locale === "en" ? "ar" : "en"}${
            router?.pathname
          }`}
        />
      </Head>
      <RentToOwnCarGrouping carId={carId} />
    </div>
  );
}

export async function getServerSideProps(context: any) {
  const { car } = context.query;

  return {
    props: {
      carId: car,
    },
  };
}
