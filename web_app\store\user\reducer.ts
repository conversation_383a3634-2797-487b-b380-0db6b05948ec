import { TProfileStatus } from "types/profile";
import {
  YAKEEN_MODAL,
  PROFILE_DATA,
  YAKEEN_COMPLETE_MISSING_MODAL,
  YAKEEN_VERIFICATION_SUCCESS,
  CLEAR_USER_DATA,
} from "./action";

export type TUserReducer = {
  is_yakeen_modal_open: boolean;
  profile: TuserProfile | null;
  is_yakeen_verification_success: "success" | "error" | null;
  is_yakeen_missing_modal_open: boolean;
};
export type TuserProfile = {
  isCustomerProfileCompleted: boolean;
  customerProfile: {
    status: TProfileStatus;
    yakeenTriesLeft: number;
    yakeenResponse: any;
    isYakeenVerified: boolean;
    alfursanMembership: {
      membershipId?: string;
    };
  };
};
export default function UserReducer(
  state: TUserReducer = {
    is_yakeen_modal_open: false,
    profile: null,
    is_yakeen_verification_success: null,
    is_yakeen_missing_modal_open: null,
  },
  action
) {
  switch (action.type) {
    case YAKEEN_MODAL:
      return { ...state, is_yakeen_modal_open: action.payload };
    case YAKEEN_COMPLETE_MISSING_MODAL:
      return { ...state, is_yakeen_missing_modal_open: action.payload };
    case YAKEEN_VERIFICATION_SUCCESS:
      return { ...state, is_yakeen_verification_success: action.payload };
    case PROFILE_DATA:
      return { ...state, profile: { ...state.profile, ...action.payload } };
    case CLEAR_USER_DATA:
      return {
        is_yakeen_modal_open: false,
        profile: null,
        is_yakeen_verification_success: null,
        is_yakeen_missing_modal_open: null,
      };

    default:
      return state;
  }
}
