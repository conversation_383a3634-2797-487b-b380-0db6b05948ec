/* eslint-disable @next/next/no-img-element */
import { useQuery } from "@apollo/client";
import Popup from "components/shared/popup";
import RequestLoader from "components/shared/requestLoader";
import { Wallet_History_Query } from "gql/queries/profile";
import i18n from "localization";
import moment from "moment";
import { Dispatch, SetStateAction } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { gql_limits } from "utilities/enums";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Styled = styled.div`
  .wallet__card {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: white;
    border-radius: var(--radius-2);
    padding-bottom: 18px;
    h3 {
      font-weight: bold;
    }
    img {
      margin-top: -18px;
      background: white;
      border-radius: var(--radius-2);
      padding: 3px;
    }
  }
  h6,
  p {
    color: #2a292f;
    opacity: 0.5;
    margin-top: 15px !important;
    /* text-align: center !important; */
  }

  hr {
    background: #c4c4c4;
    opacity: 0.5;
  }
`;

const TransactionStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: white;
  padding: 10px 0;
  border-radius: var(--radius-3);
  margin-top: 20px;
  margin-bottom: 20px;
  h6 {
    font-weight: bold;
    color: black !important;
    margin-top: 5px !important;
    margin-bottom: 5px !important;
    opacity: 1 !important;
  }

  .date {
    opacity: 0.5;
  }
  img {
    margin: 10px 0;
  }
  > div:last-child {
    text-align: center;
  }
  > div {
    display: flex;
    justify-content: space-between;
    padding: 10px 24px 10px 24px !important;
    border-radius: var(--radius-1);
    &.grid {
      background: linear-gradient(
        0deg,
        rgba(0, 0, 0, 0.03) 0%,
        rgba(255, 255, 255, 1) 100%
      );
    }
  }
  .amount {
    background: var(--color-2);
    color: white;
    border-radius: 6px;
    padding: 2px 6px 6px 6px;
  }
  .added-balance {
    color: var(--color-3);
    background: #e5f4f8;
    text-align: center;
    border-radius: 6px;
    padding: 2px 6px 6px 6px;
    width: max-content;
  }
  .used-balance {
    color: var(--color-9);
    background: #fee6e6;
    text-align: center;
    border-radius: 6px;
    padding: 2px 6px 6px 6px;
    width: max-content;
  }
  .wrap {
    display: flex;
    gap: 30px;
    align-items: center;
    flex-wrap: wrap;
    @media (max-width: 768px) {
      gap: 0px !important;
    }
  }
`;

type TtransactionData = {
  id: string;
  amount: number;
  bookingNo: string;
  transactionNo: string;
  transactionDate: string;
  transactionType: "deposit" | "installment_payment" | "payment" | "refund";
  isIncoming: boolean;
  walletSource: {
    id: string;
    img: string;
    name: string;
  };
};

function Transaction({
  data,
  t,
  language,
}: {
  data: TtransactionData;
  t: any;
  language: string;
}) {
  if (data) {
    return (
      <TransactionStyled className=" text-align-localized">
        <div className="grid">
          <div className="wrap">
            <h6>
              {t("Booking No.") as string} : {data.bookingNo}
            </h6>
            <h6>
              {t("Operation no.") as string} : {data.transactionNo}
            </h6>
            <div className="date">
              {moment(data.transactionDate).locale(language).format("ll")}
            </div>
          </div>
          <div>
            <img src={data.walletSource.img} alt="wallet source icon" />
          </div>
        </div>
        <div>
          <div className={data.isIncoming ? "added-balance" : "used-balance"}>
            {data.isIncoming ? t("Added Balance") : t("Used Balance")}
          </div>
          <div
            className="amount d-flex gap-5px"
            style={{
              flexDirection: i18n.language === "ar" ? "row-reverse" : "row",
            }}
          >
            <div>{data.isIncoming ? "  +  " : "  -  "}</div>
            <div className=" d-flex">
              <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                {" "}
                <RiyalSymbol />
              </span>
              <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                {data.amount}
              </span>
            </div>
          </div>
        </div>
      </TransactionStyled>
    );
  }
}
function WalletTransactions({ balance }: { balance: number }) {
  const { t } = useTranslation();
  const { data, loading } = useQuery(Wallet_History_Query, {
    variables: { limit: gql_limits.max },
  });
  const { collection: walletTransactions } =
    (data && data.viewWallet.walletTransactions) || {};

  return (
    <>
      <Styled>
        <div className="wallet__card">
          <img src="/assets/icons/wallet.svg" alt="wallet" />
          <div>{t("Current Balance") as string}</div>
          <h3 className="d-flex gap-5px">
            <div style={{ order: i18n.language === "ar" ? 2 : 1 }}>
              <RiyalSymbol />
            </div>
            <span style={{ order: i18n.language === "ar" ? 1 : 2 }}>
              {balance}
            </span>
          </h3>
        </div>
        <h6 className="bold pt-2">{t("wallet__history") as string}</h6>
        <hr />
        {walletTransactions?.length ? (
          walletTransactions.map((data: TtransactionData) => {
            return (
              <Transaction
                key={data.id}
                data={data}
                t={t}
                language={i18n.language}
              />
            );
          })
        ) : (
          <p className="text-center">{t("No Logs Yet") as string}</p>
        )}
      </Styled>
      <RequestLoader loading={loading} />
    </>
  );
}

export default WalletTransactions;
