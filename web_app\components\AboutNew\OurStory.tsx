/* eslint-disable @next/next/no-img-element */
import React from "react";
import { useTranslation } from "react-i18next";

import { Box, Grid, Typography } from "@material-ui/core";
import { AboutContainer, Content } from "./styles";

export default function AboutStory() {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.language === "ar";

  return (
    <Box style={{ backgroundColor: "#F8F8F8" }}>
      <AboutContainer>
        <Grid
          container
          spacing={2}
          justifyContent="space-between"
          className="our-story"
        >
          <Grid item xs={12} md="auto">
            <Box className="text-align-localized mt-1">
              <img
                src="/assets/images/about/icons/story_icon.svg"
                alt="Story Icon"
                loading="lazy"
              />
              <Typography
                variant="h3"
                className="bold"
                style={{ fontSize: "1.7rem", fontWeight: 700 }}
              >
                {String(t("aboutus.ourstory.title"))}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={10}>
            <Content
              className="text-align-localized"
              style={{ direction: isRtl ? "rtl" : "ltr" }}
            >
              <Typography
                variant="body1"
                dangerouslySetInnerHTML={{
                  __html: t("aboutus.ourstory.content") as string,
                }}
              />
              <Typography variant="body1">
                {String(t("aboutus.And this is just the beginning .."))}
              </Typography>
            </Content>
          </Grid>
        </Grid>
      </AboutContainer>
    </Box>
  );
}
