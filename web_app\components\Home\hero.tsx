/* eslint-disable react/jsx-key */
import React, { useState, useEffect } from "react";
import Swiper from "components/shared/carousel";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { Autoplay, Pagination } from "swiper";
import { Box } from "@material-ui/core";

const SwiperTag = styled(Swiper)`
  min-height: 22rem;
  display: flex;
  align-items: center;
  .swiper-wrapper {
    align-items: center;
  }
  .swiper-button-prev,
  .swiper-button-next {
    color: white !important;
    width: 50px;
    &:after {
      color: white !important;
      font-size: 18px;
      transform: ${(props) =>
        props.i18n.language === "en"
          ? "translateX(-20px)"
          : "translateX(20px)"};
    }
    &:before {
      content: "";
      width: 40px;
      height: 40px;
      background-color: white;
      opacity: 0.1;
      border-radius: 50px;
      transform: ${(props) =>
        props.i18n.language === "en" ? "translateX(5px)" : "translateX(-5px)"};
    }
  }
  @media (max-width: 900px) {
    min-height: 450px; /* Match the SlideContainer min-height */

    .swiper-button-prev,
    .swiper-button-next {
      /* Adjust navigation buttons for mobile */
      width: 50px;
      &:before {
        height: 40px; /* Smaller height for mobile */
        border-radius: 50%;
      }

      &:after {
        font-size: 16px; /* Slightly smaller arrows */
        border-radius: 50%;
      }
    }
  }
`;

const SlideContainer = styled.div`
  width: 100%;
  min-height: 67vh;
  display: flex;
  align-items: center;
  /* transform: scaleX(${(props) =>
    props.flip == true ? "-1" : "1"}) !important; */
  justify-content: ${(props) =>
    props.flip == true ? "flex-end" : "flex-start"}; // Adjust justify-content

  /* Add background image styling */
  background-image: url(${(props) => props.backgroundSrc});
  background-size: cover;
  background-repeat: no-repeat;
  background-position: ${(props) => props.backgroundPosition || "center"};
  transform: ${(props) => (props.flip == true ? "scaleX(-1)" : "scaleX(1)")};

  @media (max-width: 900px) {
    /* Portrait orientation for mobile */
    background-position: ${(props) => props.mobilePosition || "center"};
    min-height: calc(100lvh - 50px); /* Adjust height for mobile */
  }

  > div {
    @media (min-width: 901px) {
      padding: 0 8rem;
    }
    @media (max-width: 900px) {
      padding: 0 4.5rem;
      width: 100%;
    }
    direction: ${(props) => (props.direction === "rtl" ? "rtl" : "ltr")};
  }

  h6 {
    font-weight: 500;
    margin: 0 0 10px 0;
    color: var(--color-4);
    line-height: 3rem;
    @media (min-width: 900px) {
      width: 80%;
    }
  }

  h1,
  h2#h2 {
    margin: 0;
    color: var(--color-4);
    max-width: 640px;

    @media (max-width: 900px) {
      font-size: 2.5rem !important;
      margin-bottom: 0 !important;
      line-height: 2.5rem;
      padding: 20px 0;
      text-shadow: 0px 0px 8px rgba(255, 255, 255, 0.8); /* Improve text readability on mobile */
      text-align: center;
    }
    direction: ${(props) => (props.direction === "rtl" ? "rtl" : "ltr")};
  }
  h6 {
    @media (max-width: 900px) {
      font-size: 1.2rem !important;
      line-height: 1.8rem !important;
      text-align: center !important;
    }
  }
`;

const Content = styled.div`
  @media (max-width: 900px) {
    display: flex;
    justify-content: center;
  }
  pointer-events: none;
  transform: ${(props) => (props.flip == true ? "scaleX(-1)" : "scaleX(1)")};
  h2 {
    font-size: 3rem;
    font-weight: 600;
    margin-bottom: 0.5rem !important;
  }
  h6 {
    font-size: 1.65rem;
  }
`;

export default function HomeCarousel() {
  const { t, i18n } = useTranslation();
  const isArabic = i18n.language === "ar";
  const [isMobile, setIsMobile] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  // Handle responsive detection properly
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 900);
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  const slides = [
    {
      content: (
        <Box>
          <h2 id="h2">{t("Hayak") as string}</h2>
          {
            <h6>
              {
                t(
                  "Carwah is your first choice for all your destinations and trips"
                ) as string
              }
            </h6>
          }
        </Box>
      ),
      background: `/assets/images/home/<USER>/hayak.png`,
      mobileBackground: `/assets/images/home/<USER>/hayak-mobile.png`, // Different image for mobile
      mobilePosition: "top center", // Position for mobile view
      flip: i18n.language === "en",
    },
    {
      content: (
        <Box>
          <h2 id="h2">{t("Asir") as string}</h2>
          {
            <h6>
              {
                t(
                  "A unique tourist destination — visit it now and rent from Carwah"
                ) as string
              }
            </h6>
          }
        </Box>
      ),
      background: `/assets/images/home/<USER>/asseer.png`,
      mobileBackground: `/assets/images/home/<USER>/asseer-mobile.png`, // Different image for mobile
      mobilePosition: "top center", // Position for mobile view
      flip: i18n.language === "en",
    },
    {
      content: (
        <Box>
          <h2 id="h2">{t("Jeddah") as string}</h2>
          {
            <h6>
              {
                t(
                  "The Bride of the Red Sea — visit it now and rent from Carwah"
                ) as string
              }
            </h6>
          }
        </Box>
      ),
      background: `/assets/images/home/<USER>/jeddah.png`,
      mobileBackground: `/assets/images/home/<USER>/jeddah-mobile.png`, // Different image for mobile
      mobilePosition: "top center", // Position for mobile view,
      flip: i18n.language === "en",
    },
    {
      content: (
        <Box>
          <h2 id="h2">{t("Mecca Al-Mukarramah") as string}</h2>
          {
            <h6>
              {
                t(
                  "The place beloved by hearts and the cradle of revelation — visit it now and rent from Carwah"
                ) as string
              }
            </h6>
          }
        </Box>
      ),
      background: `/assets/images/home/<USER>/meccah.png`,
      mobileBackground: `/assets/images/home/<USER>/meccah-mobile.png`, // Different image for mobile
      mobilePosition: "top center", // Position for mobile view,
      flip: false,
    },
    {
      content: (
        <Box>
          <h2 id="h2">{t("Since the day we began") as string}</h2>
          {<h6>{t("And our dreams have no limits") as string}</h6>}
        </Box>
      ),
      background: `/assets/images/home/<USER>/mnyoum.png`,
      mobileBackground: `/assets/images/home/<USER>/mnyoum-mobile.png`, // Different image for mobile
      mobilePosition: "top center", // Position for mobile view,
      flip: i18n.language === "en",
    },
  ];

  // // Preload the first slide's image when component mounts
  // useEffect(() => {
  //   // Preload first slide images (both mobile and desktop versions)
  //   if (slides && slides.length > 0) {
  //     const firstSlide = slides[0];
  //     const mobileImg = new Image();
  //     mobileImg.src = firstSlide.mobileBackground;

  //     const desktopImg = new Image();
  //     desktopImg.src = firstSlide.background;
  //   }
  // }, []);

  return (
    <>
      <SwiperTag
        i18n={i18n}
        spaceBetween={0}
        slidesPerView={1}
        navigation
        loop={false}
        // autoplay={{
        //   delay: 5000,
        //   disableOnInteraction: false,
        // }}
        autoplay={false}
        modules={[Autoplay, Pagination]}
        onSwiper={(swiper) => null}
        onSlideChange={(swiper) => {
          setActiveIndex(swiper.activeIndex);
        }}
        slides={slides.map((slide, index) => (
          <SlideContainer
            key={`slide-${index}`}
            direction={isArabic ? "rtl" : "ltr"}
            mobilePosition={slide.mobilePosition}
            flip={slide.flip}
            backgroundSrc={isMobile ? slide.mobileBackground : slide.background}
            backgroundPosition={
              isMobile ? slide.mobilePosition || "center" : "center"
            }
          >
            <Content direction={isArabic ? "rtl" : "ltr"} flip={slide.flip}>
              {slide.content}
            </Content>
          </SlideContainer>
        ))}
      />
    </>
  );
}
