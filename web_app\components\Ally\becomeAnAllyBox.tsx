import { Grid } from "@material-ui/core";
import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import ArrowForwardIcon from "@material-ui/icons/ArrowForward";
import ArrowBackIcon from "@material-ui/icons/ArrowBack";

const Div = styled.div`
  margin-top: 50px;
  > div {
    width: 100%;
    height: 100%;
    border-radius: 15px;
    padding: 40px 30px;
    background: url(/assets/images/contactus-bg.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    .text {
      text-transform: capitalize;
      color: var(--color-4);
      font-weight: 400;
      letter-spacing: 2px;
      font-size: 17px;
    }
  }
  .btn {
    width: 100%;
    font-size: 17px;
    font-weight: 300;
    padding: 15px 20px;
    border: none;
    color: var(--color-4);
    background-color: var(--color-1);
    border-radius: var(--radius-1);
    a {
      display: grid;
      grid-template-columns: 1fr 20px;
    }
    @media (min-width: 961px) {
      margin: auto;
    }
    @media (max-width: 960px) {
      margin-top: 30px;
    }
    > span {
      text-align: start;
    }
    &:focus {
      outline: none;
    }
  }
`;
export default function BecomeAnAllyBox() {
  const { t, i18n } = useTranslation();

  return (
    <Div>
      <div>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item xs={12} md={5}>
            <p className="text">
              {t("Do you want to become an ally? Let's try, it's not hard")}
            </p>
          </Grid>
          <Grid item xs={12} md={4}>
            <a
              target="_blank"
              href="https://docs.google.com/forms/d/e/1FAIpQLScaMdIXlcpAegOjg-tr-yzc6USNOOHLaP0F9BRAGNAQuvlyow/viewform"
              rel="noreferrer"
              className="text-align-localized"
            >
              <button
                className="btn cursor-pointer d-flex justify-content-between"
                type="submit"
              >
                <span>{t("Become an Ally")}</span>
                {i18n.language === "ar" ? (
                  <ArrowBackIcon />
                ) : (
                  <ArrowForwardIcon />
                )}
              </button>
            </a>
          </Grid>
        </Grid>
      </div>
    </Div>
  );
}
