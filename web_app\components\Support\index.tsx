import { Container } from "@material-ui/core";
import Layout from "components/shared/layout";
import styled from "styled-components";
import BecomeAnAllyBox from "./becomeAnAllyBox";
import Contact from "./contact";
import Heading from "./heading";

const Div = styled.div`
  background-color: var(--color-6);
  @media (min-width: 961px) {
    position: relative;
    z-index: 1;
    &::after {
      content: "";
      background: url("/assets/images/support/img1.png");
      background-repeat: no-repeat;
      width: 333px;
      height: 625px;
      position: absolute;
      left: 0;
      top: 90px;
      z-index: -1;
    }
    &::before {
      content: "";
      background: url("/assets/images/support/img2.png");
      background-repeat: no-repeat;
      width: 410px;
      height: 456px;
      position: absolute;
      right: 0;
      top: 20%;
      z-index: -1;
    }
  }
  .MuiContainer-maxWidthMd {
    padding: 0;
  }
`;

export default function SupportView() {
  return (
    <Layout>
      <Div>
        <Container>
          <section>
            <Heading />
            <Container maxWidth="md">
              <Contact />
            </Container>
          </section>
          <section>
            <BecomeAnAllyBox />
          </section>
        </Container>
      </Div>
    </Layout>
  );
}
