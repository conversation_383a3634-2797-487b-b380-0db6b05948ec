import React from "react";
import RentCarInCities from "./rentCarInCities";
import RentCarInCity from "./rentCarInCity";
import SearchBox from "./searchBox";
import { Container } from "@material-ui/core";

export default function SearchContainer({ cityData, cities }) {
  return (
    <>
      <SearchBox home />
      <Container>
        {cities && cityData ? (
          <>
            <RentCarInCity cityData={cityData} />
            <hr />
            <RentCarInCities cityData={cityData} cities={cities} />
            <hr />
          </>
        ) : null}
      </Container>
    </>
  );
}
