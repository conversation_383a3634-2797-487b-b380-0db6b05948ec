import React, { CSSProperties, ReactNode } from "react";
import styled from "styled-components";

const Div = styled.div`
  background: ${(props) => (props.color ? props.color : "var(--color-5)")};
  top: 0px;
  /* left: 0; */
  position: absolute;
  width: max-content;
  /* max-height: 21rem; */
  border: solid 1px var(--color-10);
  border-radius: 20px;
  overflow-y: auto;
  padding: 10px 15px;
  left: ${(props) => (props.anchor === "right" ? 0 : "")};
  right: ${(props) => (props.anchor === "left" ? 0 : "")};
  @media (max-width: 768px) {
    max-width: 90vw !important;
  }
  z-index: 999;
`;

export default function Tooltip({
  children,
  isOpen,
  anchor,
  color,
  style,
}: {
  children: ReactNode;
  isOpen: boolean;
  anchor?: "right" | "left";
  style?: CSSProperties;
  color?: string;
}) {
  if (isOpen && Div)
    return (
      <div>
        <Div color={color} id="tool-tip" anchor={anchor} style={style}>
          {children}
        </Div>
      </div>
    );
  return null;
}
