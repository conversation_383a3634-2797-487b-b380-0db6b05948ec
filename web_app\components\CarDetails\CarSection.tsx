/* eslint-disable @next/next/no-img-element */
import Card from "./card";
import Grid from "@material-ui/core/Grid";
import Swiper from "./CarrusoleCarImage";
import { memo } from "react";
import { RootStateOrAny, useSelector } from "react-redux";
import { useRouter } from "next/router";

const CarSection = () => {
  const { bookingId } = useRouter().query;
  const { car_data } = useSelector((state: RootStateOrAny) => state.cars) || {};

  return (
    <Grid
      className="w-100  mobile-0-margin"
      container
      justifyContent="space-between"
      spacing={3}
      alignItems="center"
    >
      <Grid item md={4} xs={12} className="align-self-center rounded">
        {!bookingId && car_data?.carVersion?.images?.length ? (
          <div>
            <Swiper
              breakpoints={{
                320: {
                  slidesPerView: 1,
                  spaceBetween: 25,
                },
                900: {
                  slidesPerView: 1,
                  spaceBetween: 25,
                },
                1024: {
                  slidesPerView: 1,
                  spaceBetween: 25,
                },
              }}
              navigation
              // pagination={{ clickable: true }}
              // scrollbar={{ draggable: true }}
              onSwiper={(swiper) => null}
              onSlideChange={() => null}
              slides={car_data?.carVersion?.images}
            />
          </div>
        ) : (
          <div className="text-center">
            <img
              src={car_data?.carThumb || "/assets/images/car.png"}
              alt="car image"
            />
          </div>
        )}
      </Grid>
      <Grid item md={8} sm={12}>
        <Card carData={car_data} />
      </Grid>
    </Grid>
  );
};
export default memo(CarSection);
