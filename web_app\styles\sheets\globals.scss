:root {
  --color-1: #2a292f;
  --color-2: #fa9c3f;
  --color-3: #7ab3c5;
  --color-4: #fff;
  --color-5: #36353b;
  --color-6: #f8f8f8;
  --color-7: #ebedf1;
  --color-8: #8b8b8b;
  --color-9: #f65656;
  --color-10: #e9e9e9;
  --color-11: #aeaeae;
  --color-12: #c4c4c4;
  --color-13: #e5f4f8;
  --color-14: #bdc7cb;
  --color-15: #e1e4e5;
  --color-16: #aeaeae;
  --color-17: #36353c;
  --color-18: #f9f9f9;
  --color-19: #9ea6a9;
  --color-20: #e6e6e6;
  --color-21: #f5f5f5;
  --color-22: rgb(40, 184, 40);
  --font-en: "Jost";
  --font-ar: "Helvetica";
  --radius-1: 5px;
  --radius-2: 15px;
  --radius-3: 10px;
  --radius-4: 20px;
  --radius-5: 25px;
  --radius-6: 30px;
  --radius-7: 35px;
  --radius-8: 40px;
}
* {
  box-sizing: border-box;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}
html,
body,
#root,
.App {
  height: 100%;
}
.swal2-title{
  text-align: center !important;
}
body {
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  box-sizing: border-box;
  overflow-x: hidden;
  background-color: #f8f8f8;
  a {
    color: inherit;
    text-decoration: none;
  }
  .App {
    display: grid;
    grid-template-rows: max-content 1fr max-content;
    grid-template-columns: 100%;
  }
  > div {
    height: 100%;
  }
}
html[lang="en"] body {
  font-family: "Jost", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
    sans-serif !important;
  font-display: optional;
}
html[lang="ar"] body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
   sans-serif !important;
  direction: rtl;
  font-display: optional;
}
html[lang="ar"] {
  font-family: var(--font-ar);
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  input,
  label {
    text-align: right;
    font-family: var(--font-ar) !important;
    margin: 0;
  }
  div {
    font-family: var(--font-ar) !important;
  }
  span[data-riyal],
  p[data-riyal],
  div[data-riyal],
  label[data-riyal] {
    font-family: "Helvetica-Riyal" !important;
  }
}
html[lang="en"] {
  font-family: var(--font-ar);
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  input,
  label {
    text-align: left;
    font-family: var(--font-ar) !important;
    margin: 0;
  }
  div {
    font-family: var(--font-ar) !important;
  }
  span[data-riyal],
  p[data-riyal],
  div[data-riyal],
  label[data-riyal] {
    font-family: "Helvetica-Riyal" !important;
  }
}

// *{
//   font-family: 'Helvetica';
// }
html, body{
  height: 100%;
}
body{
  margin: 0;
  box-sizing: border-box;
}
.bold{
  font-weight: bold;
}
#main{
  position: relative;
  min-height: 100%;

}
img#top-pattern {
  
  top: 30px;
  width: 100%;
  height: 33px;
  object-fit: cover;
}
.top-pattern-wrapper{
  position: relative;
}
.top-pattern-wrapper .text-wrapper{
  position: relative !important;
  top: 5px;
  right: 10px;
  text-align: right;
  background-color: #fff;
  padding: 0px 18px;
  -moz-transform: skew(-30deg, 0deg);
  -webkit-transform: skew(-30deg, 0deg);
  -o-transform: skew(-30deg, 0deg);
  -ms-transform: skew(-30deg, 0deg);
  transform: skew(-30deg, 0deg);
  width: fit-content;
  padding: 5px;
}

html[lang= en] .top-pattern-wrapper .text-wrapper{
  left: 10px;
  right: auto;
}

.top-pattern-wrapper .text-wrapper h2{
  color: #80B1C1;
  margin: 0 0 5px 0;
  font-size: 20px;
  line-height: 16px;
  -moz-transform: skew(30deg, 0deg);
  -webkit-transform: skew(30deg, 0deg);
  -o-transform: skew(30deg, 0deg);
  -ms-transform: skew(30deg, 0deg);
  transform: skew(30deg, 0deg);
}
.top-pattern-wrapper .text-wrapper h4{
  color: #B2B2B2;
  margin: 0;
  font-size: 16px;
  line-height: 14px;
  -moz-transform: skew(30deg, 0deg);
  -webkit-transform: skew(30deg, 0deg);
  -o-transform: skew(30deg, 0deg);
  -ms-transform: skew(30deg, 0deg);
  transform: skew(30deg, 0deg);
}
img#bottom-pattern {
  position: absolute;
  bottom: 30px;
  width: 100%;
  height: 33px;
  object-fit: cover;
}
html[lang = ar]#text-terms{
  text-align: right;
  direction: rtl;
  padding: 70px 25px 10px 25px;
}
#text-terms h5{
  font-size: 18px;
  line-height: 24px;
  color: #80B1C1;
  margin: 20px 0 0 0;
  padding: 5px 0;
  width: fit-content;
  font-weight: bold;
  position: relative;
}
#text-terms h5:after {
  content: '';
  width: 100%;
  border-bottom: solid 2px #80B1C1;
  display: block;
  position: absolute;
  bottom: 0;
}
#text-terms p{
  font-size: 18px;
  line-height: 24px;
  margin: 15px 0 0 0;
  color: #77767B;
}
.note{
  color: #80B1C1;
  text-decoration: underline;
}
#carwah-copywrites{
  padding: 10px 25px 120px 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 14px;
}

@media (max-width: 768px) {
  .mobile-0-margin {
    margin: 0 !important;
  }
}

::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track-piece {
  display: none !important;
}
::-webkit-scrollbar-thumb {
  width: 5px;
  height: 5px;
  min-height: 3px;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  background: #c5c5c5 !important;
  border-radius: 5px !important;
}
::-webkit-scrollbar-thumb:hover {
  background: gray !important;
}

input.hide-arrows::-webkit-outer-spin-button,
input.hide-arrows::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input.hide-arrows[type="number"] {
  -moz-appearance: textfield;
}

.riyal-symbol {
  font-family: "Helvetica-Riyal" !important;
}

