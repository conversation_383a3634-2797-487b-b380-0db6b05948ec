import { Grid } from "@material-ui/core";
import Card from "components/Home/card";
import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  img {
    width: 120px;
  }
  > div > div {
    margin-bottom: 20px;
  }
  .white-card {
    height: 100%;
    margin: 0 10px;
    > div {
      padding: 35px 40px;
    }
    h6 {
      font-weight: 600;
      font-size: 1.3rem;
    }
    h6,
    p {
      text-align: center;
    }
    p {
      margin-top: 10px;
      font-size: 1.05rem;
    }
  }
`;

export default function Cards() {
  const { t } = useTranslation();
  const cardsList = [
    {
      img: `/assets/images/whyChoose1.svg`,
      title: t("Competitive Prices"),
      description: t("The lowest price on the market for any type of cars."),
    },
    {
      img: `/assets/images/whyChoose2.svg`,
      title: t("Largest Network"),
      description: t(
        "Our allies are always close to you because they are spread all over the Kingdom."
      ),
    },
    {
      img: `/assets/images/whyChoose3.svg`,
      title: t("Variety of Options"),
      description: t(
        "We offer you a lot of options (allies, cars, rental prices and more) in one place."
      ),
    },
    {
      img: `/assets/images/whyChoose4.svg`,
      title: t("Quality Service"),
      description: t(
        "A dedicated 24/7 customer service to provide the best services and support."
      ),
    },
  ];

  return (
    <Div>
      <Grid container wrap="wrap">
        {cardsList.map((cardData, index) => {
          return (
            <Grid item xs={12} md={6} lg={3} key={index}>
              <Card
                img={cardData.img}
                title={cardData.title}
                description={cardData.description}
              />
            </Grid>
          );
        })}
      </Grid>
    </Div>
  );
}
