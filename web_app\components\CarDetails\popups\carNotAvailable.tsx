import { useTranslation } from "react-i18next";
import MessagePopup from "./messagePopup";
import styled from "styled-components";
import { Button } from "@material-ui/core";
import { Dispatch, SetStateAction } from "react";
import { useRouter } from "next/router";

const Div = styled.div`
  * {
    text-align: center !important;
  }
  h4 {
    margin-bottom: 10px !important;
  }
  .btn {
    text-align: center;
    margin-top: 20px;
    background-color: var(--color-3);
    color: var(--color-4);
    padding: 10px 20px 12px 20px;
    font-weight: bold;
    &:hover {
      background-color: var(--color-3);
    }
  }
`;
export default function CarNotAvailable({
  isOpen,
  setIsOpen,
}: {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}) {
  const { t } = useTranslation();
  const { push } = useRouter();

  if (isOpen) {
    return (
      <MessagePopup
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        body={
          <Div>
            <h4>{t("Dear Customer") as string}</h4>
            <p>
              {
                t(
                  "The car is currently not available, you can book another car"
                ) as string
              }
            </p>
            <div className="w-100 d-flex justify-content-center">
              <Button className="btn " onClick={() => push(`/rent-to-own`)}>
                {t("Reserve another car") as string}
              </Button>
            </div>
          </Div>
        }
        title=""
      />
    );
  }
  return null;
}
