/* eslint-disable @next/next/no-img-element */
import { Container } from "@material-ui/core";
import Layout from "components/shared/layout";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import CarGroupingCard from "./Card";
import { Pagination } from "@material-ui/lab";
import { useMemo, useState } from "react";
import { useQuery } from "@apollo/client";
import { CarGrouping_Query } from "gql/queries/rentToOwn";
import RequestLoader from "components/shared/requestLoader";
import { RootStateOrAny, useSelector } from "react-redux";

const Div = styled.div`
  margin-bottom: 50px;
`;
const Header = styled.section`
  margin: 50px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;

  h1 {
    font-size: 24px;
    font-weight: bold;
  }
  @media (max-width: 768px) {
    img {
      width: 50%;
    }
  }
`;

const CardsWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 120px;
`;

function RentToOwnCarGrouping({ carId }) {
  //Hooks
  const { t } = useTranslation();

  //State
  const [page, setPage] = useState(1);

  //Functions
  function handlePageChange(event, value) {
    setPage(Number(value));
  }
  //Store
  const searchData = useSelector((state: RootStateOrAny) => state.search_data);

  //gql
  const { data: listRentToOwnCarOptionsRes, loading } = useQuery(
    CarGrouping_Query,
    {
      variables: {
        carId,
        pickUpLocationId: searchData?.user_address?.pick_up?.id,
      },
    }
  );

  const { collection, metadata } =
    useMemo(() => {
      return {
        collection:
          listRentToOwnCarOptionsRes?.listRentToOwnCarOptions?.collection,
        metadata: listRentToOwnCarOptionsRes?.listRentToOwnCarOptions?.metadata,
      };
    }, [listRentToOwnCarOptionsRes]) || {};

  return (
    <Layout>
      <RequestLoader loading={loading} />
      <Container>
        <Div>
          <Header>
            <h1>{t("Select your Car") as string}</h1>
            <img src="/assets/images/rentToOwnDark.svg" alt="rent to own" />
          </Header>
          <CardsWrapper>
            {collection?.length
              ? collection.map((item: any) => {
                  return <CarGroupingCard key={item.id} {...item} />;
                })
              : null}
          </CardsWrapper>
        </Div>
        {collection?.length ? (
          <Pagination
            onChange={handlePageChange}
            showFirstButton
            showLastButton
            className="d-flex justify-content-center"
          />
        ) : null}
      </Container>
    </Layout>
  );
}

export default RentToOwnCarGrouping;
