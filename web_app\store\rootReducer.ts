import { combineReducers } from "redux";
import LanguageReducer from "./language/reducer";
import SearchDataReducer from "./search/reducer";
import AuthenticationReducer from "./authentication/reducer";
import BookingReducer from "./boooking/reducer";
import CarsReducer from "./cars/reducer";
import CouponsReducer from "./coupons/reducer";
import ExtensionsReducer from "./extensions/reducer";
import InstallmentsReducer from "./installments/reducer";
import WalletReducer from "./wallet/reducer";
import UserReducer from "./user/reducer";
import LoyalityReducer from "./loyality/reducer";

const rootReducer = combineReducers({
  language: LanguageReducer,
  search_data: SearchDataReducer,
  authentication: AuthenticationReducer,
  booking: BookingReducer,
  cars: CarsReducer,
  coupons: CouponsReducer,
  extensions: ExtensionsReducer,
  installments: InstallmentsReducer,
  wallet: WalletReducer,
  user: UserReducer,
  loyality: LoyalityReducer,
});

export default rootReducer;
