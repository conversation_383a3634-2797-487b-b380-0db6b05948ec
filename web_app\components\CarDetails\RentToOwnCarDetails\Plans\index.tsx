/* eslint-disable @next/next/no-img-element */
/* eslint-disable react-hooks/exhaustive-deps */
import SwiperCarousel from "components/shared/carousel";
import { useRouter } from "next/router";
import { CSSProperties, useEffect } from "react";
import { TFunction, useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { setRentToOwnPlan } from "store/cars/action";
import styled from "styled-components";
import { Pagination } from "swiper";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  background-color: #fff;
  border-radius: var(--radius-2);
  margin-bottom: 24px;
  .padding-30px {
    padding: 30px;
  }
  h2 {
    font-weight: bold;
    text-align: start;
    font-size: 18px;
  }

  hr {
    margin: 0;
  }
  .plans-wrapper {
    .plan {
      &.selected {
        background-color: var(--color-2);
        color: #ffffff;
        border-color: transparent !important;
      }
      &:not(.selected) {
        span:last-child {
          background: #dddddd !important;
          color: #908f92 !important;
        }
      }
      border: solid 2px gainsboro;
      padding: 8px 15px 25px 15px;
      display: flex;
      flex-direction: column;
      border-radius: var(--radius-2);
      gap: 2px;

      align-items: center;
      span:first-child {
        display: inline-block;
        font-size: 35px;
        font-weight: bold;
        min-width: 50px;
        min-height: 50px;
      }
      span:nth-child(2) {
        display: inline-block;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 15px;
        padding: 0px 10px 5px;
        font-size: 16px;
        font-weight: bold;
      }
      &.disabled {
        cursor: not-allowed;
      }
    }
    .swiper-wrap .swiper-container {
      padding-bottom: 30px !important;
    }
  }
  .after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #b7b7b7;
    filter: blur(1px);
    opacity: 0.5;
    border-radius: var(--radius-2);
  }
`;

function Note({
  months,
  cost,
  locale,
  t,
}: {
  months: number;
  cost: number;
  locale: "en" | "ar";
  t: TFunction<"translation">;
}) {
  const style: CSSProperties = {
    textAlign: "center",
    width: "85%",
    margin: "0 auto",
    paddingBottom: "30px",
    lineHeight: "27px",
  };

  if (locale === "en") {
    return (
      <p
        {...{ style }}
        dangerouslySetInnerHTML={{
          __html: `You can own the car after <strong style="border-bottom: solid 2px;">${months} month</strong> through a priority purchase system, where a final installment of <strong style="border-bottom: solid 2px; display: inline-flex; align-items: center; gap: 5px;"> <div style="order: 1;">${cost}</div> <div style="order: 0;"><span class="riyal-symbol" style="font-family: Helvetica-Riyal;">﷼</span></div></strong> is calculated.`,
        }}
      />
    );
  } else {
    return (
      <p
        {...{ style }}
        dangerouslySetInnerHTML={{
          __html: `يمكنك امتلاك السيارة بعد <strong style="border-bottom: solid 2px;">${months} شهر </strong> بنظام أولوية الشراء حيث يتم احتساب دفعة أخيرة قدرها <span style="direction: ltr"><strong style="border-bottom: solid 2px;">${cost}${t(
            "﷼"
          )} </strong></span>`,
        }}
      />
    );
  }
}

function RentToOwnPlans() {
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const { bookingId } = router.query.bookingId || ({} as any);
  const rentalData = useSelector((state: RootStateOrAny) => state.booking);
  const { ownCarDetails } = rentalData?.rentalDetails || {};
  const { rentalOwnCarPlan } = ownCarDetails || {};

  const { rent_to_own_plan, car_data } =
    useSelector((state: RootStateOrAny) => state?.cars) || {};
  const { ownCarPlans } = car_data?.ownCarDetail || {};
  const selectPlan = (id, userSelectedPlan) => {
    const plan = ownCarPlans?.find((i: any) =>
      userSelectedPlan ? i.id == id : i?.isActive
    );
    Boolean(plan) && dispatch(setRentToOwnPlan(plan));
  };
  useEffect(() => {
    if (bookingId) {
      dispatch(setRentToOwnPlan(rentalOwnCarPlan));
    } else {
      dispatch(
        setRentToOwnPlan(
          ownCarPlans?.find((i) => {
            return i.isActive === true;
          })
        )
      );
    }
  }, [ownCarPlans, bookingId, rentalOwnCarPlan]);

  return (
    <Div>
      <h2 className="padding-30px">{t("Select Plan") as string}</h2>
      <hr />
      <div className="plans-wrapper padding-30px pb-0">
        <SwiperCarousel
          i18n={i18n}
          spaceBetween={15}
          slidesPerView={2.6}
          onSwiper={(swiper) => null}
          onSlideChange={() => null}
          pagination={{ clickable: true }}
          modules={[Pagination]}
          slides={
            ownCarPlans?.length
              ? ownCarPlans.map(
                  ({ id, isActive, noOfMonths, finalInstallment }) => {
                    return (
                      <div
                        key={`plan-months-${id}`}
                        title={!isActive ? "تواصل معنا لمعلومات اكثر" : ""}
                        className={`plan position-relative ${
                          !Boolean(router.query.bookingId) && "cursor-pointer"
                        } ${rent_to_own_plan?.id == id ? "selected" : ""} ${
                          !isActive ? "disabled" : ""
                        }`}
                        onClick={() => {
                          if (isActive && !router.query.bookingId) {
                            selectPlan(id, {
                              id,
                              isActive,
                              noOfMonths,
                              finalInstallment,
                            });
                          }
                        }}
                      >
                        <span>{noOfMonths}</span>
                        <span>{t("Month") as string}</span>
                        {!isActive ? (
                          <div className="after">
                            <img src="/assets/icons/lock.svg" alt="lock" />
                          </div>
                        ) : null}
                      </div>
                    );
                  }
                )
              : null
          }
        />
      </div>

      <Note
        {...{
          months: rent_to_own_plan?.noOfMonths,
          cost: rent_to_own_plan?.finalInstallment,
          locale: i18n.language as "en" | "ar",
          t,
        }}
      />

      <div className="d-flex gap-5px">
        <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
          <RiyalSymbol />
        </span>
        <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
          {rent_to_own_plan?.finalInstallment}
        </span>
      </div>
    </Div>
  );
}

export default RentToOwnPlans;
