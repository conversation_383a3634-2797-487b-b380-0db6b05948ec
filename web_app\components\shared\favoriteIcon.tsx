import { ReactSVG } from "react-svg";
import styled from "styled-components";

const Div = styled.div`
  .favorite-wrap {
    cursor: pointer;
  }
  .fill {
    * {
      transition: all 0.5s ease;
      fill: var(--color-9);
    }
  }
`;

export default function FavoriteIcon() {
  return (
    <Div>
      <div
        className="favorite-wrap"
        onClick={(e) =>
          e.target.closest(".favorite-wrap").classList.toggle("fill")
        }
      >
        <ReactSVG src={`/assets/images/favorite.svg`} />
      </div>
    </Div>
  );
}
