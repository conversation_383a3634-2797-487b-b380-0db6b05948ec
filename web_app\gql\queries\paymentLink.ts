import { gql } from "@apollo/client";

export const PaymentLinkDetails_Query = gql`
  query paymentLinkDetails($token: String!) {
    paymentLinkDetails(token: $token) {
      createdAt
      fullPaymentUrl
      generatedAt
      id
      payable {
        ... on Rental {
          id
          bookingNo
          status
          # paymentStatus
          paymentStatusCode
          totalAmountDue
          totalBookingPrice
          customerName
          paymentLink {
            fullPaymentUrl
          }
        }
        ... on Installment {
          id
          amount
          dueDate
          amountDue
          installmentNumber
          paymentStatus
        }
        ... on RentalDateExtensionRequest {
          id
          extensionDays
          paymentStatus
          totalAmountDue
          numberOfExtension
          hasPendingPaymentTransaction
        }
      }
      payableId
      payableType
      status
      token
      updatedAt
      validForPayment
    }
  }
`;

export const PaymentLinkCheckout_Query = gql`
  query paymentLinkCheckout(
    $paymentBrand: RentalPaymentBrand
    $token: String!
  ) {
    paymentLinkCheckout(paymentBrand: $paymentBrand, token: $token) {
      checkoutId
      gatewayResponse
      integrity
    }
  }
`;
