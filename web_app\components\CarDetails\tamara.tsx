/* eslint-disable react-hooks/exhaustive-deps */
import moment from "moment";
import { useRouter } from "next/router";
import { useEffect, useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { setTamaraAvailablility } from "store/cars/action";
import useRentPaymentLogic from "./RentPayment/useLogic";
import useInvoiceLogic from "./Invoice/useLogic";
import Script from "next/script";
import useFirebase from "useFirebase";

// Constants
const TAMARA_PICKUP_LIMIT_DAYS = 90;
const TAMARA_WIDGET_CONFIG = {
  COUNTRY: "SA",
  BADGE_RATIO: 0.8,
  FONT_SIZE: "14px",
  ICON_WIDTH: "12px",
  LINE_HEIGHT: "30px",
} as const;

// Types
interface TamaraProps {
  calculatedPrice?: number;
}

interface TamaraWidgetConfig {
  lang: string;
  country: string;
  css: string;
  style: {
    fontSize: string;
    badgeRatio: number;
  };
}

interface RemoteConfigValues {
  remove_tamara_limit?: { _value: string };
  tamara_min_limit_value?: { _value: string };
  tamara_max_limit_value?: { _value: string };
}

interface SearchData {
  date_time?: {
    pickUpDate?: string;
  };
}

interface CarsState {
  about_rent_price?: {
    totalPrice?: number;
  };
}

interface BookingState {
  rentalDetails?: {
    totalAmountDue?: number;
    pickUpDate?: string;
  };
  price_temp?: number;
}

interface WalletState {
  pay_with_wallet?: boolean;
}

// Global declarations
declare global {
  interface Window {
    tamaraWidgetConfig: TamaraWidgetConfig;
    TamaraWidgetV2: {
      refresh: () => void;
    };
  }
  namespace JSX {
    interface IntrinsicElements {
      "tamara-widget": any;
    }
  }
}

const Tamara = ({ calculatedPrice }: TamaraProps) => {
  // Hooks
  const router = useRouter();
  const { bookingId } = router.query;
  const { i18n, t } = useTranslation();
  const dispatch = useDispatch();
  const { rentalHasInstallment } = useRentPaymentLogic();
  const { getInstallmentsInvoiceData, installments } = useInvoiceLogic();
  const { remoteConfigValues } = useFirebase();

  // Remote config values with proper typing
  const {
    remove_tamara_limit: _remove_tamara_limit,
    tamara_min_limit_value: _tamara_min_limit_value,
    tamara_max_limit_value: _tamara_max_limit_value,
  } = (remoteConfigValues as RemoteConfigValues) || {};

  const tamaraConfig = useMemo(
    () => ({
      removeTamaraLimit: Boolean(_remove_tamara_limit?._value === "true"),
      tamaraMinLimit: Number(_tamara_min_limit_value?._value) || 0,
      tamaraMaxLimit: Number(_tamara_max_limit_value?._value) || Infinity,
    }),
    [_remove_tamara_limit, _tamara_min_limit_value, _tamara_max_limit_value]
  );

  // Store selectors with proper typing
  const searchData = useSelector(
    (state: RootStateOrAny) => state.search_data as SearchData
  );
  const carsState = useSelector(
    (state: RootStateOrAny) => state.cars as CarsState
  );
  const bookingState = useSelector(
    (state: RootStateOrAny) => state.booking as BookingState
  );
  const walletState = useSelector(
    (state: RootStateOrAny) => state.wallet as WalletState
  );

  // Extracted data from store
  const { about_rent_price } = carsState || {};
  const { totalPrice } = about_rent_price || {};
  const { date_time } = searchData || {};
  const { pickUpDate } = date_time || {};
  const { rentalDetails, price_temp } = bookingState || {};
  const {
    totalAmountDue: totalPriceFromDetails,
    pickUpDate: pickUpDateFromDetails,
  } = rentalDetails || {};

  // Calculate next installment
  const nextInstallmentToPay = useMemo(() => {
    const installmentData = getInstallmentsInvoiceData(installments);
    return (
      installmentData.nextInstallmentToPay ||
      installmentData.upcomingInstallmentToPay
    );
  }, [getInstallmentsInvoiceData, installments]);

  // Price eligibility check
  const isPriceEligible = useMemo(() => {
    const { removeTamaraLimit, tamaraMinLimit, tamaraMaxLimit } = tamaraConfig;

    if (removeTamaraLimit) return true;

    const checkPriceRange = (price: number) =>
      price >= tamaraMinLimit && price <= tamaraMaxLimit;

    // Check calculated price first
    if (calculatedPrice !== undefined) {
      return checkPriceRange(calculatedPrice);
    }

    // Check extension or temporary price
    if (router.asPath.includes("#extension") || price_temp !== undefined) {
      return checkPriceRange(price_temp || 0);
    }

    // Check installment price
    if (rentalHasInstallment) {
      return checkPriceRange(nextInstallmentToPay || 0);
    }

    // Check total price
    const priceToCheck = bookingId ? totalPriceFromDetails : totalPrice;
    return checkPriceRange(priceToCheck || 0);
  }, [
    tamaraConfig,
    calculatedPrice,
    router.asPath,
    price_temp,
    rentalHasInstallment,
    nextInstallmentToPay,
    bookingId,
    totalPriceFromDetails,
    totalPrice,
  ]);

  // Pickup date validation
  const isPickupIn90Days = useMemo(() => {
    const dateToCheck = bookingId ? pickUpDateFromDetails : pickUpDate;
    const dateFormat = bookingId ? "YYYY-MM-DD" : "DD/MM/YYYY";

    if (!dateToCheck) return false;

    return (
      moment(dateToCheck, dateFormat).diff(moment(), "days") <=
      TAMARA_PICKUP_LIMIT_DAYS
    );
  }, [pickUpDate, pickUpDateFromDetails, bookingId]);

  // Tamara availability
  const isTamaraPromotionalAvailable = useMemo(() => {
    const isAvailable = isPriceEligible && isPickupIn90Days;
    dispatch(setTamaraAvailablility(isAvailable));
    return isAvailable;
  }, [isPriceEligible, isPickupIn90Days, dispatch]);

  // Import useLogic hook properly
  const { hasInstallment, totalAmountDueFromRentalDetails, totalAmountDue } =
    useInvoiceLogic();

  // Calculate current price
  const getCurrentPrice = useCallback(() => {
    if (hasInstallment) {
      return (
        getInstallmentsInvoiceData(installments)?.nextInstallmentToPay || 0
      );
    }
    return bookingId
      ? totalAmountDueFromRentalDetails || 0
      : totalAmountDue || 0;
  }, [
    hasInstallment,
    getInstallmentsInvoiceData,
    installments,
    bookingId,
    totalAmountDueFromRentalDetails,
    totalAmountDue,
  ]);

  const currentPrice = getCurrentPrice();

  // Tamara widget configuration
  const tamaraWidgetConfig = useMemo((): TamaraWidgetConfig => {
    const isArabic = i18n.language === "ar";
    const isEnglish = i18n.language === "en";

    return {
      lang: i18n.language,
      country: TAMARA_WIDGET_CONFIG.COUNTRY,
      css: `
        .tamara-summary-widget__container svg { width: ${
          TAMARA_WIDGET_CONFIG.ICON_WIDTH
        }; }
        .tamara-summary-widget__container {
          position: relative;
          text-align: ${isArabic ? "right" : "left"};
          line-height: ${TAMARA_WIDGET_CONFIG.LINE_HEIGHT} !important;
          font-family: ${isEnglish ? "Jost" : "Helvetica"} !important;
        }
        .tamara-badge {
          position: absolute;
          top: 10px;
          ${isEnglish ? "right: 0;" : "left: 0;"}
        }
      `,
      style: {
        fontSize: TAMARA_WIDGET_CONFIG.FONT_SIZE,
        badgeRatio: TAMARA_WIDGET_CONFIG.BADGE_RATIO,
      },
    };
  }, [i18n.language]);

  // Refresh Tamara widget when price or language changes
  useEffect(() => {
    if (window?.TamaraWidgetV2) {
      window.TamaraWidgetV2.refresh();
    }
  }, [currentPrice, i18n.language]);

  // Render widget text based on language
  const renderWidgetText = useCallback(() => {
    const isArabic = i18n.language === "ar";

    if (isArabic) {
      return (
        <span>
          {`${t(
            "قسمها حتي 4 دفعات بدون فوائد, ادفع"
          )} {{downpaymentAmount}} لكل دفعة أو ادفعها كاملة! {{learnMoreLink}}`}
        </span>
      );
    }

    return (
      <span>
        {`${t(
          "Split in up to interest-free payments of"
        )} {{downpaymentAmount}}, or pay in full! {{learnMoreLink}}`}
      </span>
    );
  }, [i18n.language, t]);

  // Don't render if not available or price is 0
  if (!isTamaraPromotionalAvailable || currentPrice === 0) {
    return null;
  }

  return (
    <>
      <div
        style={{
          background: "white",
          border: "solid 0px white",
          borderRadius: "12px",
        }}
      >
        <tamara-widget
          type="tamara-summary"
          amount={currentPrice}
          inline-type="0"
        >
          <div className="flex justify-content-between">
            <div>{renderWidgetText()}</div>
            <div>{`{{badgeTamara}}`}</div>
          </div>
        </tamara-widget>
      </div>

      <Script id="tamara-script">
        {`window.tamaraWidgetConfig = ${JSON.stringify(tamaraWidgetConfig)};`}
      </Script>
    </>
  );
};

export default Tamara;
