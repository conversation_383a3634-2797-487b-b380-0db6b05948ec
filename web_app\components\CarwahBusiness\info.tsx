import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { Grid } from "@material-ui/core";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { GoToLogin } from "store/authentication/action";
import { useRouter } from "next/router";

const Hashtag = styled.div`
  color: #000;
  margin: 20px 0px;
`;
const DIV = styled.div`
  button.order {
    width: 100%;
    padding: 10px 0px;
    border: none;
    box-shadow: none;
    border-radius: var(--radius-3);
    background-color: var(--color-3);
    color: var(--color-4);
    font-weight: 500;
  }
  .title {
    color: var(--color-3);
  }
  .content {
    padding: 20px 0px;
  }
  .btn-link {
    color: #000;
  }
`;
export default function Info() {
  const { t } = useTranslation();
  const router = useRouter();
  const state = useSelector((state: RootStateOrAny) => state.authentication);

  const dispatch = useDispatch();
  return (
    <div>
      <Hashtag>
        <h3 className="bold">{t("Carwah Business")}</h3>
      </Hashtag>
      <DIV>
        <p className="title">{t("introduction")}</p>
        <p className="content">{t("introduction-content")}</p>
        <p className="title">{t("target-customer")}</p>
        <p className="content">{t("target-cutomer-content")}</p>
        <Grid
          container
          direction="row"
          justifyContent="center"
          alignItems="center"
          xs={12}
          md={12}
          className="form-wrapper"
        >
          <Grid item xs={8} md={8}>
            <button
              className="order"
              onClick={() => {
                !state?.login_data?.token
                  ? dispatch(GoToLogin(true))
                  : router.push("carwah-business#step1");
              }}
            >
              {t("Request Now")}
            </button>
          </Grid>
          <Grid item xs={8} md={8}>
            <button
              className="btn btn-link w-100"
              onClick={() => {
                !state?.login_data?.token
                  ? dispatch(GoToLogin(true))
                  : router.push("/my-requests");
              }}
            >
              {!state?.login_data?.token
                ? t("should be Login")
                : t("trackRequests")}
            </button>
          </Grid>
        </Grid>
      </DIV>
    </div>
  );
}
