/* eslint-disable @next/next/no-img-element */
import { Grid } from "@material-ui/core";
import AirportsTooltip from "components/shared/airportsToolTip";
import moment from "moment";
import { useRouter } from "next/router";
import { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { DateObject } from "react-multi-date-picker";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { setPayWithInstallments } from "store/installments/action";
import {
  setDateTimeAction,
  setIsBarq,
  setIsInstantConfirmation,
  setSelectionIndexAction,
  setUserPickupAddressAction,
} from "store/search/action";
import styled from "styled-components";
import Swiper from "components/shared/carousel";

const Div = styled.div`
  position: relative;
  padding: 30px 0;
  margin: 40px 0;
  @media (max-width: 900px) {
    margin: 0 0 25px 0 !important;
  }

  .section-title {
    margin-bottom: 30px;
    z-index: 1;

    h4 {
      font-size: 22px;
      font-weight: bold;
      line-height: 1.2;
      margin: 0;
      color: #333;
      font-weight: 400;
    }

    .highlight {
      /* color: var(--color-2); */
      display: block;
      font-weight: bold;
      margin-top: 5px;
      position: relative;

      &:after {
        content: "";
        position: absolute;
        bottom: -15px;
        right: ${(props) => (props.lang === "ar" ? "0" : "auto")};
        left: ${(props) => (props.lang === "ar" ? "auto" : "0")};
        width: 20px;
        border-radius: 10px;
        height: 4px;
        background-color: var(--color-2);
      }
    }
  }

  @media (max-width: 900px) {
    .tooltip-wrap {
      left: ${(props) => (props.lang === "en" ? 0 : "")};
      img {
        width: auto !important;
      }
    }
    .swiper-button-prev:before,
    .swiper-button-next:before {
      background: none !important;
    }
    .swiper-button-next:after {
      transform: translateX(0px) !important;
    }
    .card-service {
      min-height: 135px !important;
      margin: auto !important;
    }
    .swiper-slide img {
      width: 75px;
      height: 55px;
    }
  }

  .swiper-wrapper {
    align-items: center;
    min-height: 170px;
    > div {
      min-height: 170px;
    }
  }

  @media (min-width: 1025px) {
    .swiper-button-prev {
      left: -50px;
    }
    .swiper-button-next {
      right: -50px;
    }
  }

  .swiper-button-prev,
  .swiper-button-next {
    color: var(--color-2);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: var(--color-3);
    display: flex;
    justify-content: center;
    align-items: center;

    &:after {
      font-size: 18px;
      transform: none;
      margin: 0;
      font-weight: bold;
    }

    &:before {
      content: none;
    }
  }

  .card-service {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: white;
    padding: 20px;
    border-radius: 20px;
    margin: 0 10px;
    min-height: 170px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;

    .text-content {
      text-align: center;
      margin-top: 15px;
      h3 {
        font-weight: bold;
        font-size: 1.4rem;
        margin-bottom: 5px;
      }
      h6 {
        font-size: 1rem;
        margin-top: 0 !important;
      }
    }

    img {
      width: auto;
      height: 60px;
    }
  }
`;

export default function SpecialServices() {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();
  const dateTime = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );
  const { pickup_time, return_time, pickUpDate } = dateTime || {};

  function setReservationDataHandler({ days }: { days: number }) {
    dispatch(
      setDateTimeAction({
        pickUpDate,
        dropOffDate: moment(pickUpDate, "DD/MM/YYYY")
          .add(days, "days")
          .format("DD/MM/YYYY"),
        selected_days_count: days,
        pickup_time,
        return_time,
        reservation_days: [
          new DateObject({ date: pickUpDate, format: "DD/MM/YYYY" }),
          new DateObject({ date: pickUpDate, format: "DD/MM/YYYY" }).add(
            days,
            "days"
          ),
        ],
      })
    );
  }

  const { confirmed_delivery_location } =
    useSelector((state: RootStateOrAny) => state.search_data) || {};
  const [isOpen, setIsOpen] = useState(false);

  function Card({
    boldText,
    regularText,
    iconUrl,
    clickHandler,
    fillGrid,
    ...rest
  }: {
    boldText: string;
    regularText: string;
    iconUrl: string;
    clickHandler: () => void;
    fillGrid?: boolean;
  }) {
    return (
      <div
        className="card-service"
        onClick={clickHandler}
        style={{ width: "160px", height: "170px" }}
        {...rest}
      >
        <img src={iconUrl} alt={boldText} />
        <div className="text-content">
          <h3 className="text-center">{boldText}</h3>
          <h6 className="text-center">{regularText}</h6>
        </div>
      </div>
    );
  }

  const cardItems = [
    {
      boldText: t("sp.services__Offers & Rental Packages"),
      regularText: t("sp.services__Designed to meet your needs"),
      iconUrl: `assets/icons/home/<USER>
      clickHandler: () => {
        setReservationDataHandler({ days: 30 * 12 });
        dispatch(setSelectionIndexAction(1));
        router.push("/car-search#car-grid");
      },
    },
    {
      boldText: t("sp.services__Pick-up"),
      regularText: t("sp.services__from branch"),
      iconUrl: `assets/icons/home/<USER>
      clickHandler: () => {
        setReservationDataHandler({ days: 3 });
        dispatch(setSelectionIndexAction(0));
        router.push(`car-search#car-grid`);
      },
    },
    {
      boldText: t("sp.services__Airports"),
      regularText: t("sp.services__24 hour"),
      iconUrl: `assets/icons/home/<USER>
      clickHandler: () => {
        setTimeout(() => {
          dispatch(setIsInstantConfirmation(false));
          dispatch(setIsBarq(false));
          setIsOpen(true);
        }, 200);
      },
    },
    {
      boldText: t("sp.services__Instant"),
      regularText: t("sp.services__Confirmation"),
      iconUrl: `assets/icons/home/<USER>
      clickHandler: () => {
        dispatch(setIsBarq(false));
        dispatch(setIsInstantConfirmation(true));
        router.push("/car-search#car-grid");
      },
    },
    {
      boldText: t("sp.services__Express"),
      regularText: t("sp.services__Delivery"),
      iconUrl: `assets/icons/home/<USER>
      clickHandler: () => {
        dispatch(setIsInstantConfirmation(false));
        dispatch(setIsBarq(true));
        dispatch(setSelectionIndexAction(2));
        if (confirmed_delivery_location) {
          router.push("/car-search#car-grid");
        }
      },
    },
  ];

  function renderServiceCards() {
    return cardItems.map((item, index) => (
      <Card
        key={index}
        boldText={item.boldText}
        regularText={item.regularText}
        iconUrl={item.iconUrl}
        clickHandler={item.clickHandler}
      />
    ));
  }

  const SwiperRef = useRef(styled(Swiper)`
    .swiper-wrapper {
      .swiper-slide {
        > div {
          direction: ${i18n.language === "ar" ? "rtl" : "ltr"};
        }
      }
    }
  `);

  const SwiperTag = SwiperRef.current;

  useEffect(() => {
    // Moved next and prev arrows outside of swiper container for styling
    const next = document.querySelector(
      "#special-services-carousel .swiper-button-next"
    );
    const prev = document.querySelector(
      "#special-services-carousel .swiper-button-prev"
    );
    const wrap = document.querySelector(
      "#special-services-carousel .swiper-wrap"
    );
    wrap.appendChild(next);
    wrap.appendChild(prev);
  }, []);

  return (
    <Div lang={i18n.language}>
      <Grid container spacing={2} justifyContent="space-around">
        <Grid item xs={12} sm={2} md={2} lg={2}>
          <div className="section-title">
            <h4>
              {t("Selected").toString()}
              <span className="highlight">{t("For You").toString()}</span>
            </h4>
          </div>
        </Grid>
        <Grid item xs={12} sm={10} md={10} lg={10}>
          <div id="special-services-carousel">
            <SwiperTag
              spaceBetween={20}
              slidesPerView={1}
              breakpoints={{
                320: {
                  slidesPerView: 2,
                  spaceBetween: 10,
                },
                768: {
                  slidesPerView: 3.25,
                  spaceBetween: 15,
                },
                1024: {
                  slidesPerView: 4.5,
                  spaceBetween: 0,
                },
              }}
              navigation
              onSwiper={(swiper) => null}
              onSlideChange={() => null}
              slides={renderServiceCards()}
              // loop={true}
            />
          </div>
        </Grid>
      </Grid>
      {isOpen ? (
        <AirportsTooltip
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          action={setUserPickupAddressAction}
          style={{ left: window?.innerWidth > 960 ? "40vw" : "0" }}
        />
      ) : null}
    </Div>
  );
}
