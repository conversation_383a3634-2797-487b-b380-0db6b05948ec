import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  padding: 50px 0 30px 0;
  h1 {
    /* color: var(--color-11); */
    font-weight: 500;
    text-transform: uppercase;
    font-size: 16px;
  }
  p {
    font-size: 38px;
    /* line-height: 55px; */
    color: var(--color-1);
  }
  @media (max-width: 570px) {
    p {
      font-size: 1.3rem !important;
      /* line-height: 35px !important; */
    }
  }
`;

export default function BecomeAnAly() {
  const { t } = useTranslation();
  return (
    <Div>
      <h1 className="bold">{t("Become an Ally")}</h1>
      <p>{t("Feel free to reach to us about any questions you might have.")}</p>
    </Div>
  );
}
