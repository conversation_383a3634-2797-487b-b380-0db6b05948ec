import { Grid } from "@material-ui/core";
import styled from "styled-components";
export const Hr = styled.hr`
  width: 100%;
`;

export const Card = styled.div`
  padding: 20px 30px;
  border-radius: var(--radius-2);
  height: 100%;
  @media (max-width: 768px) {
    padding: 20px 15px !important;
    label {
      font-size: 12px !important;
    }
    .font-27px {
      font-size: 22px !important;
    }
  }
`;

export const Avatar = styled.div`
  position: relative;
  width: fit-content;
  > img:last-child {
    position: absolute;
    right: 0;
    bottom: 0;
  }
  .picture {
    width: 75px;
    border-radius: 50%;
    height: 75px;
    object-fit: scale-down;
    border: solid 2px var(--color-6);
  }
`;

export const InputRow = styled(Grid)`
  margin: 30px 0;
  align-items: center;
  input,
  .select {
    width: 100%;
    border: none;
    border-bottom: solid 1px var(--color-20);
    margin: 5px 0 !important;
    padding: 5px;
  }
  @media (max-width: 768px) {
    input,
    .select {
      font-size: 13px !important;
    }
  }
  .select-styled {
    border-bottom: solid 1px var(--color-20);
    div {
      border: none;
    }
    > div {
      padding-right: 0 !important;
    }
  }
  .required {
    margin-bottom: 5px;
  }
  p.color-9 {
    font-size: 14px;
  }
  .MuiSelect-selectMenu {
    padding-left: 0 !important;
  }

  input.hide-arrows::-webkit-outer-spin-button,
  input.hide-arrows::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input.hide-arrows[type="number"] {
    -moz-appearance: textfield;
  }
`;

export const PhoneWrapper = styled.div`
  & {
    flex-direction: ${(props) =>
      props.language === "ar" ? "row-reverse" : null};
  }
  #phone_number {
    div {
      flex-direction: ${(props) =>
        props.language === "en" ? "row-reverse" : null};
    }
  }
`;

export const FullCalendar = styled.div`
  @media (min-width: 768px) {
    position: relative;
  }
  @media (max-width: 768px) {
    & {
      gap: 2px !important;
    }
  }
  > div {
    width: 100%;
  }
  > img {
    position: absolute;
    left: ${(props) => (props.language === "ar" ? 0 : null)};
    right: ${(props) => (props.language === "en" ? 0 : null)};
    @media (max-width: 767px) {
      display: none;
    }
  }
  .MuiInput-underline::before,
  .MuiInput-underline::after {
    display: none;
  }
  @media (max-width: 768px) {
    input.MuiInputBase-input.MuiInput-input {
      height: 100%;
      font-size: 13px !important;
    }
  }
`;

export const ButtonsWrapper = styled.div`
  button {
    border-radius: var(--radius-3);
    border: none;
    padding: 12px 30px 16px 30px;
    &[type="submit"] {
      color: var(--color-4);
      background-color: var(--color-3);
    }
    &:not([type="submit"]) {
      color: var(--color-16);
    }
  }
`;
export const ImageCard = styled.div`
  position: relative;
  background: var(--color-6);
  height: 150px;
  border-radius: var(--radius-3);
  margin: 10px 0 30px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  > img.add {
    position: absolute;
    top: 20px;
    right: 20px;
  }
  > img:not(.add) {
    width: 75%;
    height: 75%;
    object-fit: contain;
  }
`;
export const Wallet = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  align-items: center;
  background: var(--color-4);
  border-radius: var(--radius-2);
  padding: 20px 30px;
  button {
    background: #f3f3f3;
    color: #000;
    padding: 5px 0 10px 0;
    width: 100%;
    font-weight: bold;
    &:hover {
      background: var(--color-3);
      color: var(--color-4);
    }
  }
`;
