import {
  CASH_OPTION,
  <PERSON>LEAR_DATA,
  PRICE_TEMP,
  RENTAL_BRANCH,
  SAVE,
  SET_RENTAL_ABOUT_PRICE,
} from "./action";
import {
  DELETE,
  EDIT,
  SAVEEDIT,
  REMOVEEDITSTATUS,
  REMOVELIST,
  MY_RENTALS_CURREMT_PAGE,
  RENTAL_DETAILS,
  SET_RENTAL_DETAILS,
} from "./action";

export default function BookingReducer(
  state = {
    list: [],
    EditStatus: false,
    BookingList: [],
    BookingPositionToEdit: undefined,
    BookingListToEdit: [],
    MyRentalsCurrentPage: 1,
    EditBooking: [],
    rentalDetails: undefined,
    rentalId: null,
    carId: null,
    price_temp: undefined,
    cash_option: false,
  },
  action
) {
  switch (action.type) {
    case SAVE:
      return {
        list: [...state.list, action.payload[0]],
        BookingList: [...state.BookingList, action.payload[1]],
      };
      break;
    case DELETE:
      const AllIlst = [...state.list].filter(
        (item, index) => index != action.payload
      );
      const Booking = [...state.BookingList].filter(
        (item, index) => index != action.payload
      );
      return {
        list: AllIlst,
        BookingList: Booking,
      };
      break;
    case EDIT:
      return {
        list: state.list,
        EditStatus: true,
        BookingList: state.BookingList,
        BookingListToEdit: state.BookingList[action.payload],
        EditBooking: state.list[action.payload],
        BookingPositionToEdit: action.payload,
      };
      break;
    case SAVEEDIT:
      const FilterdList = [...state.list].filter(
        (item, index) => state.BookingPositionToEdit != index
      );
      const filterBooking = [...state.BookingList].filter(
        (item, index) => index != state.BookingPositionToEdit
      );
      return {
        list: [...FilterdList, action.payload[0]],
        EditStatus: false,
        BookingList: [...filterBooking, action.payload[1]],
        BookingListToEdit: [],
        EditBooking: [],
        BookingPositionToEdit: undefined,
      };
      break;
    case REMOVEEDITSTATUS:
      return {
        ...state,
        EditStatus: false,
      };
      break;
    case REMOVELIST:
      return {
        list: [],
        EditStatus: false,
        BookingList: [],
        BookingListToEdit: [],
        EditBooking: [],
        BookingPositionToEdit: undefined,
      };
      break;
    case MY_RENTALS_CURREMT_PAGE:
      return { ...state, MyRentalsCurrentPage: action.payload };
      break;
    case RENTAL_DETAILS:
      return { ...state, rentalDetails: action.payload };
    case RENTAL_BRANCH:
      return { ...state, rentalBranch: action.payload };
    case SET_RENTAL_ABOUT_PRICE:
      return {
        ...state,
        rental_about_price: action.payload,
      };
      break;
    case SET_RENTAL_DETAILS:
      return {
        ...state,
        rentalId: action.payload.rentalId,
        carId: action.payload.carId,
      };
    case PRICE_TEMP:
      return {
        ...state,
        price_temp: action.payload,
      };
    case CASH_OPTION:
      return {
        ...state,
        cash_option: action.payload,
      };
    case CLEAR_DATA:
      return {
        ...state,
        list: [],
        EditStatus: false,
        BookingList: [],
        BookingPositionToEdit: undefined,
        BookingListToEdit: [],
        // MyRentalsCurrentPage: state?.MyRentalsCurrentPage,
        EditBooking: [],
        rentalDetails: undefined,
        rentalId: null,
        carId: null,
        price_temp: undefined,
        cash_option: false,
      };
    default:
      return state;
  }
}
