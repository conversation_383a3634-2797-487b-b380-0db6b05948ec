import {
  ABOUT_RENT_PRICE,
  CARS_LIST,
  CARS_LIST_CURRENT_PAGE,
  CAR_DATA,
  CAR_SELECTED_BRANCH,
  CAR_SELECTED_INURANCE,
  PAYMENT_TYPE,
  PAYMENT_METHOD,
  DE<PERSON>IVERY_TYPE,
  HANDOVER_<PERSON>ANCH,
  <PERSON><PERSON>IVERY,
  DELIVERY_RETURN,
  SELECTED_CAR_EXTRA_SERVICES,
  CLEAR_CAR,
  INSURANCE_TYPE,
  SHIFTS_TODAY,
  TAMARA_AVAILABLE,
  TAMARA_USED,
  WORKING_HOURS,
  IS_WORKING_HOURS_OPEN,
  AVAILABLE_PAYMENT_METHODS,
  CLEAR_DATA,
  RENT_TO_OWN_PLANS,
} from "./action";

export default function CarsReducer(
  state = {
    carsList: [],
    carsListCurrentPage: 1,
    selectedCarBranch: {},
    paymentType: "Online",
    about_rent_price: undefined,
    insuranceId: undefined,
    insuranceType: undefined,
    deliveryType: "no_delivery",
    extra_services: [],
    tamara_used: false,
    paymentMethod: undefined,
    availablePaymentMethods: undefined,
    shifts_today: undefined,
    tamara_availability: false,
    is_working_hours_modal_open: false,
    working_hours: undefined,
  },
  action
) {
  switch (action.type) {
    case CARS_LIST:
      return { ...state, carsList: action.payload };
    case CARS_LIST_CURRENT_PAGE:
      return { ...state, carsListCurrentPage: action.payload };
    case CAR_SELECTED_BRANCH:
      return { ...state, selectedCarBranch: action.payload };
    case CAR_DATA:
      return { ...state, car_data: action.payload };
    case ABOUT_RENT_PRICE:
      return { ...state, about_rent_price: action.payload };
    case CAR_SELECTED_INURANCE:
      return { ...state, insuranceId: action.payload };
    case INSURANCE_TYPE:
      return { ...state, insuranceType: action.payload };
    case PAYMENT_TYPE:
      return { ...state, paymentType: action.payload };
    case PAYMENT_METHOD:
      return { ...state, paymentMethod: action.payload };
    case AVAILABLE_PAYMENT_METHODS:
      return { ...state, availablePaymentMethods: action.payload };
    case DELIVERY_TYPE:
      return { ...state, deliveryType: action.payload };
    case HANDOVER_BRANCH:
      return {
        ...state,
        handover_in_another_branch: action.payload,
      };
    case DELIVERY:
      return {
        ...state,
        delivery: action.payload,
      };
    case DELIVERY_RETURN:
      return {
        ...state,
        delivery_return: action.payload,
      };
    case SELECTED_CAR_EXTRA_SERVICES:
      return {
        ...state,
        extra_services: action.payload,
      };
    case CLEAR_CAR:
      return {
        carsList: state.carsList,
        carsListCurrentPage: state.carsListCurrentPage,
        paymentType: "",
        extra_services: [],
        deliveryType: "no_delivery",
      };
    case SHIFTS_TODAY:
      return {
        ...state,
        shifts_today: action.payload,
      };
    case WORKING_HOURS:
      return {
        ...state,
        working_hours: action.payload,
      };
    case IS_WORKING_HOURS_OPEN:
      return {
        ...state,
        is_working_hours_modal_open: action.payload,
      };
    case TAMARA_AVAILABLE:
      return {
        ...state,
        tamara_availability: action.payload,
      };
    case TAMARA_USED:
      return {
        ...state,
        tamara_used: action.payload,
      };
    case RENT_TO_OWN_PLANS:
      return {
        ...state,
        rent_to_own_plan: action.payload,
      };
    case CLEAR_DATA:
      return {
        selectedCarBranch: state?.selectedCarBranch,
        carsList: state.carsList,
        carsListCurrentPage: state.carsListCurrentPage,
        paymentType: "Online",
        car_data: undefined,
        about_rent_price: undefined,
        insuranceId: undefined,
        insuranceType: undefined,
        deliveryType: "no_delivery",
        extra_services: [],
        tamara_used: false,
        paymentMethod: undefined,
        availablePaymentMethods: undefined,
        shifts_today: undefined,
        tamara_availability: false,
        is_working_hours_modal_open: false,
        working_hours: undefined,
        handover_in_another_branch: undefined,
        rent_to_own_plan: undefined,
      };

    default:
      return state;
  }
}
