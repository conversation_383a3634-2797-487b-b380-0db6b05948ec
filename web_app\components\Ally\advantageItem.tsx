/* eslint-disable @next/next/no-img-element */
import { RootStateOrAny, useSelector } from "react-redux";
import styled from "styled-components";

const Div = styled.div`
  text-align: initial;
  @media (min-width: 961px) {
    max-width: 90%;
    margin: 0 auto;
    margin-bottom: 50px;
  }
  @media (max-width: 960px) {
    margin: 10px 25px 20px 25px;
  }
  &,
  * {
    font-weight: 600;
  }
  img {
    max-height: 22px;
    max-width: 24px;
    object-fit: cover;
  }
  span {
    color: var(--color-11);
    font-weight: 400;
  }
  h6 {
    margin: 10px 0 !important;
    font-size: 1.3rem;
  }
  p {
    font-weight: 400;
    font-size: 1.2rem;
  }
`;

export default function Item({ data }) {
  const language = useSelector((state: RootStateOrAny) => state.language);

  return (
    <Div language={language}>
      <span>{data.count}</span>
      <h6>{data.title}</h6>
      <p>{data.description}</p>
    </Div>
  );
}
