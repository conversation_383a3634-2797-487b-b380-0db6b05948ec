/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import Layout from "components/shared/layout";
import styled from "styled-components";
import Hero from "./hero";
import SearchContainer from "./carSearchContainer";
import { Box, Container } from "@material-ui/core";
import WhyChooseCarwah from "./whyChooseCarwah";
import ContactUs from "./contactUs";
import DownloadApp from "components/shared/downloadApp";
import HeroSEO from "./heroSEO";
import EnterCoupons from "./enterCoupons";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import CarTypes from "./carTypes";
import ExtraServicesCarousel from "./extraServicesCarousel";
import SpecialServices from "./specialServices";
import {
  setCarReturnInAnotherBranch,
  setExtraServices,
  setIsBarq,
  setIsInstantConfirmation,
  setIsUnlimitedKM,
} from "store/search/action";
import RentToOwnSection from "./RentToOwn";
import { useTranslation } from "react-i18next";

const HomeContainer = styled.div`
  #search-container,
  #why-choose-carwah {
    background-color: var(--color-6);
  }
  #search-container {
    padding-top: 50px;
    margin-bottom: -10px;
    > div {
      /* z-index: 9999; */
      position: relative;
      margin-top: ${(props) => (props.home ? "-100px" : "0px")};
      padding-bottom: ${(props) => (props.home ? "50px" : "0px")};
      #inputs-wrap > div:first-child {
        border-radius: 20px !important;
        padding: 20px 30px 30px 30px !important;
        .toggle-select span {
          display: inline-block;
          padding: 10px 20px;
          border-radius: 20px;
          font-size: 14px;

          margin-bottom: 10px;
          padding: ${(props) =>
            props.lang === "ar" ? "2px 20px 10px 20px" : "5px 25px 10px 25px "};
          background-color: #f2f2f2;
          color: #888888;
          &.is-selected {
            background-color: var(--color-2);
            color: #fff;
            font-weight: 600;
            &::after {
              display: none;
            }
          }
          &:hover {
            background-color: var(--color-2);
            color: #fff;
            font-weight: 600;
          }
          transition: all 0.3s ease;
        }
      }
    }
    .MuiCollapse-root {
      margin-top: ${(props) =>
        !props.isMobile && props.home ? "-25px" : "0px"};
    }
  }
  section:first-child {
    .swiper-button-next,
    .swiper-button-prev {
      background-color: transparent !important;
    }
  }
`;

export default function Home({ cityData, cities }) {
  const dispatch = useDispatch();
  const authentication = useSelector(
    (state: RootStateOrAny) => state.authentication
  );
  const { token } = authentication?.login_data || {};
  const { i18n } = useTranslation();

  useEffect(() => {
    dispatch(setIsUnlimitedKM(undefined));
    dispatch(setCarReturnInAnotherBranch(undefined));
    dispatch(setExtraServices(undefined));
    dispatch(setIsBarq(undefined));
    dispatch(setIsInstantConfirmation(undefined));
  }, []);

  return (
    <>
      <Layout>
        <HomeContainer lang={i18n.language} home={true}>
          {cities && cityData ? (
            <section>
              <HeroSEO cityData={cityData} />
            </section>
          ) : (
            <section id="slider-container">
              <Hero />
            </section>
          )}
          <section id="search-container">
            <Container>
              <SearchContainer
                cityData={cityData || null}
                cities={cities || null}
              />
            </Container>
          </section>
          <section id="renttoown-container">
            <Container>
              <RentToOwnSection />
            </Container>
          </section>
          <section>
            <Container>
              <SpecialServices />
              <Box width="100%" height="1px" bgcolor="rgba(0,0,0,0.05)" />
            </Container>
          </section>
          <section>
            <Container>
              <CarTypes />
              <Box width="100%" height="1px" bgcolor="rgba(0,0,0,0.05)" />
            </Container>
          </section>
          <section>
            <Container>
              <ExtraServicesCarousel />
              <Box width="100%" height="1px" bgcolor="rgba(0,0,0,0.05)" />
            </Container>
          </section>

          {token ? (
            <section id="coupons">
              <EnterCoupons />
            </section>
          ) : null}
        </HomeContainer>
      </Layout>
    </>
  );
}
