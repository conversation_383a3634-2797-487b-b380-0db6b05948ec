import { useMutation } from "@apollo/client";
import { RequestPasscode_Mutation } from "gql/mutations/authentication";
import styled from "styled-components";
import { useState } from "react";
import RequestLoader from "./requestLoader";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/bootstrap.css";
import ar from "react-phone-input-2/lang/ar.json";
import Whatsapp from "public/assets/icons/whatsapp";
import Sms from "public/assets/icons/sms";
import { v4 as uuidv4 } from "uuid";

const Button = styled.button`
  background: var(--color-3);
  border-radius: var(--radius-3);
  padding: 10px 50px;
  height: 60px;
`;

const InputContainer = styled.div`
  .form-control {
    width: 100%;
  }
  .select,
  input {
    border: solid 2px var(--color-7);
    border-radius: var(--radius-3);
    padding: 10px 18px;
  }
  .select {
    padding-right: 20px;
    & > div {
      padding: 10px;
    }
  }
  input {
    height: 60px;
    @media (min-width: 769px) {
      min-width: 250px;
    }
  }
  &.showInputError {
    input {
      outline: none;
      border-color: var(--color-9);
    }
  }
  .icons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    .svg-wrap {
      text-align: center;
      margin-top: 0 !important;
      background-color: var(--color-6);
      color: ${(props) => (props.isRentToOwn ? "#000000" : "var(--color-8)")};
      width: 50%;
      border-radius: var(--radius-3);
      padding: 18px 5px !important;
      /* font-size: 0.88rem; */
      @media (max-width: 768px) {
        font-size: 0.8rem;
      }
      svg {
        width: 20px;
      }
      &.active {
        color: #fff;
        background: #f2f8fa;
        span {
          color: #7ab3c5;
        }
      }
      &:last-child.active {
        path {
          stroke: #7ab3c5 !important;
        }
      }
      &:first-child.active {
        path {
          fill: #7ab3c5 !important;
        }
      }
    }
    position: relative;
    .svg-wrap {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 5px;
      cursor: pointer;
      path {
        stroke: #959595 !important;
        /* stroke: #7ab3c5 !important; */
        stroke-width: 0px;
      }
      &:first-child {
        path {
          fill: #959595;
        }
      }
      &:last-child {
        path {
          stroke-width: 2px;
        }
      }
    }
  }
`;

export default function Form({ t, i18n, setShowForm, setMobileData }) {
  const [requestPasscode, { data, loading: requestPassCodeLoading, error }] =
    useMutation(RequestPasscode_Mutation, { errorPolicy: "all" });
  const [showInputError, setShowInputError] = useState(false);
  const [phone, setPhone] = useState("966");
  const [viaWhatsapp, setViaWhatsapp] = useState(false);

  const randomId = uuidv4();

  async function Mutation() {
    const res = await requestPasscode({
      variables: {
        input: {
          mobile: phone,
          deviceToken: randomId,
          viaWhatsapp,
        },
      },
    });
    if (!res?.errors) {
      setMobileData({
        number: phone,
        code: res.data.requestPasscode.status.replace("success", "").trim(),
        viaWhatsapp,
        deviceToken: randomId,
      });
      setShowForm(false);
    } else {
      setShowInputError(true);
    }
  }
  function submitSigninFormHandler(e) {
    e.preventDefault();
    if (!showInputError && phone) {
      Mutation();
    } else {
      return;
    }
  }

  return (
    <>
      <RequestLoader loading={requestPassCodeLoading} />
      <h6 className="font-27px mb-1 text-center">{t("Sign In / Sign Up")}</h6>
      <span className="color-12 mb-4 d-inline-block text-center w-100">
        {t("Log in now using one of the following options")}
      </span>
      <form
        id="signin-form"
        className="d-flex flex-column gap-20px"
        onSubmit={(e) => {
          submitSigninFormHandler(e);
        }}
      >
        <InputContainer
          className={`phone-wrapper ${showInputError ? "showInputError" : ""}`}
          language={i18n.language}
        >
          <div title={t("Enter Phone No.")}>
            <PhoneInput
              localization={i18n.language === "ar" ? ar : undefined}
              searchPlaceholder={t("Search")}
              enableSearch={true}
              value={phone}
              masks={{ sa: "... ... ..." }}
              onChange={(e) => {
                setPhone(e);
                if (e.startsWith("966")) {
                  if (
                    new RegExp(
                      /^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/
                    ).test(e) &&
                    e.length > 6
                  ) {
                    setShowInputError(false);
                  } else {
                    setShowInputError(true);
                  }
                  setViaWhatsapp(false);
                } else {
                  setViaWhatsapp(true);
                  setShowInputError(false);
                }
              }}
              countryCodeEditable={false}
              inputProps={{
                name: "mobile",
                required: true,
                autoFocus: true,
              }}
            />
            {showInputError ? (
              <p className="color-9" style={{ marginTop: "10px" }}>
                {t("Invalid mobile format")}
              </p>
            ) : (
              <p style={{ minHeight: "14px" }} />
            )}
          </div>
          <div
            className="flex justify-content-between icons"
            style={{ flexWrap: "nowrap" }}
          >
            <div
              className={`svg-wrap ${viaWhatsapp ? "active" : ""}`}
              onClick={() => setViaWhatsapp(true)}
            >
              <Whatsapp />
              <span>{t("Verify via WhatsApp" as string)}</span>
            </div>
            <div
              className={`svg-wrap ${!viaWhatsapp ? "active" : ""}`}
              onClick={() => setViaWhatsapp(false)}
            >
              <Sms />
              <span>{t("Verify via SMS" as string)}</span>
            </div>
          </div>

          <Button
            type="submit"
            className="font-17px color-4 border-0 medium mb-1 mt-3 w-100"
            disabled={requestPassCodeLoading || !phone || showInputError}
          >
            {t("Login")}
          </Button>
        </InputContainer>
      </form>
    </>
  );
}
