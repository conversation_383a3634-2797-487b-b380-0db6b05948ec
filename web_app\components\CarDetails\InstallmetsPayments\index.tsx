import { Button } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import InstallmentDetailsModal from "./InstallmetDetailsModal";
import useLogic from "../Invoice/useLogic";
import { CSSProperties, useState } from "react";
import { useRouter } from "next/router";
import useRentalType from "../heplers/rentalType";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  margin-top: 24px;
  .seperate-line {
    height: 45px;
    width: 1px;
    background: var(--color-11);
  }
  div.divider:last-child {
    border: none !important;
  }
`;

export function InstallmetsPaymentsPrices({
  title,
  price,
  t,
  ...props
}: {
  title: string;
  price: number;
  t: any;
  style?: CSSProperties;
}) {
  const { i18n } = useTranslation();
  return (
    <div className="d-flex align-items-start flex-column" style={props.style}>
      <div className="text-align-localized">{title}</div>
      <div style={{ fontWeight: 600, fontSize: "30px" }}>
        <div className="d-flex gap-5px">
          <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
            <RiyalSymbol />
          </span>
          <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>{price}</span>
        </div>
      </div>
    </div>
  );
}
function InstallmentsPayments({
  setExtensionId,
  extensionId,
  refetchRentalDetails,
  setCalendarTooltipOpen,
  isThereInstallmentToPay,
}) {
  const { t } = useTranslation();
  const { toalDueAmoutOutside } = useLogic();
  const [isInstallmentDetailsModalOpen, setIsInstallmentDetailsModalOpen] =
    useState(false);
  const { isRentToOwn } = useRentalType();

  return (
    <Div>
      <div>
        <div className="d-flex justify-content-between align-items-center py-3 px-4">
          <h6 className="bold">{t("Installments & Payment") as string}</h6>
          {Boolean(!isRentToOwn) && (
            <Button
              className="radius-3"
              style={{
                background: "var(--color-2)",
                color: "var(--color-4)",
                fontWeight: "bold",
                padding: "5px 25px 10px 25px",
                borderRadius: "var(--radius-3)",
              }}
              onClick={() => {
                setIsInstallmentDetailsModalOpen(true);
              }}
            >
              {t("Details") as string}
            </Button>
          )}
        </div>
        {Boolean(isRentToOwn) && (
          <>
            <div className="separator" />
            <div className="d-flex justify-content-between align-items-center px-4 py-3">
              <InstallmetsPaymentsPrices
                {...{
                  t,
                  title: t("Due Amounts Currently"),
                  price: toalDueAmoutOutside,
                }}
              />
              <Button
                className="radius-3"
                style={{
                  background: "var(--color-2)",
                  color: "var(--color-4)",
                  fontWeight: "bold",
                  padding: "5px 25px 10px 25px",
                  borderRadius: "var(--radius-3)",
                }}
                onClick={() => {
                  setIsInstallmentDetailsModalOpen(true);
                }}
              >
                {t("Details") as string}
              </Button>
            </div>
          </>
        )}
      </div>
      <InstallmentDetailsModal
        {...{
          setExtensionId,
          extensionId,
          refetchRentalDetails,
          setCalendarTooltipOpen,
          isThereInstallmentToPay,
          isInstallmentDetailsModalOpen,
          setIsInstallmentDetailsModalOpen,
        }}
      />
    </Div>
  );
}

export default InstallmentsPayments;
