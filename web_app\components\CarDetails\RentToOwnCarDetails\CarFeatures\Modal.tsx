/* eslint-disable @next/next/no-img-element */
import { Button } from "@material-ui/core";
import Popup from "components/shared/popup";
import { Dispatch, SetStateAction } from "react";
import { useTranslation } from "react-i18next";

import styled from "styled-components";
import { TFeature } from ".";

const Div = styled.div`
  > div {
    display: flex;
    align-items: center;
    gap: 30px 40px;
    flex-wrap: wrap;
    justify-content: flex-start;
    img {
      width: 100%;
      max-width: 60px;
    }
    div {
      font-weight: bold;
      font-size: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
  h2 {
    font-size: 30px;
    font-weight: bold;
    flex-basis: 100%;
    margin-bottom: 30px !important;
  }
  button {
    background-color: var(--color-3);
    color: white;
    font-size: 20px;
    font-weight: bold;
    padding: 10px 0 20px 0;
    width: 85%;
    margin: 0 auto;
    margin-top: 50px !important;
    border-radius: var(--radius-2);
  }
`;

function FeaturesModal({
  isOpen,
  setIsOpen,
  features,
}: {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  features: TFeature[];
}) {
  const { t } = useTranslation();

  return (
    <Popup {...{ isOpen, setIsOpen }}>
      <Div>
        <h2>{t("Car Features") as string}</h2>
        <div>
          {features?.length
            ? features.map(({ id, icon, name, isActive }) => {
                if (isActive) {
                  return (
                    <div key={`title-${id}`}>
                      <img src={icon} alt={icon} />
                      <div>{name}</div>
                    </div>
                  );
                }
              })
            : null}
        </div>
        <div>
          <Button
            onClick={() => {
              setIsOpen(false);
            }}
          >
            {t("Done.well") as string}
          </Button>
        </div>
      </Div>
    </Popup>
  );
}

export default FeaturesModal;
