import React from "react";
import { useTranslation } from "react-i18next";

interface RiyalSymbolProps {
  className?: string;
  style?: React.CSSProperties;
}

const RiyalSymbol: React.FC<RiyalSymbolProps> = ({
  className = "",
  style = {},
}) => {
  const { t } = useTranslation();

  return (
    <span
      className={`riyal-symbol ${className}`}
      style={{
        fontFamily: "Helvetica-Riyal",
        ...style,
        transform: "translateY(-3px)",
      }}
    >
      {t("﷼") as string}
    </span>
  );
};

export default RiyalSymbol;
