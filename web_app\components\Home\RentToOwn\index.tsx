/* eslint-disable @next/next/no-img-element */
import styled from "styled-components";
import RentToOwnTextLogo from "./RentToOwnTextLogo";
import { useTranslation } from "react-i18next";
import { Button } from "@material-ui/core";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import { setDateTimeAction } from "store/search/action";
import { default_date_time } from "store/search/reducer";
import useFirebase from "useFirebase";
const Div = styled.div`
  display: ${(props) => (props.show ? "flex" : "none")};
  background-color: #2a292f;
  border-radius: 50px;
  padding: 24px 50px;
  direction: ${(props) => (props.lang === "ar" ? "rtl" : "ltr")};
  justify-content: space-between;
  margin: 50px 0;
  align-items: center;
  position: relative;
  .wrapper {
    @media (min-width: 769px) {
      display: flex;
      justify-content: space-evenly;
      width: 100%;
    }
  }
  @media (max-width: 768px) {
    flex-direction: column;
    padding: 50px 24px !important;
  }
`;
const Descrirption = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 15px;
  img {
    margin-top: 5px;
  }
  p {
    font-size: 1.3rem;
    color: white;
    /* width: 50%; */
  }
  @media (max-width: 768px) {
    p {
      /* width: 100% !important; */
    }
    img {
      margin-top: 20px !important;
    }
  }
`;
const ButtonStyled = styled(Button)`
  background-color: var(--color-2) !important;
  padding: ${(props) =>
    props.lang === "en"
      ? "1px 15px 5px 27px !important"
      : "1px 27px 5px 15px !important"};
  border-radius: 50px !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center;
  margin-top: 5px !important;
  font-size: 1rem;
  @media (max-width: 768px) {
    margin-top: 30px !important;
  }
  img {
    width: 50px;
    margin-top: 5px;
    transform: ${(props) => (props.lang === "en" ? "rotate(180deg)" : "")};
  }
  span {
    color: white;
    font-size: 18px;
  }
`;
const New = styled.div`
  flex-grow: ${(props) => (props.lang === "en" ? "0.3" : "0.1")};

  div {
    background-color: var(--color-2) !important;
    font-size: 22px;
    padding: ${(props) =>
      props.lang === "en" ? "7px 35px 7px 35px" : "5px 35px 12px 35px"};
    color: white;
    position: absolute;
    top: 0;
    border-radius: ${(props) => (props.lang === "ar" ? "0 50px" : "50px 0")};
    right: ${(props) => (props.lang === "ar" ? "0px" : "")};
    left: ${(props) => (props.lang === "en" ? "0px" : "")};
    font-family: "ArabicBold" !important;
    font-weight: 500;
  }
`;

function RentToOwnSection() {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch();
  const { remoteConfigValues } = useFirebase();
  const { is_rent_to_own_feature_enabled } = remoteConfigValues || {
    is_rent_to_own_feature_enabled: { _value: "false" },
  };

  return (
    <Div
      lang={i18n.language}
      show={is_rent_to_own_feature_enabled?._value == "true"}
    >
      <New lang={i18n.language}>
        <div style={{ textTransform: "capitalize" }}>{t("new") as string}</div>
      </New>
      <div className="wrapper">
        <RentToOwnTextLogo />
        <Descrirption className="descrirption">
          <img src="/assets/icons/checkedCircle.svg" alt="checkedCircle" />
          <p>{t("Immediate Delivery without complications") as string}</p>
        </Descrirption>
      </div>
      <div>
        <ButtonStyled
          lang={i18n.language}
          onClick={() => {
            dispatch(setDateTimeAction(default_date_time));
            router.push("rent-to-own");
          }}
        >
          {t("Reveal") as string}
          <img src="/assets/icons/arrow-left.svg" alt="arrow" />
        </ButtonStyled>
      </div>
    </Div>
  );
}

export default RentToOwnSection;
