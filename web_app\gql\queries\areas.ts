import { gql } from "@apollo/client";

const Areas_Query = gql`
  query Areas($ids: [ID!]) {
    areas(ids: $ids) {
      arName
      centerLat
      centerLng
      enName
      id
      isAirport
      name
      order
      timezone
      airports {
        arName
        centerLat
        centerLng
        enName
        id
        name
        area {
          name
        }
      }
    }
  }
`;

const GET_AREA_QUERY = gql`
  query Area($lat: Float!, $lng: Float!) {
    area(lat: $lat, lng: $lng) {
      id
      name
    }
  }
`;

export { Areas_Query, GET_AREA_QUERY };
