import { Dialog } from "@material-ui/core";
import styled from "styled-components";
import CloseIcon from "@material-ui/icons/Close";
import { useTranslation } from "react-i18next";
import { CSSProperties, Dispatch, ReactNode, SetStateAction } from "react";

const DialogTag = styled(Dialog)`
  .MuiPaper-root {
    background-color: ${(props) => props.backgroundColor};
  }
  .MuiDialog-container > div {
    border-radius: var(--radius-2);
    min-width: 35vw;
    width: ${(props) => (props.withWidth ? "100%" : "")};
    padding: 35px;
    position: relative;

    @media (max-width: 768px) {
      padding: 18px !important;
      min-width: 95vw !important;
    }

    .close-icon {
      cursor: pointer;
      position: absolute;
      top: 5px;
      right: ${(props) => (props.language === "en" ? "5px" : null)};
      left: ${(props) => (props.language === "ar" ? "5px" : null)};
    }
  }
`;

// classes delivery-popup-header, delivery-popup-title, and delivery-popup-footer are used in the parent wrapper component and styled hear because outside styles are not applied to the popup

export default function Popup({
  isOpen,
  setIsOpen,
  children,
  title = undefined,
  fullWidth,
  maxWidth,
  onClose,
  backgroundColor,
  ...props
}: {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  children: ReactNode;
  title?: string;
  fullWidth?: boolean;
  maxWidth?: string | number;
  backgroundColor?: string;
  onClose?: () => void;
  style?: CSSProperties;
}) {
  const { i18n } = useTranslation();
  return (
    <DialogTag
      className="popup"
      open={isOpen || false}
      language={i18n.language}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      {...props.style}
      onClose={
        onClose
          ? () => {
              onClose();
              setIsOpen(false);
            }
          : () => setIsOpen(false)
      }
      backgroundColor={backgroundColor}
      {...props}
    >
      {title ? (
        <span
          style={{
            textAlign: "center",
            fontWeight: "bold",
            marginBottom: "20px",
            textTransform: "uppercase",
            fontSize: "20px",
          }}
        >
          {title}
        </span>
      ) : (
        ""
      )}
      <span
        onClick={
          onClose
            ? () => {
                onClose();
                setIsOpen(false);
              }
            : () => setIsOpen(false)
        }
        className="close-icon"
      >
        <CloseIcon />
      </span>
      {children}
    </DialogTag>
  );
}
