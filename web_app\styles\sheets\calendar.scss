.rmdp-wrapper {
  background-color: var(--color-5) !important;
  width: 400px !important;
  max-width: 100vw !important;
  padding: 5px !important;
  @media (min-width: 769px) {
    border-right: solid 2px #3e3c4d !important;
  }
  @media (max-width: 767px) {
    padding: 0 !important;
    width: calc(100vw - 95px) !important;
    margin: 0 !important;
  }
  margin: 0 15px 0 0 !important;
}
.rmdp-day span,
.rmdp-week-day,
.rmdp-week-day,
.rmdp-header-values {
  color: var(--color-4) !important;
}
.rmdp-arrow-container:hover {
  background: none !important;
  box-shadow: none !important;
}
.rmdp-arrow {
  border: solid var(--color-4) !important;
  border-width: 0 2px 2px 0 !important;
}
.rmdp-shadow {
  box-shadow: none !important;
}
.rmdp-day-picker {
  > div {
    width: 100% !important;
  }
}
.rmdp-calendar {
  width: 100% !important;
}
.rmdp-panel.right {
  display: none !important;
}
.rmdp-border-right {
  border: none !important;
}
#time-pickers {

  input {
    width: 100%;
    min-width: 30px;
    border: none !important;
    padding: 7px 0px !important;
    margin: 0 5px 0 5px !important;
    text-align: center !important;
    background-color: transparent !important;
    color: var(--color-4) !important;
    border-radius: var(--radius-1) !important;
    font-size: 18px !important;
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none !important;
      margin: 0 !important;
    }

    &[type="number"] {
      -moz-appearance: textfield !important; /* Firefox */
    }
  }
  .rmdp-wrapper {
    background-color: var(--color-5) !important;
  }
  h4 {
    font-size: 16px !important;
    color: var(--color-4) !important;
    margin-bottom: 0px !important;
    margin-top: 15px;
  }
  svg {
    font-size: 18px !important;
  }
}
.rmdp-day:not(.rmdp-disabled):not(.rmdp-day-hidden) span:hover {
  background-color: transparent !important;
}
.rmdp-header-values span {
  font-size: 17px !important;
}
.rmdp-week,
.rmdp-week-day {
  font-size: 15px !important;
  border-radius: var(--radius-1) !important;
  margin: 5px 0px !important;
}
.rmdp-week{
  gap: 5px;
}
.rmdp-day {
  background-color: var(--color-1) !important;
  width: 40px !important;
  border-radius: var(--radius-1) !important;
}
.rmdp-day.rmdp-today {
  border: solid 2px var(--color-2) !important;
  border-radius: var(--radius-1) !important;
  span {
    background: none !important;
  }
}

.rmdp-range.start,
.rmdp-range,
.rmdp-selected {
  border-radius: var(--radius-1) !important;
  background: var(--color-2) !important;
  box-shadow: none !important;
}
.rmdp-ym .rmdp-day span {
  border-radius: var(--radius-1) !important;
}
.rmdp-time-picker div {
  flex-direction: row-reverse !important;
}
#apply {
  font-size: 14px !important;
  border: none !important;
  background-color: var(--color-3) !important;
  color: var(--color-4) !important;
  padding: 14px 0px !important;
  font-weight: 500 !important;
  border-radius: 10px !important;
  border: none !important;
  box-shadow: none !important;
  // transform: translateY(48px);
  &:focus {
    outline: none !important;
  }
}
#cancel {
  font-size: 14px !important;
  border: none !important;
  // background-color: var(--color-3) !important;
  // color: var(--color-4) !important;
  padding: 14px 0px !important;
  font-weight: 500 !important;
  border-radius: 10px !important;
  border: none !important;
  box-shadow: none !important;
  // transform: translateY(48px);
  &:focus {
    outline: none !important;
  }
}
.rmdp-month-picker,
.rmdp-year-picker {
  background: #36353b !important;
}
.rmdp-day.rmdp-selected span:not(.highlight) {
  background-color: transparent !important;
  box-shadow: none !important;
}
.rmdp-disabled{
  background-color: gray !important;
}
