import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { MockedProvider } from "@apollo/client/testing";
import { IntlProvider } from "react-intl";
import { Provider } from "react-redux";
import { createStore } from "redux";
import PaymentFlow from "../PaymentFlow";
import { CHECKOUT_ID_QUERY, PAYMENT_STATUS_QUERY } from "../graphql";

// Mock translations
const messages = {
  "Select Payment Method": "Select Payment Method",
  "Complete Payment": "Complete Payment",
  "Payment completed successfully": "Payment completed successfully",
  "Processing payment...": "Processing payment...",
  "button.cancel": "Cancel",
  "Pay Now": "Pay Now",
  Mada: "Mada",
  "Credit Card": "Credit Card",
  Tamara: "Tamara",
};

// Mock Redux store
const mockStore = createStore(() => ({
  authUser: {
    user: {
      agency_id: "123",
    },
  },
}));

// Mock Firebase Remote Config
jest.mock("../../hooks/useFirebase", () => ({
  __esModule: true,
  default: () => ({
    remoteConfigValues: {
      skip_integrity: { _value: "false" },
    },
    isLoaded: true,
  }),
}));

// Mock GraphQL responses
const mocks = [
  {
    request: {
      query: CHECKOUT_ID_QUERY,
      variables: {
        rentalId: "1",
        paymentBrand: "MADA",
        withWallet: false,
      },
    },
    result: {
      data: {
        getCheckoutId: {
          checkoutId: "test-checkout-id",
          integrity: "test-integrity",
        },
      },
    },
  },
  {
    request: {
      query: PAYMENT_STATUS_QUERY,
      variables: {
        checkoutId: "test-checkout-id",
        rentalId: "1",
      },
    },
    result: {
      data: {
        getPaymentStatus: {
          status: "paid",
          rental: {
            isIntegratedRental: false,
            rentalIntegrationStatus: null,
          },
          errors: [],
        },
      },
    },
  },
];

const defaultProps = {
  isOpen: true,
  onClose: jest.fn(),
  rentalId: "1",
  totalAmount: 100,
  walletBalance: 0,
  isAgencyDeactivated: false,
  onPaymentSuccess: jest.fn(),
  onPaymentError: jest.fn(),
};

const renderPaymentFlow = (props = {}) => {
  return render(
    <Provider store={mockStore}>
      <IntlProvider locale="en" messages={messages}>
        <MockedProvider mocks={mocks} addTypename={false}>
          <PaymentFlow {...defaultProps} {...props} />
        </MockedProvider>
      </IntlProvider>
    </Provider>,
  );
};

describe("PaymentFlow Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders payment method selection initially", () => {
    renderPaymentFlow();
    expect(screen.getByText("Select Payment Method")).toBeInTheDocument();
  });

  test("shows payment methods when modal is open", () => {
    renderPaymentFlow();
    expect(screen.getByText("Mada")).toBeInTheDocument();
    expect(screen.getByText("Credit Card")).toBeInTheDocument();
    expect(screen.getByText("Tamara")).toBeInTheDocument();
  });

  test("does not render when modal is closed", () => {
    renderPaymentFlow({ isOpen: false });
    expect(screen.queryByText("Select Payment Method")).not.toBeInTheDocument();
  });

  test("prevents payment when agency is deactivated", () => {
    renderPaymentFlow({ isAgencyDeactivated: true });

    // Select a payment method
    fireEvent.click(screen.getByText("Mada"));

    // Try to proceed - should be disabled or show error
    const proceedButton = screen.getByText("Pay Now");
    expect(proceedButton).toBeDisabled();
  });

  test("handles payment method selection", () => {
    renderPaymentFlow();

    // Select Mada payment method
    fireEvent.click(screen.getByText("Mada"));

    // Should show selected method
    expect(screen.getByText("Pay Now")).toBeInTheDocument();
  });

  test("handles wallet payment option", () => {
    renderPaymentFlow({ walletBalance: 50 });

    // Should show wallet option when balance is available
    // This would depend on the actual implementation
    // Add specific wallet-related tests here
  });

  test("calls onClose when cancel button is clicked", () => {
    const onClose = jest.fn();
    renderPaymentFlow({ onClose });

    fireEvent.click(screen.getByText("Cancel"));
    expect(onClose).toHaveBeenCalled();
  });

  test("handles extension payments", () => {
    renderPaymentFlow({
      extensionId: "ext-123",
      rentalId: "1",
    });

    expect(screen.getByText("Select Payment Method")).toBeInTheDocument();
    // Extension-specific tests would go here
  });

  test("handles installment payments", () => {
    renderPaymentFlow({
      isInstallment: true,
      rentalId: "1",
    });

    expect(screen.getByText("Select Payment Method")).toBeInTheDocument();
    // Installment-specific tests would go here
  });

  test("shows loading state during payment processing", async () => {
    renderPaymentFlow();

    // Select payment method and proceed
    fireEvent.click(screen.getByText("Mada"));
    fireEvent.click(screen.getByText("Pay Now"));

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText("Processing payment...")).toBeInTheDocument();
    });
  });

  test("handles payment success", async () => {
    const onPaymentSuccess = jest.fn();
    renderPaymentFlow({ onPaymentSuccess });

    // Simulate successful payment flow
    fireEvent.click(screen.getByText("Mada"));
    fireEvent.click(screen.getByText("Pay Now"));

    // Wait for success callback
    await waitFor(() => {
      expect(onPaymentSuccess).toHaveBeenCalled();
    });
  });

  test("handles payment failure", async () => {
    const onPaymentError = jest.fn();

    // Mock failed payment response
    const failedMocks = [
      {
        request: {
          query: PAYMENT_STATUS_QUERY,
          variables: {
            checkoutId: "test-checkout-id",
            rentalId: "1",
          },
        },
        result: {
          data: {
            getPaymentStatus: {
              status: "failed",
              rental: null,
              errors: ["Payment failed"],
            },
          },
        },
      },
    ];

    render(
      <Provider store={mockStore}>
        <IntlProvider locale="en" messages={messages}>
          <MockedProvider mocks={failedMocks} addTypename={false}>
            <PaymentFlow {...defaultProps} onPaymentError={onPaymentError} />
          </MockedProvider>
        </IntlProvider>
      </Provider>,
    );

    // Simulate failed payment flow
    fireEvent.click(screen.getByText("Mada"));
    fireEvent.click(screen.getByText("Pay Now"));

    // Wait for error callback
    await waitFor(() => {
      expect(onPaymentError).toHaveBeenCalled();
    });
  });

  test("cleans up resources when component unmounts", () => {
    const { unmount } = renderPaymentFlow();

    // Add script to DOM to test cleanup
    const script = document.createElement("script");
    script.id = "payment-widget";
    document.body.appendChild(script);

    unmount();

    // Script should be cleaned up
    expect(document.getElementById("payment-widget")).toBeNull();
  });

  test("respects skip_integrity from Firebase Remote Config", () => {
    // This test would verify that the skip_integrity flag is properly used
    // In a real test environment, you would mock the Firebase hook
    // and verify that script integrity attributes are set correctly
    renderPaymentFlow();

    expect(screen.getByText("Select Payment Method")).toBeInTheDocument();
    // Additional assertions would check script loading behavior
  });
});

// Integration tests
describe("PaymentFlow Integration Tests", () => {
  test("complete payment flow for regular rental", async () => {
    const onPaymentSuccess = jest.fn();
    renderPaymentFlow({ onPaymentSuccess });

    // Step 1: Select payment method
    fireEvent.click(screen.getByText("Mada"));

    // Step 2: Proceed to payment
    fireEvent.click(screen.getByText("Pay Now"));

    // Step 3: Wait for payment completion
    await waitFor(() => {
      expect(onPaymentSuccess).toHaveBeenCalledWith("MADA");
    });
  });

  test("handles Tamara payment flow", async () => {
    renderPaymentFlow();

    // Select Tamara
    fireEvent.click(screen.getByText("Tamara"));
    fireEvent.click(screen.getByText("Pay Now"));

    // Should show Tamara widget or redirect
    await waitFor(() => {
      // Add Tamara-specific assertions
      expect(screen.getByText("Complete Payment")).toBeInTheDocument();
    });
  });
});
