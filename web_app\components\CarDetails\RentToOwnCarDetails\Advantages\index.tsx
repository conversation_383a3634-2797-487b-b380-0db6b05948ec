/* eslint-disable @next/next/no-img-element */
import { Button } from "@material-ui/core";
import RentToOwnInfoModal from "components/CarSearch/rentToOwn/RentToOwnInfoModal";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  background-color: #2a292f;
  border-radius: var(--radius-2);
  position: relative;

  div {
    padding: 15px 15px 15px 15px;
    img {
      width: 50%;
    }
  }
  .badge {
    background-color: var(--color-2);
    color: #fff;
    font-size: 20px;
    font-weight: bold;
    border-radius: ${(props) =>
      props.lang === "en" ? "var(--radius-2) 0" : "0 var(--radius-2)"};
    padding: 10px 27px 20px 27px;
    padding-bottom: ${(props) => (props.lang === "en" ? "15px" : "")};
    position: absolute;
    right: ${(props) => (props.lang === "ar" ? "0" : "")};
    left: ${(props) => (props.lang === "en" ? "0" : "")};
  }
`;

const AdvantagesWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;

  .advantage {
    border-radius: var(--radius-3);
    background-color: #343339;
    position: relative;

    img {
      position: absolute;
      top: -5px;
      right: ${(props) => (props.lang === "ar" ? "-5px" : "")};
      left: ${(props) => (props.lang === "en" ? "-5px" : "")};
      width: 30px !important;
    }
    h4 {
      font-size: 15px;
      text-align: center;

      span:first-child {
        color: var(--color-2);
        font-weight: bold;
      }
      span:last-child {
        color: #aeadb0;
      }
    }
  }
  button {
    background-color: var(--color-2);
    border-radius: 50px;
    color: #ffffff;
    font-size: 20px;
    width: 100% !important;
    padding-bottom: 10px;
    font-weight: bold;
    grid-column: 1/-1;
    transform: translateY(24px) !important;
    border: 2px solid transparent;
    &:hover {
      border: 2px solid var(--color-2);
    }
  }
`;

function RentToOwnAdvantages({
  advantages,
}: {
  advantages: { text: string }[];
}) {
  const { t, i18n } = useTranslation();
  const [rentToOwnInfoModalOpen, setRentToOwnInfoModalOpen] = useState(false);

  return (
    <Div lang={i18n.language}>
      <div className="badge">{t("RentToOwn.Advantages") as string}</div>
      <div className="d-flex justify-content-end">
        <img src="/assets/images/rentToOwn.svg" alt="rentToOwn" />
      </div>
      <AdvantagesWrapper lang={i18n.language}>
        {advantages?.length
          ? advantages?.map(({ text }, index) => {
              return (
                <div key={`advantage-${index}`} className="advantage">
                  <img
                    src="/assets/icons/rentToOwn/checkOrange.svg"
                    alt="check"
                  />
                  <h4>
                    <span>{text.split(" ").slice(0, 2).join(" ")}</span>
                    <br />
                    <span>{text.split(" ").slice(2).join(" ")}</span>
                  </h4>
                </div>
              );
            })
          : null}
        <Button
          onClick={() => {
            setRentToOwnInfoModalOpen(true);
          }}
        >
          {t("Learn More") as string}
        </Button>
        <div></div>
      </AdvantagesWrapper>
      <RentToOwnInfoModal
        {...{ rentToOwnInfoModalOpen, setRentToOwnInfoModalOpen }}
      />
    </Div>
  );
}

export default RentToOwnAdvantages;
