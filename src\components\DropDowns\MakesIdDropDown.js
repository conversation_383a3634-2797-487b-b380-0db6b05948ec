/* eslint-disable prettier/prettier */
import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { persist } from "constants/constants";
import { GetMakesQueryForSelect } from "gql/queries/Cars.queries.gql";

export default function MakesIdDropDown({
  loading,
  setSelectedMake,
  selectedMake,
  error,
  valueAttribute,
  inBooking,
  onMakeChange,
  ...props
}) {
  const { locale, formatMessage } = useIntl();

  const { data: makesRes, loading: gettingMakes } = useQuery(GetMakesQueryForSelect, {
    variables: { limit: persist.unlimitedLimit, orderBy: `${locale}_name`, sortBy: "asc" },
  });

  const options =
    makesRes?.makes?.collection?.map((x) => ({
      value: x[valueAttribute || "enName"],
      label: x[`${locale}Name`],
    })) || [];

  React.useEffect(() => {
    if (!selectedMake) {
      onClear();
    }
  }, [selectedMake]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  if(inBooking){
    options.unshift({ value: "all", label: formatMessage({ id: "all" }) });

  }
  return (
    <Select
      className={`dropdown-select makes-select ${error ? "selection-error" : ""}`}
      options={options}
      ref={selectInputRef}
      loadOptions={gettingMakes || loading}
      isMulti={inBooking}
      isClearable
      defaultValue={options.find((optn) => `${optn.value}` === `${selectedMake}`)}
      value={options.find((optn) => `${optn.value}` === `${selectedMake}`)}
      placeholder={formatMessage({ id: "car.make" })}
      onChange={(selection) => {
        if (selection && selection[selection?.length - 1]?.value == "all") {
          setSelectedMake([{ value: "all", label: formatMessage({ id: "all" }) }]);
          return;
        }
        if (inBooking) {
          setSelectedMake(selection), onMakeChange(selection);
        } else {
          setSelectedMake(selection), onMakeChange(selection?.value);
        }
      }}
      noOptionsMessage={() => {
        if (gettingMakes) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
      {...props}
    />
  );
}
MakesIdDropDown.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectedMake: PropTypes.string,
  setSelectedMake: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
