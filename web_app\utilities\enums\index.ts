import moment from "moment";

const minutes = moment().minutes();
const hours_plus = moment()
  .add(2, "hours")
  .add(minutes > 30 ? 1 : 0, "hours")
  .format("HH:mm");
const two_hours_plusArr = hours_plus.split(":");
const defaultTime = `${two_hours_plusArr[0]}:${
  Number(two_hours_plusArr[1]) < 30 && Number(two_hours_plusArr[1]) > 0
    ? 30
    : "00"
}`;

let STORED_LANGUAGE;
if (process.browser) {
  STORED_LANGUAGE = JSON.parse(sessionStorage.getItem("state"))?.language;
}

const gql_limits = {
  max: 10000,
};

const patterns = {
  mobile: /^5[0-9]{8}$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
};

const token = process.browser && sessionStorage.getItem("token");

const rentalPaymentBrand = {
  APPLEPAY: "APPLEPAY",
  CREDIT_CARD: "CREDIT_CARD",
  MADA: "MADA",
};

export type TpaymentMethods =
  | "APPLEPAY"
  | "CREDIT_CARD"
  | "MADA"
  | "TAMARA"
  | "CASH";

export type TavialablePaymentMethods = "all" | "cash" | "online";

const weekDaysAr = ["أحد", "أثن", "ثلا", "أرب", "خمي", "جمع", "سبت"];
const monthsAr = [
  "يناير",
  "فبراير",
  "مارس",
  "أبريل",
  "مايو",
  "يونيو",
  "يوليو",
  "أغسطس",
  "سبتمبر",
  "أكتوبر",
  "نوفمبر",
  "ديسمبر",
];

const defaultDaysCount = 3;

const defaultPackagesDays = [
  7, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330, 360, 720,
];

const installmentStatuses = {
  upcoming: {
    name: {
      ar: "مجدولة",
      en: "Upcoming",
    },
    color: "var(--color-14)",
    value: "upcoming",
  },
  partially_refunded: {
    name: {
      ar: "مستردة جزئيًا",
      en: "Partially Refunded",
    },
    color: "var(--color-2)",
    value: "partially_refunded",
  },
  paid: {
    name: {
      ar: "مدفوعة",
      en: "Paid",
    },
    color: "green",
    value: "paid",
  },
  overdue: {
    name: {
      ar: "متأخرة",
      en: "Overdue",
    },
    color: "var(--color-9)",
    value: "overdue",
  },
  not_collectable: {
    name: {
      ar: "غير قابل للتحصيل",
      en: "Not-collectable",
    },
    color: "#d5d500",
    value: "not_collectable",
  },
  not_collected: {
    name: {
      ar: "مديونية",
      en: "not_collected",
    },
    color: "#d5d500",
    value: "not_collected",
  },
  fully_refunded: {
    name: {
      ar: "مستردة كليًا",
      en: "Fully Refunded",
    },
    color: "var(--color-2)",
    value: "fully_refunded",
  },
  due: {
    name: {
      ar: "مستحقة",
      en: "Due",
    },
    color: "var(--color-3)",
    value: "due",
  },
};

enum loyalityTypes {
  ALFURSAN = "alfursan",
}
export {
  STORED_LANGUAGE,
  gql_limits,
  patterns,
  token,
  rentalPaymentBrand,
  two_hours_plusArr,
  defaultTime,
  defaultDaysCount,
  weekDaysAr,
  monthsAr,
  defaultPackagesDays,
  installmentStatuses,
  loyalityTypes,
};

export const ENCRYPTION_SECRET_KEY = "carwah-web";

export type TdeliverType = "no_delivery" | "one_way" | "two_ways";

export const TAMARA_LIMITS = {
  min: 99,
  max: 5000,
};

export const androidStoreUrl =
  "https://play.google.com/store/apps/details?id=com.carwah.customer&hl=en_US&gl=US";

export const iosStoreUrl =
  "https://apps.apple.com/sa/app/carwah-%D9%83%D8%B1%D9%88%D8%A9/id1387161215";
