/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { ClickAwayListener, Grid } from "@material-ui/core";
import PopupComponent from "components/shared/popup";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import Map from "../shared/map";
import AreasTooltip from "components/shared/areasToolTip";
import {
  setconfirmedDeliveryLocationAction,
  setDeliveryLocationAction,
  setIsBarq,
  setSelectionIndexAction,
  setUserPickupAddressAction,
} from "store/search/action";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useQuery } from "@apollo/client";
import { GET_AREA_QUERY } from "gql/queries/areas";
import { useSnackbar } from "notistack";

// popup styling are in the styles folder so it can be applied to popup modal

export default function Popup({ isOpen, setIsOpen }) {
  //Store
  const userAddress = useSelector(
    (state: RootStateOrAny) => state.search_data.user_address
  );
  const deliveryLocation = useSelector(
    (state: RootStateOrAny) => state.search_data.delivery_location
  );

  const currentLocation = useSelector(
    (state: RootStateOrAny) => state.search_data.current_location
  );
  const dispatch = useDispatch();
  //Hooks
  const { t } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();
  //Functions

  //State
  const [deliveryToolTipOpen, setDeliveryToolTipOpen] = useState(false);
  const [disabledConfirm, setDisabledConfirm] = useState(true);

  //GQL
  const { data: areaData, loading: loadingArea } = useQuery(GET_AREA_QUERY, {
    skip: !deliveryLocation?.lat && !deliveryLocation?.lng,
    variables: {
      lat: deliveryLocation?.lat,
      lng: deliveryLocation?.lng,
    },
    fetchPolicy: "no-cache",
    nextFetchPolicy: "no-cache",
  });

  useEffect(() => {
    if (!userAddress?.pick_up?.id) {
      dispatch(setUserPickupAddressAction(deliveryLocation));
    }
  }, [deliveryLocation]);

  useEffect(() => {
    // Id ensures that selection is from cities / airports list to enable confirm button
    if (deliveryLocation?.id) {
      setDisabledConfirm(false);
    } else if (deliveryLocation) {
      setDisabledConfirm(true);
      if (areaData?.area?.id == userAddress?.pick_up?.id) {
        setDisabledConfirm(false);
      }
    }
  }, [areaData]);

  return (
    <PopupComponent
      isOpen={isOpen}
      title={t("Choose Delivery Location")}
      setIsOpen={setIsOpen}
      maxWidth="md"
      onClose={() => {
        if (!deliveryLocation) {
          dispatch(setSelectionIndexAction(0));
          dispatch(setIsBarq(false));
        }
        setIsOpen(false);
      }}
    >
      {/* <RequestLoader loading={loadingArea} /> */}
      <Grid
        className="delivery-popup-header mb-4"
        container
        justifyContent="space-between"
        alignItems="center"
      >
        <Grid item xs={12} className="position-relative text-align-localized">
          <ClickAwayListener
            mouseEvent={"onMouseDown"}
            touchEvent={"onTouchStart"}
            onClickAway={() => {
              setTimeout(() => {
                setDeliveryToolTipOpen(false);
              }, 500);
            }}
          >
            <input
              type="text"
              placeholder={t("City")}
              style={{
                borderRadius: "var(--radius-3)",
                border: "solid 2px var(--color-7)",
                padding: "12px 24px",
                width: "100%",
              }}
              value={deliveryLocation?.airport?.name || deliveryLocation?.name}
              readOnly
              onClick={() => {
                setDeliveryToolTipOpen(true);
              }}
            />
          </ClickAwayListener>
          {deliveryToolTipOpen ? (
            <AreasTooltip
              action={setDeliveryLocationAction}
              isOpen={deliveryToolTipOpen}
              setIsOpen={setDeliveryLocationAction}
            />
          ) : null}
        </Grid>
      </Grid>
      <Map
        lat={
          deliveryLocation?.lat ||
          currentLocation?.lat ||
          userAddress?.pick_up?.lat ||
          userAddress?.pick_up?.centerLat
        }
        lng={
          deliveryLocation?.lng ||
          currentLocation?.lng ||
          userAddress?.pick_up?.lng ||
          userAddress?.pick_up?.centerLng
        }
        geoCode
        action={setDeliveryLocationAction}
      />
      <Grid
        container
        justifyContent="space-between"
        className="delivery-popup-footer"
        direction="column"
        // style={{ visibility: !deliveryLocation ? "hidden" : "visible" }}
      >
        <Grid
          item
          container
          style={{
            maxWidth: "300px",
            minWidth: "25vw",
            position: "relative",
          }}
          alignItems="flex-start"
          xs={12}
          spacing={2}
          // direction="column"
        >
          <Grid item>
            <img src="/assets/images/deliverypickup.svg" alt="icon" />
          </Grid>
          <Grid item>
            <h6>
              <span>{t("Delivery Location")}</span>
              <br />
              <strong>
                {deliveryLocation?.airport?.name ||
                  deliveryLocation?.address ||
                  currentLocation?.address}
              </strong>
            </h6>
          </Grid>
        </Grid>
        <Grid
          item
          xs={12}
          container
          justifyContent="center"
          alignItems="center"
          className="mt-4"
        >
          <Grid item>
            <button
              className="w-100"
              disabled={disabledConfirm}
              onClick={() => {
                if (
                  userAddress?.pick_up?.id &&
                  deliveryLocation?.id &&
                  userAddress?.pick_up?.id !== deliveryLocation?.id
                ) {
                  setDisabledConfirm("true");
                  enqueueSnackbar(
                    `${t("This place is outside of")} ${
                      userAddress?.pick_up?.name
                    }`,
                    { variant: "error" }
                  );
                } else {
                  dispatch(
                    setconfirmedDeliveryLocationAction(deliveryLocation)
                  );
                  dispatch(setDeliveryLocationAction());
                  setTimeout(() => {
                    setIsOpen(false);
                  });
                }
              }}
            >
              {t("Confirm")}
            </button>
          </Grid>
        </Grid>
      </Grid>
    </PopupComponent>
  );
}
