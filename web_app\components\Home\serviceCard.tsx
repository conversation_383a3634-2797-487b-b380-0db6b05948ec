/* eslint-disable @next/next/no-img-element */
import React from "react";
import styled from "styled-components";
import WhiteCard from "components/shared/whiteCard";

const Div = styled.div`
  height: 100%;
  > div {
    img {
      margin-bottom: 10px;
      height: 45px;
      width: 100%;
    }
    h6 {
      text-align: center !important;
      width: 85%;
      margin: 0 auto !important;
      @media (min-width: 901px) {
        font-size: 18px;
      }
    }
    padding: 25px 0;
  }
`;

export default function Card({ icon, text }) {
  return (
    <Div>
      <WhiteCard>
        <img src={icon} alt="icon" />
        <h6>{text}</h6>
      </WhiteCard>
    </Div>
  );
}
