/* eslint-disable react/jsx-no-target-blank */
/* eslint-disable @next/next/no-img-element */
import { InfoOutlined, Phone, Star } from "@material-ui/icons";
import Popup from "components/shared/popup";
import { useRouter } from "next/router";
import { memo, useCallback, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import styled from "styled-components";
import AllyConditions from "./allyConditions";
import LocationMap from "./locationMap";
import { TdeliverType } from "utilities/enums";
import { useQuery } from "@apollo/client";
import { Branch_Data } from "gql/queries/car";

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  padding: 20px 10px;
  .ally-class {
    border-radius: var(--radius-1);
    font-weight: bold;
    padding: 1px 10px 2px 10px;
    background-color: var(--color-13);
    color: var(--color-3);
  }
  .branch-rate {
    display: flex;
    gap: 8px;
    font-weight: bold;
    svg {
      fill: var(--color-2);
    }
  }
`;
const Location = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  padding: 20px 24px;
  #location {
    .icon-wrapper {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
`;

const Contact = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  padding: 20px 10px;
`;

function AllyData() {
  // Hooks
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const { pathname } = router;
  const bookingId = router?.query.bookingId;

  //State
  const [isAllyClassOpen, setIsAllyClassOpen] = useState(false);
  const [isAllyConditionsOpen, setIsAllyConditionsOpen] = useState(false);

  //Store
  const { rentalBranch, rentalDetails } =
    useSelector((state: RootStateOrAny) => state.booking) || {};
  const { car_data } = useSelector((state: RootStateOrAny) => state.cars) || {};

  const { branchId, status, deliverType, handoverAntherCity, dropOffBranchId } =
    rentalDetails || {};

  const showDropOff =
    bookingId &&
    handoverAntherCity &&
    ((deliverType as TdeliverType) === "one_way" ||
      ((deliverType as TdeliverType) === "no_delivery" &&
        status === "car_received"));

  const { data: _branchData } = useQuery(Branch_Data, {
    variables: {
      id: bookingId
        ? showDropOff
          ? dropOffBranchId
          : branchId
        : pathname !== "/my-rentals"
        ? car_data?.branch?.id
        : rentalBranch?.id,
    },
  });
  const {
    name: branchName,
    lat: branchLat,
    lng: branchLng,
    districtName: branchDistrictName,
  } = _branchData?.branch || {};

  //Variables

  const branchData =
    pathname !== "/my-rentals" ? car_data?.branch : rentalBranch;

  const isPendingRental = Boolean(
    pathname !== "/my-rentals" && bookingId && status === "pending"
  );

  const showLocation = useCallback(() => {
    if (bookingId && (deliverType as TdeliverType) === "two_ways") {
      return false;
    }
    return {
      title: showDropOff ? t("Drop-off location") : t("Branch location"),
      distance: !showDropOff
        ? Number(branchData?.distanceBetweenBranchUser || 0).toFixed(0)
        : "",
      address: `${branchName || ""} ${
        branchDistrictName ? `- ${branchDistrictName}` : ""
      } `,
      isHandover: Boolean(showDropOff),
      location: {
        lat: branchLat,
        lng: branchLng,
      },
    };
  }, [
    deliverType,
    branchData,
    branchName,
    branchDistrictName,
    showDropOff,
    branchLat,
    branchLng,
  ]) as any;

  const hideAllyInfo = (() => {
    if (!bookingId || isPendingRental) return true;
  })();

  const showRentalData =
    (pathname === "/my-rentals" && rentalBranch) ||
    (bookingId && (status === "confirmed" || status === "car_received"));

  return (
    <>
      <Div>
        <div className="d-flex justify-content-between gap-5px">
          <div className="d-flex gap-5px align-items-center">
            <div>
              <span className="ally-class">
                {branchData?.allyCompany?.allyClass}
              </span>
            </div>
            <span className="text-align-localized mx-1">
              {t("Ally's class") as string}
            </span>
            <img
              src="/assets/images/info.svg"
              alt="icon"
              className="cursor-pointer"
              onClick={() => setIsAllyClassOpen(true)}
            />
            <Popup
              maxWidth="md"
              isOpen={isAllyClassOpen}
              setIsOpen={setIsAllyClassOpen}
            >
              {t(`${branchData?.allyCompany?.allyClass}-class Ally`) as string}
            </Popup>
          </div>
          <div className="branch-rate">
            <span>{branchData?.allyCompany?.allyRate?.name}</span>
            <span>|</span>
            <span>{branchData?.allyCompany?.rate}/5</span>
            <Star />
          </div>
        </div>
        {showLocation() ? (
          <>
            <Location className="shadow mt-4">
              <div className="d-flex justify-content-between mb-2">
                <h6 className="bold">{showLocation().title}</h6>
                {showLocation().distance ? (
                  <span className="bold">
                    {i18n.language === "en"
                      ? `${showLocation().distance} Km away`
                      : `يبعد عنك ${showLocation().distance} كم`}
                  </span>
                ) : null}
              </div>

              {!hideAllyInfo && (
                <div className="d-flex justify-content-between mb-4 w-100">
                  <div id="location">
                    <div
                      className="icon-wrapper"
                      style={{
                        direction: "ltr",
                        flexDirection:
                          i18n.language === "en" ? "row" : "row-reverse",
                      }}
                    >
                      <img
                        src="/assets/images/carBranches/locationPin.svg"
                        alt="icon"
                      />
                      <span>{showLocation().address}</span>
                    </div>
                  </div>
                </div>
              )}

              <div style={{ pointerEvents: hideAllyInfo ? "none" : "initial" }}>
                <LocationMap
                  lat={showLocation().location.lat}
                  lng={showLocation().location.lng}
                  showRentalData={showRentalData}
                />
              </div>

              {!showLocation().isHandover &&
                (branchData?.branchState === "open" ||
                  branchData?.branchState === "closed") && (
                  <div className="mt-3 text-align-localized d-flex gap-5px">
                    <span>*{String(t("Branch is"))}</span>
                    {branchData.branchState === "open" ? (
                      <span style={{ color: "green" }}>
                        {String(t("Open Now"))}
                      </span>
                    ) : (
                      <span className="color-9">
                        {String(t("Closed Now"))}{" "}
                      </span>
                    )}
                  </div>
                )}
            </Location>

            {isPendingRental && !showLocation().isHandover && (
              <div className="mt-3 text-align-localized d-flex gap-5px">
                {String(
                  t("Car location is provided when booking is confirmed")
                )}
              </div>
            )}
          </>
        ) : null}

        {Boolean(showRentalData) && (
          <>
            <Contact className="d-flex justify-content-between align-items-baseline mt-4 shadow px-4">
              <div>
                <span className="bold">{branchData?.officeNumber}</span>
                <p>{t("Ally number") as string}</p>
              </div>
              <div className="cursor-pointer">
                <a href={`tel:${branchData?.officeNumber}`}>
                  <Phone className="color-3" />
                </a>
              </div>
            </Contact>
          </>
        )}

        <p
          className="mt-4 text-decoration-underline cursor-pointer"
          onClick={() => setIsAllyConditionsOpen(true)}
        >
          {t("Ally's Conditions") as string}
        </p>
        <Popup
          maxWidth="md"
          isOpen={isAllyConditionsOpen}
          setIsOpen={setIsAllyConditionsOpen}
          title={t("Ally's Conditions")}
        >
          <AllyConditions setIsAllyConditionsOpen={setIsAllyConditionsOpen} />
        </Popup>
      </Div>
    </>
  );
}

export default memo(AllyData);
