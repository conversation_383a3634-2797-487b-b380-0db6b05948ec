import { Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import ReviewsCarousel from "./reviewsCarousel";

const Div = styled.div`
  padding: 100px 0 0 0;
`;

export default function Reviews() {
  const { t } = useTranslation();

  return (
    <Div>
      <Grid container justifyContent="space-between">
        <Grid item xs={12} md={3}>
          <h4>{t("Reviews")}</h4>
        </Grid>
        <Grid item xs={12} md={8}>
          <ReviewsCarousel />
        </Grid>
      </Grid>
    </Div>
  );
}
