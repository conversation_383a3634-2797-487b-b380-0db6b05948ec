import Popup from "components/shared/popup";
import ExtensionRequest from "../ExtensionRequest";

export default function ViewAllExtensionsModal({
  isOpen,
  setIsOpen,
  title,
  rentalDateExtensionRequests,
  lastRentalDateExtensionRequest,
}) {
  return (
    <Popup
      maxWidth="sm"
      fullWidth
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      title={title}
    >
      {rentalDateExtensionRequests?.length
        ? rentalDateExtensionRequests
            .filter(
              (extension) => extension.id != lastRentalDateExtensionRequest?.id
            )
            ?.map((extension) => (
              <div key={extension.id}>
                <ExtensionRequest extensionDetails={extension} />
              </div>
            ))
        : null}
    </Popup>
  );
}
