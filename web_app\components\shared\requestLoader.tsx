/* eslint-disable react-hooks/exhaustive-deps */
import { CircularProgress } from "@material-ui/core";
import { useEffect, useState } from "react";

function RequestLoader({ loading }) {
  const [hideLoading, setHideLoading] = useState(false);

  useEffect(() => {
    return () => {
      setHideLoading(true);
    };
  }, []);
  return loading && !hideLoading ? (
    <div
      style={{
        position: "fixed",
        width: "100vw",
        height: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 9999,
        left: 0,
        top: 0,
      }}
    >
      <CircularProgress />
    </div>
  ) : null;
}

export default RequestLoader;
