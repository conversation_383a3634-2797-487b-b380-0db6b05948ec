/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect, useCallback } from "react";
import styled from "styled-components";

// Constants
const CONNECTION_CHECK_TIMEOUT = 10000;
const WEAK_CONNECTION_THRESHOLDS = {
  DOWNLINK: 0.1,
  RTT: 800,
} as const;

const FALLBACK_ENDPOINTS = {
  PRIMARY: "https://www.google.com/favicon.ico",
} as const;

// Types
type ConnectionStatus = "connected" | "weak" | "lost" | undefined;

interface NetworkConnection {
  downlink?: number;
  rtt?: number;
}

// Styled components
const Snackbar = styled.div`
  .snackbar {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #333;
    color: white;
    text-align: center;
    padding: 16px;
    z-index: 1000;
    opacity: 0.9;
    display: flex;
    justify-content: center;
    gap: 7px;
  }
`;

const ConnectionStatus = () => {
  const [connectionStatus, setConnectionStatus] =
    useState<ConnectionStatus>(undefined);

  const getNetworkConnection = useCallback((): NetworkConnection | null => {
    if (typeof navigator === "undefined") return null;

    return (
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection ||
      null
    );
  }, []);

  const isSafariBrowser = useCallback((): boolean => {
    if (typeof navigator === "undefined") return false;
    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  }, []);

  const checkConnectionWithFetch = useCallback((): Promise<void> => {
    return fetch(FALLBACK_ENDPOINTS.PRIMARY, {
      method: "HEAD",
      mode: "no-cors",
      cache: "no-cache",
    })
      .then(() => setConnectionStatus("connected"))
      .catch(() => {
        // Double-check with image loading approach
        const img = new Image();
        img.onload = () => setConnectionStatus("connected");
        img.onerror = () => setConnectionStatus("weak");
        img.src = `${FALLBACK_ENDPOINTS.PRIMARY}?${Math.random()}`;
      });
  }, []);

  const checkConnectionStatus = useCallback(() => {
    if (typeof window === "undefined" || !navigator.onLine) {
      setConnectionStatus("lost");
      return;
    }

    const connection = getNetworkConnection();

    if (connection?.downlink !== undefined && connection?.rtt !== undefined) {
      // Use Network Information API when available
      const { downlink, rtt } = connection;
      if (
        downlink < WEAK_CONNECTION_THRESHOLDS.DOWNLINK ||
        rtt > WEAK_CONNECTION_THRESHOLDS.RTT
      ) {
        setConnectionStatus("weak");
      } else {
        setConnectionStatus("connected");
      }
    } else if (isSafariBrowser()) {
      // Safari fallback - navigator.onLine is generally reliable in Safari
      setConnectionStatus("connected");
    } else {
      // Other browsers fallback using fetch
      checkConnectionWithFetch();
    }
  }, [getNetworkConnection, isSafariBrowser, checkConnectionWithFetch]);

  const handleOnline = useCallback(() => {
    checkConnectionStatus();
  }, [checkConnectionStatus]);

  const handleOffline = useCallback(() => {
    setConnectionStatus("lost");
  }, []);

  // Main effect for setting up connection monitoring
  useEffect(() => {
    if (typeof window === "undefined") return;

    // Initial status check
    checkConnectionStatus();

    // Event listeners
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [checkConnectionStatus, handleOnline, handleOffline]);

  // Effect for auto-hiding status messages
  useEffect(() => {
    if (!connectionStatus) return;

    const timer = setTimeout(() => {
      setConnectionStatus(undefined);
    }, CONNECTION_CHECK_TIMEOUT);

    return () => clearTimeout(timer);
  }, [connectionStatus]);

  if (!connectionStatus) return null;

  const renderStatusMessage = () => {
    switch (connectionStatus) {
      case "lost":
        return (
          <div className="snackbar">
            <p>No internet connection - </p>
            <p>لا يوجد اتصال بالإنترنت</p>
          </div>
        );
      default:
        return null;
    }
  };

  return <Snackbar>{renderStatusMessage()}</Snackbar>;
};

export default ConnectionStatus;
