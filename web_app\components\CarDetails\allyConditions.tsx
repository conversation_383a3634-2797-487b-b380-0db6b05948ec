/* eslint-disable react/jsx-key */
import { Collapse } from "@material-ui/core";
import {
  KeyboardArrowDown,
  KeyboardArrowUp,
  KeyboardArrowRight,
  KeyboardArrowLeft,
} from "@material-ui/icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export default function AllyConditions({ setIsAllyConditionsOpen }) {
  //State Definition
  const [isLicenseCollapsed, setIsLicenseCollapsed] = useState(false);
  const [isAgeCollapsed, setIsAgeCollapsed] = useState(false);
  const [isPaymentCollapsed, setIsPaymentCollapsed] = useState(false);
  const [isFuelCollapsed, setIsFuelCollapsed] = useState(false);
  //Hooks
  const { t, i18n } = useTranslation();

  //Condition Component
  const Condition = ({
    setIsCollapsed,
    collapsed,
    title,
    subTitle,
    conditionsDetails_en,
    conditionsDetails_ar,
  }) => {
    return (
      <>
        <div
          className="d-flex flex-row gap-20px align-items-center cursor-pointer"
          onClick={() => setIsCollapsed(!collapsed)}
        >
          <div className="text-align-localized">
            <h6 className="bold color-2 font-18px">{title}</h6>
            <span className="font-13px color-8">{subTitle}</span>
          </div>
          {!collapsed ? (
            i18n.language === "en" ? (
              <KeyboardArrowRight className="color-2" />
            ) : (
              <KeyboardArrowLeft className="color-2" />
            )
          ) : (
            <KeyboardArrowDown className="color-2" />
          )}
        </div>
        <Collapse in={collapsed} timeout="auto" className=" mt-1 mb-3">
          <div className="text-align-localized">
            {i18n.language === "en"
              ? conditionsDetails_en.map((cond) => {
                  return <>{cond}</>;
                })
              : conditionsDetails_ar.map((cond) => {
                  return <>{cond}</>;
                })}
          </div>
        </Collapse>
      </>
    );
  };

  //Conditions Data
  const dataList = [
    {
      setIsCollapsed: setIsLicenseCollapsed,
      isCollapsed: isLicenseCollapsed,
      title: t("Requirements from the driver"),
      subTitle: t("Driving license and identification"),
      conditionsDetails_en: [
        `If the driver is a Saudi citizen or resident of Saudi Arabia, the driver must bring the national identity, a valid driver's license, as well as an active account in "Abshir". For the GCC citizens, they must bring their original national ID with a valid GCC or international driving license. A tourist/visitor has to bring a valid international driving license as well as a valid passport.`,
      ],
      conditionsDetails_ar: [
        `"إذا كان السائق مواطن سعودي أو مقيم في السعودية فيجب عليه إحضار أصل الهوية الوطنية ورخصة القيادة سارية الصلاحية، كما يجب وجود حساب مفعل في أبشر، وإذا كان من مواطني دول مجلس التعاون فيجب عليه إحضار أصل الهوية الوطنية لبلده وأصل رخصة القيادة  الخليجية أو الدولية سارية الصلاحية، أما إذا كان زائر فعليه إحضار أصل رخصة القيادة الدولية سارية الصلاحية وكذلك أصل جواز السفر."`,
      ],
    },
    {
      setIsCollapsed: setIsAgeCollapsed,
      isCollapsed: isAgeCollapsed,
      title: t("Rental Age"),
      subTitle: t("21 - 60 years old"),
      conditionsDetails_en: [
        `The driver must be between 21 - 60 years old and it depends on the ally's policy and vehicle types.`,
      ],
      conditionsDetails_ar: [
        `يشترط أن يكون عمر السائق ما بين ٢١ - ٦٠ سنة حسب سياسة الحليف وفئة السيارة.`,
      ],
    },
    {
      setIsCollapsed: setIsPaymentCollapsed,
      isCollapsed: isPaymentCollapsed,
      title: t("Payment & Cancellation Policy"),
      subTitle: t("Security deposit, Cancellation Fees"),
      conditionsDetails_en: [
        `Credit card are required. The ally may request a security deposit at the pick up of the car, the security deposit amount may varies depending on the vehicle type.`,
        `In the event that you cancel the reservation while it is PENDING the full amount will be refunded to your card automatically.`,
        <p className="my-1">
          In the event that you cancel the reservation while it is CONFIRMED 24
          hours before the time of receiving the car, the full amount will be
          refunded to your card. Please provide feedback by accessing our Report
          Feedback section through Help & Support via Carwah App and completing
          the form to receive your refund within 3 to 7 business days.
        </p>,
        <p className="my-1">
          In the event that you cancel the reservation, and it is CONFIRMED
          within 24 hours from the time of receiving the car, the value of one
          rental day, inclusive of tax will be deducted ad the remaining amount
          will be refunded to your card. Please provide feedback by accessing
          our Report Feedback section through Help & Support via Carwah App and
          completing the form to receive your refund within 3 to 7 business
          days.
        </p>,
      ],
      conditionsDetails_ar: [
        `الدفع بالبطاقة الإئتمانية، وقد يشترط الحليف دفع مبلغ ضمان مسترد حسب فئة السيارة.`,
        `في حال إلغائك الحجز وهو قيد الانتظار يتم استرجاع المبلغ كاملاً لحساب بطاقتك آليًا.`,
        <p className="my-1">
          في حال إلغائك الحجز وهو مؤكد قبل 24 ساعة من موعد استلام السيارة يتم
          استرجاع المبلغ كاملاً لحساب بطاقتك. يرجى إضافة ملاحظاتك من خلال قسم
          الملاحظات عبر الدعم والمساعدة عبر تطبيق كروه، وتعبئة النموذج لاسترداد
          مبلغك خلال مدة أقصاها 3 إلى 7 أيام عمل.
        </p>,
        <p>
          في حال إلغائك الحجز وهو مؤكد خلال 24 ساعة من موعد استلام السيارة يتم
          خصم قيمة إيجار يوم شامل الضريبة و يتم استرجاع المبلغ المتبقي لحساب
          بطاقتك. يرجى إضافة ملاحظاتك من خلال قسم الملاحظات عبر الدعم والمساعدة
          عبر تطبيق كروه، وتعبئة النموذج لاسترداد مبلغك خلال مدة أقصاها 3 إلى 7
          أيام عمل.
        </p>,
      ],
    },
    {
      setIsCollapsed: setIsFuelCollapsed,
      isCollapsed: isFuelCollapsed,
      title: t("Fuel Policy"),
      subTitle: t("Fuel as it is"),
      conditionsDetails_en: [
        `Our Allies fuel policy is the customer should return the car with the same fuel as picked up.`,
      ],
      conditionsDetails_ar: [
        `سياسة الوقود لدى حلفائنا هي المثل بالمثل بمعنى أنه يتم إعادة خزان وقود السيارة بنفس كمية الوقود التي تم استلام السيارة بها.`,
      ],
    },
  ];

  //JSX
  return (
    <div className="d-flex flex-column ">
      {dataList.map(
        ({
          id,
          setIsCollapsed,
          isCollapsed,
          title,
          subTitle,
          conditionsDetails_en,
          conditionsDetails_ar,
        }) => {
          return (
            <Condition
              key={id}
              setIsCollapsed={setIsCollapsed}
              collapsed={isCollapsed}
              title={title}
              subTitle={subTitle}
              conditionsDetails_en={conditionsDetails_en}
              conditionsDetails_ar={conditionsDetails_ar}
            />
          );
        }
      )}
      <span
        className="text-center cursor-pointer text-uppercase bold mt-3"
        onClick={() => setIsAllyConditionsOpen(false)}
      >
        {t("Done")}
      </span>
    </div>
  );
}
