import { gql } from "@apollo/client";

const Checkout_Id = gql`
  query GetCheckoutId(
    $paymentBrand: RentalPaymentBrand
    $rentalId: ID!
    $withWallet: Boolean
  ) {
    getCheckoutId(
      paymentBrand: $paymentBrand
      rentalId: $rentalId
      withWallet: $withWallet
    ) {
      checkoutId
      integrity
    }
  }
`;
const Installment_Checkout_Id = gql`
  query GetIntallmentCheckoutId(
    $paymentBrand: RentalPaymentBrand
    $rentalId: ID!
    $withWallet: Boolean
  ) {
    installmentGetCheckoutId(
      paymentBrand: $paymentBrand
      rentalId: $rentalId
      withWallet: $withWallet
    ) {
      checkoutId
      integrity
    }
  }
`;
const Payment_Status = gql`
  query GetPaymentStatus($checkoutId: String!, $rentalId: ID!) {
    getPaymentStatus(checkoutId: $checkoutId, rentalId: $rentalId) {
      status
      rental {
        isIntegratedRental
        rentalIntegrationStatus
      }
      errors
    }
  }
`;
const Installment_Payment_Status = gql`
  query GetInstallmentPaymentStatus($checkoutId: String!, $rentalId: ID!) {
    installmentGetPaymentStatus(checkoutId: $checkoutId, rentalId: $rentalId) {
      status
      errors
    }
  }
`;
const Customer_Rentals = gql`
  query customerRentals($limit: Int, $page: Int, $status: String) {
    customerRentals(limit: $limit, page: $page, status: $status) {
      collection {
        # addsPrice
        # allyCompanyId
        # allyName
        allyRate
        allyRentalRate
        isRentToOwn
        # arAllyName
        arDropOffCityName
        arMakeName
        arModelName
        arPickUpCityName
        # arVersionName
        # assignedAt
        # assignedBy
        # assignedByName
        # assignedTo
        # assignedToName
        bookingNo
        # branchAreaId
        # branchAreaNameAr
        # branchAreaNameEn
        # branchDistrictNameAr
        # branchDistrictNameEn
        # branchId
        # branchName
        # cancelledAt
        # cancelledReason
        carId
        carImage
        # closedAt
        # couponCode
        # couponDiscount
        # couponId
        # couponType
        # createdAt
        # customerBookingLat
        # customerBookingLng
        # customerDob
        # customerLocale
        # customerMobile
        # customerName
        # customerProfileImage
        # customerRate
        # customerRentalRate
        # dailyPrice
        # deliverAddress
        # deliverLat
        # deliverLng
        # deliverType
        # deliveryDistance
        # deliveryPrice
        # discountPercentage
        # discountType
        # discountValue
        dropOffBranchId
        # dropOffCityId
        # dropOffCityName
        dropOffDate
        # dropOffTime
        # enAllyName
        enDropOffCityName
        enMakeName
        enModelName
        enPickUpCityName
        # enVersionName
        # handoverAddress
        # handoverAntherCity
        # handoverDistance
        # handoverLat
        # handoverLng
        # handoverPrice
        hasPendingExtensionRequests
        pendingExtensionRequest {
          requestNo
        }
        # hasUnseenExtensionConfirmation
        id
        # insuranceId
        # insuranceIncluded
        # invoicePic
        # invoicedAt
        # is24Passed
        # isExtendable
        # isIntegratedRental
        isPaid
        # isPassed
        # isUnlimited
        # isUnlimitedFree
        # makeName
        # modelName
        # newGrandTotal
        # note
        numberOfDays
        payable
        # paymentBrand
        paymentMethod
        # paymentStatus
        # paymentStatusCode
        # paymentStatusMessage
        # pickUpCityId
        # pickUpCityName
        pickUpDate
        pickUpTime
        # priceBeforeDiscount
        # priceBeforeInsurance
        # priceBeforeTax
        # pricePerDay
        # refundable
        # refundedAmount
        # refundedAt
        # refundedBy
        # rentalIntegrationErrorMessage
        # rentalIntegrationResponse
        # rentalIntegrationStatus
        status
        statusLocalized
        subStatus
        installments {
          status
          amountDue
          amount
        }
        # subStatusLocalized
        # suggestedPrice
        # taxNote
        # taxValue
        totalBookingPrice
        totalAmountDue
        # normal rent no installment
        # totalExtraServicesPrice
        # totalInsurancePrice
        # totalUnlimitedFee
        # unlimitedFeePerDay
        # userId
        # valueAddedTaxPercentage
        # versionName
        year
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;
export {
  Customer_Rentals,
  Checkout_Id,
  Payment_Status,
  Installment_Checkout_Id,
  Installment_Payment_Status,
};
