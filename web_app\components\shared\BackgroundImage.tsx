import React, { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import { CircularProgress } from "@material-ui/core";

interface BackgroundImageProps {
  src: string;
  children?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  loadingColor?: string;
  priority?: boolean;
  placeholder?: string;
  flip?: boolean;
}

const Container = styled.div<{
  bgImage: string;
  placeholderImage: string;
  isLoaded: boolean;
}>`
  background-image: ${(props) =>
    props.isLoaded
      ? `url(${props.bgImage})`
      : props.placeholderImage
      ? `url(${props.placeholderImage})`
      : "none"};
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  width: 100%;
  height: 100%;
  transition: background-image 0.3s ease-in-out;
  transform: scaleX(${(props) => (props.flip == true ? "-1" : "1")}) !important;
  justify-content: ${(props) =>
    props.flip == true ? "flex-end" : "flex-start"}; // Adjust justify-content
`;

const LoadingContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 1;
`;

const BackgroundImage: React.FC<BackgroundImageProps> = ({
  src,
  children,
  style,
  className,
  loadingColor = "white",
  priority = false,
  placeholder = "",
  flip,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const imgRef = useRef<HTMLImageElement | null>(null);

  useEffect(() => {
    // Reset loading state when src changes
    setIsLoaded(false);

    // Skip loading if no src provided
    if (!src) {
      setIsLoaded(true);
      return;
    }

    // If priority is true, set loading attribute to eager
    // otherwise use lazy loading
    const loadingStrategy = priority ? "eager" : "lazy";

    // Preload the image
    const img = new Image();
    img.loading = loadingStrategy;
    img.src = src;
    img.onload = () => {
      setIsLoaded(true);
    };

    imgRef.current = img;

    // Clean up
    return () => {
      if (imgRef.current) {
        imgRef.current.onload = null;
      }
    };
  }, [src, priority]);

  return (
    <Container
      bgImage={src}
      placeholderImage={placeholder}
      isLoaded={isLoaded}
      style={style}
      className={className}
      flip={flip}
    >
      {!isLoaded && (
        <LoadingContainer>
          <CircularProgress style={{ color: loadingColor }} />
        </LoadingContainer>
      )}
      {children}
    </Container>
  );
};

export default BackgroundImage;
