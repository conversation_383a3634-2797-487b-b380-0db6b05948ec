/* eslint-disable no-restricted-globals */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
import React, { memo, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { persist } from "constants/constants";
import { AvailableBranches } from "gql/queries/AllBranches.gql";

function BranchesDropDownBooking({
  loading,
  setSelectedBranch,
  selectedBranch,
  error,
  valueAttribute,
  allyId,
  multiple,
  required,
  isAlly,
  manageradd,
  areaIds,
  noSkip,
  isDisabled,
  coupon,
  BranchesDropDown,
  setList,
  banner,
  cities,
  isRentToOwn,
  canDelivery,
  ...props
}) {
  const { data: allbranches, loading: gettingModels } = useQuery(AvailableBranches, {
    skip: !allyId?.length || !areaIds?.length ,
    variables: {
      limit: persist.higherUnlimited,
      allyCompanyIds: allyId || isAlly,
      isActive: true  ,
      areaIds,
      isRentToOwn,
      canDelivery,
      
      
    },
  });
  
  useEffect(() => {
    if (!selectedBranch) {
      onClear();
    }
  }, [selectedBranch]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  const { formatMessage } = useIntl();
  const [options, setOptions] = useState([]);



  useEffect(() => {
    const alloptions =
      allbranches?.availableBranches.collection?.map((x) => ({
        value: x[valueAttribute || "enName"],
        label: x.name,
        allyExtraServicesForAlly: x.allyCompany.allyExtraServicesForAlly,
        branchExtraServices: x.branchExtraServices,
      branchDeliveryPrices: x?.branchDeliveryPrices,

      })) || [];
  if(alloptions.length){
      setOptions([...alloptions]);
    }
    else {
      setOptions([]);
    }
  }, [allbranches]);
  return (
    <Select
      ref={selectInputRef}
      isMulti={multiple}
      isDisabled={isDisabled}
      isClearable
      className={`dropdown-select ${multiple ? "multiple" : ""}  ${required ? "required" : ""} ${
        error ? "selection-error" : ""
      }`}
      options={options}
      loadOptions={gettingModels || loading}
      value={
        multiple
          ?  options?.filter((optn) => selectedBranch?.includes(+optn.value))
          : options?.find((optn) => `${optn.value}` === `${selectedBranch}`)
      }
      placeholder={
        coupon || banner
          ? formatMessage({ id: "ally.branches" })
          : formatMessage({ id: "branches" })
      }
      onChange={(selection) => {
      
          setSelectedBranch(selection);
        
      }}
      noOptionsMessage={() => {
        if (gettingModels) {
          return <CircularProgress />;
        }
        if (!allbranches?.length) return <FormattedMessage id="No data found" />;
      }}
      {...props}
    />
  );
}
BranchesDropDownBooking.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectedBranch: PropTypes.string,
  setSelectedBranch: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
export default memo(BranchesDropDownBooking);
