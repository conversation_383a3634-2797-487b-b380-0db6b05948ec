import { DateObject } from "react-multi-date-picker";
import { defaultDaysCount, defaultTime } from "utilities/enums";
import { calendarDaysConvert } from "utilities/helpers";
import {
  SELECTION_INDEX,
  DATE_TIME,
  USER_PICKUP_ADDRESS,
  USER_RETURN_ADDRESS,
  V<PERSON>H<PERSON>LE_TYPE,
  RENTAL_MONTHS,
  DELIVERY_LOCATION,
  CONFIRMED_DELIVERY_LOCATION,
  CURRENT_LOCATION,
  SEARCH_MAKE_ID,
  SEARCH_MODAL_ID,
  SEARCH_TRANSMISSION,
  SEARCH_VEHICLETYPE,
  SEARCH_INSURANCE,
  SEARCH_EXTRASERVICE,
  SEARCH_YEAR,
  SEARCH_DailyPrice,
  <PERSON><PERSON><PERSON>_FILTERS,
  SEARCH_MAKE_MODEL,
  AREAS_LIST,
  UNLIMITED_KM,
  CAR_RETURN_IN_ANOTHER_BRANCH,
  IS_BARQ,
  IS_INSTANT_CONFIRMATION,
  FILTERS_IN_SEARCH,
  IS_NEW_CAR,
  FULL_INSURANCE,
} from "./action";
import moment from "moment";

export const default_date_time = {
  reservation_days: [new DateObject(), new DateObject().add(3, "days")],
  pickUpDate:
    moment().hour() >= 22
      ? calendarDaysConvert(new DateObject().add(1, "days"))
      : calendarDaysConvert(new DateObject()),
  dropOffDate:
    moment().hour() >= 22
      ? calendarDaysConvert(new DateObject().add(4, "days"))
      : calendarDaysConvert(new DateObject().add(3, "days")),
  selected_days_count: defaultDaysCount,
  pickup_time: defaultTime,
  return_time: defaultTime,
};
export default function searchDataReducer(
  state = {
    selection_index: 0,
    date_time: default_date_time,
    user_address: {
      pick_up: null,
      return: null,
    },
    vehicleType: null,
    rental_months: 12,
    delivery_location: null,
    filters: {},
    areas: [],
    handover_in_another_branch: {},
    isNewCar: true,
  },
  action
) {
  switch (action.type) {
    case SELECTION_INDEX:
      return { ...state, selection_index: action.payload };
    case DATE_TIME:
      return {
        ...state,
        date_time: { ...state.date_time, ...action.payload },
      };
    case USER_PICKUP_ADDRESS:
      return {
        ...state,
        user_address: {
          ...state.user_address,
          pick_up: action.payload,
          return: action.payload,
        },
        confirmed_delivery_location: undefined,
      };
    case USER_RETURN_ADDRESS:
      return {
        ...state,
        user_address: { ...state.user_address, return: action.payload },
      };
    case VEHICLE_TYPE:
      return {
        ...state,
        vehicleType: action.payload,
      };
    case RENTAL_MONTHS:
      return {
        ...state,
        rental_months: action.payload,
      };
    case DELIVERY_LOCATION:
      return {
        ...state,
        delivery_location: action.payload,
      };
    case CONFIRMED_DELIVERY_LOCATION:
      return {
        ...state,
        confirmed_delivery_location: action.payload,
      };
    case CURRENT_LOCATION:
      return {
        ...state,
        current_location: action.payload,
      };
    case SEARCH_MAKE_MODEL:
      return {
        ...state,
        search_makeModel: action.payload,
      };
    case SEARCH_MAKE_ID:
      return {
        ...state,
        filters: {
          ...state.filters,
          make: action.payload,
        },
      };
    case SEARCH_MODAL_ID:
      return {
        ...state,
        filters: {
          ...state.filters,
          model: action.payload,
        },
      };
    case SEARCH_TRANSMISSION:
      return {
        ...state,
        filters: {
          ...state.filters,
          transmission: action.payload == "all" ? null : action.payload,
        },
      };
    case SEARCH_VEHICLETYPE:
      return {
        ...state,
        filters: {
          ...state.filters,
          vehicleType: action.payload == "-1" ? undefined : action.payload,
        },
      };
    case SEARCH_EXTRASERVICE:
      return {
        ...state,
        filters: {
          ...state.filters,
          extraServices: action.payload,
          // extraServices: action.payload.length ? action.payload : undefined,
        },
      };
    case UNLIMITED_KM:
      return {
        ...state,
        isUnlimited: action.payload,
      };
    case CAR_RETURN_IN_ANOTHER_BRANCH:
      return {
        ...state,
        canHandoverInBranch: action.payload,
      };
    case FULL_INSURANCE:
      return {
        ...state,
        fullInsurance: action.payload,
      };
    case IS_BARQ:
      return {
        ...state,
        isBarq: action.payload,
      };
    case IS_INSTANT_CONFIRMATION:
      return {
        ...state,
        isInstantConfirmation: action.payload,
      };
    case SEARCH_YEAR:
      return {
        ...state,
        filters: {
          ...state.filters,
          yearFrom: action.payload,
          yearTo: action.payload,
        },
      };

    case SEARCH_INSURANCE:
      return {
        ...state,
        filters: {
          ...state.filters,
          insuranceType: action.payload == -1 ? null : action.payload,
        },
      };
    case SEARCH_DailyPrice:
      return {
        ...state,
        filters: {
          ...state.filters,
          dailyPriceFrom: action?.payload?.dailyPriceFrom || undefined,
          dailyPriceTo: action?.payload?.dailyPriceTo || undefined,
        },
      };
    case FILTERS_IN_SEARCH:
      return {
        ...state,
        filters_used_in_search: action.payload,
      };
    case IS_NEW_CAR:
      return {
        ...state,
        isNewCar: action.payload,
      };
    case CLEAR_FILTERS:
      return {
        ...state,
        filters: {},
      };
    case AREAS_LIST:
      return {
        ...state,
        areas: action.payload,
      };

    default:
      return state;
  }
}
