import {
  ArrowBackIosOutlined,
  ArrowForwardIosOutlined,
} from "@material-ui/icons";
import { useTranslation } from "react-i18next";

/* eslint-disable @next/next/no-img-element */
function ActionButton({
  isDisabled,
  actionHandler,
  buttonText,
  buttonIconUrl,
  changePymentMethodHandler,
}: {
  isDisabled: boolean;
  actionHandler: any;
  buttonText: string;
  buttonIconUrl: string;
  changePymentMethodHandler: any;
}) {
  const { t, i18n } = useTranslation();

  return (
    <div className="d-flex justify-content-center align-items-center gap-10px mt-4">
      <button
        id="rent-action"
        className={`button mt-0  w-100 border-0 `}
        disabled={isDisabled}
        onClick={(e) => {
          actionHandler(e);
        }}
        style={{
          color: "var(--color-4)",
          padding: "15px 24px 15px 24px",
          cursor: "pointer",
          backgroundColor: "#000",
          borderRadius: "var(--radius-3)",
          fontWeight: "bold",
        }}
      >
        <div
          className={`d-flex ${
            buttonIconUrl ? "justify-content-between" : "justify-content-center"
          } align-items-center gap-5px`}
        >
          <span
            style={{
              marginTop: "-5px",
              display: "inline-block",
              width: "max-content",
            }}
          >
            {buttonText}
          </span>
          {buttonIconUrl ? (
            <img
              src={buttonIconUrl}
              style={{
                background: "white",
                padding: "3px",
                borderRadius: "4px",
              }}
              alt="payment icon"
              height={27}
            />
          ) : null}
        </div>
      </button>
      {changePymentMethodHandler ? (
        <div
          className="font-14px d-flex  cursor-pointer align-items-center mt-1"
          onClick={() => changePymentMethodHandler()}
        >
          <span
            className="text-center"
            style={{ fontWeight: 600, padding: "5px 5px" }}
          >
            {t("Change Payment Method") as string}
          </span>
          {i18n.language === "en" ? (
            <ArrowForwardIosOutlined style={{ width: "14px" }} />
          ) : (
            <ArrowBackIosOutlined style={{ width: "14px" }} />
          )}
        </div>
      ) : null}
    </div>
  );
}
export default ActionButton;
