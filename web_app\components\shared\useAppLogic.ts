/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { setDateTimeAction } from "store/search/action";
import { default_date_time } from "store/search/reducer";

function useAppLogic() {
  const { i18n } = useTranslation();
  const { locale } = useRouter() || {};
  const dispatch = useDispatch();

  const router = useRouter();
  const dateTime = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );
  const pickUpDate = dateTime?.pickUpDate;

  function redirectioToStoreHandler() {
    if (router.asPath.includes("#redirect")) {
      window.open("https://onelink.to/gdzadj"); // https://onelink.to/gdzadj is a universal redirect to strore depending on OS
      return;
    }
    return;
  }

  useEffect(() => {
    if (typeof window != "undefined") {
      i18n?.changeLanguage(locale || "ar");
    }

    redirectioToStoreHandler();
  }, []);

  useEffect(() => {
    if (typeof window != "undefined") {
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, [router.pathname]);

  useEffect(() => {
    // resets dates past to today's date
    if (moment(pickUpDate, "DD/MM/YYYY").diff(moment(Date.now()), "days") < 0) {
      dispatch(setDateTimeAction(default_date_time));
    }
  }, [pickUpDate]);
}

export default useAppLogic;
