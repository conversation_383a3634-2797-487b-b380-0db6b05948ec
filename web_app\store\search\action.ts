export const SELECTION_INDEX = "SELECTION_INDEX";
export const DATE_TIME = "DATE_TIME";
export const USER_PICKUP_ADDRESS = "USER_PICKUP_ADDRESS";
export const USER_RETURN_ADDRESS = "USER_RETURN_ADDRESS";
export const VEHICLE_TYPE = "VEHICLE_TYPE";
export const RENTAL_MONTHS = "RENTAL_MONTHS";
export const DELIVERY_LOCATION = "DELIVERY_LOCATION";
export const CONFIRMED_DELIVERY_LOCATION = "CONFIRMED_DELIVERY_LOCATION";
export const CURRENT_LOCATION = "CURRENT_LOCATION";
export const SEARCH_MAKE_MODEL = "SEARCH_MAKE_MODEL";
export const SEARCH_MAKE_ID = "SEARCH_MAKE_ID";
export const SEARCH_MODAL_ID = "SEARCH_MODAL_ID";
export const SEARCH_TRANSMISSION = "SEARCH_TRANSMISSION";
export const SEARCH_VEHICLETYPE = "SEARCH_VEHICLETYPE";
export const SEARCH_INSURANCE = "SEARCH_INSURANCE";
export const SEARCH_EXTRASERVICE = "SEARCH_EXTRASERVICE";
export const UNLIMITED_KM = "UNLIMITED_KM";
export const CAR_RETURN_IN_ANOTHER_BRANCH = "CAR_RETURN_IN_ANOTHER_BRANCH";
export const FULL_INSURANCE = "FULL_INSURANCE";
export const SEARCH_YEAR = "SEARCH_YEAR";
export const SEARCH_DailyPrice = "SEARCH_DailyPrice";
export const CLEAR_FILTERS = "CLEAR_FILTERS";
export const AIRPORT = "AIRPORT";
export const AREAS_LIST = "AREAS_LIST";
export const IS_BARQ = "IS_BARQ";
export const IS_INSTANT_CONFIRMATION = "IS_INSTANT_CONFIRMATION";
export const FILTERS_IN_SEARCH = "FILTERS_IN_SEARCH";
export const IS_NEW_CAR = "IS_NEW_CAR";

export function setSelectionIndexAction(payload) {
  return {
    type: SELECTION_INDEX,
    payload,
  };
}

export function setDateTimeAction(payload) {
  return {
    type: DATE_TIME,
    payload,
  };
}

export function setUserPickupAddressAction(payload) {
  return {
    type: USER_PICKUP_ADDRESS,
    payload,
  };
}

export function setUserReturnAddressAction(payload) {
  return {
    type: USER_RETURN_ADDRESS,
    payload,
  };
}

export function setVehicleTypeAction(payload) {
  return {
    type: VEHICLE_TYPE,
    payload,
  };
}

export function setRentalMonthsAction(payload) {
  return {
    type: RENTAL_MONTHS,
    payload,
  };
}

export function setDeliveryLocationAction(payload) {
  return {
    type: DELIVERY_LOCATION,
    payload,
  };
}
export function setconfirmedDeliveryLocationAction(payload) {
  return {
    type: CONFIRMED_DELIVERY_LOCATION,
    payload,
  };
}
export function setcurrentLocationAction(payload) {
  return {
    type: CURRENT_LOCATION,
    payload,
  };
}

export function searchByMakeModel(payload) {
  return {
    type: SEARCH_MAKE_MODEL,
    payload,
  };
}

export function setMake(payload) {
  return {
    type: SEARCH_MAKE_ID,
    payload,
  };
}
export function setModal(payload) {
  return {
    type: SEARCH_MODAL_ID,
    payload,
  };
}
export function setTransmission(payload) {
  return {
    type: SEARCH_TRANSMISSION,
    payload,
  };
}
export function setVehiclesType(payload) {
  return {
    type: SEARCH_VEHICLETYPE,
    payload,
  };
}
export function setInsuranceType(payload) {
  return {
    type: SEARCH_INSURANCE,
    payload,
  };
}
export function setExtraServices(payload) {
  return {
    type: SEARCH_EXTRASERVICE,
    payload,
  };
}
export function setFullInsuranceExtraService(payload) {
  return {
    type: FULL_INSURANCE,
    payload,
  };
}
export function setIsUnlimitedKM(payload) {
  return {
    type: UNLIMITED_KM,
    payload,
  };
}
export function setCarReturnInAnotherBranch(payload) {
  return {
    type: CAR_RETURN_IN_ANOTHER_BRANCH,
    payload,
  };
}
export function setIsBarq(payload) {
  return {
    type: IS_BARQ,
    payload,
  };
}
export function setIsInstantConfirmation(payload) {
  return {
    type: IS_INSTANT_CONFIRMATION,
    payload,
  };
}
export function setYear(payload) {
  return {
    type: SEARCH_YEAR,
    payload,
  };
}
export function setDailyPrice(payload) {
  return {
    type: SEARCH_DailyPrice,
    payload,
  };
}
export function setfiltersUsedInSearch(payload) {
  return {
    type: "FILTERS_IN_SEARCH",
    payload,
  };
}
export function setIsNewCar(payload) {
  return {
    type: "IS_NEW_CAR",
    payload,
  };
}
export function clearFilter() {
  return {
    type: CLEAR_FILTERS,
  };
}

export function setAreasList(payload) {
  return {
    type: AREAS_LIST,
    payload,
  };
}
