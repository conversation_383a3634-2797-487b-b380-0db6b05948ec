/* eslint-disable @next/next/no-img-element */
import { Switch } from "@material-ui/core";
import { useRouter } from "next/router";
import { memo } from "react";
import { RootStateOrAny, useSelector } from "react-redux";
import styled from "styled-components";

const Div = styled.div`
  background: white;
  border: solid 0px white;
  border-radius: 12px;
  padding: 20px;
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  img {
    padding: 0 10px;
  }
  .setting {
    padding: 3px;
    border-radius: 20px;
    background: #f8f8f8;
    color: #6d6d6d;
    padding: 7px 15px 5px 15px;
  }
`;

function PartenersCard({ logo, setting, checked, setChecked }) {
  const { profile } =
    useSelector<RootStateOrAny>((state) => state.user) || ({} as any);
  const { push, asPath } = useRouter();

  return (
    <Div>
      <div>
        <img src={logo} alt="partener-logo" />
      </div>
      <div className="setting">
        <div>{setting}</div>
      </div>

      <Switch
        checked={checked}
        onChange={(e) => {
          if (!profile?.isCustomerProfileCompleted) {
            push(`/my-account?route=${asPath}`);
          } else {
          }
          setChecked(e.target.checked);
        }}
        inputProps={{ "aria-label": "controlled" }}
      />
    </Div>
  );
}

export default memo(PartenersCard);
