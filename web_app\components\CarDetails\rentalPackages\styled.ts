import styled from "styled-components";

export const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  margin-top: 24px;
  .seperate-line {
    height: 45px;
    width: 1px;
    background: var(--color-11);
  }
  div.divider:last-child {
    border: none !important;
  }
  .packages-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 14px;
    > div {
      background-color: var(--color-21);
      border-radius: var(--radius-3);
      overflow: hidden;
      padding: 16px 0px;
      position: relative;
      min-height: 85px;
    }
  }
  .badge {
    width: 100%;
    display: flex;
    justify-content: start;
    position: absolute;
    bottom: -15px;
    span {
      font-size: 0.7rem;
      color: var(--color-2);
      display: inline-block;
      border: solid 1px var(--color-2);
      padding: ${(props) =>
        props.lang == "en" ? "5px 8px 20px 8px" : "5px 0 20px 0"};
      border-radius: ${(props) =>
        props.lang == "en"
          ? "0 var(--radius-3) var(--radius-3) 0"
          : "var(--radius-3) 0 0 var(--radius-3)"};

      display: inline-block;
      min-width: 60px;
      margin-left: ${(props) => (props.lang == "en" ? "-6px" : "0px")};
      margin-right: ${(props) => (props.lang == "ar" ? "-6px" : "0px")};
      &.high-req {
        background-color: var(--color-2);
        color: var(--color-4);
        padding: 5px 12px 20px 12px;
      }
      &.smallest-rate {
        background-color: #6fcfa1;
        color: var(--color-4);
        padding: 5px 12px 20px 12px;
        position: relative;
        z-index: 9;
        border: 0 !important;
      }
    }
  }
`;
