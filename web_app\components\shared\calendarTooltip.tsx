import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import styled from "styled-components";
import Tooltip from "./tooltip";
import { Switch, ClickAwayListener } from "@material-ui/core";
import DateTimePicker from "components/Home/dateTimePicker";
import SingleDayCalendar from "./singleDayCalendar";
import groupArray from "group-array";
import { convertToEnglishNumbers } from "utilities/helpers";
import moment from "moment";
import { DateObject } from "react-multi-date-picker";

const SwitchCalendarDiv = styled.div`
  display: flex;
  justify-content: flex-start !important;
  flex-direction: "row";
`;

export default function CalendarTooltip({
  isOpen,
  setIsOpen,
  setChanged,
  isSingleDaySelection,
  minDate,
  maxDate,
  hidePickUpTime,
  hideReturnTime,
  isSingleSelection,
  days,
  setDays,
  time,
  setTime,
  handleButtonClick,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  switchChecked?: boolean;
  setChanged?: React.Dispatch<React.SetStateAction<boolean>>;
  minDate?: DateObject;
  maxDate?: DateObject;
  hidePickUpTime?: boolean;
  hideReturnTime?: boolean;
  isSingleSelection?: boolean;
  isSingleDaySelection?: boolean;
  days?: any;
  setDays?: React.Dispatch<React.SetStateAction<any>>;
  time?: any;
  setTime?: React.Dispatch<React.SetStateAction<any>>;
  handleButtonClick?: () => void;
}) {
  const [switchChecked, setSwitchChecked] = useState(false);
  const { i18n, t } = useTranslation();
  const { car_data, deliveryType } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const [pickUpShifts, setPickupShifts] = useState();
  const [dropOffShifts, setDropOffShifts] = useState();
  const [dayShifts, setDayShifts] = useState();

  function generateAvailableShifts(
    reservation_days_start,
    reservation_days_end,
    days
  ) {
    // Helper function to filter and group working days by 'weekDay'.
    const filterAndGroupWorkingDays = (data, key) =>
      data?.filter((i) => i.isOn).length
        ? Object.values(
            groupArray(
              data.filter((i) => i.isOn),
              key
            )
          )
        : [];

    // Helper function to generate time slots in half-hour increments

    const generateTimeSlots = (shift) => {
      const slots = [];
      const startHour = Number(shift.startTime.substr(0, 2));
      const endHour = Number(shift.endTime.substr(0, 2));
      const endMinutes = Number(shift.endTime.substr(3));
      const pickupdateNotToday =
        (reservation_days_start &&
          moment(reservation_days_start.format(), "YYYY/MM/DD").diff(
            moment(moment().format("YYYY/MM/DD"), "YYYY/MM/DD"),
            "days"
          ) > 0) ||
        days;
      const nowPlusTwoHours = moment().add(2, "hours"); // Get the time two hours from now

      for (let hour = startHour; hour <= endHour; hour++) {
        // First slot (on the hour)
        let slot = moment({ hour }).format("HH:mm");
        if (pickupdateNotToday) {
          slots.push(slot);
        } else {
          if (moment(slot, "HH:mm").isAfter(nowPlusTwoHours)) {
            slots.push(slot);
          }
        }

        // Second slot (half past the hour), if not the last hour or if endMinutes >= 30
        if (hour !== endHour || endMinutes >= 30) {
          slot = moment({ hour, minute: 30 }).format("HH:mm");
          if (pickupdateNotToday) {
            slots.push(slot);
          } else {
            if (moment(slot, "HH:mm").isAfter(nowPlusTwoHours)) {
              slots.push(slot);
            }
          }
        }
      }

      // Adjusting for slots at the boundary conditions
      if (endMinutes === 0) slots.pop(); // Remove the last slot if endMinutes == 0
      if (endMinutes > 30) {
        // Add an extra slot if endMinutes > 30
        let slot = moment({ hour: endHour + 1 }).format("HH:mm");
        if (pickupdateNotToday) {
          slots.push(slot);
        } else {
          if (moment(slot, "HH:mm").isAfter(nowPlusTwoHours)) {
            slots.push(slot);
          }
        }
      }

      return slots;
    };

    // Helper function to process and set shifts based on available shifts.
    const processAndSetShifts = (availableShifts, setShifts) => {
      if (availableShifts?.length) {
        const shiftsOptions = availableShifts.flatMap((shift) =>
          generateTimeSlots(shift)
        );
        const uniqueShiftsOptions = shiftsOptions.filter(
          (item, index, array) => array.indexOf(item) === index
        );
        setShifts(uniqueShiftsOptions);
      } else {
        setShifts([]);
      }
    };

    const branchWorkingDays = filterAndGroupWorkingDays(
      car_data?.branch?.branchWorkingDays,
      "weekDay"
    );
    const deliveryBranchWorkingDays = filterAndGroupWorkingDays(
      car_data?.branch?.deliveryBranchWorkingDays,
      "weekDay"
    );

    // Determine the shifts based on the day index.
    const getShiftsForDay = (dayIndex, isDelivery) =>
      (isDelivery ? deliveryBranchWorkingDays : branchWorkingDays).find(
        (i) => i?.[0]?.weekDay == dayIndex
      );

    if (!days) {
      const pickUpDayIndex = moment(
        convertToEnglishNumbers(reservation_days_start.format()),
        "YYYY/MM/DD"
      ).day();
      const dropOffDayIndex = moment(
        convertToEnglishNumbers(reservation_days_end.format()),
        "YYYY/MM/DD"
      ).day();

      processAndSetShifts(
        getShiftsForDay(pickUpDayIndex, deliveryType !== "no_delivery"),
        setPickupShifts
      );
      processAndSetShifts(
        getShiftsForDay(dropOffDayIndex, deliveryType !== "no_delivery"),
        setDropOffShifts
      );
    } else {
      const dayIndex = moment(
        convertToEnglishNumbers(days.format()),
        "YYYY/MM/DD"
      ).day();
      processAndSetShifts(
        getShiftsForDay(dayIndex, deliveryType !== "no_delivery"),
        setDayShifts
      );
    }
  }

  return (
    <ClickAwayListener
      mouseEvent="onMouseDown"
      touchEvent="onTouchStart"
      onClickAway={() => {
        setIsOpen(false);
      }}
    >
      <div
        className="tooltip-wrap calendar"
        style={{
          transform:
            i18n.language === "en" ? "translateX(-50%)" : "translateX(50%)",
          zIndex: 9,
          top: 0,
        }}
      >
        <Tooltip
          isOpen={isOpen}
          // position={i18n.language === "en" ? "left" : "right"}
        >
          {!isSingleSelection ? (
            <DateTimePicker
              {...{
                settoolTipOpen: setIsOpen,
                switchChecked,
                setChanged,
                pickUpShifts,
                dropOffShifts,
                generateAvailableShifts,
                minDate,
                maxDate,
                hidePickUpTime,
                hideReturnTime,
                isSingleDaySelection,
              }}
            />
          ) : (
            <SingleDayCalendar
              dayShifts={dayShifts}
              switchChecked={switchChecked}
              generateAvailableShifts={generateAvailableShifts}
              days={days}
              setDays={setDays}
              time={time}
              setTime={setTime}
              handleButtonClick={handleButtonClick}
              settoolTipOpen={setIsOpen}
              minDate={minDate}
              maxDate={maxDate}
            />
          )}
          <SwitchCalendarDiv>
            <Switch
              checked={switchChecked}
              onChange={() => setSwitchChecked(!switchChecked)}
              inputProps={{ "aria-label": "controlled" }}
            />
            <p className="color-4" style={{ transform: "translateY(5px)" }}>
              {t("Hijri Calendar") as string}
            </p>
          </SwitchCalendarDiv>
        </Tooltip>
      </div>
    </ClickAwayListener>
  );
}
