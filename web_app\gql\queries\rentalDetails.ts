import { gql } from "@apollo/client";
const Bussiness_Rental_Details = gql`
  query businessRentalDetails($id: ID!) {
    businessRentalDetails(id: $id) {
      acceptedOffer {
        additionalKilometer
        allyClass
        allyCompanyArName
        allyCompanyEnName
        allyCompanyId
        allyCompanyName
        allyRate

        businessRentalId
        carInsuranceFull
        carInsuranceStandard
        createdAt
        id
        kilometerPerMonth
        offerPrice
        policyAndConditions
        rate
        status
        statusLocalized
      }
      additionalNotes
      allyClass
      allyCompanyId
      allyRate
      arBusinessActivity
      arMakeName
      arModelName
      arPickUpCityName
      bookingNo
      businessActivityId
      businessActivityName
      businessCancelledReasons {
        arBody
        body
        enBody
        id
      }
      businessClosedReasons {
        arBody
        body
        enBody
        id
      }
      businessRentalOffers {
        additionalKilometer
        allyClass
        allyCompanyArName
        allyCompanyEnName
        allyCompanyId
        allyCompanyName
        allyRate

        businessRentalId
        carInsuranceFull
        carInsuranceStandard
        createdAt
        id
        kilometerPerMonth
        offerPrice
        policyAndConditions
        rate
        status
        statusLocalized
      }
      cancelledAt
      cancelledReason
      carImage
      carModelId
      carVersionId
      closedAt
      closedReason
      commercialRegistrationCertificate
      commercialRegistrationNo
      companyEmail
      companyName
      companyPhone
      createdAt
      customerMobile
      customerName
      customerNid
      dropOffDatetime
      enBusinessActivity
      enMakeName
      enModelName
      enPickUpCityName
      id
      insuranceId
      insuranceName
      invoicePic
      invoicedAt
      makeId
      makeName
      modelName
      newGrandTotal
      numberOfCars
      numberOfMonths
      otherCarName
      phone
      pickUpCityId
      pickUpCityName
      pickUpDatetime
      pricePerMonth
      status
      statusLocalized
      totalBookingPrice
      userId
      year
    }
  }
`;

export { Bussiness_Rental_Details };
