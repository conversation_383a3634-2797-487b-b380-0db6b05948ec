/* eslint-disable react-hooks/exhaustive-deps */
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { memo, useState } from "react";
import { useRouter } from "next/router";

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  padding: 20px;
  margin-bottom: 24px;
  .badge {
    background-color: var(--color-18);
    color: var(--color-16);
    border-radius: var(--radius-3);
    padding: 10px 7px 7px 7px;
  }
`;

const PaymentOrder = ({ paymentOrder }) => {
  //Hooks
  const { t } = useTranslation();
  const router = useRouter();

  return (
    <>
      <Div>
        <div className="d-flex gap-5px mb-3">
          <span className="bold">{t("New Payment Order") as string}</span>
        </div>

        <>
          <div className="d-flex justify-content-between mt-2 mb-2">
            <div>{t("Type") as string}</div>
            <div className="badge">{t(paymentOrder.orderType) as any}</div>
          </div>
          <div className="d-flex justify-content-between mt-2 mb-2">
            <div>{t("Paid Amount") as string}</div>
            <div className="">{t(paymentOrder.totalPaidAmount) as any}</div>
          </div>
          <div className="d-flex justify-content-between mt-2 mb-2">
            <div>{t("Remaining Due") as string}</div>
            <div className="">{t(paymentOrder.totalRemainingDue) as any}</div>
          </div>
          <div className="d-flex justify-content-between mt-2 mb-2">
            <div>{t("Grand Total +vat") as string}</div>
            <div className="">{t(paymentOrder.totalBookingPrice) as any}</div>
          </div>
        </>
      </Div>
    </>
  );
};
export default memo(PaymentOrder);
