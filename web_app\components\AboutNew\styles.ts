import styled from "styled-components";
import { Box, Container, Grid } from "@material-ui/core";

// Main container styles
export const AboutWrapper = styled.div`
  position: relative;
  width: 100%;
  overflow: hidden;
`;

export const AboutBackground = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f8f8f8;
  z-index: 0;
`;

export const AboutContainer = styled(Container)`
  position: relative;
  z-index: 1;
  padding: 60px 0;
  display: flex !important;
  flex-direction: column;
  gap: 4rem;
  @media (max-width: 768px) {
    padding: 40px 0;
  }
`;

export const StoryIcon = styled.div`
  position: absolute;
  top: 40px;
  right: ${(props) => (props.isRtl ? "auto" : "40px")};
  left: ${(props) => (props.isRtl ? "40px" : "auto")};
  width: 60px;
  height: 60px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  @media (max-width: 768px) {
    top: 20px;
    right: ${(props) => (props.isRtl ? "auto" : "20px")};
    left: ${(props) => (props.isRtl ? "20px" : "auto")};
    width: 40px;
    height: 40px;
  }
`;

export const Content = styled.div`
  p:first-child {
    margin-bottom: 1rem;
    line-height: 1.6;
  }
  p:nth-child(2) {
    margin-top: 2rem;
    font-weight: bold;
  }
`;

export const AboutusWarapper = styled.div`
  background-color: #fff;
  position: relative;
  padding: 3rem 3.5rem;
  overflow: hidden;
  border-radius: 30px;
  img {
    position: absolute;
    top: 0;
    left: ${(props) => (props.lang === "ar" ? 0 : "")};
    right: ${(props) => (props.lang === "ar" ? "" : 0)};
    height: 100%;

    @media (max-width: 768px) {
      display: none;
    }
  }
  position: relative;
`;
export const StatusWrapper = styled(Box)`
  width: 70% !important;
  @media (max-width: 900px) {
    width: 90% !important;
    position: absolute;
    bottom: -10px;
    padding: 20px;
    left: 1rem;
    gap: 10px;
  }

  .stats {
    font-size: 2.5rem;
    font-weight: bold;
    display: flex;
    flex-direction: column;
    direction: ltr;
    @media (max-width: 768px) {
      font-size: 1.1rem;
      text-align: center;
    }
    .plus {
      color: var(--color-2);
    }
    span:nth-child(2) {
      font-size: 0.8rem;
      font-weight: normal;
      color: #b7b7b7;
    }
  }
`;

export const StyledGrid = styled(Grid)`
  .text-numbered {
    position: relative;
    span {
      position: absolute;
      top: -3rem;
      left: ${(props) => (props.lang === "ar" ? "auto" : 0)};
      right: ${(props) => (props.lang === "ar" ? 0 : "auto")};
      font-size: 6rem;
      font-weight: bold;
      color: #ededed;
      z-index: -1;
    }
  }
`;
