import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { CircularProgress } from "@material-ui/core";

interface ProgressiveImageProps {
  src: string;
  placeholder?: string;
  alt?: string;
  onLoad?: () => void;
  style?: React.CSSProperties;
  className?: string;
}

const ImageContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
`;

const StyledImage = styled.img<{ isLoaded: boolean }>`
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: ${(props) => (props.isLoaded ? 1 : 0)};
  transition: opacity 0.3s ease-in-out;
`;

const LoadingContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1);
`;

const ProgressiveImage: React.FC<ProgressiveImageProps> = ({
  src,
  placeholder = "",
  alt = "image",
  onLoad,
  style,
  className,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder || src);

  useEffect(() => {
    // Reset loading state when src changes
    setIsLoaded(false);

    // Start with placeholder if available
    if (placeholder) {
      setCurrentSrc(placeholder);
    }

    // Preload the main image
    const img = new Image();
    img.src = src;
    img.onload = () => {
      setCurrentSrc(src);
      setIsLoaded(true);
      if (onLoad) onLoad();
    };
  }, [src, placeholder, onLoad]);

  return (
    <ImageContainer className={className} style={style}>
      <StyledImage src={currentSrc} alt={alt} isLoaded={isLoaded} />
      {!isLoaded && (
        <LoadingContainer>
          <CircularProgress style={{ color: "white" }} />
        </LoadingContainer>
      )}
    </ImageContainer>
  );
};

export default ProgressiveImage;
