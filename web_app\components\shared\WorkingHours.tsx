/* eslint-disable react-hooks/exhaustive-deps */
import { KeyboardArrowLeft, KeyboardArrowRight } from "@material-ui/icons";
import Popup from "components/shared/popup";
import groupArray from "group-array";
import { useRouter } from "next/router";
import moment from "moment";
import { memo, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import {
  setShiftsToday,
  setWorkingHours,
  setWorkingHoursModalOpen,
} from "store/cars/action";
import styled from "styled-components";

/* eslint-disable @next/next/no-img-element */

const Div = styled.div`
  position: relative;
  border-radius: var(--radius-2);
  padding: 20px 20px;
  margin: 24px 0;
  @media (max-width: 768px) {
    padding: 10px;
  }
  .more {
    right: ${(props) => (props.language === "en" ? "0px" : "")};
    left: ${(props) => (props.language === "ar" ? "0px" : "")};
  }
`;

function WorkingHours() {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();

  function setIsPopupOpen(e) {
    setTimeout(() => {
      dispatch(setWorkingHoursModalOpen(e));
    });
  }

  const { deliveryType, is_working_hours_modal_open, car_data } =
    useSelector((state: RootStateOrAny) => state.cars) || {};

  const todayWeekDay = moment().day();

  const branchWorkingDays = car_data?.branch?.branchWorkingDays?.filter(
    (i) => i.isOn === true
  );
  const bookingId = router?.query.bookingId;
  const rentalData = useSelector((state: RootStateOrAny) => state.booking);

  const branchWorkingDaysArr = branchWorkingDays?.length
    ? Object.values(groupArray(branchWorkingDays, "weekDay"))
    : [];

  const deliveryBranchWorkingDaysArr =
    car_data?.branch?.deliveryBranchWorkingDays?.filter((i) => i.isOn === true);

  const deliveryBranchWorkingDays = deliveryBranchWorkingDaysArr?.length
    ? Object.values(groupArray(deliveryBranchWorkingDaysArr, "weekDay"))
    : [];

  const groupingArr: any =
    bookingId && rentalData?.rentalDetails
      ? rentalData.rentalDetails.deliverType == "no_delivery"
        ? branchWorkingDaysArr
        : deliveryBranchWorkingDays
      : deliveryType === "no_delivery"
      ? branchWorkingDaysArr
      : deliveryBranchWorkingDays;

  const shiftsToday = groupingArr
    ?.find((i: any) => i?.find((i: any) => i.weekDay === todayWeekDay))
    ?.filter((i: any) => i.isOn);

  useEffect(() => {
    dispatch(setShiftsToday(shiftsToday));
    dispatch(setWorkingHours(groupingArr));
  }, []);

  useEffect(() => {
    dispatch(setShiftsToday(shiftsToday));
    dispatch(setWorkingHours(groupingArr));
  }, [deliveryType]);

  return (
    <Div language={i18n.language}>
      <Popup
        isOpen={is_working_hours_modal_open}
        setIsOpen={setIsPopupOpen}
        title={t("Working Hours")}
      >
        {groupingArr?.length
          ? groupingArr.map((item) => {
              return (
                <div
                  key={item[0].id}
                  className="d-flex justify-content-between align-items-center border-bottom mb-1 mt-1 pb-1 gap-30px"
                >
                  <h6
                    className={todayWeekDay == item[0].weekDay ? "color-3" : ""}
                  >
                    {item[0].weekDayString}
                  </h6>
                  <div>
                    {item.map((singleDayObj) => {
                      return (
                        <div key={singleDayObj.id} className="d-flex gap-15px">
                          {singleDayObj.is24hAvailable ? (
                            <span>{t("24 hours") as string}</span>
                          ) : (
                            <>
                              <div>
                                <span className="color-2">
                                  {t("From") as string}:{" "}
                                </span>
                                {moment(singleDayObj.startTime, "HH:mm").format(
                                  "hh:mm A"
                                )}
                              </div>
                              <div>
                                <span className="color-2">
                                  {t("To") as string}:{" "}
                                </span>
                                {moment(singleDayObj.endTime, "HH:mm").format(
                                  "hh:mm A"
                                )}
                              </div>
                            </>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })
          : null}
      </Popup>
      <div
        className="d-flex align-items-center justify-content-between cursor-pointer"
        onClick={() => {
          setIsPopupOpen(true);
        }}
      >
        <div className="d-flex align-items-center justify-content-between cursor-pointer flex-wrap">
          <div className="d-flex gap-10px align-items-center">
            <img src="/assets/images/time.svg" alt="working-hours" />
            <h6 className="bold">{t("Working Hours") as string}</h6>
          </div>

          {shiftsToday?.length ? (
            <div
              className="d-flex gap-5px align-items-baseline mx-2"
              style={{ marginTop: "-5px" }}
            >
              <span>{t("Today") as string}:</span>
              <div className="d-flex flex-column">
                {shiftsToday?.map(
                  ({ id, startTime, endTime, is24hAvailable }) => {
                    return (
                      <div key={id} className="d-flex gap-10px">
                        {is24hAvailable ? (
                          <span>{`${t("Open")} ${t("24 hours")}`}</span>
                        ) : (
                          <>
                            <div>
                              <span className="color-2 mx-1 d-inline">
                                {t("From") as string}
                              </span>
                              {moment(startTime, "HH:mm").format("hh:mm A")}
                            </div>
                            <div>
                              <span className="color-2 mx-1 d-inline">
                                {t("To") as string}
                              </span>
                              {moment(endTime, "HH:mm").format("hh:mm A")}
                            </div>
                          </>
                        )}
                      </div>
                    );
                  }
                )}
              </div>
            </div>
          ) : (
            <>
              {t("Today")}:{" "}
              <span className="bold color-9">{`${t("Closed")}`}</span>
            </>
          )}
        </div>
        <div className="cursor-pointer d-flex align-items-center more">
          <span
            className="color-3 mx-2 font-16px bold"
            style={{ marginTop: "-2px" }}
          >
            {t("More") as string}
          </span>
          <img
            src="/assets/icons/blueMoreArrowRounded.svg"
            alt="more arrow"
            style={{
              transform: i18n.language === "en" ? "rotate(180deg)" : "",
            }}
          />
        </div>
      </div>
    </Div>
  );
}

export default memo(WorkingHours);
