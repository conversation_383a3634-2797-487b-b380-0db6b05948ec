import styled from "styled-components";

export const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  margin: ${(props) => (props.isRentToOwn ? "0px 0" : "24px 0")};
  position: relative;

  .button {
    text-align: center;
    background-color: var(--color-3);
    border-radius: var(--radius-2);
    color: var(--color-4);
    padding: 15px 20px 15px 20px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 20px;
  }
  .coupon-text {
    color: black !important;
  }
`;
export const CouponWrapper = styled.div`
  .tooltiptext {
    width: 120px;
    margin-top: 10px;
    background-color: red;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    transition: opacity 0.3s;
    padding: 1px 15px;
  }
  .tooltiptext::before {
    content: "";
    position: absolute;
    top: 23px;
    left: ${(props) => (props.language === "ar" ? null : "10%")};
    right: ${(props) => (props.language === "ar" ? "10%" : null)};
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: red transparent transparent transparent;
    transform: rotateX(180deg);
  }
`;

export const Modal = styled.div`
  section {
    margin-bottom: 20px;

    > div {
      display: flex;
      justify-content: space-between;
      gap: 15px;
      margin-bottom: 5px;
    }
    h5 {
      font-size: 14px;
      color: var(--color-16);
      margin-bottom: 15px;
    }
    .break {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
      gap: 5px;
      align-items: baseline;
      div:last-child {
        width: -webkit-fill-available;
        max-width: fit-content;
      }
    }
  }

  .total {
    background: var(--color-21);
    padding: 20px;
    border-radius: var(--radius-2);
    > div {
      display: flex;
      justify-content: space-between;
      gap: 30px;
      margin-bottom: 5px;
      align-items: baseline;
      div:first-child {
        display: flex;
        gap: 5px;

        @media (max-width: 768px) {
          align-items: baseline;
        }
        h6 {
          font-size: 20px;
          @media (max-width: 768px) {
            font-size: 15px !important;
          }
          font-weight: 600;
        }
        span {
          @media (max-width: 768px) {
            font-size: 13px !important;
          }
        }
      }
      &:last-child {
        .grand-total {
          color: var(--color-3);
        }
      }
    }
  }
`;
