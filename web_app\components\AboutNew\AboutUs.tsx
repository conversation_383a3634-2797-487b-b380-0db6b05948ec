/* eslint-disable @next/next/no-img-element */
import { Box, Grid, Typography } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import {
  AboutContainer,
  AboutusWarapper,
  Content,
  StatusWrapper,
} from "./styles";

function AboutUs() {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.language === "ar";

  return (
    <Box style={{ backgroundColor: "#F8F8F8" }}>
      <AboutContainer>
        <Grid
          container
          spacing={2}
          justifyContent="space-between"
          className="our-story"
        >
          <Grid item xs={12} md="auto">
            <Box className="text-align-localized mt-1">
              <img
                src="/assets/images/about/icons/about.svg"
                alt="Story Icon"
                loading="lazy"
              />
              <Typography
                variant="h3"
                className="bold"
                style={{ fontSize: "1.7rem", fontWeight: 700 }}
              >
                {String(t("aboutus.about.title"))}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={10}>
            <AboutusWarapper lang={i18n.language}>
              <Content
                className="text-align-localized"
                style={{ direction: isRtl ? "rtl" : "ltr", width: "80%" }}
              >
                <Typography
                  variant="body1"
                  dangerouslySetInnerHTML={{
                    __html: t("aboutus.about.content") as string,
                  }}
                />
              </Content>
              <img
                style={{
                  transform: i18n.language === "en" ? "scale(-1, 1)" : "",
                }}
                src="/assets/images/about/logo.svg"
                alt="item-1"
              />
              <StatusWrapper
                className="justify-content-between flex-wrap stats-wrapper"
                style={{ display: "flex" }}
              >
                <Box className="stats">
                  <span>
                    <span className="plus">+</span>110K
                  </span>
                  <span className="text-align-localized">
                    {String(t("aboutus.about.stats.Cars"))}
                  </span>
                </Box>
                <Box className="stats">
                  <span>
                    <span className="plus">+</span>55
                  </span>
                  <span className="text-align-localized">
                    {String(t("aboutus.about.stats.Cities and Airports"))}
                  </span>
                </Box>
                <Box className="stats">
                  <span>
                    <span className="plus">+</span>1.8K
                  </span>
                  <span className="text-align-localized">
                    {String(t("aboutus.about.stats.Rental Offices"))}
                  </span>
                </Box>
              </StatusWrapper>
            </AboutusWarapper>
          </Grid>
        </Grid>
      </AboutContainer>
    </Box>
  );
}

export default AboutUs;
