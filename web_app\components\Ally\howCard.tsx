/* eslint-disable @next/next/no-img-element */
import styled from "styled-components";

const Div = styled.div`
  & {
    display: flex;
    align-items: center;
    gap: 45px;
    padding: 20px;
    background-color: var(--color-4);
    border-radius: var(--radius-2);
    span {
      flex-shrink: 0;
      color: var(--color-1);
      font-size: 38px;
      text-align: center;
      font-weight: 500;
      position: relative;
      z-index: 1;
      width: 115px;
      height: 115px;
      display: grid;
      align-items: center;
      &::after {
        content: "";
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        border-radius: 100%;
        background-color: var(--color-6);
        position: absolute;
        z-index: -1;
      }
    }
    h5 {
      font-weight: 500;
      text-transform: capitalize;
      margin-bottom: 10px !important;
    }
    @media (max-width: 570px) {
      flex-direction: column;
      gap: 15px;
    }
  }
`;

export default function Card({ data }) {
  return (
    <Div>
      <span>{data.count}</span>
      <div>
        <h5>{data.title}</h5>
        <h6>{data.description}</h6>
      </div>
    </Div>
  );
}
