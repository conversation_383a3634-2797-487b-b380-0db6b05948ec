/* eslint-disable @next/next/no-img-element */
import { Box, Grid, Typography } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import { AboutContainer, Content } from "./styles";

function Features() {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.language === "ar";

  return (
    <AboutContainer background="#fff">
      <Grid
        container
        spacing={2}
        justifyContent="space-between"
        className="our-story"
      >
        <Grid item xs={12} md="auto">
          <Box className="text-align-localized mt-1">
            <img
              src="/assets/images/about/icons/tick.svg"
              alt="Story Icon"
              loading="lazy"
            />
            <Typography
              variant="h3"
              className="bold"
              style={{ fontSize: "1.7rem", fontWeight: 700 }}
              dangerouslySetInnerHTML={{
                __html: t("aboutus.features.title") as string,
              }}
            />
          </Box>
        </Grid>
        <Grid item xs={12} md={10} className="pt-3">
          <Grid
            container
            direction="row-reverse"
            justifyContent="space-between"
            spacing={2}
          >
            <Grid item xs={12} md={5} className="my-2 text-align-localized">
              <img
                className="mb-2"
                src="/assets/images/about/icons/f1.svg"
                alt="features"
                width={50}
              />
              <Typography
                variant="body1"
                dangerouslySetInnerHTML={{
                  __html: t("aboutus.features.content1") as string,
                }}
              />
            </Grid>

            <Grid item xs={12} md={5} className="my-2 text-align-localized">
              <img
                className="mb-2"
                src="/assets/images/about/icons/f2.svg"
                alt="features"
                width={50}
              />
              <Typography
                variant="body1"
                dangerouslySetInnerHTML={{
                  __html: t("aboutus.features.content2") as string,
                }}
              />
            </Grid>
            <Grid item xs={12} md={5} className="my-2 text-align-localized">
              <img
                className="mb-2"
                src="/assets/images/about/icons/f3.svg"
                alt="features"
                width={50}
              />
              <Typography
                variant="body1"
                dangerouslySetInnerHTML={{
                  __html: t("aboutus.features.content3") as string,
                }}
              />
            </Grid>
            <Grid item xs={12} md={5} className="my-2 text-align-localized">
              <img
                className="mb-2"
                src="/assets/images/about/icons/f4.svg"
                alt="features"
                width={50}
              />
              <Typography
                variant="body1"
                dangerouslySetInnerHTML={{
                  __html: t("aboutus.features.content4") as string,
                }}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </AboutContainer>
  );
}

export default Features;
