/* eslint-disable @next/next/no-img-element */
import React, { useEffect, useRef, useState } from "react";
import GoogleMapReact from "google-map-react";
import { useDispatch } from "react-redux";
import useGeoLocaiton from "hooks/useGeoLocation";

function Map({ lat, lng, action, geoCode }) {
  //State
  const [map, setMap] = useState();
  const [maps, setMaps] = useState();

  //Store
  const dispatch = useDispatch();
  const { gecodeLatLng } = useGeoLocaiton();

  //Map Props
  const defaultProps = {
    center: {
      lat: lat || 21.403357,
      lng: lng || 39.8170105,
    },
    zoom: 15,
  };
  //variables
  const prevMarkersRef = useRef([]);
  //Functions

  const handleApiLoaded = (gmap, gmaps) => {
    setMap(gmap);
    setMaps(gmaps);
  };
  function createMarker(position, map) {
    return new window.google.maps.Marker({
      position: position,
      map: map,
      draggable: true,
    });
  }
  function clearMarkers(markers) {
    for (let m of markers) {
      m.setMap(null);
    }
  }

  function _onClick(obj) {
    action({
      lat: obj.lat,
      lng: obj.lng,
    });
    if (geoCode) {
      gecodeLatLng(obj.lat, obj.lng).then((response) => {
        dispatch(
          action({
            lat: obj.lat,
            lng: obj.lng,
            name: response,
            address: response,
          })
        );
      });
    }
  }

  useEffect(() => {
    let clean = false;
    if (!clean) {
      clearMarkers(prevMarkersRef.current);
      if (lat && lng && map) {
        const marker = createMarker({ lat: lat, lng: lng }, map);
        prevMarkersRef.current.push(marker);
        map.setCenter({ lat, lng });
      }
    }
    return (clean = true);
  }, [lat, lng, map]);

  return (
    <div
      style={{
        height: "18rem",
        width: "100%",
        minWidth: "100%",
      }}
    >
      <GoogleMapReact
        bootstrapURLKeys={{
          key: process.env.NEXT_PUBLIC_MAP_API,
        }}
        defaultCenter={defaultProps.center}
        defaultZoom={defaultProps.zoom}
        options={{
          gestureHandling: "greedy",
        }}
        draggable={true}
        yesIWantToUseGoogleMapApiInternals
        onGoogleApiLoaded={({ map, maps }) => handleApiLoaded(map, maps)}
        onClick={_onClick}
      ></GoogleMapReact>
    </div>
  );
}

export default Map;
