/* eslint-disable @next/next/no-img-element */
/* eslint-disable react/jsx-key */
import React, { useEffect, useLayoutEffect, useRef } from "react";
import {
  setCarReturnInAnotherBranch,
  setExtraServices,
  setFullInsuranceExtraService,
  setIsUnlimitedKM,
} from "store/search/action";
import Swiper from "components/shared/carousel";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { useQuery } from "@apollo/client";
import { useRouter } from "next/router";
import { ExtraService_Query } from "gql/queries/car";
import { Grid } from "@material-ui/core";

const Div = styled.div`
  position: relative;
  padding: 50px 0;
  margin: 20px 0 30px 0 !important;
  @media (max-width: 900px) {
    margin: 0 !important;
  }

  .section-title {
    text-align: right;
    margin-bottom: 30px;
    z-index: 1;

    h4 {
      font-size: 22px;
      font-weight: bold;
      line-height: 1.2;
      margin: 0;
      color: #333;
      font-weight: 400;
    }

    .highlight {
      /* color: var(--color-2); */
      display: block;
      font-weight: bold;
      margin-top: 5px;
      position: relative;

      &:after {
        content: "";
        position: absolute;
        bottom: -15px;
        right: ${(props) => (props.language === "ar" ? "0" : "auto")};
        left: ${(props) => (props.language === "ar" ? "auto" : "0")};
        width: 20px;
        border-radius: 10px;
        height: 4px;
        background-color: var(--color-2);
      }
    }
  }

  @media (max-width: 900px) {
    .swiper-button-prev:before,
    .swiper-button-next:before {
      background: none !important;
    }
    .swiper-button-next:after {
      transform: translateX(0px) !important;
    }
    .swiper-button-prev:after {
      /* transform: translateX(-7px) !important; */
    }
    .card-service {
      min-width: 150px;
      min-height: 135px !important;
      margin: auto !important;
      h6 {
        font-size: 15px !important;
      }
    }
  }
  @media (min-width: 901px) {
    .extra-services-mobile {
      display: none !important;
    }
  }

  .swiper-wrapper {
    align-items: center;
    min-height: 170px;
    > div {
      min-height: 170px;
    }
  }
  h6 {
    text-align: center !important;
    font-size: 17px;
    line-height: 24px;
    /* margin-top: 15px !important; */
    @media (max-width: 900px) {
      font-size: 17px;
    }
  }
  @media (min-width: 1025px) {
    .swiper-button-prev {
      left: -50px;
    }
    .swiper-button-next {
      right: -50px;
    }
  }
  @media (max-width: 1024px) {
    .swiper-button-prev {
      left: 0;
    }
    .swiper-button-next {
      right: ${(props) => (props.language === "en" ? "-5px" : "")};
      left: ${(props) => (props.language === "ar" ? "-5px" : "")};
    }
  }
  .swiper-button-prev,
  .swiper-button-next {
    color: var(--color-2);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: var(--color-3);
    display: flex;
    justify-content: center;
    align-items: center;

    &:after {
      font-size: 18px;
      transform: none;
      margin: 0;
      font-weight: bold;
    }

    &:before {
      content: none;
    }
  }
  .card-service {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: white;
    padding: 15px;
    border-radius: 20px;
    margin: 0 10px;
    min-height: 170px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    h6 {
      min-height: 50px;
    }
    img {
      height: 60px;
      margin-bottom: 10px;
    }
  }
`;
function ExtraServices(data) {
  const dispatch = useDispatch();
  const router = useRouter();

  return data
    ?.filter((i) => i.isSpecial)
    ?.map((item) => {
      return (
        <div
          className="cursor-pointer card-service"
          style={{ width: "160px", height: "170px" }}
          onClick={() => {
            dispatch(setIsUnlimitedKM(undefined));
            dispatch(setCarReturnInAnotherBranch(undefined));
            if (item.id === "unlimitedKM") {
              dispatch(setIsUnlimitedKM(true));
            } else if (item.id === "carReturnInAnotherBranch") {
              dispatch(setCarReturnInAnotherBranch(true));
            } else if (item.id === "fullInsurance") {
              dispatch(setFullInsuranceExtraService(true));
            } else {
              dispatch(
                setExtraServices([
                  {
                    label: item.title,
                    value: item.id,
                    action: "extraService",
                  },
                ])
              );
            }
            router.push("car-search#car-grid");
          }}
        >
          <img src={item.homepageIconUrl} alt={item.title} />
          <h6
            style={
              data?.[0]?.value == item.id ? { color: "var(--color-3)" } : null
            }
          >
            {item.title}
          </h6>
        </div>
      );
    });
}
function ExtraServicesCarousel() {
  //Hooks
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();

  //Store
  const { extraServices } =
    useSelector((state: RootStateOrAny) => state.search_data?.filters) || {};
  //gQL
  const { data: extraservicesRes, loading } = useQuery(ExtraService_Query, {
    variables: { limit: 500, page: 1, isActive: true, isDisplayed: true },
    fetchPolicy: "cache-first",
  });
  const extraServicesData =
    !loading && extraservicesRes
      ? [
          {
            id: "unlimitedKM",
            title: t("Unlimited Kilometers"),
            homepageIconUrl: "/assets/icons/home/<USER>",
            isSpecial: true,
          },
          {
            id: "carReturnInAnotherBranch",
            title: t("Car Return in Another Branch"),
            homepageIconUrl: "/assets/icons/home/<USER>",
            isSpecial: true,
          },
          {
            id: "fullInsurance",
            title: t("Full Insurance"),
            homepageIconUrl: "/assets/icons/home/<USER>",
            isSpecial: true,
          },
          ...extraservicesRes?.extraServices?.collection,
        ]
      : [];

  const SwiperRef = useRef(styled(Swiper)`
    .swiper-wrapper {
      .swiper-slide {
        > div {
          direction: ${i18n.language === "ar" ? "rtl" : "ltr"};
        }
      }
    }
  `);
  const SwiperTag = SwiperRef.current;
  useEffect(() => {
    // Moved next and prev arrows outside of swiper container for styling
    const next = document.querySelector(
      "#extra-services-carousel .swiper-button-next"
    );
    const prev = document.querySelector(
      "#extra-services-carousel .swiper-button-prev"
    );
    const wrap = document.querySelector(
      "#extra-services-carousel .swiper-wrap"
    );
    wrap.appendChild(next);
    wrap.appendChild(prev);
  }, []);

  return (
    <Div className="mt-0" language={i18n.language}>
      <Grid container justifyContent="space-around" spacing={2}>
        <Grid item xs={12} sm={2} md={2} lg={2}>
          <div className="section-title">
            <h4>
              {t("Search By").toString()}
              <span className="highlight">{t("Extra Service").toString()}</span>
            </h4>
          </div>
        </Grid>
        <Grid item xs={12} sm={10} md={10} lg={10}>
          <div id="extra-services-carousel">
            <SwiperTag
              spaceBetween={20}
              slidesPerView={2}
              breakpoints={{
                320: {
                  slidesPerView: 2,
                  spaceBetween: 20,
                },
                900: {
                  slidesPerView: 3.25,
                  spaceBetween: 30,
                },
                1024: {
                  slidesPerView: 4.5,
                  spaceBetween: 0,
                },
              }}
              navigation
              onSwiper={(swiper) => null}
              onSlideChange={() => null}
              slides={ExtraServices(extraServicesData)}
              loop={false}
            />
          </div>
        </Grid>
      </Grid>
    </Div>
  );
}

export default React.memo(ExtraServicesCarousel);
