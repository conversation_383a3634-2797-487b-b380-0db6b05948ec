/* eslint-disable @next/next/no-img-element */
/* eslint-disable react-hooks/exhaustive-deps */
import {
  Container,
  Grid,
  IconButton,
  InputAdornment,
  TextField,
} from "@material-ui/core";
import Layout from "components/shared/layout";
import styled from "styled-components";
import Card from "./card";
import { Pagination } from "@material-ui/lab";
import { useTranslation } from "react-i18next";
import SearchBox from "components/Home/searchBox";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { carsListCurrentPageAction } from "store/cars/action";
import { useCarsList } from "hooks/useCarList";
import SearchIcon from "@material-ui/icons/Search";
import {
  searchByMakeModel,
  setCarReturnInAnotherBranch,
  setDailyPrice,
  setExtraServices,
  setFullInsuranceExtraService,
  setInsuranceType,
  setIsBarq,
  setIsInstantConfirmation,
  setIsNewCar,
  setIsUnlimitedKM,
  setMake,
  setModal,
  setTransmission,
  setUserPickupAddressAction,
  setVehiclesType,
  setYear,
  setfiltersUsedInSearch,
} from "store/search/action";
import RequestLoader from "components/shared/requestLoader";
import { useEffect, useState } from "react";
import { Close } from "@material-ui/icons";
import RentToOwnInfoModal from "./rentToOwn/RentToOwnInfoModal";
import ToggleTabs from "components/shared/ToggleTabs";
import { MyRentalsCurrentPageAction } from "store/boooking/action";
import RentToOwnSearchBox from "./rentToOwn/SearchBox";
import { useRouter } from "next/router";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  padding-top: 45px;
  h1 {
    @media (max-width: 900px) {
      margin-bottom: 15px !important;
    }
  }
`;

const FloatingInfo = styled.div`
  position: absolute;
  top: 148px !important;
  right: ${(props) => (props.lang === "ar" ? "0" : "")};
  left: ${(props) => (props.lang === "en" ? "0" : "")};
  cursor: pointer;
  background-color: var(--color-2);
  display: flex;
  gap: 2px;
  align-items: center;
  padding: ${(props) =>
    props.lang === "ar" ? "5px 10px 12px 20px" : "5px 20px 12px 10px"};
  font-weight: bold;
  border-radius: ${(props) =>
    props.lang === "ar" ? "40px 0px 0 40px" : "0px 40px 40px 0px"};
  img {
    transform: ${(props) =>
      props.lang === "ar" ? "translateY(5px)" : "translateY(2px)"};
  }
  span {
    color: white;
    width: 50px;
    font-size: 16px;
    text-align: start;
  }
  @media (max-width: 768px) {
    span {
      font-size: 14px;
    }
    top: 65px !important;
  }
`;

const SearchMakeModel = styled.div`
  label:not(.Mui-focused) {
    right: ${(props) => (props.lang === "ar" ? "30px" : "")};
  }
  .inputRounded {
    background: white;
    border-radius: 50px;
    fieldset {
      border: none;
    }
    input {
      padding: 14px;
    }
  }
`;

export default function CarSearch({ isRentToOwn }: { isRentToOwn?: boolean }) {
  //Variables
  const {
    filters_used_in_search,
    filters,
    user_address,
    isUnlimited,
    canHandoverInBranch,
    isInstantConfirmation,
    isBarq,
    date_time,
    search_makeModel,
    fullInsurance,
  } = useSelector((state: RootStateOrAny) => state.search_data) || {};
  //Hooks
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const { query, push } = useRouter();
  const { getCarList, loadingCarsList } = useCarsList({
    isRentToOwn: isRentToOwn,
  });
  const [refetch, setRefetch] = useState(false);

  //State
  const [rentToOwnInfoModalOpen, setRentToOwnInfoModalOpen] = useState(false);

  //Store
  const carsList = useSelector(
    (state: RootStateOrAny) => state?.cars?.carsList
  );
  const [fetchCars, setFetchCars] = useState(false);

  const carsListCurrentPage = useSelector(
    (state: RootStateOrAny) => state?.cars?.carsListCurrentPage
  );
  const { isNewCar } = useSelector(
    (state: RootStateOrAny) => state?.search_data
  );

  //Functions
  const handlePageChange = (event: any, value: any) => {
    dispatch(carsListCurrentPageAction(Number(value)));
    setFetchCars(!fetchCars);
    document.querySelector("#car-grid").scrollIntoView();
  };

  useEffect(() => {
    if (refetch) {
      getCarList(undefined);
      dispatch(setfiltersUsedInSearch(filters));
      setRefetch(false);
    }
  }, [refetch]);

  function TagCMP({ key, label, item, action, isArrayItem }) {
    return (
      <div
        key={key}
        style={{
          fontSize: "14px",
          padding: "7px 14px 10px 14px",
          borderRadius: "20px",
          fontWeight: "bold",
          background: "var(--color-3)",
        }}
        className="d-flex align-items-center justify-content-center gap-10px text-white"
      >
        <span style={{ direction: i18n.language === "en" ? "rtl" : "ltr" }}>
          {label}
        </span>
        <label title={t("Delete")}>
          <Close
            style={{ fontSize: "16px", cursor: "pointer" }}
            onClick={() => {
              if (item?.actionFunction) return item.actionFunction();
              if (action) {
                if (!isArrayItem) {
                  dispatch(action(item?.actionDefaultValue));
                } else {
                  dispatch(action(item?.filter((i) => i.label != label)));
                }
              }
              setRefetch(!refetch);
            }}
          />
        </label>
      </div>
    );
  }

  const dispatchingActions = {
    make: setMake,
    model: setModal,
    year: setYear,
    extraService: setExtraServices,
    insurance: setInsuranceType,
    dailyPrice: setDailyPrice,
    vehicle: setVehiclesType,
    transmission: setTransmission,
    unlimited: setIsUnlimitedKM,
    handover: setCarReturnInAnotherBranch,
    instant: setIsInstantConfirmation,
    isBarq: setIsBarq,
    airport: setUserPickupAddressAction,
    fullInurance: setFullInsuranceExtraService,
  };

  function FilterTagsGenerator({ payload }): any {
    const cloneObj = { ...payload };
    if (cloneObj?.yearTo) delete cloneObj.yearTo;

    if (cloneObj?.dailyPriceFrom && cloneObj?.dailyPriceTo) {
      cloneObj.dailyPrice = {
        label: `${cloneObj.dailyPriceFrom.label} <RiyalSymbol /> ~ ${cloneObj.dailyPriceTo.label} <RiyalSymbol />`,
        action: "dailyPrice",
      };
      delete cloneObj.dailyPriceFrom;
      delete cloneObj.dailyPriceTo;
    }

    return Object?.values(cloneObj)?.map((item: any) => {
      if (item?.length) {
        return item?.map((i: any) => {
          return (
            <TagCMP
              key={i}
              label={i.label}
              item={item}
              action={
                !i?.actionFunction ? dispatchingActions[i.action] : undefined
              }
              isArrayItem={true}
            />
          );
        });
      }
      if (item && item?.label && item?.id != -1) {
        return (
          <TagCMP
            key={item}
            item={item}
            label={item.label}
            action={
              !item?.actionFunction
                ? dispatchingActions[item.action]
                : undefined
            }
            isArrayItem={undefined}
          />
        );
      }
      return <></>;
    });
  }

  useEffect(() => {
    if (user_address?.pick_up && user_address?.return && date_time) {
      getCarList(undefined);
    }
  }, [fetchCars]);
  return (
    <Layout>
      {isRentToOwn ? (
        <>
          <FloatingInfo
            lang={i18n.language}
            onClick={() => {
              setRentToOwnInfoModalOpen(true);
            }}
          >
            <span>{t("Learn More") as string}</span>
            <img src="/assets/icons/infoWhite.svg" alt="info icon" />
          </FloatingInfo>
          <RentToOwnInfoModal
            {...{ rentToOwnInfoModalOpen, setRentToOwnInfoModalOpen }}
          />
        </>
      ) : null}

      <RequestLoader loading={loadingCarsList} />
      <Div className="grey-background pb-5">
        {isRentToOwn ? (
          <div className="mb-5" id="search-box">
            <RentToOwnSearchBox setFetchCars={setFetchCars} />
          </div>
        ) : null}
        <Container id="car-grid">
          {!isRentToOwn ? (
            <div className="my-4" id="search-box">
              <SearchBox
                fetchCars={fetchCars}
                setFetchCars={setFetchCars}
                home={undefined}
              />
            </div>
          ) : null}
          <div
            className="d-lg-flex justify-content-between align-items-center mb-2"
            style={{ overflow: "hidden" }}
          >
            <h1 className="font-27px bold`">{t("Search Results") as string}</h1>
            {isRentToOwn ? (
              <ToggleTabs
                tabs={[
                  {
                    title: t("New") as string,
                    clickAction: () => {
                      dispatch(setIsNewCar(true));
                      dispatch(MyRentalsCurrentPageAction(1));
                      dispatch(setIsNewCar(true));
                      setRefetch(true);
                    },
                    routerQuery: "new",
                  },
                  {
                    title: t("Used") as string,
                    clickAction: () => {
                      dispatch(setIsNewCar(false));
                      dispatch(MyRentalsCurrentPageAction(1));
                      dispatch(setIsNewCar(false));
                      setRefetch(true);
                    },
                    routerQuery: "used",
                  },
                ]}
                activeTab={isNewCar ? "new" : "used"}
              />
            ) : null}
            <SearchMakeModel lang={i18n.language}>
              <TextField
                variant="outlined"
                className="inputRounded"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => {
                          setFetchCars(!fetchCars);
                        }}
                      >
                        <SearchIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                value={search_makeModel}
                onChange={(e) => {
                  dispatch(carsListCurrentPageAction(1));
                  dispatch(searchByMakeModel(e.target.value));
                }}
                onKeyDown={(e) => {
                  if (e.keyCode == 13) {
                    setFetchCars(!fetchCars);
                  }
                }}
              />
            </SearchMakeModel>
          </div>
          <div
            id="filter-tags"
            className="d-flex gap-20px"
            style={{ flexWrap: "wrap" }}
          >
            <FilterTagsGenerator
              payload={{
                isUnlimited: isUnlimited
                  ? {
                      label: t("Unlimited kilometers"),
                      value: isUnlimited,
                      action: "unlimited",
                    }
                  : undefined,
                handOver: canHandoverInBranch
                  ? {
                      label: t("Car Return in Another Branch"),
                      value: canHandoverInBranch,
                      action: "handover",
                    }
                  : undefined,
                fullInsurance: fullInsurance
                  ? {
                      label: t("Full Insurance"),
                      value: fullInsurance,
                      action: "fullInurance",
                    }
                  : undefined,
                isInstantConfirmation: isInstantConfirmation
                  ? {
                      label: t("Instant Confirmation"),
                      value: isInstantConfirmation,
                      action: "instant",
                    }
                  : undefined,
                isBarq: isBarq
                  ? {
                      label: t("Express Delivery"),
                      value: isBarq,
                      action: "isBarq",
                    }
                  : undefined,
                ...filters_used_in_search,
                ...filters,
                airport: {
                  label: user_address?.pick_up?.airport?.name,
                  value: user_address?.pick_up?.airport?.id,
                  action: "airport",
                  actionDefaultValue: {
                    ...user_address?.pick_up,
                    airport: undefined,
                  },
                },
                topDeals: query?.bannerId
                  ? {
                      label: t("Top Deals"),
                      value: query?.bannerId,
                      actionFunction: () => {
                        window.location.href = "car-search#car-grid";
                      },
                    }
                  : undefined,
              }}
            />
          </div>
          <div>
            {carsList?.collection?.length ? (
              <>
                <Grid
                  container
                  alignItems="stretch"
                  spacing={3}
                  className="my-1"
                >
                  {carsList.collection.map((item: any) => {
                    return (
                      <Grid item lg={3} md={4} xs={12} key={item.carId}>
                        <Card t={t} {...item} isRentToOwn={isRentToOwn} />
                      </Grid>
                    );
                  })}
                </Grid>
                <Pagination
                  page={carsListCurrentPage}
                  onChange={handlePageChange}
                  count={carsList.metadata.totalPages}
                  showFirstButton
                  showLastButton
                  className="d-flex justify-content-center"
                />
              </>
            ) : !loadingCarsList &&
              carsList?.collection &&
              carsList?.collection?.length == 0 ? (
              <p className="text-center text-uppercase mt-4">
                {
                  t(
                    "No results - There are no cars found matched your filter"
                  ) as string
                }
              </p>
            ) : null}
          </div>
        </Container>
      </Div>
    </Layout>
  );
}
