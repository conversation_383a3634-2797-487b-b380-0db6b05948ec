import { Container, Grid } from "@material-ui/core";
import Layout from "components/shared/layout";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import Accordion from "@material-ui/core/Accordion";
import AccordionSummary from "@material-ui/core/AccordionSummary";
import AccordionDetails from "@material-ui/core/AccordionDetails";
import Typography from "@material-ui/core/Typography";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
const Div = styled.div`
  padding: 70px 0 50px 0;
  &,
  > div,
  > div > div {
    min-height: calc(100vh - 250px);
  }
`;

export default function MyFAQs({ data }) {
  const { t } = useTranslation();

  return (
    <Layout>
      <Div>
        <Container>
          <Grid container direction="column">
            <h3>{t("FAQ")}</h3>
            {data?.length
              ? data?.map((item, index) => (
                  <Accordion style={{ margin: "10px" }} key={index}>
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls="panel1a-content"
                      id="panel1a-header"
                    >
                      <Typography>{item.question}</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <p>
                        {item.Answer}{" "}
                        {item.link ? (
                          <a style={{ color: "#007bff" }} href={item.link}>
                            {item.link}
                          </a>
                        ) : null}
                      </p>
                    </AccordionDetails>
                  </Accordion>
                ))
              : null}
          </Grid>
        </Container>
      </Div>
    </Layout>
  );
}
