import { <PERSON><PERSON>, <PERSON><PERSON>ield, TextareaAutosize } from "@material-ui/core";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import SocialIcons from "components/shared/socialIcons";
import ToAppPopup from "components/shared/toAppPopup";

const Div = styled.div`
  @media (min-width: 961px) {
    margin-top: 50px;
    padding-bottom: 40px;
  }
  > div {
    direction: ltr;
  }
  .info {
    > div:not(first-child) {
      margin-bottom: 40px;
      h6 {
        color: #4d5156;
        margin-bottom: 15px;
        text-transform: capitalize;
        direction: ${(props) => (props.language === "ar" ? "rtl" : null)};
      }
      p {
        font-size: 27px;
        line-height: 28px;
      }
    }
    .social-icons {
      direction: ${(props) => (props.language === "ar" ? "rtl" : null)};
    }
  }
  .form-wrapper {
    background-color: var(--color-4);
    border-radius: var(--radius-2);
    @media (max-width: 960px) {
      padding: 35px 30px 20px 30px;
    }
    @media (min-width: 961px) {
      padding: 75px 70px 60px 70px;
    }
    h4 {
      text-transform: capitalize;
      font-weight: 400;
    }
    form {
      width: 100%;
      margin-top: 40px;
      & > div {
        width: 100%;
        &:not(last-child) {
          margin-bottom: 35px;
        }
        label {
          font-size: 14px;
          color: #bdc7cb !important;
          text-transform: uppercase;
          font-weight: 400 !important;
          right: ${(props) => (props.language === "ar" ? 0 : null)};
          transform: ${(props) =>
            props.language === "ar"
              ? "translate(75px, 10px) scale(0.75)"
              : null};
        }
        > div {
          background-color: transparent !important;
          border: solid 1px #bdc7cb !important;
          border-radius: var(--radius-1);
          input {
            font-size: 17px;
          }
          .Mui-focused {
            * {
              color: #bdc7cb !important;
            }
          }
        }
      }
      textarea {
        text-align: ${(props) => (props.language === "ar" ? "right" : null)};
      }
    }
    button[type="submit"] {
      width: 100%;
      padding: 10px 0px;
      border: none;
      box-shadow: none;
      border-radius: var(--radius-3);
      background-color: var(--color-3);
      color: var(--color-4);
      font-weight: 500;
    }
  }
`;

export default function Contact() {
  const { t, i18n } = useTranslation();
  const [isOpenToAppPopup, setIsOpenToAppPopup] = useState<boolean>();

  return (
    <>
      <ToAppPopup
        isOpenToAppPopup={isOpenToAppPopup}
        setIsOpenToAppPopup={setIsOpenToAppPopup}
      />
      <Div language={i18n.language}>
        <Grid container justifyContent="space-between">
          <Grid item xs={12} md={5} className="info">
            <div>
              <h6>{t("Email") as string}:</h6>
              <a
                href="mailto:<EMAIL>"
                className="bold text-decoration-underline"
              >
                <p><EMAIL></p>
              </a>
            </div>
            <div>
              <h6>{t("Toll Number") as string}:</h6>
              <a
                href="tel:920033296"
                className="bold text-decoration-underline"
              >
                <p>920033296</p>
              </a>
            </div>
            <div>
              <h6>{t("Address") as string}:</h6>
              <p className="bold" style={{ fontSize: "24px" }}>
                {t("address-text") as string}
              </p>
            </div>
            <div>
              <h6>{t("Social networks") as string}:</h6>
              <SocialIcons />
            </div>
          </Grid>
          <Grid item xs={12} md={6} className="form-wrapper">
            <h4>{t("Write to us") as string}</h4>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                setIsOpenToAppPopup(true);
              }}
            >
              <TextField
                label={t("Enter your email") as string}
                type="email"
                variant="filled"
              />
              <TextField
                label={t("Enter your Phone") as string}
                type="tel"
                variant="filled"
              />
              <TextField
                label={t("Enter your message") as string}
                type="text"
                variant="filled"
                multiline
                rows={6}
              />
              <button type="submit">{t("Send") as string}</button>
            </form>
          </Grid>
        </Grid>
      </Div>
    </>
  );
}
