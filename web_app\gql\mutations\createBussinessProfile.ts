import { gql } from "@apollo/client";

export const  Create<PERSON><PERSON>inesProfile=gql`
mutation CreateProfile (
    $commercialRegistrationCertificate:String!
    $commercialRegistrationNo:String!
    $companyEmail:String!
     $companyName:String!
     $companyPhone:String!
     $businessActivityId:ID!
     $phone:String
     $businessActivityString:String
     $commercialRegistrationCertificateOriginalFilename:String!
     ){
    businessProfile(
        input:{
        commercialRegistrationCertificate:$commercialRegistrationCertificate
        commercialRegistrationNo: $commercialRegistrationNo
        companyEmail: $companyEmail
        companyName: $companyName
        companyPhone:   $companyPhone
        businessActivityString:$businessActivityString
        businessActivityId :$businessActivityId 
        phone :$phone
        commercialRegistrationCertificateOriginalFilename:$commercialRegistrationCertificateOriginalFilename
        }
    ) {
      
      errors
      status
      user{
      businessProfile {
      companyName
      companyPhone
    }
      }

    }
  }
  `;