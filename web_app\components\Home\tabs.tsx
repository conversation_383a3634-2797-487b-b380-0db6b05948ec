import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  display: flex;
  flex-direction: row;
  /* margin: 0 -15px 20px -15px; */
  /* overflow-x: auto; */
  flex-wrap: wrap;

  @media (max-width: 570px) {
    justify-content: flex-start;
    /* overflow-x: auto; */
    -webkit-overflow-scrolling: touch;
    margin: 0 -10px;
    padding: 0 10px;
  }

  span:not(.note) {
    cursor: pointer;
    text-transform: uppercase;
    margin: 0 15px;
    color: var(--color-8);
    font-weight: 500;
    white-space: nowrap;

    @media (max-width: 570px) {
      margin: 0 5px !important;
      font-size: 12px !important;
      padding-right: 12px !important;
      padding-left: 12px !important;
    }

    &.is-selected {
      color: var(--color-1);
      position: relative;
      display: inline-block;
      &:after {
        content: "";
        width: 100%;
        height: 1px;
        background: black;
        display: inline-block;
        bottom: -1px;
        position: absolute;
        left: 0;
      }
    }
    position: relative;
  }
  .note {
    color: #bdc7cb;
    font-size: 13px;
    transform: translateY(3px);
    position: absolute;
    width: max-content;
    white-space: nowrap;
    @media (min-width: 571px) {
      left: ${(props) =>
        props.language === "en" ? "calc(100% + 10px)" : null};
      right: ${(props) =>
        props.language === "ar" ? "calc(100% + 10px)" : null};
      top: 0px;
    }
    @media (max-width: 570px) {
      position: absolute;
      bottom: -15px;
      right: ${(props) => (props.language === "en" ? "-10px" : null)};
      left: ${(props) => (props.language === "ar" ? "-10px" : null)};
      font-size: 10px;
      display: block;
      width: auto;
      margin-top: 5px;
      text-align: center;
    }
  }
  &.toggle-select {
    display: flex;
    /* justify-content: space-between; */
  }
`;

export default function Tabs({ items, selectionIndex, onClickHandler }) {
  const { i18n } = useTranslation();

  return (
    <Div className="toggle-select" language={i18n.language}>
      {items.map((item) => {
        return (
          <div key={item.index} className="tab-item position-relative">
            <span
              className={`${
                selectionIndex === item.index ? "is-selected" : null
              }`}
              onClick={() => onClickHandler(item.index)}
            >
              {item.text}
            </span>
            {item?.note && selectionIndex === 2 ? (
              <p className="note">{item.note}</p>
            ) : null}
          </div>
        );
      })}
    </Div>
  );
}
