/* eslint-disable @next/next/no-img-element */
import { Container } from "@material-ui/core";
import Layout from "components/shared/layout";
import styled from "styled-components";
import DownloadApp from "components/shared/downloadApp";
import About from "./about";
import ContactUs from "./contactUs";
import Discover from "./discover";
import Features from "./features";
import Services from "./services";
import VisionMission from "./visionMission";

const Div = styled.div`
  section:first-child {
    background-color: var(--color-6);
    padding-bottom: 20px;
    position: relative;
    div {
      position: relative;
      z-index: 1;
    }
    > img {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 0;
    }
  }
  section:nth-child(3) {
    background-color: var(--color-6);
    padding: 50px 0 10px 0;
  }
`;

export default function AboutCarwah() {
  return (
    <Layout>
      <Div id="about-carwah">
        <section>
          <Container>
            <Discover />
            <About />
            <VisionMission />
          </Container>
          <img
            src="/assets/images/about/doodles.png"
            alt="Carwah Car Rental - كروة لأيجار السيارات"
          />
        </section>
        <section>
          <Container>
            <Services />
          </Container>
        </section>
        <section>
          <Container>
            <Features />
          </Container>
        </section>
        <section>
          <Container>
            <ContactUs />
          </Container>
        </section>
        <section>
          <Container>
            <DownloadApp />
          </Container>
        </section>
      </Div>
    </Layout>
  );
}
