/* eslint-disable @next/next/no-img-element */
import { Box, Container, Grid, Typography } from "@material-ui/core";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import SocialIcons from "components/shared/socialIcons";
import Link from "next/link";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import {
  setDateTimeAction,
  setIsBarq,
  setIsInstantConfirmation,
  setSelectionIndexAction,
  setUserPickupAddressAction,
} from "store/search/action";
import { useRouter } from "next/router";
import AirportsTooltip from "./airportsToolTip";
import moment from "moment";
import { DateObject } from "react-multi-date-picker";

const Links = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 5px 0;
  h4 {
    font-size: 1.25rem;
    @media (max-width: 1300px) {
      font-size: 1rem !important;
    }
  }
  a {
    white-space: nowrap;
    cursor: pointer;
    transition: all ease 0.3s;
    font-size: 1.1rem;
    @media (max-width: 1300px) {
      font-size: 0.95rem !important;
    }
    color: #a2a0ad;
    :hover {
      color: white !important;
      text-decoration: none;
    }
  }
`;
const Div = styled.div`
  border-top: solid 1px #e1e3e7;
  padding: 35px 0;
  border-radius: 50px 50px 0px 0px;
  background-color: #2a292f;
  nav {
    @media (max-width: 960px) {
      max-width: 100%;
      margin-top: 10px;
      margin-bottom: 30px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-row-gap: 5px;
      li {
        margin: 0 !important;
      }
    }
    a:after {
      display: none;
    }
  }
  > div {
    > div {
      @media (max-width: 960px) {
        flex-direction: column;
      }
    }
  }
  .footer-title {
    @media (max-width: 1300px) {
      font-size: medium !important;
    }
  }
  .footer-divider {
    @media (max-width: 1300px) {
      display: none;
    }
  }
  .qr-wrapper {
    @media (max-width: 1300px) {
      flex-direction: column;
    }
    @media (max-width: 960px) {
      flex-direction: row;
    }
    > div {
      @media (max-width: 960px) {
        width: 50%;
      }
      @media (min-width: 960px) {
        width: 10vw;
      }
    }
  }
`;
export default function Footer() {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const dateTime = useSelector(
    (state: RootStateOrAny) => state.search_data.date_time
  );
  const { pickup_time, return_time, pickUpDate } = dateTime || {};
  const areas =
    useSelector((state: RootStateOrAny) => state?.search_data?.areas?.list) ||
    [];

  function setReservationDataHandler({ days }: { days: number }) {
    dispatch(
      setDateTimeAction({
        pickUpDate,
        dropOffDate: moment(pickUpDate, "DD/MM/YYYY")
          .add(days, "days")
          .format("DD/MM/YYYY"),
        selected_days_count: days,
        pickup_time,
        return_time,
        reservation_days: [
          new DateObject({ date: pickUpDate, format: "DD/MM/YYYY" }),
          new DateObject({ date: pickUpDate, format: "DD/MM/YYYY" }).add(
            days,
            "days"
          ),
        ],
      })
    );
  }

  return (
    <Div>
      <Container maxWidth="xl">
        <Box className={window?.innerWidth > 900 ? "px-5" : "px-2"}>
          <Grid
            container
            justifyContent="space-between"
            spacing={2}
            className="mb-4 position-relative"
            wrap="wrap"
          >
            {isOpen ? (
              <AirportsTooltip
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                action={setUserPickupAddressAction}
                // anchor={"right"}
              />
            ) : null}
            <Grid item xs={6} md="auto">
              <Links>
                <Typography
                  className="mb-2"
                  variant="h4"
                  style={{ color: "var(--color-2)", fontWeight: "bold" }}
                >
                  {t("Top Cities") as string}
                </Typography>
                {["Riyadh", "Jeddah", "Mecca", "Dammam"].map((item) => {
                  const filteredCity = areas.find(
                    (i: any) => i.enName === item
                  );
                  return (
                    <a
                      key={item}
                      className="my-1 text-align-localized"
                      onClick={() => {
                        if (!filteredCity) return;
                        dispatch(
                          setUserPickupAddressAction({
                            ...filteredCity,
                            airport: null,
                            address: filteredCity?.[`${i18n.language}Name`],
                            name: filteredCity?.[`${i18n.language}Name`],
                            lat: filteredCity.centerLat,
                            lng: filteredCity.centerLng,
                          })
                        );
                        window.location.href = `/${i18n.language}/car-search#car-grid`;
                      }}
                    >
                      {filteredCity?.[`${i18n.language}Name`]}
                    </a>
                  );
                })}
              </Links>
            </Grid>
            <Grid item xs={6} md="auto">
              <Links>
                <Typography
                  className="mb-3"
                  variant="h4"
                  style={{ color: "var(--color-2)", fontWeight: "bold" }}
                >
                  {t("Carwah") as string}
                </Typography>
                <Link href="/about-carwah">
                  <a className="my-1 text-align-localized">
                    {t("About.Us") as string}
                  </a>
                </Link>
                <Link href="/carwah-business">
                  <a className="my-1 text-align-localized">
                    {t("Carwah B2B") as string}
                  </a>
                </Link>
              </Links>
            </Grid>
            <Grid item xs={6} md="auto">
              <Links>
                <Typography
                  className="mb-3"
                  variant="h4"
                  style={{ color: "var(--color-2)", fontWeight: "bold" }}
                >
                  {t("Support") as string}
                </Typography>
                <Link href="/support">
                  <a className="my-1 text-align-localized">
                    {t("Customer Service") as string}
                  </a>
                </Link>
                <Link href="/FAQs">
                  <a className="my-1 text-align-localized">
                    {t("FAQ") as string}
                  </a>
                </Link>
                <Link href="/ally">
                  <a className="my-1 text-align-localized">
                    {t("Join as Ally") as string}
                  </a>
                </Link>
              </Links>
            </Grid>
            <Grid item xs={6} md="auto">
              <Links>
                <Typography
                  className="mb-3"
                  variant="h4"
                  style={{ color: "var(--color-2)", fontWeight: "bold" }}
                >
                  {t("Special Services") as string}
                </Typography>
                <a
                  className="my-1 text-align-localized"
                  onClick={() => {
                    dispatch(setIsInstantConfirmation(false));
                    dispatch(setIsBarq(true));
                    dispatch(setSelectionIndexAction(2));
                    router.push("/car-search#car-grid");
                  }}
                >
                  {t('"BARQ" Express Delivery') as string}
                </a>

                <a
                  className="my-1 text-align-localized"
                  onClick={() => {
                    dispatch(setIsBarq(false));
                    dispatch(setIsInstantConfirmation(true));
                    router.push("/car-search#car-grid");
                  }}
                >
                  {t("Instant Confirmation") as string}
                </a>
                <Box className="text-align-localized">
                  <a
                    className="my-1 text-align-localized"
                    onClick={async () => {
                      await new Promise((resolve) => setTimeout(resolve, 200));
                      dispatch(setIsInstantConfirmation(false));
                      dispatch(setIsBarq(false));
                      setIsOpen(true);
                    }}
                  >
                    {t('Airports "24 hour"') as string}
                  </a>
                </Box>
                <a
                  className="my-1 text-align-localized"
                  onClick={() => {
                    setReservationDataHandler({ days: 30 * 12 });
                    dispatch(setSelectionIndexAction(1));
                    router.push("/car-search#car-grid");
                  }}
                >
                  {t("Rental Packages") as string}
                </a>
              </Links>
            </Grid>

            <Grid item xs={12} md="auto">
              <Box
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                gridGap={"30px"}
                alignItems={"center"}
                height={"100%"}
                className="py-5"
              >
                <Box display={"flex"} gridGap={"28px"}>
                  <img
                    src="/assets/icons/footer/tamara.svg"
                    alt="tamara"
                    width={130}
                    style={{
                      borderRadius: "10px",
                      background: "white",
                      padding: "10px 18px",
                    }}
                  />
                  <img
                    src="/assets/icons/footer/visa-mastercard.svg"
                    alt="visamastercard"
                    width={130}
                    style={{
                      borderRadius: "10px",
                      background: "white",
                      padding: "10px 18px",
                    }}
                  />
                </Box>
                <Box display={"flex"} gridGap={"25px"}>
                  <img
                    src="/assets/icons/footer/applepay.svg"
                    alt="applepay"
                    width={80}
                    style={{
                      borderRadius: "10px",
                      background: "white",
                      padding: "3px 7px",
                    }}
                  />
                  <img
                    src="/assets/icons/footer/mada.svg"
                    alt="mada"
                    width={80}
                    style={{
                      borderRadius: "10px",
                      background: "white",
                      padding: "3px 7px",
                    }}
                  />
                  <img
                    src="/assets/icons/footer/cash.svg"
                    alt="cash"
                    height={50}
                    style={{
                      borderRadius: "10px",
                      background: "white",
                      padding: "6px 20px",
                    }}
                  />
                </Box>
              </Box>
            </Grid>
            <Grid item xs="auto">
              <Box>
                <Box
                  className="mb-4"
                  display={"flex"}
                  gridGap={"20px"}
                  alignItems={"center"}
                >
                  <Typography
                    variant="h6"
                    style={{
                      color: "white",
                      width: "max-content",
                      whiteSpace: "nowrap",
                    }}
                    className="footer-title"
                  >
                    {t("Download the App Now") as string}
                  </Typography>
                  <Box
                    style={{
                      width: "100%",
                      height: "1px",
                      backgroundColor: "white",
                      opacity: "0.2",
                    }}
                    className="footer-divider"
                  />
                </Box>
                <Box
                  display={"flex"}
                  gridGap={"1.5vw"}
                  style={{ flexWrap: "wrap" }}
                  justifyContent={"center"}
                  className="qr-wrapper"
                >
                  <Box
                    display={"flex"}
                    flexDirection={"column"}
                    style={{
                      background: "white",
                      borderRadius: "24px",
                      padding: "16px 32px",
                      cursor: "pointer",

                      height: "fit-content",
                    }}
                    sx={{
                      width: { xs: "50%", sm: "50%", md: "10vw" },
                    }}
                    onClick={() => {
                      window.open(
                        "https://apps.apple.com/sa/app/carwah-%D9%83%D8%B1%D9%88%D8%A9/id1387161215",
                        "_blank"
                      );
                    }}
                  >
                    <img src="/assets/icons/ios.svg" alt="download ios" />
                    <img
                      src="/assets/images/qr.gif"
                      alt="download app"
                      className="mt-2"
                    />
                  </Box>
                  <Box
                    display={"flex"}
                    flexDirection={"column"}
                    style={{
                      background: "white",
                      borderRadius: "24px",
                      padding: "16px 32px",
                      cursor: "pointer",

                      height: "fit-content",
                    }}
                    onClick={() => {
                      window.open(
                        "https://play.google.com/store/apps/details?id=com.carwah.customer&hl=en_US&gl=US",
                        "_blank"
                      );
                    }}
                  >
                    <img
                      src="/assets/icons/google.svg"
                      alt="download android"
                    />
                    <img
                      src="/assets/images/qr.gif"
                      alt="download app"
                      className="mt-2"
                    />
                  </Box>
                </Box>
              </Box>
            </Grid>
          </Grid>
          <Box
            borderRadius={"50px"}
            style={{
              backgroundColor: "rgba(255,255,255, 0.07)",
            }}
            sx={{
              margin: "0 auto",
              padding: "15px 30px",
            }}
          >
            <Grid
              container
              justifyContent="space-between"
              alignItems="center"
              style={{ flexWrap: "wrap" }}
              spacing={2}
            >
              <Grid item xs={"auto"}>
                <Box
                  display={"flex"}
                  gridGap={"15px"}
                  alignItems={"center"}
                  color={"white"}
                  height={"100%"}
                  flexWrap={"wrap"}
                  justifyContent={"center"}
                >
                  <img
                    src="/assets/icons/logo-white.svg"
                    alt="logo"
                    loading="lazy"
                  />
                  <Box
                    style={{
                      width: "1px",
                      height: "45px",
                      background: "white",
                    }}
                  />
                  <Typography
                    variant="subtitle1"
                    style={{ fontWeight: "bold" }}
                  >
                    {t("#Rental_Future") as string}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={"auto"}>
                <Box
                  display={"flex"}
                  gridGap={"15px"}
                  alignItems={"center"}
                  color={"white"}
                  flexWrap={"wrap"}
                  justifyContent={"center"}
                >
                  <Typography
                    variant="subtitle2"
                    style={{ fontWeight: "bold" }}
                  >
                    {
                      t(
                        "All rights reserved with Carwah Holding Company LCC"
                      ) as string
                    }
                  </Typography>
                  <span style={{ opacity: "0.3" }}>|</span>
                  <Link href="/terms-conditions">
                    <Typography
                      variant="subtitle2"
                      style={{ cursor: "pointer", textDecoration: "underline" }}
                    >
                      {t("Terms & Conditions") as string}
                    </Typography>
                  </Link>
                  <span style={{ opacity: "0.3" }}>|</span>
                  <Link href="/privacy-policy">
                    <Typography
                      variant="subtitle2"
                      style={{
                        cursor: "pointer",
                        textDecoration: "underline",
                      }}
                    >
                      {t("Privacy_Policy") as string}
                    </Typography>
                  </Link>
                </Box>
              </Grid>
              <Grid item xs={12} md="auto">
                <SocialIcons />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/* <Grid container justifyContent="space-between">
          <Grid item xs={12} md={2}>
            <p className="d-flex text-center" style={{ gap: "2px" }}>
              <span>{t("Carwah") as string}</span>
              <span>@</span>
              <span>{`${new Date().getFullYear()}`}</span>
            </p>
          </Grid>
          <Grid item xs={12} md={8}>
            <Menu footer />
          </Grid>
          <Grid item xs={1} md="auto">
            <SocialIcons />
          </Grid>
        </Grid>
        <p className="text-center mt-5 bold">
          {t("All rights reserved with Carwah Holding Company LCC") as string}
        </p> */}
      </Container>
    </Div>
  );
}
