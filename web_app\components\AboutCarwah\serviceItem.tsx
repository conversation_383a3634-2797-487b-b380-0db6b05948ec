/* eslint-disable @next/next/no-img-element */
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import styled from "styled-components";

const Div = styled.div`
  margin: 0px 0px 0px 0px;
  text-align: ${(props) => (props.language === "ar" ? "right" : null)};

  img {
    margin-bottom: 25px;
    height: 93px;
    object-fit: cover;
  }
  span {
    display: block;
    color: #aeaeae;
    margin-bottom: 20px;
  }
  h6 {
    margin-bottom: 10px !important;
  }
  p {
    font-size: 1.4rem;
  }
`;

export default function Item({ data }) {
  const { i18n } = useTranslation();
  const language = i18n.language;

  return (
    <Div language={language}>
      <img src={data.img} alt="Carwah Car Rental - كروة لأيجار السيارات" />
      <span>0{data.number}</span>
      <h6>{data.title}</h6>
      <p>{data.description}</p>
    </Div>
  );
}
