/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
import { Container } from "@material-ui/core";
import Layout from "components/shared/layout";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import CarCard from "./carCard";
import Tabs from "./tabs";
import { useQuery } from "@apollo/client";
import { Requests } from "gql/queries/Requests";
import Button from "@material-ui/core/Button";
import ClickAwayListener from "@material-ui/core/ClickAwayListener";
import Grow from "@material-ui/core/Grow";
import Paper from "@material-ui/core/Paper";
import Popper from "@material-ui/core/Popper";
import MenuItem from "@material-ui/core/MenuItem";
import MenuList from "@material-ui/core/MenuList";
import { makeStyles } from "@material-ui/core/styles";
import { useRef } from "react";
import { useEffect } from "react";
import CircularProgress from "@material-ui/core/CircularProgress";
import { Pagination } from "@material-ui/lab";
import PerPage from "components/shared/PerPage";

const Div = styled.div`
  .active-item {
    color: #7ab3c5;
  }
`;

const useStyles = makeStyles((theme) => ({
  root: {
    display: "flex",
  },
  paper: {
    marginRight: theme.spacing(2),
  },
}));
export default function MyRequestsView() {
  const { t, i18n } = useTranslation();
  const [orderBy, setOrderBy] = useState();
  const classes = useStyles();

  const [activeItem, setActiveItem] = useState(0);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState("current");
  const [open, setOpen] = useState(false);
  const anchorRef = useRef(null);
  const {
    data: AllRequests,
    loading,
    refetch,
  } = useQuery(Requests, {
    variables: { limit, page, status, orderBy },
  });

  useEffect(() => {
    let clean = false;
    if (!clean) refetch();
    return (clean = true);
  }, [status]);
  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event, order) => {
    if (order) {
      setOrderBy(order);
    }

    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }

    setOpen(false);
  };
  function handleListKeyDown(event) {
    if (event.key === "Tab") {
      event.preventDefault();
      setOpen(false);
    }
  }
  const prevOpen = useRef(open);
  useEffect(() => {
    let clean = false;
    if (!clean) {
      if (prevOpen.current === true && open === false) {
        anchorRef.current.focus();
      }

      prevOpen.current = open;
    }
    return (clean = true);
  }, [open]);

  return (
    <Layout>
      <Div className="grey-background" language={i18n.language}>
        <Container>
          <div className="d-flex justify-content-between align-items-center pt-4">
            <h2 className="title bold pt-3 pb-2">{t("My Requests")}</h2>

            <div>
              <Button
                ref={anchorRef}
                aria-controls={open ? "menu-list-grow" : undefined}
                aria-haspopup="true"
                onClick={handleToggle}
                className="sort"
              >
                {t("Sort-by")}
                <img src="/assets/images/arrows.svg" alt="arrows" />
              </Button>
              <Popper
                open={open}
                anchorEl={anchorRef.current}
                role={undefined}
                transition
                disablePortal
              >
                {({ TransitionProps, placement }) => (
                  <Grow
                    {...TransitionProps}
                    style={{
                      transformOrigin:
                        placement === "bottom" ? "center top" : "center bottom",
                      background: "#000",
                      color: "#fff",
                    }}
                  >
                    <Paper>
                      <ClickAwayListener
                        touchEvent={"onTouchStart"}
                        mouseEvent={"onMouseDown"}
                        onClickAway={handleClose}
                      >
                        <MenuList
                          autoFocusItem={open}
                          id="menu-list-grow"
                          onKeyDown={handleListKeyDown}
                        >
                          <MenuItem
                            className={orderBy == "status" ? "active-item" : ""}
                            onClick={(e) => handleClose(e, "status")}
                          >
                            {t("Request.status")}
                          </MenuItem>
                          <MenuItem
                            className={orderBy == "newest" ? "active-item" : ""}
                            onClick={(e) => handleClose(e, "newest")}
                          >
                            {t("Newest")}
                          </MenuItem>

                          <MenuItem
                            className={orderBy == "oldest" ? "active-item" : ""}
                            onClick={(e) => handleClose(e, "oldest")}
                          >
                            {t("Oldest")}
                          </MenuItem>
                        </MenuList>
                      </ClickAwayListener>
                    </Paper>
                  </Grow>
                )}
              </Popper>
            </div>
          </div>

          <Tabs
            t={t}
            language={i18n.language}
            activeItem={activeItem}
            setActiveItem={setActiveItem}
            setStatus={setStatus}
            refetchData={refetch}
          />
        </Container>

        <hr />

        <Container>
          <div className="d-flex flex-column mt-5">
            {AllRequests?.customerBusinessRentals?.collection?.length ? (
              AllRequests?.customerBusinessRentals?.collection.map((i) => {
                return (
                  <CarCard
                    key={i.id}
                    {...i}
                    tabstatus={status}
                    refetchData={refetch}
                  />
                );
              })
            ) : loading ? (
              <div className="text-center">
                <CircularProgress />
              </div>
            ) : (
              <p className="text-center mb-2" style={{ minHeight: "400px" }}>
                {t("No.Recordes")}
              </p>
            )}
          </div>
          {AllRequests?.customerBusinessRentals.metadata.totalPages ? (
            <div className="d-flex justify-content-around pb-5">
              <Pagination
                showFirstButton
                showLastButton
                style={{ direction: "ltr" }}
                count={Math.ceil(
                  AllRequests?.customerBusinessRentals.metadata?.totalCount /
                    limit
                )}
                page={
                  AllRequests?.customerBusinessRentals.metadata?.currentPage
                }
                onChange={(e, value) => {
                  setPage(value);
                  // history.replace({ hash: `page=${value}` });
                }}
              />
              <PerPage
                specialPagination={[10, 20, 40, 100]}
                handlePerPageChange={(value) => setLimit(value)}
                perPage={limit}
                setPage={setPage}
              />
            </div>
          ) : null}
        </Container>
      </Div>
    </Layout>
  );
}
