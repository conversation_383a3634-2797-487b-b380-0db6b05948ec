/* eslint-disable react-hooks/exhaustive-deps */
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import styled from "styled-components";

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  position: relative;
`;

function DetailsCard() {
  const { t, i18n } = useTranslation();

  const { rentalDetails } =
    useSelector((state: RootStateOrAny) => state.booking) || {};

  const { closingReason, statusLocalized, status, bookingNo, cancelledReason } =
    rentalDetails || {};

  return (
    <Div>
      <div className="p-4 mt-4">
        <div className="d-flex align-items-center justify-content-between">
          <h6>{t("Booking No") as string}:</h6>
          <h6>{bookingNo}</h6>
        </div>
        <div className="d-flex align-items-center justify-content-between mt-2">
          <h6>{t("Rental Status") as string}:</h6>
          <h6
            style={{
              color:
                status?.toLowerCase() === "cancelled" ? "var(--color-9)" : "",
            }}
          >
            {statusLocalized}
          </h6>
        </div>
        {closingReason?.length ||
        (closingReason && typeof closingReason === "string") ? (
          <>
            <hr className="w-100 mt-4" />
            <div>
              <h6 className="color-9">{t("Closing Reason") as string}:</h6>
              {closingReason?.length > 1 && cancelledReason ? (
                <ul
                  style={{
                    lineHeight: 2,
                    textAlign: i18n?.language == "ar" ? "right" : "left",
                  }}
                >
                  {[...closingReason, cancelledReason]?.map((reason, index) => (
                    <li key={index}>
                      <span>{reason}</span>
                    </li>
                  ))}
                </ul>
              ) : closingReason?.length > 1 ? (
                <ul
                  style={{
                    lineHeight: 2,
                    textAlign: i18n?.language == "ar" ? "right" : "left",
                  }}
                >
                  {[...closingReason]?.map((reason, index) => (
                    <li key={index}>
                      <span>{reason}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <h6 className="mt-2">{closingReason}</h6>
              )}
            </div>
          </>
        ) : null}
      </div>
    </Div>
  );
}

export default DetailsCard;
