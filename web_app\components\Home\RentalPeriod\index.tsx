import { Grid, <PERSON>lide<PERSON> } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import useLogic from "./useLogic";

const GridTag = styled(Grid)`
  margin-top: 0px;
  h6,
  h5 {
    font-size: 15px;
  }
  & {
    h6 {
      color: #bdc7cb;
      margin-bottom: 5px !important;
      text-transform: uppercase;
      font-weight: 500;
    }
    .MuiSlider-rail {
      opacity: 1;
      height: 5px;
      border-radius: var(--radius-2);
      color: var(--color-6);
    }
    .MuiSlider-track {
      border-radius: var(--radius-2);
      height: 5px;
      color: var(--color-3);
    }
    .MuiSlider-thumb {
      width: 20px;
      height: 20px;
      top: 10px;
      color: var(--color-3);
      @media (max-width: 570px) {
        top: 18px;
      }
    }
  }
`;

function RentalPeriod() {
  const { t, rental_months, handleSliderChange, monthLocale } = useLogic();

  return (
    <GridTag container justifyContent="space-between">
      <Grid item xs={12}>
        <h6>{t("Rental Period") as string}</h6>
      </Grid>
      <Grid item xs={9}>
        <Slider
          value={rental_months}
          onChange={handleSliderChange}
          aria-labelledby="input-slider"
          step={1}
          min={1}
          max={24}
        />
      </Grid>
      <Grid item xs={3}>
        <h5 className="text-center">
          <strong>{rental_months}</strong>{" "}
          <span>{monthLocale(rental_months)}</span>
        </h5>
      </Grid>
    </GridTag>
  );
}

export default RentalPeriod;
