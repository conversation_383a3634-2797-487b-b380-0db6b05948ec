/* eslint-disable @next/next/no-img-element */
import { Container } from "@material-ui/core";
import LocationInputs from "components/Home/locationInputs";
import { Dispatch, SetStateAction } from "react";
import styled from "styled-components";

const Div = styled.div`
  background-color: #2a292f;
  padding-top: 65px;
  padding-bottom: 65px;

  .wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }
  @media (max-width: 768px) {
    padding-top: 90px;
  }
`;

function RentToOwnSearchBox({
  setFetchCars,
}: {
  setFetchCars: Dispatch<SetStateAction<boolean>>;
}) {
  return (
    <Div>
      <Container>
        <div className="wrapper">
          <img src="/assets/images/rentToOwn.svg" alt="rent to own" />
          <LocationInputs
            hideInputLabel
            inputStyles={{
              backgroundColor: "#55535B",
              color: "#fff",
              border: "none",
              width: "100%",
              maxWidth: "385px",
              margin: "0 auto",
            }}
            inputChangeHandler={() => {
              setFetchCars((fetchCars) => !fetchCars);
            }}
          />
        </div>
      </Container>
    </Div>
  );
}

export default RentToOwnSearchBox;
