{"About Carwah": "About Carwah", "Become an Ally": "Join as <PERSON>", "Deals": "Deals", "Support": "Support", "Login": "<PERSON><PERSON>", "Welcome": "Welcome", "Your first choice in all your destinations and trips": "Your first choice in all your destinations and trips", "Carwah your first choice for car rental": "Carwah your first choice for car rental", "Choose from our large fleet of cars from 900 branches in 42 cities and airports across Saudi Arabia": "Choose from our large fleet of cars from 900 branches in 42 cities and airports across Saudi Arabia", "Daily": "Daily", "Monthly": "Monthly", "Delivery": "Delivery", "pickup": "pickup-up", "return": "return", "Online": "Online", "online": "Online", "Cash": "Cash", "cash": "Cash", "comment": "comment", "done": "done", "* 900+ rental offices distributed within 45 cities and airports.": "* 900+ rental offices distributed within 45 cities and airports.", "*Over 80,000 cars in inventory": "*Over 80,000 cars in inventory", "Every present has a future, and carwah is the future of car rental.": "Every present has a future, and carwah is the future of car rental.", "Customers have the option of selecting their desired car rental from:": "Customers have the option of selecting their desired car rental from:", "Our approach is simplicity. Carwah simplifies car rental booking via our application which is available across all platforms. Our easy-to-navigate application links our Allies (car rental companies) with customers.": "Our approach is simplicity. Carwah simplifies car rental booking via our application which is available across all platforms. Our easy-to-navigate application links our Allies (car rental companies) with customers.", "An idea was formed to create an online platform where car rental companies could be featured in one place, giving customers control over their car rental options. A platform that focuses on the car rental needs of the consumer and makes car rental features the center of the process, not the car rental company names.": "An idea was formed to create an online platform where car rental companies could be featured in one place, giving customers control over their car rental options. A platform that focuses on the car rental needs of the consumer and makes car rental features the center of the process, not the car rental company names.", "Carwah started with an idea from one of its Co-Founder while he was waiting for his turn in the airport car rental line. He noticed that there were so many car rental companies and as a customer, he was still determining which company offered the car that would meet his needs or which had the best price. He thought there had to be an easier way of renting a car than just going with car rental company name recognition and leaving the car rental companies to decide what car he was getting or if the features he wanted would be available. Why shouldn't booking a car rental be as easy as ordering a pizza?": "<PERSON><PERSON><PERSON> started with an idea from one of its Co-Founder while he was waiting for his turn in the airport car rental line. He noticed that there were so many car rental companies and as a customer, he was still determining which company offered the car that would meet his needs or which had the best price. He thought there had to be an easier way of renting a car than just going with car rental company name recognition and leaving the car rental companies to decide what car he was getting or if the features he wanted would be available. Why shouldn't booking a car rental be as easy as ordering a pizza?", "Cancel Reservation": "Cancel Reservation", "Rate Ally": "Rate Ally", "Ally Rated Sucessfully": "Ally Rated Sucessfully", "Rate Ally by the quality of communication and the level of service": "Rate Ally by the quality of communication and the level of service", "Rent Updated Successfully": "Rent Updated Successfully", "rent cancelled successfully": "Rent cancelled Successfully", "Select Your Insurance": "Select Your Insurance", "Insurance": "Insurance", "Return to the same city": "Return to the same city", "Current location": "Current location", "Pick-up Car Time": "Pick-up Car Time", "Drop-off Day & time": "Drop-off Day & time", "Our allies have two types of insurance": "Our allies have two types of insurance", "Request Follow-up": "Request Follow-up", "Standard in the event of personal responsibility, you pay the percentage of bearing the costs of the accident": "Standard: in the event of personal responsibility, you pay the percentage of bearing the costs of the accident", "Apply": "Apply", "next": "Next", "close": "Close", "Phone": "Phone ", "Request has been received successfully": "Request has been received successfully", "Your request will be answered as soon as possible": "Your request will be answered as soon as possible", "edit": "Edit", "email pattern": "Invalid Email Format", "Invalid phone number": "Invalid phone number", "phone": "Phone", "other": "Other", "Pick-up time": "Pick-up time", "The max allowed size is 5 Mb": "The max allowed size is 5 Mb", "file extensions PDF / JPG / JPEG / PNG only allowed": "file's extensions: PDF / JPG / JPEG / PNG only allowed", "file uploaded successfully": "File Uploaded Successfully", "Attach files have the following extensions": "Attach files have the following extensions", "Commercial registration certificate ": "Commercial registration certificate ", "Company Info": "Company Info", "CompanyName": "Company Name", "Business activity": "Business Activity", "Commercial registration number": "Commercial Registration Number", "email": "Company Email", "File has been attached": "File has been attached", "step1": "step1", "Max. no of requests is 10 per time": "Max. no of requests is 10 per time", "step2": "step2", "step3": "step3", "back": "Back", "Submit_Request": "Submit Request", "Return time": "Return time", "Request_Summary": "Request Summary", "Car No. #": "Car No. #", "Terms_and_Conditions": "Terms and Conditions", "and": "and", "add": "Add", "Yes": "Yes", "No": "No", "btn.edit": "Edit", "Delete": "Delete", "b.month": "months", "Done successfully": "Done successfully", "Privacy_Policy": " Privacy Policy", "Are you sure you want to delete this car?": "Are you sure you want to delete this car?", "By_submitting_you_agree_to_the": "By submitting, you agree to the ", "target-cutomer-content": "The business sector, whether companies, establishments or the government sector", "introduction": "Introduction", "target-customer": "Our target customers", "introduction-content": "It is one of Carwah's Products for the business sector, as it allows you to easily order, provides you with more options for your facility, and meets all your needs in terms of car rental.", "Please Select a date": "Please Select a date range", "CHOOSE YOUR RENTAL CAR TYPE": "CHOOSE YOUR RENTAL CAR TYPE", "Economy": "Economy", "Request Now": "Request Now", "should be Login": "<PERSON><PERSON>", "Sedan": "Sedan", "SUV": "SUV", "Small": "Small", "Sport": "Sport", "Pick-up": "Pick-up", "Luxury": "Luxury", "Commercial": "Commercial", "Search By.extra": "Extra", "Search By.veichle": "<PERSON><PERSON><PERSON><PERSON>", "Extra services fav": "Services", "Service": "Service", "Delivery Service": "Delivery Service", "Unlimited kilometers": "Unlimited kilometers", "Drop off another city": "Drop off another city", "Car with Driver": "Car with Driver", "Jeddah": "Jeddah", "Check Our": "Check Our", "Great Deals": "Great Deals", "City": "City", "Airport": "Airport", "Riyadh City": "Riyadh City", "Sort By": "Sort By", "All Deals": "All Deals", "Price Discount": "Price Discount", "Coupon Code": "Coupon Code", "Free Services": "Free Services", "Hyundai Accent": "Hyundai Accent", "or similar": "or similar car", "Riyadh, Alyasamin Dist.": "Riyadh, Alyasamin Dist.", "Riyadh": "Riyadh", "Alyasamin": "Alyasamin", "Dist.": "Dist.", "20% Rental Price Discount": "20% Rental Price Discount", "Use Coupon ( Carwah ) for 20% off up to 50 SR": "Use Coupon for 20% off up to 50 SR", "Take 5 days and get +1 day free": "Take 5 days and get +1 day free", "SR/day": "﷼ /day", "SR/month": "﷼ / Month", "Why choose": "Why choose", "Carwah": "Carwah", "Competitive Prices": "Competitive Prices", "The lowest price on the market for any type of cars.": "The lowest price on the market for any type of cars.", "Largest Network": "Largest Network", "Our allies are always close to you because they are spread all over the Kingdom.": "Our allies are always close to you because they are spread all over the Kingdom.", "Variety of Options": "Variety of Options", "We offer you a lot of options (allies, cars, rental prices and more) in one place.": "We offer you a lot of options (allies, cars, rental prices and more) in one place.", "Quality Service": "Quality Service", "We offer dedicated 24/7 customer service to ensure top-notch assistance and support for all your needs": "We offer dedicated 24/7 customer service to ensure top-notch assistance and support for all your needs", "Download": "Download", "The App Now": "The App Now", "Download the App Now": "Download the App Now", "Renting a car has never been easier with Carwah app. Get connected to get exclusive offers, personalized experience and other features with our app on Android or iOS platforms.": "Get the ultimate convenience of renting a car with the Carwah App. Enjoy exclusive offers, personalized features, and a seamless rental experience on both Android and iOS platforms. Don't miss out on our special deals - keep your notifications on to stay updated with our latest offers. It's an easy way to rent a car!", "Branch": "Branch", "Dammam": "<PERSON><PERSON><PERSON>", "Qasim": "<PERSON>", "Cities": "Cities", "Airports": "Airports", "King Abdulaziz": "King <PERSON><PERSON><PERSON>", "King Khaled": "King <PERSON><PERSON><PERSON>", "King Fahad": "King <PERSON><PERSON><PERSON>", "Abha": "<PERSON><PERSON>", "SR": "SR", "sr": "﷼", "ok": "Ok", "Error": "Error", "Back to Home": "Back to Home", "See Rental Details": "See Rental Details", "Dear customer.. your rental is pending now. You can also follow the status of the rental through the 'Rental' screen or by clicking on": "Dear customer.. your rental is pending now. You can also follow the status of the rental through the 'Rental' screen or by clicking on", "Contact Us": "Contact Us", "my-rentals": "Rentals", "No rentals found": "No rentals found", "You have not submitted a car rental request yet. To find a car go to the search section": "You have not submitted a car rental request yet. To find a car go to the search section", "Rental request has been sent": "Rental request has been sent", "For Any Questions": "For Any Questions", "You Can Follow Us": "You Can Follow Us", "Enter your email here": "Enter your email here", "Subscribe": "Subscribe", "© 2018-2020 Carwah": "© 2018-2021 Carwah", "Choose Pickup location first": "Choose Pickup location first", "Month": "Month", "2Months": "Months", "Months": "Months", "Month/s": "Months", "Rental Period": "Rental Period", "Delivery Pick-up Car Location": "Delivery Pick-up Car Location", "Delivery return location": "Delivery return location", "Edit": "Edit", "Choose delivery pickup location": "Choose delivery pickup location", "Choose delivery return location": "Choose delivery return location", "Return to the same location": "Return to the same location", "Confirm": "Confirm", "save this location to your profile": "save this location to your profile", "Additional fees may apply": "Additional fees may apply", "Discover Carwah": "Discover Carwah", "At Carwah, we build the community through strong partnerships with our allies to provide many advantages for our clients and partners": "Discover Carwah, the ultimate hassle-free car rental solution that allows you to effortlessly connect to exclusive offers and enjoy a personalized experience on both Android and iOS platforms. With the Carwah App, renting a car has never been easier.", "About": "About", "Carwah is an online platform that specializes in facilitating and regulating car rentals with the latest technology and software with greater flexibility, higher security and privacy.": "Carwah is an online platform that specializes in facilitating and regulating car rentals with the latest technology and software with greater flexibility, higher security and privacy.", "Cars": "Cars", "Locations": "Locations", "Branches": "Branches", "Companies": "Companies", "Our Vision": "Our Vision", "Our Message": "Our Message", "Our vision is to be a one-stop site for consumers in the GCC looking for car rentals, with a consumer-focused platform that creates value for every company and shareholder that collaborates with us.": "Our vision is to be a one-stop site for consumers in the GCC looking for car rentals, with a consumer-focused platform that creates value for every company and shareholder that collaborates with us.", "Every present has a future and carwah is the future of car rental.": "Every present has a future and carwah is the future of car rental.", "Services": "Services", "And Products": "And Products", "Bidding": "Bidding - Launching soon", "Lightning deal...! This product is new and exclusive in Carwah only - the fast, urgent customer requests within your budget.": "Get ready for an amazing deal! Our latest product at Carwah is both exclusive and budget-friendly, and it's designed to meet our customers' urgent needs with lightning speed. Stay tuned for updates on its release!", "Karam Carwah": "<PERSON><PERSON> – Launching soon", "Loyalty program for our dear customers who can take advantage of their bookings by collecting points and transferring them to a balance in their wallets.": "Welcome to the Carwah Loyalty Program! You can earn points with every booking and redeem them for fantastic rewards. We greatly value your loyalty and would like to thank you for choosing Carwa<PERSON>. Start earning rewards today!", "Carwah Lease": "<PERSON><PERSON><PERSON>", "Carwah Business": "Carwah B2B", "Is a service designed to meet the long-term needs of individuals wishing to rent carsoffering special offers and additional services.": "Looking for a long-term car rental solution? Look no further than 'Carwah Lease'! Our premium leasing service offers convenience and flexibility to meet all of your extended car rental needs. Whether you need a car for weeks, months, or even longer, Carwah Lease has the perfect vehicle for your journey ahead. Enjoy the freedom of driving without the commitment of ownership and discover your dream car today for an unparalleled experience on the road", "Is a program that targets sectors of all kinds government, private and charitable to meet their needs and facilitate their leasing operations.": "Carwah Business provides a comprehensive range of leasing solutions to cater to the diverse needs of government agencies, private businesses, and charitable organizations. We offer customized programs specifically designed to meet the unique requirements of each sector, ensuring that our clients receive a tailored leasing experience that addresses their individual needs. With our all-inclusive approach, Carwah Business is committed to providing top-notch services that enable our clients to meet their leasing goals efficiently and effectively.", "Guests of Carwah": "Guests of Carwa<PERSON>", "Program seeks to offer special discounts, offers and services to employees of the entities and companies that have joined this program.": "Program seeks to offer special discounts, offers and services to employees of the entities and companies that have joined this program.", "Features": "Features", "For Customer": "For Customer", "Pre-booking": "Pre-booking", "Insurance options": "Insurance options", "delivery and pick-up anywhere": "delivery and pick-up anywhere", "Competitive prices": "Competitive prices", "wide spread locations": "wide spread locations", "diverse of cars fleet": "Variety of car types", "24-hour service": "24-hour service", "Take the advantage of becoming an Ally with us to be a part of our network throughout the Kingdom.": "Take the advantage of becoming an Ally with us to be a part of our network throughout the Kingdom.", "How": "How", "It Works": "It Works?", "Fill in the registration form with the required information and then we will get back to you as soon as possible.": "Fill in the registration form with the required information and then we will get back to you as soon as possible.", "Training": "Training", "We provide professional training to our allies through a trained dedicated team on Carwah platform.": "Carwah provide professional training to our allies through a trained dedicated team on Carwah platform.", "start boosting your profit": "start boosting your profit", "Start receiving more bookings from our customers, increasing your sales, and comprehensive reporting systems with our platform.": "Start receiving more bookings from our customers, increasing your sales, and comprehensive reporting systems with our platform.", "Why Did We Call": "Why Did We Call", "Them An Ally": "Them An Ally", "Ally in Arabic the one who is with you and considered as the best friend who supported you in all of your situations. Carwah's allies are our friends from car rental companies who seek to provide the best service together for their customers.": "Ally in Arabic is the one who is with you and considered as the best friend, who supports you in your journey. <PERSON><PERSON><PERSON>'s  allies are our close friends who work together to provide the highest quality  service to our  customers.", "Do you want to become an ally? Let's try, it's not hard": "Do you want to become an ally? Join us now", "Allies": "Allies", "Advantages": "Advantages", "Benefit from our strategic partnerships to get the best services.": "Benefit from our strategic partnerships to get the best services.", "Customers": "Customers", "See all information about the customer before he visits your branch.": "See all information about the customer before he visits your branch.", "Evaluation": "Evaluation", "Get the evaluation and booking history of the customer.": "Get the evaluation and booking history of the customer.", "Behavior": "Behavior", "Discover and track the customer driving behavior with our Carwah`s GPS.": "Discover and track the customer driving behavior with our Carwah`s GPS.", "Finance": "Finance", "The ability to get financial information of each car and the company overview.": "The ability to get financial information of each car and the company overview.", "Operation": "Operation", "Easy renting process (book- receive - handover) through smartphones.": "Easy renting process (book- receive - handover) through smartphones.", "Train allies and its employees on how to use Carwah system.": "Train allies and its employees on how to use Carwah system.", "How can you become an ally?": "How can you become an ally?", "Sign up with us now and join the list of famous Carwah`s allies.": "Sign up with us now and join the list of famous Car<PERSON><PERSON>`s allies.", "Our": "Our", "Reviews": "Reviews", "Hertz": "<PERSON><PERSON>", "To create an electronic market in the care rental sector, which is safe and beneficial to allies, and exceed the customers": "To create an electronic market in the care rental sector, which is safe and beneficial to allies, and exceed the customers", "Contacts": "Contacts", "Terms & Conditions": "Terms & Conditions", "Feel free to reach to us about any questions you might have.": "Feel free to reach out about any questions you might have.", "Email": "Email", "Toll Number": "Toll Number", "Social networks": "Social networks", "Write to us": "Write to us", "Enter your email": "Enter your email", "Enter your Phone": "Enter your Phone", "Enter your message": "Enter your message", "Send": "Send", "How to get benefits for all allies?": "How to get benefits for all allies?", "Please fill out the form below with your data and we will get in touch with you as soon as possible!": "Please fill out the form below with your data and we will get in touch with you as soon as possible!", "Name": "Name", "Phone Number": "Company Phone", "Personal Phone Number": "Phone", "Message": "Message", "Save": "Save", "My Rentals": " Rentals", "My Requests": "My Requests", "car_no": "Car No", "Standard": "Standard", "close.success": "Close", "months": "months", "No.Recordes": "No Reecords Found !", "month": "month", "Newest": "Newest", "Oldest": "Oldest", "Request.status": "Status", "Sort-by": "Sort By", "booking.Offers": "Offers", "Cancel Request": "Cancel Request", "View Offers": "View Offers", "Current": "Current", "components.perpage.limit": "Show", "Current.request": "Current", "Booking No": "Booking No", "extraservice": "Extra Services", "Show.results": "Show results", "insurance": "Insurance", "transmission": "Transmission", "auto": "Automatic", "km.per.day": "km per day", "manual": "Manual", "km.away": "Away", "all": "All", "filter": "Filters", "car.Filters": "Car Filters", "Unlimited Kilometer": "Unlimited Kilometer", "is unlimited": "Is Unlimited", "extra.km": "Extra km", "300 and more": "300 and more", "less than 89": "less than 89", "Vehicle.Type": "Vehicle Type", "booking.Status": "Status", "PickupDate": "Pick-up date", "You have a due installment": "You have a due installment", "You have an overdue installment. Please settle it promptly.": "You have an overdue installment. Please settle it promptly.", "The car will be delivered on time": "The car will be delivered on time", "Your rental will be confirmed soon.": "Your rental will be confirmed soon.", "2.month": "2 months", "car.make": "Make", "car.model": "Model", "clear.all": "Clear All", "car.year": "Year", "Cancel.request": "Cancel request", "View Details": "View Details", "Payment Method": "Payment Method", "Coupon will not apply as it incompatible with the rent": "Coupon will not apply as it incompatible with the rent", "Proceed": "Proceed", "You have a pending extension request": "You have a pending extension request", "Wallet": "Wallet", "New Return Time": "New Return Time", "Extension Duration": "Extension Duration", "Request No.": "Request No.", "paid": "Paid", "Payment Status": "Payment Status", "notpaid": "not paid", "Request Status": "Request Status", "Cancel Extension Request": "Cancel Extension Request", "Extension Request": "Extension Request", "Coupon not applied": "Coupon not applied", "Coupon discount": "Coupon discount", "request.has.been.canceled.successfully": "Request has.been canceled successfully", "History": "History", "Extension request has been confirmed": "Extension request has been confirmed", "Grand.Total": "Grand.Total", "cancelReasons": "Cancel Reasons", "Dear customer.. your extension request is confirmed now. You can follow up on your request through Rental": "Dear customer.. your extension request is confirmed now. You can follow up on your request through Rental", "History.request": "History", "Booking Id": "Booking no:", "see_more": "see more", "Due Amount": "Due Amount", "profile created success": "profile saved success", "There was a problem with this booking To fix the problem,": "There was a problem with this booking To fix the problem,", "Click Here": "Click Here", "Trip Date": "Pick-Up Date:", "Pick-up Car Location": "Pick-up  Location:", "my.rentals.sr": "﷼", "Duration": "Duration:", "Return Location": "Return Location:", "days": "days", "Riyadh Airport": "Riyadh Airport", "Jeddah Airport": "Jeddah Airport", "no_delivery": "No Delivery", "Branch Details": "Branch Details", "Details": " Details", "Amount is higher than max limit": "Amount is higher than max limi", "Total": "Total", "About Price": "About Price", "Drop-off location": "Drop-off location", "pickup location": "Car Pick-up Location", "Return at different location": "Return at different location", "Family": "Family", "Car Delivery": "Car Delivery", "Chauffeur Services": "Chauffeur Services", "Carwah provides the most competitive prices with our network of allies": "Carwah provides the most competitive prices with our network of allies", "More Locations": "More Locations", "Our ally's location is spread across the Kingdom to serve you better": "Our ally's location is spread across the Kingdom to serve you better", "Carwah provides many options and services on one easy-to-use platform": "Carwah provides many options and services on one easy-to-use platform", "All rights reserved with Carwah Holding Company LCC": "All rights reserved with Carwah Holding Company LCC", "Established in 2018, today Carwah is the leading platform for the car rental field in Saudi Arabia, with exceptional features and multiple options for our customers.": "Established in 2018, today Carwah is the leading platform for the car rental field in Saudi Arabia, with exceptional features and multiple options for our customers.", "We offer exclusive services for our customers, with 750+ rental offices and over 66,000 types of cars, disturbed across 42 cities and airports around the Kingdom of Saudi Arabia. Carwah will connect our Allies with our customers in a seamless car rental experience.": "We offer exclusive services for our customers, with 750+ rental offices and over 66,000 types of cars, distributed across 42 cities and airports around the Kingdom of Saudi Arabia. Carwah will connect our Allies with our customers in a seamless car rental experience.", "Our approach is the SIMPLICITY - Simplified car rental booking by using Carwah's application, which is an all-inclusive application that will allow consumer access to our ally's fleet of cars. Customer service is the heart of Carwah, we strive to add convenience and peace of mind to all our customers. Pick-up or drop-off your car rental at any convenient time or place for you, or have your car rental delivered, Carwah allies are at your service.": "Our approach is the SIMPLICITY - Simplified car rental booking by using Carwah's application, which is an all-inclusive application that will allow consumer access to our ally's fleet of cars. Customer service is the heart of Carwah, we strive to add convenience and peace of mind to all our customers. Pick-up or drop-off your car rental at any convenient time or place for you, or have your car rental delivered, Carwah allies are at your service.", "Our promise to all Carwah customers is, we will develop and upgrade our services to provide convenience to our valued customers.": "Our promise to all Carwah customers is, we will develop and upgrade our services to provide convenience to our valued customers.", "Our Mission": "Our Mission", "Our Values": "Our Values", "Our Story": "Our Story", "Simplicity": "Simplicity", "Transparency": "Transparency", "Customer Commitment": "Customer Commitment", "Innovation": "Innovation", "Partnership": "Partnership", "We believe car rental should be simple, intuitive, and hassle-free. Our platform is designed to make the process easy for everyone.": "We believe car rental should be simple, intuitive, and hassle-free. Our platform is designed to make the process easy for everyone.", "Honesty and clarity are at the core of our business. We provide clear pricing, terms, and features with no hidden fees or surprises.": "Honesty and clarity are at the core of our business. We provide clear pricing, terms, and features with no hidden fees or surprises.", "Our customers are our priority. We are dedicated to providing exceptional customer service and support, available 24/7 to ensure peace of mind at every stage of the rental journey.": "Our customers are our priority. We are dedicated to providing exceptional customer service and support, available 24/7 to ensure peace of mind at every stage of the rental journey.", "We are committed to continuous innovation, constantly developing new solutions to meet evolving customer needs and improve their experience.": "We are committed to continuous innovation, constantly developing new solutions to meet evolving customer needs and improve their experience.", "We foster strong, collaborative relationships with our allies, working together to deliver the highest quality service to customers.": "We foster strong, collaborative relationships with our allies, working together to deliver the highest quality service to customers.", "Carwah is Saudi Arabia's leading digital car rental platform, transforming the way individuals and businesses rent vehicles across the Kingdom. Established in 2018, Carwah was created to solve a simple yet significant problem long waits, limited options, and lack of transparency in traditional car rental services.": "Carwah is Saudi Arabia's leading digital car rental platform, transforming the way individuals and businesses rent vehicles across the Kingdom. Established in 2018, Carwah was created to solve a simple yet significant problem long waits, limited options, and lack of transparency in traditional car rental services.", "features": "Features", "About Us": "And this is just the beginning ..", "cities and airports": "Cities and Airports", "rental offices": "Rental Offices", "To become the go-to car rental platform in the GCC, creating value for customers, partners, and shareholders while shaping the future of mobility.": "To become the go-to car rental platform in the GCC, creating value for customers, partners, and shareholders while shaping the future of mobility.", "Cloud Yard Innovation": "Cloud Yard Innovation", "We introduced the Cloud Yard a virtual fleet hub that allows rental cars to be delivered or picked up wherever the customer is. This reduces waiting times, eliminates the need for physical branch visits, and ensures a seamless car rental experience.": "We introduced the Cloud Yard a virtual fleet hub that allows rental cars to be delivered or picked up wherever the customer is. This reduces waiting times, eliminates the need for physical branch visits, and ensures a seamless car rental experience.", "A Platform for All": "A Platform for All", "Carwah works with rental companies of all sizes, from well-known international brands to small and medium-sized local companies, giving customers the widest variety of cars, services, and competitive pricing.": "Carwah works with rental companies of all sizes, from well-known international brands to small and medium-sized local companies, giving customers the widest variety of cars, services, and competitive pricing.", "Tailored B2B Solutions": "Tailored B2B Solutions", "For businesses and government sectors, Carwah offers long-term leasing solutions and on-demand fleet services, ensuring operational flexibility and cost efficiency.": "For businesses and government sectors, Carwah offers long-term leasing solutions and on-demand fleet services, ensuring operational flexibility and cost efficiency.", "Customer-Centric Service": "Customer-Centric Service", "At Carwah, customer convenience is at the heart of everything we do. Our 24/7 support, easy-to-use interface, and continuous product innovations keep us ahead in delivering exceptional rental experiences.": "At Carwah, customer convenience is at the heart of everything we do. Our 24/7 support, easy-to-use interface, and continuous product innovations keep us ahead in delivering exceptional rental experiences.", "We've got something special for you in our apps!": "We've got something special for you in our apps!", "Our website will be backed up with more excited and stunning features for your best car renting experience, till that time you can now download our Apps and enjoy!": "Our website will be backed up with more excited and stunning features for your best car renting experience, till that time you can now download our Apps and enjoy!", "GCC": "GCC", "Hyundai Camry": "Hyundai Camry", "Hyundai Kona": "Hyundai Kona", "page not found": "page not found", "Search Results": "Search Results", "Sort by": "Sort by", "Most Popular": "Most Popular", "km/d": "KM / DAY", "km": "km", "volume": "volume", "rating": "rating", "Filters": "Filters", "Cars filters": "Cars filters", "According to the filters 115 cars": "According to the filters 115 cars", "Clear All": "Clear All", "Make / Model": "Make / Model", "Choose": "<PERSON><PERSON>", "Ally features": "Ally features", "Radius": "<PERSON><PERSON>", "Year": "Year", "Similar Sedan": "Similar Sedan", "White": "White", "Car Type": "Car Type", "Daily Price": "Daily Price", "Days": "Days", "Rent Car": "Rent Car", "Sign In": "Sign In", "Welcome, glad to see you": "Welcome, glad to see you", "Password": "Password", "Forgot your password?": "Forgot your password?", "Restore": "Rest<PERSON>", "Don't have an account?": "Don't have an account?", "Register": "Register", "Enter 4-digits code": "Enter 4-digits code", "We've sent you code to": "We've sent you code to", "Resend code in": "Resend code in", "Verification Completed": "Verification Completed", "Done": "Done", "Check your email !!": "Check your email !!", "Plase confirm email address we sent you": "<PERSON><PERSON> confirm email address we sent you", "Ok, I will verify my email": "Ok, I will verify my email", "Sign Up": "Sign Up", "Have Account?": "Have Account?", "Create": "Create", "Resend": "Resend", "Enter the correct verification code": "Enter the correct verification code", "Logout": "Logout", "My account": "My Profile", "My rentals": "My Rentals", "Profile": "Profile", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Language": "Language", "Basic Data": "Basic Data", "Gender": "Gender", "Male": "Male", "Female": "Female", "ID Type": "ID Type", "Citizen / Resident": "Citizen / Resident", "Visitor": "Visitor", "National ID / Iqama": "National ID / Iqama", "First Name": "First Name", "Last Name": "Last Name", "Type": "Type", "Your phone is verified": "Your phone is verified", "Rent a car in": "Rent a car in", "Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in.": "Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in these cities Rent a car in.", "Rent a car in these cities": "Rent a car in these cities", "Makkah": "<PERSON><PERSON><PERSON>", "Madina": "<PERSON><PERSON>", "Return Car Time": "Return Car Time", "#Rental_Future": "#Rental_Future", "Car No.": "Car No.", "Model": "Model", "Count": "Count", "Make": "Make", "Duration in months": "Duration in months", "Ok": "Ok", "Cancel": "Cancel", "Expected pick up date": "Expected pick up date", "Insurance type": "Insurance", "Full": "Full", "Basic": "Basic", "Additional Notes": "Additional Notes", "Type here": "Type here", "This field is required": "This field is required", "Max is 60 Months": "Max is 60 Months", "Min is 1 Month": "Min is 1 Month", "Request sent successfully": "Request sent successfully", "Status": "Status", "Passport No.": "Passport No.", "Passport Expiray Date": "Passport Expiry Date", "minDateMessage": "Date should not be before minimal date", "Border Number": "Border No.", "Driver Liscence Expiry Date": "Driver License Expiry Date", "Date of Birth": "Date of Birth", "Images": "Images", "International Driver Licence": "International Driver Licence", "Driver Licence": "Driver Licence Image", "International Driver Licence with Selfie": "International Driver Licence with <PERSON><PERSON>", "Driver Licence with Selfie": "Driver Licence with <PERSON><PERSON>", "Passport Front Image": "Passport Front Image", "Please enter valid value": "Please enter valid value", "National ID": "National ID", "National ID Expiray Date": "National ID Expiry Date", "Iqama ID": "Iqama No.", "Gulf National ID": "Gulf National ID", "Invalid ID value": "Invalid ID value", "Version": "Version", "Iqama expiry date": "Iqama Expiry Date", "Nationality": "Nationality", "The max. allowed size is 20 Mb.": "The max. allowed size is 20 Mb.", "File's extensions are JPG / JPEG / PNG only allowed": "File's extensions JPG / JPEG / PNG are only allowed", "Driver license number": "Driver License Number", "Max. 20 numbers": "Max. 20 numbers", "Gulf ID Expiray Date": "Gulf ID Expiry Date", "Expiry date": "Expiry date", "You have to be signed in": "You have to be signed in", "We advise you to rent a car from the Carwah app, which will make it easy for you to move around easily and easily and visit all the tourist places in the city": "We advise you to rent a car from the Carwah app, which will make it easy for you to move around easily and easily and visit all the tourist places in the city", "Make Year": "Year", "Back": "Back", "Min. 1& Max. 60": "Min. 1& Max. 60", "Add Request": "Add Request", "Toyota Camry 2021": "Toyota Camry 2021", "Other": "Other", "Basic profile updated": "Basic data updated", "License profile updated": "Identity data updated", "profile image changed": "profile image changed", "trackRequests": "My requests", "Includes vat": "Including VAT", "kilometer/month": "Kilometer/Month", "AllyPolicies": "Ally's Policy & Conditions", "Accept": "Accept", "Reject": "Reject", "Offer has been accepted": "Offer has been accepted", "Offer has been rejected": "Offer has been rejected", "Rejected": "Rejected", "Accepted": "Accepted", "﷼ Monthly": "﷼ Monthly", "Sign In / Sign Up": "Sign In / Sign Up", "Show More": "Show More", "Show Less": "Show Less", "Hijri Calendar": "Hijri Calendar", "Find a car": "Find a car", "Choose Delivery Location": "Choose Delivery Location", "Delivery Location": "<PERSON>ose Delivery", "Delivery Terms & Conditions": "Delivery Terms & Conditions", "Delivery Policy": "Delivery Policy", "Delivery Policy Text": "There may be additional fees for the car delivery service to the customer, or it could be free it depends on the availability of <PERSON><PERSON><PERSON>'s allies, also you maybe require a minimum number of rental days per booking for them to provide you with the delivery service.", "Please fill search data!": "Please fill search data!", "There are no cars now": "There are no cars now", "Available at": "Available at:", "branch": "branch", "start from": "start from", "Search by make/model": "Search by make/model", "Choose from Allies below": "Choose from Allies below", "Open Now": "Open Now", "Closed Now": "Closed Now", "Closed": "Closed", "available": "available", ".sort": "By Distance", "max_distance_per_day.sort": "More Kilometer Allowed", "current_open_branches.sort": "Open Now", "daily_price_desc.sort": "Highest Price First", "daily_price.sort": "Lowest Price First", "Ally's class": "Ally's class", "A-class Ally": "Ally has a variety of rental services in several branches and cities with high quality services.", "B-class Ally": "Ally has limited rental services in specific branches and cities with a medium quality services.", "C-class Ally": "Ally has basic rental services in a few branches and cities.", "Branch location": "Branch location", "We will share the car location after confirmation": "We will share the car location after confirmation", "Get the Carwah App": "Get the Carwah App", "Ally's Conditions": "Ally's Conditions", "Requirements from the driver": "Requirements from the driver", "Driving license and identification": "Driving license and identification", "Rental Age": "Rental Age", "21 - 60 years old": "21 - 60 years old", "Payment & Cancellation Policy": "Payment & Cancellation Policy", "Security deposit, Cancellation Fees": "Security deposit, Cancellation Fees", "Fuel Policy": "Fuel Policy", "Fuel as it is": "Fuel as it is", "Working Hours": "Working Hours", "From": "From", "To": "To", "Booking Time": "Booking Time", "Booking Duration": "Duration", "Pickup Day & Time": "Pickup Day & Time", "Return Day & Time": "Return Day & Time", "Done Successfully": "Done Successfully", "Car is not available on time": "Car is not available on time", "Check your connection!": "Check your connection!", "Something went wrong": "Something went wrong!", "All": "All", "Unlimited Kilometer Free": "Unlimited Kilometer Free", "Car Return In Another City": "Car Return In Another City", "Today": "Today", "Allowed kilometer per day": "Allowed kilometer per day", "Extra Kilometer Cost": "Extra Kilometer Cost", "﷼ / KM": "﷼ / KM", "Standard Insurance Deductible": "Standard Insurance Deductible", "Full Insurance Per Day": "Full Insurance Per Day", "Unlimited Kilometer Cost": "Unlimited Kilometer Cost", "Extra services": "Extra services", "Show results": "Show results", "No results - There are no cars found matched your filter": "No results - There are no cars found matched your filter", "Car return in another branch": "Car return in another branch", "Service Fee": "Service Fee", "Choose car return location": "Choose car return location", "Free": "Free", "/ Per Destination": "/ Per Destination", "Pick-up location": "Pick-up location", "Return at same pick-up location": "Return at same pick-up location", "Return to the branch": "Return to the branch", "+VAT": "+ VAT", "Rent": "Rent", "Price per day": "Price per day", "Total days": "Total days", "Discount": "Discount", "dic.": "dic.", "Car Delivery Fee": "Car Delivery Fee", "VAT": "VAT", "one_way": "1 Destination", "two_ways": "2 Destinations", "Grand Total": "Grand Total", "Unlimited Kilometers": "Unlimited Kilometers", "Available time for pickup is": "Available time for pickup", "Available time for dropoff is": "Available time for dropoff", "View": "View", "the branch closed, please pick-up another time": "the branch closed, please pick-up another time", "Choose a payment method": "Choose a payment method", "Credit Card": "Credit Card", "Mada": "<PERSON><PERSON>", "sp.services__Pick-up": "Pick-up", "sp.services__from branch": "from branch", "sp.services__Delivery": "Delivery", "sp.services__Express": "Express", "sp.services__Instant": "Instant", "sp.services__Airports": "Airports", "sp.services__24 hour": "24 hour", "sp.services__Offers & Rental Packages": "Offers", "sp.services__Designed to meet your needs": "and Rental Packages", "sp.services__Confirmation": "Confirmation", "rental.integrated.status.Reject": "Not Paid", "rental.integrated.Reject": "We regret that your request wasn't accepted because you didn't comply with the terms and policy of the ally, the amount will be refunded to your card automatically", "rental.integrated.status.Error": "Unsuccessful Payment", "rental.integrated.status.Pending": "Pending payment", "rental.integrated.Error": "Dear customer.. the payment process was unsuccessful and your rental status is 'Pending', for immediate confirmation try to pay again", "rental.integrated.status.Success": "Payment has been received", "rental.integrated.Success": "Dear customer.. your rental has been successfully confirmed", "rental.integrated.Pending": "Dear customer... Your payment process is currently pending by Hyperpay. Meanwhile, you have created a pending booking, and its payment status will be updated once the payment is completed.", "rental.status.Success": "Payment completed successfully", "rental.status.Pending": "Pending payment", "installment.status.Success": "Payment has been received", "EDIT_RENTAL": "<PERSON>", "New Payment Order": "Payment Order", "rental.btn.pay": "Pay Now", "Installment Number": "Installment Number:", "installment.status.Pending": "Pending payment", "rental.Success": "Dear customer.. your reservation is a priority for us and will be confirmed as soon as possible. You can also follow the status of the reservation through the 'Rental' screen or by clicking on", "installment.Success": "Dear customer.. We assure you that we have received the installment amount", "renttoown.Success": "Dear customer.. We assure you that we have received the installment amount", "rental.status.Error": "Failed payment", "installment.status.Error": "Failed payment", "installment.status.RentError": "Failed payment", "rental.status.RentError": "Failed payment", "renttoown.Error": "Dear customer.. your payment was declined, and your car booking is pending. Please pay within 2 hours to avoid booking cancellation.", "rental.Error": "Dear customer.. the payment process was unsuccessful, try to pay again by clicking on", "installment.Error": "Dear customer.. your payment process failed, please try to pay by pressing Pay Now", "renttoown.Pending": "Dear customer... Your payment process is currently pending by Hyperpay. The payment status will be updated once the payment is completed. Please note that the rental will be canceled within 2 hours if the payment is not received.", "rental.Pending": "Dear customer... Your payment process is currently pending by Hyperpay. Meanwhile, you have created a pending booking, and its payment status will be updated once the payment is completed.", "installment.Pending": "Dear customer... Your payment process is currently pending by Hyperpay. Meanwhile, you have created a pending booking, and its payment status will be updated once the payment is completed.", "extension.Pending": "Dear customer... Your payment process is currently pending by Hyperpay. Meanwhile, you have created a pending booking, and its payment status will be updated once the payment is completed.", "rental.btn.Error": "Pay Now", "installment.btn.Error": "Pay Now", "renttoown.btn.RentError": "Pay Now", "rental.btn.Pending": "See Rental Details", "installment.btn.Pending": "See Rental Details", "rental.btn.Success": "See Rental Details", "rental.integrated.btn.Pending": "See Rental Details", "renttoown.btn.Pending": "See Rental Details", "installment.btn.Success": "Ok", "renttoown.btn.Success": "Ok", "rental.integrated.btn.Error": "Pay Now", "rental.integrated.btn.Success": "Pay Now", "rental.integrated.btn.Reject": "Okay", "update Rent": "Update Rent", "Ally number": "Ally number", "24 hours": "24 hours", "This place is outside of": "This place is out side of", "Branch is": "Branch is", "Please choose a delivery location": "Please choose a delivery location", "Tamara": "3 Payments interest free", "Do you have": "Do you have", "a coupon?": "a coupon?", "Add coupon here": "Add coupon here", "Remove": "Remove", "Invalid discount coupon": "Invalid coupon", "Coupon": "Coupon", "View car list": "View car list", "Coupon usage Terms": "Coupon usage Terms", "free_days": "Free Days", "free_delivery": "Free Delivery", "free_handover": "Free Handover", "Done.well": "Done", "tamara-payment-label": "Tamara: Split in up to 4 payments", "tamara-payment-subLabel": "Pay a fraction of your total now, and the rest over time. No hidden fees, no interest!", "Search by Vehicle Type": "Search by Vehicle Type", "Services.title": "Services", "Select your preferred package": "Select your preferred package:", "Extension Duration.modal": "Extension Duration", "Carwah Origin Story ": "Carwah Origin Story ", "Carwah was founded on the idea of making car rental easier for customers. One of the company's co-founders came up with the idea while waiting in line for an airport car rental. He realized that there were so many different car rental companies to choose from and it was difficult to determine which one offered the car that would meet his needs or have the best price. He believed there had to be a better way to rent a car than simply relying on name recognition and leaving the decision-making process up to the car rental companies. He wanted to create a platform that would give customers control over their car rental options. The result was an online platform that featured multiple car rental companies in one place and made the rental process more focused on the needs of the consumer rather than the name of the car rental company.": "Carwah was founded on the idea of making car rental easier for customers. One of the company's co-founders came up with the idea while waiting in line for an airport car rental. He realized that there were so many different car rental companies to choose from and it was difficult to determine which one offered the car that would meet his needs or have the best price. He believed there had to be a better way to rent a car than simply relying on name recognition and leaving the decision-making process up to the car rental companies. He wanted to create a platform that would give customers control over their car rental options. The result was an online platform that featured multiple car rental companies in one place and made the rental process more focused on the needs of the consumer rather than the name of the car rental company.", "Carwah officially launched its application on June 24, 2018 - a historic day for women in Saudi Arabia as it marked the day when women were granted the right to drive. The company's approach is simplicity. It offers an easy-to-navigate application that is available on all platforms and links customers with its allies (car rental companies). With Carwah, booking a car rental has never been easier.": "Carwah officially launched its application on June 24, 2018 - a historic day for women in Saudi Arabia as it marked the day when women were granted the right to drive. The company's approach is simplicity. It offers an easy-to-navigate application that is available on all platforms and links customers with its allies (car rental companies). With Carwah, booking a car rental has never been easier.", "Proceed.rent": "Proceed", "address-text": "H.Q - Kingdom of Saudi Arabia - Riyadh - Al-Takasosi Street - North Al-Maathar", "The monthly installments are as follows": "The monthly installments are as follows:", "wallet__history": "History", "Wallet.payment": "Wallet:", "Brief.Description": "During the previous years of our work at Carwah and understanding the market needs, we have collaborated with our partners to launch the Rent-to-Own service. This service aims to facilitate customers in owning the leased car if they choose to do so. Taking into consideration their right to cancel the contract at any time without restrictions, except for the non-refundable initial payment.", "rentToOwnFAQ": "FAQ", "FAQ1": "Are there complex conditions like financial institutions?", "FAQA1": "No complex conditions, only the presence of a national ID for citizens/ Iqama ID for residents + a valid Saudi driving license.", "FAQ2": "Are there financial obligations when returning the car before the end of the contract?", "FAQA2": "You can cancel the contract through the Carwah app, and coordination will be made with you for the return of the car and the finalization of the invoice until the day of delivery to the ally, with no possibility of refunding the initial payment. Your entitlement to use the car will be calculated at a rate of 7,000 kilometers per month, and any excess will be calculated according to the contract terms.", "FAQ3": "What is the final payment? And what are the procedures for owning the car?", "FAQA3": "The final payment is shown to you when renting the car, and <PERSON><PERSON><PERSON> assures you that the amount will not increase regardless of any price changes. After paying the final payment, ownership of the car is transferred directly to you.", "RentToOwn.Advantages": "Advantages", "invoice.1st payment": "1st payment", "Due Amounts Currently": "Due Amounts Currently:", "Due Amount Currently": "Due Amount Currently:", "view": "view extension request", "view.widget": "View", "request": "Extension Request", "creating": "In order to start creating a new extension request, you need bypass the current request.", "car rental refuse": "Dear customer.. Once you've received the car, you can't be able to cancel the rental.", "YakeenText": "Dear customer, welcome to 'Yakeen' Elm's account verification service. Account verification streamlines the reservation process, enabling our allies to confirm your booking swiftly.", "citizen": "Citizen", "gulf_citizen": "Gulf Citizen", "vistior": "Vistior", "resident": "Resident", "currency_sar": "﷼", "Cards": "Cards:", "Booking Number": "Booking Number:", "not verified": "Account Not Verified", "Verified": "Account", "Your Account": "Verified", "Pending rental alert": "Dear Customer, you already have a pending rental, you can proceed to rent another car after confirming or cancelling your pending rental.", "Membership ID.input": "Membership ID", "REPLACEMENT_CAR": "Replacement Car", "EXTRA_SERVICE": "Extra Services", "EDIT_PERIOD": "Edit Period", "payment_link_expired": "Payment link has expired", "request_new_payment_link": "To make your payments again, please request an additional payment link from the service provider", "return_to_home": "Return to Home", "payment_link_validity": "Payment Link Validity", "payment_data_secure": "Your transactions and data are 100% secure with us", "total": "Total", "includes_vat": "Includes VAT", "loading_payment": "Loading payment information...", "payment_error": "Payment Error", "thank_you": "Thank You", "payment_completed": "Payment completed successfully", "Your payment has been successfully processed.": "Your payment has been successfully processed.", "Your payment is being processed.": "Your payment is being processed.", "continue": "Continue", "carwah_payment": "Carwah Payment", "booking_number": "Booking Number", "cards": "Cards", "terms_conditions": "Terms & Conditions", "accept_terms": "By continuing you agree to", "card_number": "Card Number", "cvv": "CVV", "expiry_date": "Expiry Date", "card_holder": "Card Holder", "enter_card_holder": "Enter card holder name", "enter_card_number": "Enter card number", "enter_cvv": "Enter CVV", "enter_expiry": "MM/YY", "card_holder_error": "Please enter card holder name", "card_number_error": "Please enter a valid card number", "cvv_error": "Please enter a valid CVV", "expiry_error": "Please enter a valid expiry date", "aboutus": {"ourstory": {"title": "Our Story", "content": "<span class='bold'><PERSON><PERSON><PERSON>'s</span> journey began with a simple observation by one of our co-founders while standing in a long car rental queue at an airport. Despite the many rental companies available, customers still faced confusion, limited transparency, and time-consuming processes. The question arose: why shouldn't booking a rental car be as easy and convenient as ordering a pizza? <br /> From that idea, Carwah was born a digital platform bringing together car rental companies of all sizes into one smart, user-friendly space. Since our official <span class='bold'>launch on June 24, 2018 </span> a historic day marking women's right to drive in Saudi Arabia Carwah has grown into the Kingdom's leading car rental solution. We have built more than just an app; we've built a platform that empowers customers to choose the car they want, from the provider they prefer, with transparent pricing and hassle-free booking. From Riyadh to Jeddah, from airports to city centers, Carwah continues to shape the future of car rentals through innovation, reliability, and simplicity."}, "about": {"title": "About Us", "content": "<span class='bold'>Carwah</span> is Saudi Arabia’s leading digital car rental platform, transforming the way individuals and businesses rent vehicles across the Kingdom. Established in 2018, Carwah was created to solve a simple yet significant problem: long waits, limited options, and lack of transparency in traditional car rental services.", "stats": {"Cars": "Cars", "Cities and Airports": "Cities and Airports", "Rental Offices": "Rental Offices"}}, "visionmission": {"title": "<div class='d-flex flex-column'><span class='bold'>Our Vision</span> <span style='font-weight: normal;'>Our Mission</span></div>", "vision": {"title": "Vision", "content": "To become the go-to car rental platform in the GCC, creating value for customers, partners, and shareholders while shaping the future of mobility."}, "mission": {"title": "Mission", "content": "Every present has a future and Carwah is the future of car rental."}}, "features": {"title": "Features", "content1": "<div class='bold'>A Platform for All</div>Carwah works with rental companies of all sizes, from well-known international brands to small and medium-sized local companies, giving customers the widest variety of cars, services, and competitive pricing.", "content2": "<div class='bold'>Cloud Yard Innovation</div> We introduced the Cloud Yard a virtual fleet hub that allows rental cars to be delivered or picked up wherever the customer is. This reduces waiting times, eliminates the need for physical branch visits, and ensures a seamless car rental experience.", "content3": "<div class='bold'>Customer-Centric Service</div>At Carwah, customer convenience is at the heart of everything we do. Our 24/7 support, easy-to-use interface, and continuous product innovations keep us ahead in delivering exceptional rental experiences.", "content4": "<div class='bold'>Tailored B2B Solutions</div>For businesses and government sectors, Carwah offers long-term leasing solutions and on-demand fleet services, ensuring operational flexibility and cost efficiency."}, "values": {"title": "Our Values", "content1": "<div class='bold color-2 pb-2'>Simplicity</div>We believe car rental should be simple, intuitive, and hassle-free. Our platform is designed to make the process easy for everyone.", "content2": "<div class='bold color-2 pb-2'>Innovation</div> We are committed to continuous innovation, constantly developing new solutions to meet evolving customer needs and improve their experience.", "content3": "<div class='bold color-2 pb-2'>Transparency</div>Honesty and clarity are at the core of our business. We provide clear pricing, terms, and features with no hidden fees or surprises.", "content4": "<div class='bold color-2 pb-2'>Partnership</div>We foster strong, collaborative relationships with our allies, working together to deliver the highest quality service to customers.", "content5": "<div class='bold color-2 pb-2'>Customer Commitment</div>Our promise to all Carwah customers is, we will develop and upgrade our services to provide convenience to our valued customers."}, "And this is just the beginning ..": "And this is just the beginning .."}, "Hayak": "Welcome", "About.Us": "About Carwah", "Selected For You": "Selected For You", "Search By Vehicle Type": "Search By Vehicle Type", "Search By Extra Service": "Search By Extra Service", "Have a coupon?": "Have a coupon", "discount": "discount?", "If you face any issues, kindly use a supported browser such as:": "If you face any issues, kindly use a supported browser such as:", "Chrome, Firefox, Edge": "Chrome, Firefox, Edge", "Close": "Close", "Pay Again": "Pay Again"}