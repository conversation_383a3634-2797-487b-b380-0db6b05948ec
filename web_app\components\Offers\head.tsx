import { Container } from "@material-ui/core";
import React from "react";
import Link from "next/link";
import styled from "styled-components";

const Div = styled.div`
  padding-bottom: 20px;
  border-bottom: solid 1px #e1e3e7;
  @media (max-width: 768px) {
    > div {
      flex-direction: column;
      gap: 20px;
    }
  }
`;

export function Head({ t, offersRes }) {
  return (
    <Div>
      {offersRes?.businessRentalOffers ? (
        <Container className="d-flex justify-content-between align-items-center">
          <div className="d-flex gap-10px font-27px">
            <Link href="/my-requests" prefetch={false}>
              <span className="color-11 cursor-pointer">
                {t("My Requests")}
              </span>
            </Link>
            <span>&gt;</span>
            <span className="medium">{t("booking.Offers")}</span>
          </div>
          <div className="d-flex align-align-items-center justify-content-center gap-30px">
            <div
              className={`badge ${
                offersRes?.businessRentalOffers?.collection[0]?.businessRental
                  ?.status === "confirmed"
                  ? "bg-success"
                  : "bg-status"
              } font-16px text-white px-4 py-2 d-flex align-items-center`}
            >
              {
                offersRes?.businessRentalOffers?.collection[0]?.businessRental
                  ?.statusLocalized
              }
            </div>
            <div>
              <h6>{t("Booking No")}</h6>
              <p className="medium">
                {
                  offersRes?.businessRentalOffers?.collection[0]?.businessRental
                    ?.bookingNo
                }
              </p>
            </div>
          </div>
        </Container>
      ) : null}
    </Div>
  );
}
