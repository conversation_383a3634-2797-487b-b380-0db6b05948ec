import SwiperCarousel from "components/shared/carousel";
import moment from "moment";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { RootStateOrAny, useSelector } from "react-redux";
import styled from "styled-components";
import { Pagination } from "swiper";

const localizedMonths = {
  1: { en: "1 month", ar: "شهر" },
  2: { en: "2 months", ar: "شهرين" },
  3: { en: "3 months", ar: "ثلاثة أشهر" },
  4: { en: "4 months", ar: "أربعة أشهر" },
  5: { en: "5 months", ar: "خمسة أشهر" },
  6: { en: "6 months", ar: "ستة أشهر" },
  7: { en: "7 months", ar: "سبعة أشهر" },
  8: { en: "8 months", ar: "ثمانية أشهر" },
  9: { en: "9 months", ar: "تسعة أشهر" },
  10: { en: "10 months", ar: "عشرة أشهر" },
  11: { en: "11 months", ar: "أحد عشر شهراً" },
  12: { en: "12 months", ar: "اثنا عشر شهراً" },
  13: { en: "13 months", ar: "ثلاثة عشر شهراً" },
  14: { en: "14 months", ar: "أربعة عشر شهراً" },
  15: { en: "15 months", ar: "خمسة عشر شهراً" },
  16: { en: "16 months", ar: "ستة عشر شهراً" },
  17: { en: "17 months", ar: "سبعة عشر شهراً" },
  18: { en: "18 months", ar: "ثمانية عشر شهراً" },
  19: { en: "19 months", ar: "تسعة عشر شهراً" },
  20: { en: "20 months", ar: "عشرون شهراً" },
  21: { en: "21 months", ar: "واحد وعشرون شهراً" },
  22: { en: "22 months", ar: "اثنان وعشرون شهراً" },
  23: { en: "23 months", ar: "ثلاثة وعشرون شهراً" },
  24: { en: "24 months", ar: "أربعة وعشرون شهراً" },
};

const monthsCount = 24;

const Div = styled.div``;
const Wrapper = styled.div`
  .month {
     {
      &.selected {
        color: var(--color-3);
        font-weight: bold;
      }
      padding: 50px 10px;
      border: 2px solid;
      border-radius: var(--radius-3);
      display: flex;
      justify-content: center;
    }
    margin-bottom: 40px;
  }
  .button-wrap {
    display: flex;
    justify-content: center;
    button {
      padding: 15px 50px;
      background-color: var(--color-3);
      color: white;
      border: none;
      border-radius: var(--radius-3);
      margin-top: 20px;
    }
  }
  h6 {
    padding-bottom: 25px;
    /* color: var(--color-3); */
  }
  .swiper-container {
    bottom: 10px !important;
  }
`;

function InstallmentExtensionRequestModal({
  requestHandler,
  bookingId: rentalId,
  paymentMethod,
  extensionDetails,
}) {
  const { t, i18n } = useTranslation();
  const months = Array.from({ length: monthsCount }, (_, i) => i + 1);
  const [selectedMonth, setSelectedMonth] = useState();
  const rentalDetails = useSelector(
    (state: RootStateOrAny) => state?.booking?.rentalDetails
  );

  const { dropOffDate, dropOffTime } = rentalDetails || {};

  return (
    <Div>
      <Wrapper>
        <h6>{t("Choose Extension Period") as string}:</h6>
        <SwiperCarousel
          i18n={i18n}
          spaceBetween={15}
          slidesPerView={3.6}
          onSwiper={(swiper) => null}
          onSlideChange={() => null}
          pagination={{
            clickable: true,
            dynamicBullets: true,
            dynamicMainBullets: 3,
          }}
          modules={[Pagination]}
          slides={months.map((item: any) => {
            return (
              <div
                key={item}
                className={`month cursor-pointer font-14px ${
                  selectedMonth == item ? "selected" : ""
                }`}
                onClick={() => {
                  setSelectedMonth(item);
                }}
              >
                <p>{localizedMonths[item][i18n.language]}</p>
              </div>
            );
          })}
        />
        <div className="button-wrap">
          <button
            onClick={() =>
              requestHandler({
                paymentMethod: "ONLINE",
                withInstallment: true,
                withWallet: false,
                rentalId,
                dropOffTime,
                dropOffDate: selectedMonth
                  ? moment(dropOffDate)
                      .add(selectedMonth * 30, "days")
                      .format("DD/MM/YYYY")
                  : undefined,
              })
            }
            disabled={!selectedMonth}
          >
            {t("Confirm") as string}
          </button>
        </div>
      </Wrapper>
    </Div>
  );
}

export default InstallmentExtensionRequestModal;
