import { Grid } from "@material-ui/core";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  margin-top: 50px;
  > div {
    @media (max-width: 960px) {
      display: grid;
      grid-row-gap: 20px;
    }
    .bordered-card {
      border: solid 1px #e4e1e1;
      border-radius: var(--radius-2);
      padding: 40px 30px 30px 30px;
      margin: 0px 20px;
      height: 100%;
    }
  }
  h6 {
    margin-bottom: 10px !important;
  }
  p {
    font-size: 1.2rem;
  }
`;

export default function VisionMission() {
  const { t } = useTranslation();

  return (
    <Div>
      <Grid container justifyContent="space-between">
        <Grid item xs={12} md={2}>
          <div className="section-fancy-title">
            <h2>
              {t("Our Vision") as string}{" "}
              <span>{t("Our Message") as string}</span>
            </h2>
          </div>
        </Grid>
        <Grid item xs={12} md={5}>
          <div className="bordered-card">
            <h6>{t("Our Vision") as string}</h6>
            <p>
              {
                t(
                  "Our vision is to be a one-stop site for consumers in the GCC looking for car rentals, with a consumer-focused platform that creates value for every company and shareholder that collaborates with us."
                ) as string
              }
            </p>
          </div>
        </Grid>
        <Grid item xs={12} md={5}>
          <div className="bordered-card">
            <h6>{t("Our Mission") as string}</h6>
            <p>
              {
                t(
                  "“Every present has a future and carwah is the future of car rental.”"
                ) as string
              }
            </p>
          </div>
        </Grid>
      </Grid>
    </Div>
  );
}
