/* eslint-disable @next/next/no-img-element */
import WhiteCard from "components/shared/whiteCard";
import Link from "next/link";

export default function CityCard({ img, city, link }) {
  return (
    <WhiteCard>
      <Link href={`/city/${link}`} prefetch={false}>
        <div className="py-5 cursor-pointer text-center w-100">
          <img src={img} alt="img" />
          <h4 className="mt-4 medium text-center">{city}</h4>
        </div>
      </Link>
    </WhiteCard>
  );
}
