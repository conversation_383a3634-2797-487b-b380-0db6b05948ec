import { gql } from "@apollo/client";
const Accept_Business_Offers = gql`
  mutation acceptBusinessRentalOffer(
    $businessRentalId: ID!
    $businessRentalOfferId: ID!
  ) {
    acceptBusinessRentalOffer(
      businessRentalId: $businessRentalId
      businessRentalOfferId: $businessRentalOfferId
    ) {
      errors
      status
    }
  }
`;

const Reject_Business_Offers = gql`
  mutation rejectBusinessRentalOffer(
    $businessRentalId: ID!
    $businessRentalOfferId: ID!
  ) {
    rejectBusinessRentalOffer(
      businessRentalId: $businessRentalId
      businessRentalOfferId: $businessRentalOfferId
    ) {
      errors
      status
    }
  }
`;

export { Accept_Business_Offers, Reject_Business_Offers };
