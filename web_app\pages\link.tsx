import { Container, Grid } from "@material-ui/core";
import Layout from "components/shared/layout";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { androidStoreUrl, iosStoreUrl } from "utilities/enums";

function DeepLinkPage() {
  const { t } = useTranslation();

  const handleDeepLink = () => {
    if (typeof window == "undefined") return;
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    const isAndroid = /android/i.test(userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream;

    return { isAndroid, isIOS };
  };

  const { isAndroid, isIOS } = handleDeepLink();

  useEffect(() => {
    if (isAndroid) {
      window.location.href = androidStoreUrl;
    } else if (isIOS) {
      window.location.href = iosStoreUrl;
    }
  }, [isAndroid, isIOS]);

  return (
    <Layout>
      <div
        className="d-flex align-items-center"
        style={{ height: "calc(100vh - 270px)" }}
      >
        <Container>
          <p className="text-center mb-5">
            {t("Click the button below to open the app.") as string}
          </p>
          <Grid container justifyContent="center" spacing={4} id="buttons-wrap">
            {!isIOS ? (
              <Grid id="Android" item>
                <a href={androidStoreUrl} target="_blank" rel="noreferrer">
                  <img src={`/assets/images/android.svg`} alt="android" />
                </a>
              </Grid>
            ) : null}
            {!isAndroid ? (
              <Grid id="ios" item>
                <a href={iosStoreUrl} target="_blank" rel="noreferrer">
                  <img src={`/assets/images/ios.svg`} alt="ios" />
                </a>
              </Grid>
            ) : null}
          </Grid>
        </Container>
      </div>
    </Layout>
  );
}

export default DeepLinkPage;
