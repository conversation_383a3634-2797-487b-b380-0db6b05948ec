/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
import React, { useState, useEffect } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import InfoCard from "components/shared/InfoCard";
import { FormatDate } from "util/IntlMessages";
import moment from "moment";

export function CustomerDataDisplay({
  customerDetailsRes,
  withimages = true,
  walletBalance,
  refetch,
  isAgency,
}) {
  const [customerDetails, setCustomerDetails] = useState();
  const { locale } = useIntl();
  const customerInfo = customerDetailsRes?.user || customerDetailsRes?.users?.collection?.[0] || {};
  const user_data = JSON.parse(localStorage.getItem("user_data"));
  const agencyId = user_data?.agency_id;

  useEffect(() => {
    if (customerInfo) {
      const {
        dob,
        driverLicense,
        firstName,
        gender,
        lastName,
        licenseFrontImage,

        nid,
        passportNumber,
        email,
        companyName,
        middleName,
        customerProfile,
        licenseSelfieImage,
        status,
        isDeleted,
      } = customerInfo?.customerProfile || {};
      const { mobile, isActive, successfulRentals, profileImage } = customerInfo;
      const driverLicenseExpireAt =
        customerDetailsRes?.user?.customerProfile?.driverLicenseExpireAt ||
        customerDetailsRes?.users?.collection?.[0]?.customerProfile?.driverLicenseExpireAt;
      const nationalIdExpireAt =
        customerDetailsRes?.user?.customerProfile?.nationalIdExpireAt ||
        customerDetailsRes?.users?.collection?.[0]?.customerProfile?.nationalIdExpireAt;
      const nationalIdVersion =
        customerDetailsRes?.user?.customerProfile?.nationalIdVersion ||
        customerDetailsRes?.users?.collection?.[0]?.customerProfile?.nationalIdVersion;
      const nationality =
        customerDetailsRes?.user?.customerProfile?.nationality ||
        customerDetailsRes?.users?.collection?.[0]?.customerProfile?.nationality;
      const customerClassLocalized =
        customerDetailsRes?.user?.customerProfile?.customerClassLocalized ||
        customerDetailsRes?.users?.collection?.[0]?.customerProfile?.customerClassLocalized;

      let customerDetails = [
        { msgId: "rental.nameFirstName", value: firstName },
        { msgId: "rental.nameMiddleName", value: middleName },
        { msgId: "rental.nameLastName", value: lastName },
        { msgId: "email.address", value: email },
        { msgId: "rental.mobileNumber", value: mobile },
        { msgId: "rental.companyName", value: companyName },
        { msgId: "rental.userType", value: <FormattedMessage id={status || "-"} /> },

        {
          msgId: "status",
          value: isDeleted ? (
            <FormattedMessage id="Deleted" />
          ) : Boolean(
              agencyId
                ? customerInfo?.agencyCustomerProfiles?.find((i) => i.agencyId == agencyId)
                    ?.isActive
                : isActive,
            ) ? (
            <FormattedMessage id="active" />
          ) : (
            <FormattedMessage id="inactive" />
          ),
        },
        { msgId: "dob", value: dob },
        { msgId: "rental.driverSLicenseIfFound", value: driverLicense },
        { msgId: "rental.gender", value: <FormattedMessage id={gender || "-"} /> },
        { msgId: "driverLicenseExpireAt", value: driverLicenseExpireAt },
        !isAgency ? { msgId: "customerClass", value: customerClassLocalized } : undefined,
        { msgId: "Wallet balance", value: walletBalance?.userWallet?.balance },
        { msgId: "Successful Bookings", value: successfulRentals },
      ];

      const citizenData = [
        { msgId: "rental.nationalId", value: nid },
        {
          msgId: "nationalIdVersion.label",
          value: customerProfile?.nationalIdVersion,
        },
        {
          msgId: "nationalIdExpireAt",
          value: customerProfile?.nationalIdExpireAt ? (
            <FormatDate value={customerProfile?.nationalIdExpireAt} />
          ) : (
            ""
          ),
        },
        { msgId: "rental.age", value: moment().diff(moment(dob), "years") },
      ];
      const residentData = [
        { msgId: "iqama.no", value: nid },
        { msgId: "nationality", value: nationality?.[`${locale}Name`] },

        { msgId: "nationalIdVersion.label", value: nationalIdVersion },
        { msgId: "iqamaIdExpireAt", value: nationalIdExpireAt },
      ];
      const GulfData = [
        { msgId: "Gulf_ID", value: nid },
        { msgId: "nationality", value: nationality?.[`${locale}Name`] },
        { msgId: "GulfIdExpireAt", value: nationalIdExpireAt },
      ];

      const visitorData = [
        { msgId: "rental.passportNumber", value: passportNumber },

        {
          msgId: "passportExpireAt",
          value: customerProfile?.passportExpireAt ? (
            <FormatDate value={customerProfile?.passportExpireAt} />
          ) : (
            ""
          ),
        },
        { msgId: "rental.age", value: dob ? moment().diff(moment(dob), "years") : "" },
        customerProfile?.nationality?.id && {
          msgId: "nationality",
          value: customerProfile.nationality[`${locale}Name`],
        },
      ];

      if (status === "citizen") {
        customerDetails = [...customerDetails, ...citizenData];
      }
      if (status === "visitor") {
        customerDetails = [...customerDetails, ...visitorData];
      }
      if (status === "resident") {
        customerDetails = [...customerDetails, ...residentData];
      }
      if (status === "gulf_citizen") {
        customerDetails = [...customerDetails, ...GulfData];
      }

      if (withimages) {
        customerDetails.unshift({
          image: profileImage,
          imageDetails: {
            className: "img-responsive",
            containerClassName: "profile-userpic",
          },
        });

        if (licenseFrontImage) {
          customerDetails.push({
            id: "licenseFrontImage",
            image: licenseFrontImage,
            imageDetails: { className: "w-75" },
          });
        }
        if (licenseSelfieImage) {
          customerDetails.push({
            id: "licenseSelfieImage",
            image: licenseSelfieImage,
            imageDetails: { className: "w-75" },
          });
        }
        if (customerProfile?.passportFrontImage) {
          customerDetails.push({
            id: "passportFrontImage",
            image: customerProfile?.passportFrontImage,
            imageDetails: { className: "w-75" },
          });
        }
        if (customerProfile?.businessCard) {
          customerDetails.push({
            id: "businessCard",
            image: customerProfile.businessCard,
            imageDetails: { className: "w-75" },
          });
        }
      }
      setCustomerDetails(customerDetails);
    }
  }, [customerDetailsRes, walletBalance]);

  return (
    <InfoCard
      fullwidth
      data={customerDetails}
      yakeenTriesLeft={customerInfo?.customerProfile?.yakeenTriesLeft}
      userId={customerInfo?.id}
      isYakeenVerified={customerInfo?.customerProfile?.isYakeenVerified}
      titleId="rental.customer.details"
      refetch={refetch}
      agencyCustomerProfiles={customerInfo?.agencyCustomerProfiles}
    />
  );
}
