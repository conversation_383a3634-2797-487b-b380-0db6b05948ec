/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect } from "react";
import { clearBookingData, setRentalBranchData } from "store/boooking/action";
import { setClearCarData } from "store/cars/action";
import useRentalType from "./heplers/rentalType";
import { claerUserDataAction } from "store/user/action";
import { useRouter } from "next/router";
import {
  clearLoyalityData,
  verifyFursanMembershipId,
} from "store/loyality/action";
import { setShowInstallmmentBreakdownModal } from "store/installments/action";
import { setFullyPaidByWallet } from "store/wallet/action";
import {
  setDateTimeAction,
  setSelectionIndexAction,
} from "store/search/action";
import { default_date_time } from "store/search/reducer";

const useCustomEffect = ({
  bookingId,
  rentalDetails,
  getRentalDetails,
  dispatch,
  carDataRes,
  setCarData,
  GetRentalDetails,
  payWithWalletAction,
  setPayWithInstallments,
  getRentPrice,
  search_data,
  cars,
  pay_with_installments,
  pay_with_wallet,
  RentalAboutPrice,
  setRentalAboutPrice,
  availablePaymentMethods,
  deliverType,
  deliveryPaymentMethod,
  branchSupportedPayment,
  SetAvialablePaymentMethods,
  paymentMethod,
  SelectPaymentMethod,
  rentalData,
  isExtensionModalOpen,
  clearExtensionsData,
  rentalPaymentMethod,
  dropOffBranchId,
  branchId,
  getBranchData,
  refetchRentalAboutPrice,
}) => {
  const router = useRouter();
  const { car } = router.query || {};

  const { isRentToOwn } = useRentalType();

  useEffect(() => {
    if (bookingId) {
      getRentalDetails().then((res) => {
        const { carId } = res.data.rentalDetails || {};
        if (carId && !car) {
          //handle redirection to carId in case pending rental exists
          router.push(`${router.pathname}?car=${carId}&bookingId=${bookingId}`);
        }
      });
    }
    refetchRentalAboutPrice();
  }, [bookingId]);

  useEffect(() => {
    if (carDataRes) {
      dispatch(setCarData(carDataRes));
    }
  }, [carDataRes]);

  useEffect(() => {
    if (bookingId && rentalDetails) {
      dispatch(GetRentalDetails(rentalDetails.rentalDetails));
      if (rentalDetails?.rentalDetails?.installments?.length && !isRentToOwn) {
        dispatch(setPayWithInstallments(true));
      } else {
        dispatch(setPayWithInstallments(false));
      }
      getBranchData({ variables: { id: dropOffBranchId || branchId } }).then(
        (res) => {
          if (res?.data) {
            dispatch(setRentalBranchData(res?.data?.branch));
          }
        }
      );
    }
  }, [rentalDetails]);

  useEffect(() => {
    if (search_data && !bookingId && getRentPrice) {
      getRentPrice();
    }
  }, [
    search_data,
    cars?.extra_services,
    cars?.deliveryType,
    cars?.paymentMethod,
    pay_with_installments,
    pay_with_wallet,
    cars?.rent_to_own_plan?.id,
    cars?.insuranceId,
    cars?.handover_in_another_branch,
  ]);
  useEffect(() => {
    const { rentalAboutPrice } = RentalAboutPrice || {};
    if (rentalAboutPrice) {
      dispatch(setRentalAboutPrice(rentalAboutPrice));
    }
  }, [RentalAboutPrice?.rentalAboutPrice]);

  useEffect(() => {
    // In car details set avialable payment methods from about price and in rental set either payment method or delivery payment method method
    const method = bookingId
      ? deliverType?.includes("way")
        ? deliveryPaymentMethod
        : branchSupportedPayment
      : availablePaymentMethods;
    dispatch(
      SetAvialablePaymentMethods(method === "all" ? "cash_and_online" : method)
    );
    const selectePaymentMethod = method === "cash" ? "CASH" : "MADA";
    dispatch(SelectPaymentMethod(selectePaymentMethod));
  }, [
    bookingId,
    availablePaymentMethods,
    deliverType,
    deliveryPaymentMethod,
    branchSupportedPayment,
  ]);

  useEffect(() => {
    return () => {
      dispatch(clearExtensionsData());
      dispatch(payWithWalletAction(false));
      dispatch(setClearCarData());
      dispatch(clearBookingData());
      dispatch(setPayWithInstallments(false));
      dispatch(setSelectionIndexAction(0));
      dispatch(setDateTimeAction(default_date_time));
      dispatch(claerUserDataAction());
      dispatch(clearLoyalityData());
      dispatch(verifyFursanMembershipId(false));
      dispatch(setShowInstallmmentBreakdownModal(false));
      dispatch(setFullyPaidByWallet("Error"));
      dispatch(setRentalAboutPrice(null));
    };
  }, []);
};

export default useCustomEffect;
