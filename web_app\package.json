{"name": "web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.8.1", "@date-io/date-fns": "1.x", "@date-io/hijri": "^1.3", "@date-io/moment": "1.x", "@material-ui/core": "^4.12.3", "@material-ui/icons": "^4.11.2", "@material-ui/lab": "^4.0.0-alpha.60", "@material-ui/pickers": "^3.3.10", "@testing-library/jest-dom": "^S5.14.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "babel-plugin-styled-components": "^1.13.2", "base64url": "^3.0.1", "bootstrap": "^5.0.2", "browserslist": "^4.20.0", "crypto": "^1.0.1", "date-fns": "^2.28.0", "dotenv": "^10.0.0", "exceljs": "^4.3.0", "firebase": "^10.10.0", "google-map-react": "^2.1.10", "graphql": "^16.2.0", "group-array": "^1.0.0", "html-react-parser": "^1.4.4", "i18next": "^20.3.5", "i18next-browser-languagedetector": "^6.1.2", "material-ui-pickers": "^2.2.4", "material-ui-pickers-hijri-utils": "^0.5.4", "moment": "^2.29.1", "moment-hijri": "^2.1.2", "next": "^12.0.7", "next-transpile-modules": "^8.0.0", "next-with-less": "^1.0.1", "nextjs-toast": "^1.0.4", "notistack": "^1.0.10", "react": "^17.0.2", "react-alice-carousel": "^2.5.1", "react-dom": "^17.0.2", "react-geocode": "^0.2.3", "react-hook-form": "^7.23.0", "react-i18next": "^11.11.4", "react-modal-image": "^2.6.0", "react-multi-date-picker": "^3.1.0", "react-phone-input-2": "2.14.0", "react-phone-number-input": "^3.1.44", "react-photoswipe-gallery": "^3.0.1", "react-redux": "^7.2.4", "react-scripts": "4.0.3", "react-select": "^5.2.2", "react-share-social": "^0.1.55", "react-smartbanner": "^5.1.4", "react-svg": "^14.0.9", "read-excel-file": "^5.6.1", "redux": "^4.1.1", "redux-devtools-extension": "^2.13.9", "redux-persist": "^6.0.0", "sass": "^1.37.2", "sitemap": "^7.0.0", "styled-components": "^5.3.0", "sweetalert2": "^11.4.0", "swiper": "^6.8.1", "use-file-upload": "^1.0.11", "uuid": "^11.1.0", "web-vitals": "^1.1.2", "xlsx": "^0.18.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "18.7.1", "@types/react": "18.0.1", "@types/react-dom": "18.0.6", "eslint": "8.21.0", "eslint-config-next": "12.2.4", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "prettier": "^2.3.2", "typescript": "4.7.4"}}