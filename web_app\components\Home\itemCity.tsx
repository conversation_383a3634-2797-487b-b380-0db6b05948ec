/* eslint-disable @next/next/no-img-element */
import { Grid } from "@material-ui/core";
import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import NavigateNextIcon from "@material-ui/icons/NavigateNext";
import NavigateBeforeIcon from "@material-ui/icons/NavigateBefore";

const GridElement = styled(Grid)`
  padding: 10px;
  border-radius: var(--radius-2);
  cursor: pointer;
  transition: all ease 0.5s;
  background-color: var(--color-4);
  img {
    border-radius: 50%;
    width: 100%;
  }

  > div:last-child {
    width: fit-content;
    h5 {
      font-size: 17px;
      font-weight: 900;
      margin-bottom: 5px !important;
    }
    h6 {
      span {
        color: var(--color-3);
        font-size: 14px;
        &:first-child {
          font-weight: 900;
        }
        &:last-child {
          margin: 0 5px;
        }
      }
    }
    svg {
      fill: #9ea6a9;
    }
  }
  &.is-active {
    background-color: var(--color-3);
    h5,
    span {
      color: var(--color-4) !important;
    }
    svg {
      fill: var(--color-4) !important;
    }
  }
`;

export default function Item({
  img,
  name,
  count,
  isActive,
  itemIndex,
  setSelectedItemIndex,
}) {
  const { t, i18n } = useTranslation();

  return (
    <GridElement
      container
      alignItems="center"
      justifyContent="space-between"
      className={`${isActive ? "is-active" : null} box-item`}
      onClick={() => setSelectedItemIndex(itemIndex)}
    >
      <Grid item xs={3}>
        <img src={img} alt="Carwah Car Rental - كروة لأيجار السيارات" />
      </Grid>
      <Grid
        item
        xs={8}
        container
        justifyContent="space-between"
        alignItems="center"
      >
        <Grid item>
          <h5>{name}</h5>
          <h6>
            <span>{count}</span>
            <span>{t("Branch")}</span>
          </h6>
        </Grid>
        <Grid item>
          {process.browser ? (
            i18n.language === "en" ? (
              <NavigateNextIcon />
            ) : (
              <NavigateBeforeIcon />
            )
          ) : null}
        </Grid>
      </Grid>
    </GridElement>
  );
}
