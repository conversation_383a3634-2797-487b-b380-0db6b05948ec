import { useTranslation } from "react-i18next";
import styled from "styled-components";

const Div = styled.div`
  padding: 40px 0 40px 0;
  h1,
  h6 {
    /* color: var(--color-11); */
    font-weight: 500;
    text-transform: uppercase;
    font-size: 22px;
    margin-bottom: 10px !important;
  }
  p {
    font-size: 2rem;
    /* line-height: 55px; */
    color: var(--color-1);
  }
  @media (max-width: 570px) {
    p {
      font-size: 1.25rem !important;
      line-height: 35px !important;
    }
  }
`;

export default function Discover() {
  const { t } = useTranslation();
  return (
    <Div>
      <h1>{t("About Carwah") as string}</h1>
      <p>
        {
          t(
            "At Carwah, we build the community through strong partnerships with our allies to provide many advantages for our clients and partners"
          ) as string
        }
      </p>
    </Div>
  );
}
