import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { TProfileStatus } from "types/profile";

const statusLookup: TProfileStatus[] = [
  "citizen",
  "resident",
  "gulf_citizen",
  "visitor",
];

function Statuses({ control, watch }) {
  const { t } = useTranslation();

  return (
    <div className="status-wrapper">
      {statusLookup.map((item) => {
        return (
          <Controller
            key={item}
            name={"status"}
            control={control}
            render={({ field, fieldState }) => {
              return (
                <div
                  onClick={() => {
                    field.onChange(item);
                  }}
                  style={{
                    backgroundColor:
                      watch("status") === item ? "#F2F8FA" : "#F8F8F8",
                    color: watch("status") === item ? "#7AB3C5" : "#4D5156",
                    fontWeight: "bold",
                  }}
                  {...field}
                >
                  {t(item) as string}
                </div>
              );
            }}
          />
        );
      })}
    </div>
  );
}

export default Statuses;
