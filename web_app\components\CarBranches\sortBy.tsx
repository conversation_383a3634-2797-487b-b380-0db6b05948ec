/* eslint-disable @next/next/no-img-element */
import { ClickAwayListener } from "@material-ui/core";
import Tooltip from "components/shared/tooltip";
import { useRouter } from "next/router";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

//Styled Components
const TooltipWrapper = styled.div``;

const TooltipBody = styled.div`
  color: var(--color-4);
  padding: 0 25px;
  > div {
    display: flex;
    gap: 40px;
    justify-content: space-between;
    img:not(.active) {
      display: none;
    }
    .active + img {
      display: block;
    }
    span:hover {
      color: var(--color-3);
    }
    span.active {
      color: var(--color-3);
    }
  }
`;
export default function SortBy({ sortBy, setSortBy, sort_by }) {
  //State
  const [isSortingTooltipOpen, setIsSortingTooltipOpen] = useState(false);

  //Hooks
  const { t, i18n } = useTranslation();
  const router = useRouter();

  return (
    <ClickAwayListener
      touchEvent={"onTouchStart"}
      mouseEvent={"onMouseDown"}
      onClickAway={() => setIsSortingTooltipOpen(false)}
    >
      <TooltipWrapper
        className="position-relative"
        onClick={() => setIsSortingTooltipOpen(true)}
      >
        <span className="cursor-pointer">{t("Sort by")}</span>
        <img
          className="cursor-pointer"
          src="/assets/images/sort.svg"
          alt="icon"
        />
        <Tooltip
          isOpen={isSortingTooltipOpen}
          style={{
            left: i18n.language === "en" ? "" : "0",
            right: i18n.language === "ar" ? "" : "0",
          }}
        >
          <TooltipBody>
            {[
              "",
              "daily_price_desc",
              "daily_price",
              "max_distance_per_day",
              "current_open_branches",
            ].map((item) => {
              return (
                <div key={item} className="py-2">
                  <span
                    onClick={(e) => {
                      setSortBy(null);
                      e.target.classList.add("active");
                      setSortBy(item);
                      router.replace({
                        query: { ...router.query, sort_by: item },
                      });
                    }}
                    className={`sort-label ${
                      sortBy === item || sort_by === item ? "active" : ""
                    }`}
                    data-value="distance"
                  >
                    {t(`${item}.sort`)}
                  </span>
                  <img src="/assets/images/rightMark.svg" alt="icon" />
                </div>
              );
            })}
          </TooltipBody>
        </Tooltip>
      </TooltipWrapper>
    </ClickAwayListener>
  );
}
