/* eslint-disable @next/next/no-img-element */
import Popup from "components/shared/popup";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { RootStateOrAny, useSelector } from "react-redux";
import { useRouter } from "next/router";

const Div = styled.div`
  font-size: 20px;
  line-height: 30px;
  max-width: 90%;
  margin: 0 auto;
  gap: 20px;
  h3 {
    @media (max-width: 900px) {
      font-size: 18px;
      margin-top: 15px !important;
    }
  }
  p {
    text-align: center !important;
    font-weight: 400;
    @media (max-width: 900px) {
      font-size: 16px;
    }
  }
  img {
    width: 60px;
  }
  .app {
    gap: 20px;
  }
  /* @media (min-width: 901px) {
    width: %;
  } */
`;
const Button = styled.button`
  background: var(--color-3);
  border-radius: var(--radius-3);
  padding: 10px 18px;
  height: 60px;
`;
export default function UnseenExtensionPopUp({
  isOpen,
  setIsOpen,
  hideActionButton = false,
  _carId,
  _rentalId,
  cb,
}) {
  const { t } = useTranslation();
  const { rentalId, carId } = useSelector(
    (state: RootStateOrAny) => state.authentication
  );
  const route = useRouter();
  const SeeRental = () => {
    route.push(
      `/car-details?car=${_carId || carId}&bookingId=${_rentalId || rentalId}`
    );
    setIsOpen(false);
    cb && cb();
  };
  return (
    <Popup isOpen={isOpen} setIsOpen={setIsOpen}>
      <Div className="d-flex justify-content-center align-items-center flex-column">
        <img
          src="/assets/icons/checked.svg"
          alt="checked"
          style={{ margin: "0 auto" }}
        />
        <h3 className="bold text-center">
          {t("Extension request has been confirmed") as string}
        </h3>

        <p className="bold">
          {
            t(
              "Dear customer.. your extension request is confirmed now. You can follow up on your request through Rental"
            ) as string
          }
        </p>
        {!hideActionButton ? (
          <div className="d-flex flex-column app justify-content-center">
            <Button
              type="submit"
              className="font-17px color-4 border-0 medium w-100 mt-4"
              onClick={() => SeeRental()}
            >
              {t("See Rental Details")}
            </Button>
          </div>
        ) : null}
      </Div>
    </Popup>
  );
}
