import Head from "next/head";
import { useRouter } from "next/router";
import MyFAQs from "components/FAQs";
import { useEffect, useState } from "react";
function FAQs({ locale }) {
  const router = useRouter();
  const [data, setData] = useState();

  useEffect(() => {
    if (typeof window != "undefined") {
      fetch(`${window.location.origin}/faq.json`)
        .then((res) => res.json())
        .then((data) => setData(data[locale]));
    }
  }, [locale]);
  return (
    <div>
      <Head>
        <link
          rel="canonical"
          href={`https://carwah.com.sa/${locale}${router?.pathname}`}
        />
        <link
          rel="alternate"
          hrefLang={locale === "en" ? "ar" : "en"}
          href={`https://carwah.com.sa/${locale === "en" ? "ar" : "en"}${
            router?.pathname
          }`}
        />
      </Head>
      <MyFAQs data={data} />
    </div>
  );
}

export async function getStaticProps(context: { locale: string }) {
  const { locale } = context;

  return {
    props: {
      locale,
    },
  };
}

export default FAQs;
