import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import MessagePopup from "./messagePopup";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import styled from "styled-components";
import { setShowInstallmmentBreakdownModal } from "store/installments/action";
export const DIV = styled.div`
  .button {
    color: var(--color-3);
    padding: 15px 20px 15px 20px;
    cursor: pointer;
    background: #f2f8fa;
  }
  .btn-back {
    color: #000;
    padding: 10px 20px 15px 20px;
    cursor: pointer;
  }
`;

export default function SuccessPopup({ isOpen, setIsOpen, isInstallment }) {
  const router = useRouter();
  const { car, bookingId } = router.query || {};
  const { t } = useTranslation();
  const { rentalId, carId, rentalDetails } = useSelector(
    (state: RootStateOrAny) => state.booking
  );

  const dispatch = useDispatch();
  const { pay_with_wallet, is_paid_by_wallet } =
    useSelector((state: RootStateOrAny) => state.wallet) || {};
  const _isInstallment = !pay_with_wallet && isInstallment;
  const RentalMessge = () => {
    return (
      <>
        <p>
          {
            t(
              "Dear customer.. your rental is pending now. You can also follow the status of the rental through the “Rental” screen or by clicking on"
            ) as string
          }
        </p>
        <DIV className="d-flex justify-content-between mt-3">
          <div
            className="button"
            onClick={() => {
              router.push(
                `car-details?car=${carId || rentalDetails?.carId}&bookingId=${rentalId || rentalDetails?.id || bookingId}`
              );
              dispatch(setShowInstallmmentBreakdownModal(false));
              setIsOpen(false);
            }}
          >
            {t("See Rental Details") as string}
          </div>
          <div className="btn-back" onClick={() => router.push("/")}>
            {t("Back to Home") as string}
          </div>
        </DIV>
      </>
    );
  };
  const CashInstallmentMessge = () => {
    return (
      <>
        <p>
          {
            t(
              "Dear customer.. Kindly visit the branch to pay the due installment"
            ) as string
          }
        </p>
        <DIV className="d-flex justify-content-between mt-3">
          <div
            className="button"
            onClick={() => {
              if (_isInstallment) {
                router.push(
                  `${router.pathname}?car=${car}&bookingId=${bookingId || rentalId}`
                );
                dispatch(setShowInstallmmentBreakdownModal(false));
                setIsOpen(false);
              }
            }}
          >
            {t("Ok") as string}
          </div>
        </DIV>
      </>
    );
  };

  if (isOpen) {
    return (
      <MessagePopup
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        onClose={() => {
          router.push(`${router.pathname}?car=${car}&bookingId=${bookingId || rentalId}`);
          dispatch(setShowInstallmmentBreakdownModal(false));
          setIsOpen(false);
        }}
        body={
          is_paid_by_wallet === "Success" ? (
            <p>{t("Payment Done Successfully") as string}</p>
          ) : !_isInstallment ? (
            <RentalMessge />
          ) : (
            <CashInstallmentMessge />
          )
        }
        title={
          is_paid_by_wallet === "Success"
            ? "Payment Status"
            : !_isInstallment
              ? t("Rental request has been sent")
              : t("Cash Payment")
        }
      />
    );
  }
  return null;
}
