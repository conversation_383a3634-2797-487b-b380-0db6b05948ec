/* eslint-disable react-hooks/exhaustive-deps */
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { Switch } from "@material-ui/core";
import { memo, useEffect, useState } from "react";
import ReactSelect from "react-select";
import groupArray from "group-array";
import { RootStateOrAny, useDispatch, useSelector } from "react-redux";
import { setDeliveryType, setHandoverInAnotherBranch } from "store/cars/action";
import { useRouter } from "next/router";
import RiyalSymbol from "components/shared/RiyalSymbol";

const Div = styled.div`
  border-radius: var(--radius-2);
  background-color: var(--color-4);
  margin: 24px 0;
  position: relative;
  > div {
  }
`;
function HandoverInAnotherBranch() {
  //Hooks
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();

  //Store
  const { handover_in_another_branch, delivery, car_data } =
    useSelector((state: RootStateOrAny) => state.cars) || {};
  const { user_address } =
    useSelector((state: RootStateOrAny) => state.search_data) || {};

  //Varaibles
  const { canDelivery } = car_data?.branch || {};
  const { availableHandoverCities } = car_data?.branch || {};
  const { availableHandoverBranches } = car_data?.branch || {};

  const mappedCities = availableHandoverCities?.map((city) => {
    return {
      ...city,
      areaId:
        city.pickUpCity.id == user_address?.pick_up?.id
          ? city.dropOffCity.id
          : city.pickUpCity.id,
    };
  });

  const mappedBranches = availableHandoverBranches
    ?.filter((i) => i.isActive)
    ?.map((branch) => {
      return { ...branch, areaId: branch.area.id };
    });

  const groupedCities = groupArray(mappedCities, "areaId");

  delete groupedCities[user_address?.pick_up?.id];

  const branchesWithCities = Object?.values(
    //Restructuring data to form array of objects each object contains area and branches
    groupArray(mappedBranches, "areaId")
  )
    .map((i) => {
      return { area: groupedCities[i[0].areaId], branches: i };
    })
    .filter((i) => i.area);

  const branchesWithCitiesGroupedOptions = branchesWithCities?.map((i: any) => {
    // Restructure array to fit react-select options schema
    return {
      label:
        i.area[0].dropOffCity.id == i.area[0].areaId
          ? i.area[0].dropOffCity.name
          : i.area[0].pickUpCity.name,
      price: i.area[0].price,
      cityId:
        i.area[0].dropOffCity.id == i.area[0].areaId
          ? i.area[0].dropOffCity.id
          : i.area[0].pickUpCity.id,
      isFree: i.area[0].isFree,
      options: i.branches.map((branch) => {
        return {
          label: branch.name,
          value: branch.id,
          price: i.area[0].price,
          isFree: i.area[0].isFree,
          cityId:
            i.area[0].dropOffCity.id == i.area[0].areaId
              ? i.area[0].dropOffCity.id
              : i.area[0].pickUpCity.id,
        };
      }),
    };
  });

  const [selectedBranch, setSelectedBranch] = useState(
    handover_in_another_branch?.option ||
      branchesWithCitiesGroupedOptions[0]?.options[0]
  );

  //Functions
  function switchHandler(e) {
    const checked = e?.target?.checked;
    dispatch(
      setHandoverInAnotherBranch({ ...handover_in_another_branch, checked })
    );
    if (checked && canDelivery && delivery?.checked) {
      dispatch(setDeliveryType("one_way"));
    } else if (checked && canDelivery && !delivery?.checked) {
      dispatch(setDeliveryType("no_delivery"));
    }
  }

  const formatGroupLabel = (data: any) => (
    <div className="d-flex justify-content-between">
      <span>{data.label}</span>
      <span style={{ color: "#6FCFA1" }}>
        {!data.isFree && (
          <div className="d-flex gap-5px">
            <div style={{ order: i18n.language === "ar" ? 1 : 0 }}>
              <RiyalSymbol />
            </div>
            <div style={{ order: i18n.language === "ar" ? 0 : 1 }}>
              {data.price}
            </div>
          </div>
        )}
        {data.isFree && `${t("Free")}`}
      </span>
    </div>
  );

  //LifeCycle
  useEffect(() => {
    if (
      branchesWithCitiesGroupedOptions?.length &&
      !handover_in_another_branch?.option
    ) {
      dispatch(
        setHandoverInAnotherBranch({
          branchId: branchesWithCitiesGroupedOptions[0]?.options[0]?.value,
          option: branchesWithCitiesGroupedOptions[0],
        })
      );
    }
  }, [branchesWithCitiesGroupedOptions]);
  const rentalData = useSelector((state: RootStateOrAny) => state.booking);
  const router = useRouter();

  const bookingId = router?.query.bookingId;

  return (
    <Div className="p-4">
      <div className="d-flex gap-10px align-items-center">
        <div className="d-flex justify-content-between w-100 align-items-center">
          <h6 className="bold">
            {t("Car return in another branch") as string}
          </h6>
          <Switch
            key={`${rentalData?.rentalDetails} ${handover_in_another_branch}`}
            checked={
              bookingId
                ? rentalData?.rentalDetails?.handoverAntherCity
                : handover_in_another_branch?.checked
            }
            onChange={switchHandler}
            inputProps={{ "aria-label": "controlled" }}
          />
        </div>
      </div>
      {(handover_in_another_branch?.checked && !bookingId) ||
      (bookingId && rentalData?.rentalDetails?.handoverAntherCity) ? (
        <div>
          {selectedBranch ? (
            <>
              <span className="color-12 d-block text-align-localized">
                {t("Service Fee") as string}
              </span>
              <p>
                {!selectedBranch.isFree ? (
                  <span style={{ color: "#6FCFA1" }} className="d-flex gap-2px">
                    <span style={{ order: i18n.language === "ar" ? 1 : 0 }}>
                      <RiyalSymbol />
                    </span>
                    <span style={{ order: i18n.language === "ar" ? 0 : 1 }}>
                      {`${
                        selectedBranch.price
                          ? `${selectedBranch.price}`
                          : t("Free")
                      }`}
                    </span>
                  </span>
                ) : (
                  (t("Free") as string)
                )}
              </p>
            </>
          ) : null}
          <div className="text-align-localized">
            <label className="mt-4 mb-1 color-12">
              {t("Choose car return location") as string}
            </label>
            <ReactSelect
              defaultValue={
                handover_in_another_branch?.option ||
                branchesWithCitiesGroupedOptions[0]?.options[0] ||
                {}
              }
              value={selectedBranch}
              onChange={(e) => {
                setSelectedBranch(e);
                dispatch(
                  setHandoverInAnotherBranch({
                    branchId: e.value,
                    option: e,
                    checked: handover_in_another_branch?.checked,
                  })
                );
              }}
              options={branchesWithCitiesGroupedOptions || []}
              formatGroupLabel={formatGroupLabel}
            />
          </div>
        </div>
      ) : null}
    </Div>
  );
}

export default memo(HandoverInAnotherBranch);
