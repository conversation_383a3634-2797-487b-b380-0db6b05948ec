import { gql } from "@apollo/client";
const Makes_Query = gql`
  query Makes($limit: Int, $status: Boolean) {
    makes(limit: $limit, status: $status) {
      collection {
        name
        arName
        enName
        id
      }
    }
  }
`;

const ModelsOfMake_Query = gql`
  query Make($id: ID!) {
    make(id: $id) {
      id
      carModels {
        name
        arName
        enName
        id
      }
    }
  }
`;

const Version_Query = gql`
  query CarVersions(
    $isActive: Boolean
    $carMakeId: ID
    $carModelId: Int
    $limit: Int
  ) {
    carVersions(
      isActive: $isActive
      carMakeId: $carMakeId
      carModelId: $carModelId
      limit: $limit
    ) {
      collection {
        id
        year
      }
    }
  }
`;

const Insurances_Query = gql`
  query Insurances {
    insurances {
      id
      name
      details
      enName
      arName
    }
  }
`;
const ExtraService_Query = gql`
  query extraServices(
    $isActive: Boolean
    $isDisplayed: Boolean
    $limit: Int
    $page: Int
  ) {
    extraServices(
      isActive: $isActive
      isDisplayed: $isDisplayed
      limit: $limit
      page: $page
    ) {
      collection {
        arDescription
        arSubtitle
        arTitle
        description
        displayOrder
        enDescription
        enSubtitle
        enTitle
        homepageIconUrl
        iconUrl
        id
        isActive
        isDisplayed
        isSpecial
        payType
        subtitle
        title
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

const AlliesBranchesByCar_Query = gql`
  query cars(
    $canDelivery: Boolean
    $canHandover: Boolean
    $canHandoverInAntherCity: Boolean
    $canHandoverInBranch: Boolean
    $carModelName: String
    $carName: String
    $carVersionName: String
    $couponId: ID
    $dailyPrice: Int
    $dailyPriceFrom: Int
    $dailyPriceTo: Int
    $distance: Float
    $dropOffDeliverLat: Float
    $dropOffDeliverLng: Float
    $dropOffLocationId: ID
    $extraServices: [ID!]
    $filter: String
    $insuranceId: ID
    $insuranceType: Int
    $isActive: Boolean
    $isUnlimited: Boolean
    $kmFrom: Float
    $kmTo: Float
    $lat: Float
    $limit: Int
    $lng: Float
    $make: ID
    $makeName: String
    $model: ID
    $page: Int
    $pickEndDate: String
    $pickEndTime: String
    $pickStartDate: String
    $pickStartTime: String
    $pickUpDeliverLat: Float
    $pickUpDeliverLng: Float
    $pickUpLocationId: ID
    $sortBy: String
    $transmission: String
    $userLat: Float
    $userLng: Float
    $vehicleType: ID
    $version: ID
    $yearFrom: Int
    $yearTo: Int
  ) {
    cars(
      canDelivery: $canDelivery
      canHandover: $canHandover
      canHandoverInAntherCity: $canHandoverInAntherCity
      canHandoverInBranch: $canHandoverInBranch
      carModelName: $carModelName
      carName: $carName
      carVersionName: $carVersionName
      couponId: $couponId
      dailyPrice: $dailyPrice
      dailyPriceFrom: $dailyPriceFrom
      dailyPriceTo: $dailyPriceTo
      distance: $distance
      dropOffDeliverLat: $dropOffDeliverLat
      dropOffDeliverLng: $dropOffDeliverLng
      dropOffLocationId: $dropOffLocationId
      extraServices: $extraServices
      filter: $filter
      insuranceId: $insuranceId
      insuranceType: $insuranceType
      isActive: $isActive
      isUnlimited: $isUnlimited
      kmFrom: $kmFrom
      kmTo: $kmTo
      lat: $lat
      limit: $limit
      lng: $lng
      make: $make
      makeName: $makeName
      model: $model
      page: $page
      pickEndDate: $pickEndDate
      pickEndTime: $pickEndTime
      pickStartDate: $pickStartDate
      pickStartTime: $pickStartTime
      pickUpDeliverLat: $pickUpDeliverLat
      pickUpDeliverLng: $pickUpDeliverLng
      pickUpLocationId: $pickUpLocationId
      sortBy: $sortBy
      transmission: $transmission
      userLat: $userLat
      userLng: $userLng
      vehicleType: $vehicleType
      version: $version
      yearFrom: $yearFrom
      yearTo: $yearTo
    ) {
      collection {
        id
        dailyPrice
        totalMonthPrice
        carPrice
        carMonthsPrice
        unlimitedFeePerDay
        additionalDistanceCost
        distanceByDay
        isUnlimited
        isUnlimitedFree
        carInsurances {
          value
          monthlyValue
          insuranceName
          insuranceId
        }
        branch {
          id
          branchState
          address
          distanceBetweenBranchUser
          canDelivery
          branchState
          deliverToAirport
          isOnlinePayEnable
          districtName
          area {
            name
          }
          region {
            name
          }
          canHandoverInBranch
          canDelivery
          branchExtraServices {
            id
            isActive
            payType
            subtitle
            serviceValue
            allyExtraService {
              extraService {
                title
                description
                iconUrl
                homepageIconUrl
              }
            }
          }
          allyCompany {
            canHandoverInAntherCity
            id
            address
            allyClass
            rate
            allyRate {
              name
            }

            allyExtraServices {
              id
              isActive
              subtitle
              serviceValue
              extraService {
                id
                description
                iconUrl
                title
              }
            }
            allyExtraServicesForAlly {
              id
              isActive
              subtitle
              serviceValue
              extraService {
                id
                description
                iconUrl
                title
              }
            }
            allyExtraServicesForBranch {
              id
              isActive
              subtitle
              serviceValue
              extraService {
                id
                description
                iconUrl
                title
              }
            }
          }
        }
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

const CarProfile_Query = gql`
  query CarProfile($id: ID!, $lat: Float, $lng: Float, $period: Int) {
    carProfile(id: $id, lat: $lat, lng: $lng, period: $period) {
      allyConditions
      allyName
      availabilityStatus
      bookingStatus
      isUnlimited
      isUnlimitedFree
      distanceByDay
      carInsurances {
        value
        monthlyValue
        insuranceName
        insuranceId
      }
      branch {
        id
        branchState
        canHandover
        canDelivery
        availableHandoverBranches {
          isActive
          area {
            id
            name
          }
          id
          name
        }
        availableHandoverCities {
          isFree
          price
          pickUpCity {
            id
            name
          }
          dropOffCity {
            id
            name
          }
        }
        distanceBetweenBranchUser
        paymentMethod
        deliveryPaymentMethod
        branchWorkingDays {
          endTime
          id
          is24hAvailable
          isOn
          startTime
          weekDay
          weekDayString
        }
        deliveryBranchWorkingDays {
          endTime
          id
          is24hAvailable
          isOn
          startTime
          weekDay
          weekDayString
        }
        shiftsToday {
          endTime
          id
          is24hAvailable
          isOn
          startTime
          weekDay
          weekDayString
        }
        area {
          id
          name
        }
        region {
          id
          name
        }
        districtName
        address
        allyCompanyId
        branchClass
        branchExtraServices {
          id
          isRequired
          isActive
          payType
          subtitle
          serviceValue
          allyExtraService {
            extraService {
              title
              description
              iconUrl
              homepageIconUrl
            }
          }
        }
        allyCompany {
          canHandoverInAntherCity
          addedBy
          address
          allyClass
          allyExtraServices {
            id
            isRequired
            isActive
            subtitle
            serviceValue
            extraService {
              id
              description
              iconUrl
              title
            }
          }
          allyExtraServicesForAlly {
            id
            isActive
            subtitle
            isRequired
            serviceValue
            extraService {
              id
              description
              iconUrl
              title
            }
          }
          allyExtraServicesForBranch {
            id
            isActive
            subtitle
            isRequired
            serviceValue
            extraService {
              id
              description
              iconUrl
              title
            }
          }
          allyHandoverCities {
            allyCompanyId
            dropOffCity {
              arName
              centerLat
              centerLng
              enName
              id
              isAirport
              name
              order
              timezone
            }
            dropOffCityId
            id
            isFree
            pickUpCity {
              arName
              centerLat
              centerLng
              enName
              id
              isAirport
              name
              order
              timezone
            }
            pickUpCityId
            price
          }
          allyRate {
            arName
            displayOrder
            enName
            id
            isActive
            isBarq
            isInstantConfirmation
            name
          }
          allyRateId
          arName
          canHandoverInAntherCity
          conditions
          email
          enName
          features
          id
          isActive
          lat
          lng
          lonlat
          name
          rate
        }
      }
      branchClass
      branchId
      carInsurances {
        carId
        id
        insuranceDetails
        insuranceId
        insuranceName
        monthlyValue
        value
        monthlyValue
      }
      carModelId
      carThumb
      carVersionId
      carVersion {
        images
      }
      carVersionName
      dailyPrice
      distance
      distanceByWeek
      guaranteeAmount
      id
      lat
      lng
      makeId
      monthlyInsurancePerDay
      monthlyPrice
      transmission
      transmissionName
      unlimitedFeePerDay
      updatedBy
      vehicleTypeName
      weeklyPrice
      ## RentToOwnData ##
      carImages
      carMakeName
      carModelName
      year
      distanceByMonth
      distanceBetweenCarUser
      additionalDistanceCost
      ownCarDetail {
        availableAfterDays
        ownCarMedia {
          id
          displayOrder
          mediaType
          mediaUrl
        }
        ownCarPlans {
          id
          isActive
          noOfMonths
          finalInstallment
        }
        ownCarFeature {
          features {
            id
            icon
            name
            isActive
          }
          colorName
          km
          transmissionName
        }
      }
    }
  }
`;

const AvailableCar_Query = gql`
  query CarAvailability(
    $carId: ID!
    $dropOffDate: String!
    $dropOffTime: String!
    $pickUpDate: String!
    $pickUpTime: String!
  ) {
    carAvailability(
      carId: $carId
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      pickUpDate: $pickUpDate
      pickUpTime: $pickUpTime
    ) {
      status
    }
  }
`;

const AboutRentPrice_Query = gql`
  query GetRentPrice(
    $carId: ID!
    $deliverLat: Float
    $deliverLng: Float
    # Can be no_delivery, one_way or two_ways
    $deliveryType: String
    $deliveryPrice: Float
    $handoverPrice: Float
    $handoverBranchPrice: Float
    $handoverBranch: ID
    $dropOffDate: String
    $dropOffTime: String
    $insuranceId: ID
    $pickUpDate: String
    $pickUpTime: String
    $allyExtraServices: [ID!]
    $branchExtraServices: [ID!]
    $couponId: ID
    $isUnlimited: Boolean
    $usedPrice: Float
    $paymentMethod: AboutPricePaymentMethod
    $payWithInstallments: Boolean
    $walletPaidAmount: Float
    $withWallet: Boolean
    $ownCarPlanId: ID
    $paymentBrand: CouponPaymentBrand
  ) {
    aboutRentPrice(
      carId: $carId
      deliverLat: $deliverLat
      deliverLng: $deliverLng
      deliveryPrice: $deliveryPrice
      handoverPrice: $handoverPrice
      handoverBranchPrice: $handoverBranchPrice
      handoverBranch: $handoverBranch
      deliveryType: $deliveryType
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      insuranceId: $insuranceId
      pickUpDate: $pickUpDate
      pickUpTime: $pickUpTime
      allyExtraServices: $allyExtraServices
      branchExtraServices: $branchExtraServices
      couponId: $couponId
      isUnlimited: $isUnlimited
      usedPrice: $usedPrice
      paymentMethod: $paymentMethod
      payWithInstallments: $payWithInstallments
      walletPaidAmount: $walletPaidAmount
      withWallet: $withWallet
      ownCarPlanId: $ownCarPlanId
      paymentBrand: $paymentBrand
    ) {
      packages {
        daysCount
        name
        price
        selected
        smallestRate
        highlyRequested
      }
      totalAmountDue
      deliveryPrice
      handoverInTheSameCityPrice
      addsPrice
      dailyPrice
      numberOfDays
      priceBeforeDiscount
      discountValue
      discountPercentage
      discountType
      priceBeforeInsurance
      insuranceValue
      handoverPrice
      isUnlimited
      isUnlimitedFree
      priceBeforeTax
      valueAddedTaxPercentage
      taxValue
      totalPrice
      availablePaymentMethods
      couponDiscount
      discountType
      couponCode
      isCouponApplied
      couponErrorMessage
      pricePerDay
      unlimitedFeePerDay
      totalUnlimitedFee
      allyExtraServices {
        extraService {
          title
        }
        allyCompanyId
        arSubtitle
        enSubtitle
        extraServiceId
        id
        isActive
        isRequired
        payType
        serviceValue
        showFor
        subtitle
        totalServiceValue
      }
      branchExtraServices {
        allyExtraService {
          extraService {
            title
          }
        }
        allyExtraServiceId
        arSubtitle
        branchId
        enSubtitle
        id
        isActive
        isRequired
        payType
        serviceValue
        subtitle
        totalServiceValue
      }
      installmentsBreakdown {
        amount
        amountDue
        dueDate
        installmentNumber
        status
      }
      walletAmount
      rentToOwnInstallmentBreakdown {
        monthlyInstallment
        finalInstallment
        firstInstallment
        firstPayment
        installmentNumber
      }
      remainingDueInstallmentsAmount
    }
  }
`;

const Branch_Data = gql`
  query Branch($id: ID!, $userLat: Float, $userLng: Float) {
    branch(id: $id, userLat: $userLat, userLng: $userLng) {
      id
      name
      branchState
      distanceBetweenBranchUser
      districtName
      lat
      lng
      branchClass
      rate
      paymentMethod
      allyCompany {
        allyClass
        rate
        allyRate {
          id
          name
        }
      }
      officeNumber
    }
  }
`;

export {
  Makes_Query,
  ModelsOfMake_Query,
  Version_Query,
  Insurances_Query,
  AlliesBranchesByCar_Query,
  ExtraService_Query,
  AvailableCar_Query,
  CarProfile_Query,
  AboutRentPrice_Query,
  Branch_Data,
};
